# ----------------------------------------------------------------------
#                    LOW MARGIN NOTIFIER - CONSTANTS
# ----------------------------------------------------------------------
import os
import sys

import numpy as np

sys.path.append(os.path.abspath("../"))
from notification_utils import NotificationType

# API constants
API_LINK_DEV = "https://ij1yddlun5.execute-api.us-east-1.amazonaws.com/dev-1"
API_LINK_PROD = "https://hz9ylqv3we.execute-api.us-east-1.amazonaws.com/prod-1"

# Email constants
SUBJECT_DEV = "[Notifications][Dev] Truce Losing Loads Report - {notification_date}"
SUBJECT_PROD = "Truce Losing Loads Report - {notification_date}"
TYPE = NotificationType.INSIGHT.value
SENDER = "low_margin_notifier"
SEND_EMAIL = True
BCC = ["<EMAIL>"]

# Query constants
QUERY_PARAMS = {
    "is_prod": False,
    "brokerId": "{id}",
    "ingestion_time_window": "{week_ago}",
    "ship_time_window": "{month_ago}",
    "margin_percent_limit": 0.0,  # Margin percent limit < 0.0%
    "use_cache": True,
}

# Computation constants for overall savings range
UPPER_BOUND_COGS_MARGIN = 0.08  # 8% margin
LOWER_BOUND_COGS_MARGIN = 0.12  # 12% margin

# RNG Brackets for suggested rate.
# Cogs range -> target margin range
# For given cogs, compute suggested rate to achieve a margin in target range.
# Ex: cogs == 500, falls in bracket (-np.inf, 500) -> target margin between 14%-15%.
# Suggested rate computation is done by 500 / (1 - rand(0.14, 0.15)).
RNG_BRACKETS = {
    # Ex: 14%-15% margin applied for cogs < 500
    (-np.inf, 500): (0.14, 0.15),
    (501, 1000): (0.12, 0.13),
    (1001, 1500): (0.1, 0.12),
    (1501, 2000): (0.09, 0.11),
    (2001, 3000): (0.08, 0.09),
    (3001, np.inf): (0.06, 0.08),
}

BUSINESS_HOURS = 10

# User type whitelist
USER_TYPE_WHITELIST = "shipper"

# Pixel tracking constants
TRACKING_URL_DEV = "https://cukxv5x7l3.execute-api.us-east-1.amazonaws.com/test/open?email=[EMAIL]&type=[TYPE]"
TRACKING_URL_PROD = ""

EMAIL_TAG = "[EMAIL]"
TYPE_TAG = "[TYPE]"

NOTIFICATION_TYPE = "low-margin"
