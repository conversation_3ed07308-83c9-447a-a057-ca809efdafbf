from string import Template

email_template = Template(
    """
        <html>
            <head>
                <style>
                    #shipmenttable {
                        border-collapse: collapse;
                        }

                    #shipmenttable td, #shipmenttable th {
                        border: 1px solid #ddd;
                        padding: 10px;
                    }

                    #shipmenttable th {
                        padding-top: 12px;
                        padding-bottom: 12px;
                        text-align: left;
                    }
                </style>
            </head>
            <body>
                <p>We detected losing loads for $broker on the following loads. You had <b style="font-size: 14px;">$count</b> losing loads with total losses of <b style="font-size: 14px;">$total</b>.</p>
                <p>The occurence counter (col Occ.) represents the number of times this shipper / lane combination has appeared on the losing loads report.</p>
                <table id="shipmenttable">
                    <tr>
                    <th>Shipper</th>
                    <th>Lane</th>
                    <th>Equip.</th>
                    <th>Occ.</th>
                    <th>Ship Date</th>
                    <th>Broker Load #</th>
                    <th>Spend</th>
                    <th>Truck Cost</th>
                    <th>Pre-Book</th>
                    <th>Margin</th>
                    </tr>
                $ROWS
                </table>
                <p>Simply reply to this email with any questions or concerns,
                    or login to our platform https://www.truce.io/app to learn more!</p>
                <p>Thanks, <br />
                The Truce Team</p>
                <br />
                You can <a href="https://www.truce.io/app">unsubscribe</a> from notifications in our platform!
                <img src="$TRACKING" width="1" height="1" style="display:none;"/>
            </body>
        </html>
    """
)

row_template = Template(
    """
        <tr>
            <td>$shipper</td>
            <td>$origin_city, $origin_state -> $dest_city, $dest_state</td>
            <td>$equipment_type</td>
            <td>$occurrence</td>
            <td>$ship_date</td>
            <td>$shipment_primary_ref</td>
            <td>$spend</td>
            <td>$cost</td>
            <td>$prebook</td>
            <td style="font-size: 15px;"><b>$margin_dollars / $margin_percent</b></td>
        </tr>
    """
)
