# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.


class QueryGenerator:
    def __init__(self, query_params):
        # Parameters
        self.db = "mvp_db" if query_params.get("is_prod") else "mvp_db_dev"
        self.broker_id = query_params.get("brokerId")
        self.email = query_params.get("email")

        # Rolling window
        self.week_ago = query_params.get("ingestion_time_window")
        self.month_ago = query_params.get("ship_time_window")

        # Limit
        self.limit = query_params.get("margin_percent_limit", 0.0)

        # Filters
        self.cogs_accessorial_filter = "-COALESCE(cogsAccessorial,0)"
        self.revenue_accessorial_filter = "-COALESCE(revenueAccessorial,0)"
        self.cache_filter = (
            "notificationID is NULL AND" if query_params.get("use_cache", True) else ""
        )

    def scan_db(self):
        # REMOVE ACCESORIALS
        scan = f"""
                SELECT distinct id,
                        brokerPrimaryReference,
                        brokerName,
                        shipperName,
                        laneTableId,
                        shipperTableId,
                        originCloseTime,
                        destinationCloseTime,
                        revenueTotal,
                        cogsTotal,
                        preBook,
                        CASE
                            WHEN COALESCE(cogsAccessorial,0) < 0 THEN cogsTotal
                            ELSE cogsTotal{self.cogs_accessorial_filter}
                        END cogs,
                        (revenueTotal-cogsTotal) as margin_dollars,
                        clt,
                        cityTableOriginCity,
                        cityTableOriginState,
                        cityTableDestinationCity,
                        cityTableDestinationState,
                        equipmenttype,
                        sm.shipmentIngestionTime,
                        shipmentClass
                from {self.db}.shipments as s
                join (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype from {self.db}.lanes) AS l ON s.laneid = l.lanetableid
                join (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                join (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                JOIN (SELECT id AS brokerTableId, name AS brokerName FROM {self.db}.brokers) AS b ON s.brokerId = b.brokerTableId
                JOIN (SELECT id AS shipperTableId, name AS shipperName FROM {self.db}.shippers) AS sr ON s.shipperId = sr.shipperTableId
                JOIN (SELECT shipmentId AS sid, shipmentIngestionTime from {self.db}.shipmentmetadata) AS sm on s.id = sm.sid
                LEFT JOIN (SELECT shipmentId, notificationId from {self.db}.low_margin_notifications) AS n on s.id = n.shipmentId
                WHERE sm.shipmentIngestionTime > '{self.week_ago}' AND
                        {self.cache_filter}
                        (revenueTotal-cogsTotal)/revenueTotal < {self.limit} AND
                        originCloseTime > '{self.month_ago}' AND
                        brokerID='{self.broker_id}' AND
                        shipmentClass = 'canonical'
                ORDER BY shipperName,
                        cityTableOriginCity,
                        cityTableOriginState,
                        cityTableDestinationCity,
                        cityTableDestinationState,
                        margin_dollars DESC;
                """
        return scan

    def update_low_margin_notifs(self, shipment_id, notification_id):
        update = f"""
                INSERT INTO {self.db}.low_margin_notifications (shipmentId, notificationId)
                VALUES ('{shipment_id}', '{notification_id}');
        """
        return update
