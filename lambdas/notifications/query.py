from threading import Thread

from common import credentials


class ThreadQuery(Thread):
    CREDENTIALS = credentials.Credentials()
    secret_arn = CREDENTIALS.rds_secret_arn
    cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"
    database_name = "mvp_db_dev"

    def __init__(
        self, client=None, group=None, name=None, args=(), kwargs={}, Verbose=None
    ):
        Thread.__init__(self, group, self.query, name, args, kwargs)
        self.client = client
        self._return = None

    def run(self):
        if self._target is not None:
            self._return = self._target(*self._args, **self._kwargs)

    def join(self, *args):
        Thread.join(self, *args)
        return self._return

    def query(self, query_text):
        include_metadata = True
        response = self.client.execute_statement(
            resourceArn=self.cluster_arn,
            database=self.database_name,
            secretArn=self.secret_arn,
            sql=query_text,
            includeResultMetadata=include_metadata,
        )
        return response
