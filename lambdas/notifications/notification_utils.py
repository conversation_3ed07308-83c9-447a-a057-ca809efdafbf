# Utils and constants file general to all notifications
import random
from datetime import date
from datetime import datetime
from enum import Enum
from typing import Dict
from typing import List

import boto3
from query import ThreadQuery
from query_generator_global import QueryGeneratorGlobal


# Enum for supported notification types:
#   INSIGHT : insight or optimization for recipient
#   MESSAGE : general message or communication for recipient
#   WARNING : urgent alert or warning for recipient
class NotificationType(Enum):
    INSIGHT = "insight"
    MESSAGE = "message"
    ALERT = "alert"


def eliminates_nones(value) -> str:
    """Replaces None with empty string if none."""
    if value is None:
        return ""
    return value


def format_dollars(value: str) -> str:
    """Formats number value to dollar amount, no cents."""
    if value == "":
        return ""

    value = int(round(float(value)))
    if value < 0:
        return "-$" + f"{abs(value):,}"
    return "$" + f"{value:,}"


def format_percent(value: str) -> str:
    """Formats number value to percent amount."""
    if value == "":
        return ""

    value = int(round(float(value) * 100))
    return str(value) + "%"


def format_datetime_string(
    value: str, format="%Y-%m-%d %H:%M:%S", output_format="%m-%d-%Y %I:%M %p"
) -> str:
    """Formats input date string in @format into @outputformat"""
    mydate = datetime.strptime(value, format)
    return mydate.strftime(output_format)


def minutes_to_hours_minutes(minutes: int) -> str:
    """Formats minutes into _ h _ m or _ m if under 1 hr."""
    minutes_modulo = minutes % 60
    hours = minutes // 60
    hour_str = "" if hours == 0 else f"{hours} h "
    return f"{hour_str}{minutes_modulo} m"


def get_subscribers(is_prod: bool, whitelist: str, notif_name: str) -> dict:
    """Pulls active subscribers from Dynamo."""
    dynamodb = boto3.resource("dynamodb")
    table_name = "NotificationSubscribers" if is_prod else "NotificationSubscribersDev"
    sub_table = dynamodb.Table(table_name)
    response = sub_table.scan()

    id_to_emails = {}
    for sub in response["Items"]:
        if "customer_type" not in sub:
            continue

        if "customer_type" in sub and sub["customer_type"] != whitelist:
            continue

        if notif_name in sub:
            hm_metadata = sub[notif_name]
            # only email if user is subscribed and if a notif should be sent today based on the frequency
            if hm_metadata[
                "subscribed"
            ]:  # and evaluate_frequency(hm_metadata["frequency"])
                id_to_emails[hm_metadata["id"]] = id_to_emails.get(
                    hm_metadata["id"], []
                ) + [sub["email"]]

    print(id_to_emails)
    return id_to_emails


def format(response: Dict[str, List[str]]) -> List[Dict[str, any]]:
    """Formats return response from RDS."""
    metadata = response["columnMetadata"]
    records = response["records"]

    return [
        {
            metadata[j]["label"]: (
                records[i][j][next(iter(records[i][j]))]
                if next(iter(records[i][j])) != "isNull"
                else None
            )
            for j in range(len(metadata))
        }
        for i in range(len(records))
    ]


def store_lane_occurrences(loads: List[Dict[str, any]], opp_id_name: str):
    lane_to_count = {}
    for load in loads:
        key = f"{load.get('lanetableid')}&{load.get(opp_id_name)}"
        lane_to_count[key] = lane_to_count.get(key, 0) + 1

    return lane_to_count


def get_notification_occurrences(
    client: boto3.client,
    qg: QueryGeneratorGlobal,
    lane_id: str,
    broker_id: str,
    shipper_id: str,
    notif_type: str,
):
    lane_occurrence_thread = ThreadQuery(
        name="get_notification_occurrences",
        client=client,
        args=(qg.get_lane_occurrences(lane_id, broker_id, shipper_id, notif_type),),
    )
    lane_occurrence_thread.start()
    return format(lane_occurrence_thread.join())


def start_occurrence_update_thread(
    client: boto3.client,
    qg: QueryGeneratorGlobal,
    lane_id: str,
    broker_id: str,
    shipper_id: str,
    new_count: int,
    notif_type: str,
    new_occurrence=False,
):
    update_occurrence_thread = ThreadQuery(
        name=f"update_occurrences_{random.randint(0, 60000)}",
        client=client,
        args=(
            qg.update_lane_occurrences(
                lane_id, broker_id, shipper_id, new_count, notif_type, new_occurrence
            ),
        ),
    )
    update_occurrence_thread.start()
    return update_occurrence_thread


def evaluate_frequency(frequency: str) -> bool:
    """
    Evaluate if notification should be sent based on the frequency
    Monday - 0, Tuesday - 1, Wednesday - 2, Thursday - 3, Friday - 4
    """
    return str(date.today().weekday()) in frequency.split(",")
