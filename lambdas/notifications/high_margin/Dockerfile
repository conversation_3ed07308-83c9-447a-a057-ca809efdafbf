FROM common:latest

# Install lambda requirements.
COPY lambdas/notifications/high_margin/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
ADD lambdas/notifications/notification_utils.py .
ADD lambdas/notifications/query_generator_global.py .
ADD lambdas/notifications/query.py .
ADD lambdas/notifications/high_margin .
ADD lambdas/notifications/high_margin/constants.py .
ADD lambdas/notifications/high_margin/email_template.py .
ADD lambdas/notifications/high_margin/query_generator.py .
ADD lambdas/notifications/high_margin/main.py .

# Set CMD to handler
CMD ["python", "main.py"]
