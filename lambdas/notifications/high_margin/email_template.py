from string import Template

email_template = Template(
    """
                                <html>
                                    <head>
                                        <style>
                                            #shipmenttable {
                                                border-collapse: collapse;
                                                }

                                            #shipmenttable td, #shipmenttable th {
                                                border: 1px solid #ddd;
                                                padding: 10px;
                                            }

                                            #shipmenttable th {
                                                padding-top: 12px;
                                                padding-bottom: 12px;
                                                text-align: left;
                                            }
                                        </style>
                                    </head>
                                    <body>
                                        <p>We have identified above-market margins for $shipper on the following loads.
                                            You could have saved between <b style="font-size: 14px;">$savings_lower</b> and
                                            <b style="font-size: 14px;">$savings_upper</b> by renegotiating rates.</p>
                                            <p>We have provided a suggested rate that can be presented to your broker partners as a starting point for negotiations.
                                            However, the final rate will depend on several factors such as the supply and demand in the market and the quality of service provided.
                                            </p>
                                        <table id="shipmenttable">
                                            <tr>
                                            <th>Broker</th>
                                            <th>Lane</th>
                                            <th>Equip.</th>
                                            <th>Occ.</th>
                                            <th>Ship Date</th>
                                            <th>Ref #</th>
                                            <th>Spend</th>
                                            <th>Truck Cost</th>
                                            <th>Margin</th>
                                            <th>Sug. All-In Rate</th>
                                            </tr>
                                        $ROWS
                                        </table>
                                        <p>Simply reply to this email with any questions or concerns,
                                           or login to our platform https://www.truce.io/app to learn more!</p>
                                        <p>Thanks, <br />
                                        The Truce Team</p>
                                        <br />
                                        You can <a href="https://www.truce.io/app">unsubscribe</a> from notifications in our platform!
                                        <img src="$TRACKING" width="1" height="1" style="display:none;"/>
                                    </body>
                                </html>
                            """
)

row_template = Template(
    """
                                            <tr>
                                                <td>$broker</td>
                                                <td>$origin_city, $origin_state -> $dest_city, $dest_state</td>
                                                <td>$equipment_type</td>
                                                <td>$occurrence</td>
                                                <td>$ship_date</td>
                                                <td>$shipment_primary_ref</td>
                                                <td>$spend</td>
                                                <td>$cost</td>
                                                <td style="font-size: 15px;"><b>$margin_dollars / $margin_percent</b></td>
                                                <td>$suggested</td>
                                            </tr>
                    """
)
