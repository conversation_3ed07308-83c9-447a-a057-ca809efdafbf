import argparse
import datetime
import os
import random
import sys

import boto3
import pytz
import requests
from constants import API_LINK_DEV
from constants import API_LINK_PROD
from constants import BARILLA_PERCENT_LIMIT
from constants import BARILLA_PROD_ID
from constants import BA<PERSON>LL<PERSON>_SPEND_LIMIT
from constants import BC<PERSON>
from constants import EMA<PERSON>_TAG
from constants import LOWER_BOUND_COGS_MARGIN
from constants import NOTIFICA<PERSON>ON_TYPE
from constants import QUERY_<PERSON>RA<PERSON>
from constants import RNG_BRACKETS
from constants import SEND_EMA<PERSON>
from constants import <PERSON>NDER
from constants import SUBJECT_DEV
from constants import SUBJECT_PROD
from constants import TRACKING_URL_DEV
from constants import TRACKING_URL_PROD
from constants import TYP<PERSON>
from constants import TYPE_TAG
from constants import UPPER_BOUND_COGS_MARGIN
from email_template import email_template
from email_template import row_template
from query_generator import QueryGenerator


sys.path.append(os.path.abspath("../"))
from notification_utils import (
    format_dollars,
    eliminates_nones,
    get_subscribers,
    format,
    store_lane_occurrences,
    get_notification_occurrences,
    start_occurrence_update_thread,
    format_percent,
)

from query import ThreadQuery
from query_generator_global import QueryGeneratorGlobal
import copy
from typing import Dict, List, Tuple
import time


# ----------------------------------------------------------------------
#         WARNING :: RUN WITHOUT --prod DURING DEVELOPMENT
# ----------------------------------------------------------------------
parser = argparse.ArgumentParser()
parser.add_argument(
    "--prod",
    action="store_true",
    help="Run a notification against prod db / subscribers. DO NOT INCLUDE WHEN MANUALLY RUNNING NOTIFICATIONS.",
)

parser.add_argument(
    "--update-counters",
    action="store_true",
    help="Update occurrence counters. Do not include during development to avoid unnecessary inflating counters.",
)


def scan_for_high_margin_shipments(
    qg: QueryGenerator, client: boto3.client
) -> List[Dict[str, any]]:
    """Scans for high margin shipments in database."""
    scan_thread = ThreadQuery(
        name="high_margin_scan", client=client, args=(qg.scan_db(),)
    )
    scan_thread.start()
    high_margin_loads = format(scan_thread.join())
    return high_margin_loads


def update_notification_mapping(
    qg: QueryGenerator, client: boto3.client, shipper_id: str, notification_id: str
) -> None:
    """Updates shipment to notification cache."""
    update_thread = ThreadQuery(
        name="update_notif_mapping",
        client=client,
        args=(qg.update_high_margin_notifs(shipper_id, notification_id),),
    )
    update_thread.start()
    update_thread.join()


def format_table_row(
    load: Dict[str, str],
    truck_cost_avgs: Dict[str, Tuple[float, float]],
    lane_counts: Dict[str, int],
) -> str:
    """Constructs row of load table in email from high margin results."""

    origin_city = load.get("cityTableOriginCity", "")
    origin_state = load.get("cityTableOriginState", "")
    dest_city = load.get("cityTableDestinationCity", "")
    dest_state = load.get("cityTableDestinationState", "")
    shipment_primary_ref = load.get("shipperPrimaryReference", "")
    equipment_type = load.get("equipmenttype", "")
    broker = load.get("brokerName", "")

    # Numerical values
    spend = format_dollars(load.get("revenueTotal", ""))
    cost = format_dollars(load.get("cogsTotal", ""))
    margin_dollars = format_dollars(load.get("margin_dollars", ""))
    margin_percent = (
        load.get("revenueTotal", 0) - load.get("cogsTotal", 0)
    ) / load.get("revenueTotal", 0)
    margin_percent = format_percent(margin_percent)

    # Suggested rate
    lane_broker_key = f"{load.get('lanetableid', '')}&{load.get('brokerTableId', '')}"
    avg_cogs_per_lane, offset = truck_cost_avgs[lane_broker_key]
    suggested_rate = format_dollars(avg_cogs_per_lane / offset)

    ship_date = load.get("originCloseTime", "")
    if ship_date != "" and ship_date is not None:
        ship_date = ship_date.split(" ")[0]
        y, m, d = ship_date.split("-")
        ship_date = "-".join([m, d, y])

    # Count
    occurrence = lane_counts.get(lane_broker_key, 1)

    return row_template.safe_substitute(
        origin_city=eliminates_nones(origin_city),
        origin_state=eliminates_nones(origin_state),
        dest_city=eliminates_nones(dest_city),
        dest_state=eliminates_nones(dest_state),
        equipment_type=eliminates_nones(equipment_type),
        shipment_primary_ref=eliminates_nones(shipment_primary_ref),
        spend=eliminates_nones(spend),
        cost=eliminates_nones(cost),
        broker=eliminates_nones(broker),
        ship_date=eliminates_nones(ship_date),
        margin_dollars=eliminates_nones(margin_dollars),
        suggested=suggested_rate,
        occurrence=occurrence,
        margin_percent=eliminates_nones(margin_percent),
    )


def send_notification(targets: List[str], content: str, is_prod: bool) -> str:
    """Sends notification email."""

    # TODO(P1): Create subscriber metadata table with timezone.
    current_datetime = datetime.datetime.now(pytz.timezone("US/Central"))
    current_datetime = current_datetime.strftime("%x")
    link = API_LINK_PROD if is_prod else API_LINK_DEV
    subject = SUBJECT_PROD if is_prod else SUBJECT_DEV

    r = requests.put(
        f"{link}/putNotification",
        json={
            "query_params": {
                "targets": targets,
                "email": int(SEND_EMAIL),
                "message": content,
                "subject": subject.format(notification_date=current_datetime),
                "sender": SENDER,
                "type": TYPE,
                "is_dev_env": int(not is_prod),
            }
        },
        timeout=60,
    )
    return r.content


def compute_potential_savings(loads: List[Dict[str, any]]) -> Tuple[str, str]:
    """Computes savings range by applying margin to cogs."""

    cogs_aggregate = 0
    old_revenue_aggregate = 0

    for load in loads:
        cogs = eliminates_nones(float(load.get("cogsTotal", "")))
        revenue = eliminates_nones(float(load.get("revenueTotal", "")))

        if cogs != "":
            cogs_aggregate += int(round(float(cogs)))

        if revenue != "":
            old_revenue_aggregate += int(round(float(revenue)))

    new_revenue_upper_subtrahend = cogs_aggregate / (1 - UPPER_BOUND_COGS_MARGIN)
    new_revenue_lower_subtrahend = cogs_aggregate / (1 - LOWER_BOUND_COGS_MARGIN)

    savings_upper = old_revenue_aggregate - new_revenue_upper_subtrahend
    savings_lower = old_revenue_aggregate - new_revenue_lower_subtrahend

    return format_dollars(savings_lower), format_dollars(savings_upper)


def store_lane_cogs_averages(
    loads: List[Dict[str, any]]
) -> Dict[str, Tuple[float, float]]:
    """Caches lane cogs totals and counts for computation of suggested rate."""

    truck_cost_counts = {}
    truck_cost_avgs = {}
    for load in loads:
        cogs = eliminates_nones(float(load.get("cogs", "")))
        if cogs == "":
            continue

        # Cogs avg for each distinct lane-broker pair.
        # TODO(P1): Fix column name capitalization to be consistent.
        lane_broker_key = (
            f"{load.get('lanetableid', '')}&{load.get('brokerTableId', '')}"
        )
        truck_cost_counts[lane_broker_key] = tuple(
            sum(t_pair)
            for t_pair in zip(truck_cost_counts.get(lane_broker_key, (0, 0)), (cogs, 1))
        )

    for key in truck_cost_counts:
        total_cogs, count = truck_cost_counts[key]

        for bracket in RNG_BRACKETS:
            lower_limit, upper_limit = bracket

            # If cogs are within brackets, apply margin within respective range.
            if total_cogs >= lower_limit and total_cogs <= upper_limit:
                rng_ll, rng_ul = RNG_BRACKETS[bracket]

                # Suggested rate is computed per lane as cogs_avg / (1 - rand(target_margin_range)).
                truck_cost_avgs[key] = (
                    total_cogs / count,
                    1 - random.uniform(rng_ll, rng_ul),
                )

    return truck_cost_avgs


def main():
    args = parser.parse_args()
    is_prod = args.prod
    update_counters = args.update_counters

    USER_TYPE_WHITELIST = "shipper"

    # TODO (P1): Add subscriber constraints, whitelist / blacklist to db
    shippers = {}
    today = datetime.datetime.now().replace(microsecond=0)
    week_ago = today - datetime.timedelta(days=7)
    month_ago = today - datetime.timedelta(days=30)
    client = boto3.client("rds-data")
    shippers = get_subscribers(is_prod, USER_TYPE_WHITELIST, "high_margin")
    if not shippers:
        raise ValueError("No subscribers found. Notification will not be sent.")

    for id in shippers:
        print("Subscribers", id, shippers[id])

        # Fill query_params
        query_params = copy.deepcopy(QUERY_PARAMS)

        if id == BARILLA_PROD_ID:
            query_params["margin_percent_limit"] = BARILLA_PERCENT_LIMIT
            query_params["barilla"] = True
            query_params["spend_limit"] = BARILLA_SPEND_LIMIT

        query_params["is_prod"] = is_prod
        query_params["shipperId"] = QUERY_PARAMS["shipperId"].format(id=id)
        query_params["ingestion_time_window"] = QUERY_PARAMS[
            "ingestion_time_window"
        ].format(week_ago=week_ago)
        query_params["ship_time_window"] = QUERY_PARAMS["ship_time_window"].format(
            month_ago=month_ago
        )
        qg = QueryGenerator(query_params)
        qgg = QueryGeneratorGlobal(query_params)

        # Scan for high margin loads
        high_margin_loads = scan_for_high_margin_shipments(qg, client)
        if len(high_margin_loads) == 0:
            print("NO VALID OR UNSENT ROWS. NOT NOTIFYING.")
            continue

        # Compute savings values and cogs average cache
        agg_savings_lower, agg_savings_upper = compute_potential_savings(
            high_margin_loads
        )
        lane_cogs_adjusted_avgs = store_lane_cogs_averages(high_margin_loads)
        lane_to_count = store_lane_occurrences(high_margin_loads, "brokerTableId")

        # Reconcile lane counts
        update_threads = []
        for key in lane_to_count:
            lane_id, broker_id = tuple(key.split("&"))
            curr_occurences = get_notification_occurrences(
                client, qgg, lane_id, broker_id, id, "high_margin"
            )
            if curr_occurences != []:
                lane_to_count[key] = lane_to_count.get(key) + int(
                    curr_occurences[0]["occurrence"]
                )

            if update_counters:
                update_threads.append(
                    start_occurrence_update_thread(
                        client,
                        qgg,
                        lane_id,
                        broker_id,
                        id,
                        lane_to_count[key],
                        "high_margin",
                        new_occurrence=(curr_occurences != []),
                    )
                )

        for thread in update_threads:
            thread.join()

        rows = ""
        shipper = None
        for load in high_margin_loads:
            if shipper is None:
                shipper = load.get("shipperName", None)
            rows += format_table_row(load, lane_cogs_adjusted_avgs, lane_to_count)

        # Create email html
        email_html = email_template.safe_substitute(
            ROWS=rows,
            savings_lower=agg_savings_lower,
            savings_upper=agg_savings_upper,
            shipper=shipper,
            TRACKING=TRACKING_URL_PROD if is_prod else TRACKING_URL_DEV,
        )

        email_list = []
        shipper_emails = shippers[id]
        # If there are shippers that are subscribed, then combine those emails with the BCC email list
        if shipper_emails:
            email_list = shipper_emails + BCC

        # Send emails individually
        for email in email_list:
            try:
                # Replace tags with pixel tracking details
                final_email_html = email_html.replace(EMAIL_TAG, email).replace(
                    TYPE_TAG, NOTIFICATION_TYPE
                )
                notification_insertion = str(
                    send_notification([email], final_email_html, is_prod), "UTF-8"
                )

                # Cache shipments
                for load in high_margin_loads:
                    shipper_id = load["id"]
                    notification_id = notification_insertion.split("_")[1]
                    update_notification_mapping(qg, client, shipper_id, notification_id)

            except Exception as e:
                print(f"Failed to send notification: {e}")
                print(f"Response: {notification_insertion}")

            time.sleep(5)


if __name__ == "__main__":
    main()
