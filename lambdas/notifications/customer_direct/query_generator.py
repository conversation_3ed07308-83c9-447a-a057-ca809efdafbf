# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.


class QueryGenerator:
    def __init__(self, query_params):
        # Parameters
        self.db = "mvp_db" if query_params.get("is_prod") else "mvp_db_dev"
        self.email = query_params.get("email")
        is_shipper = query_params.get("is_shipper", False)

        if not is_shipper:
            self.id = query_params.get("brokerId")
            self.notif_id_join_point = "brokerId"
            self.user_query = f"brokerId='{self.id}'"
            self.reference = "brokerPrimaryReference"
            self.order_by = "shipperName"
        else:
            self.id = query_params.get("shipperId")
            self.notif_id_join_point = "shipperId"
            self.user_query = f"shipperId='{self.id}'"
            self.reference = "shipperPrimaryReference"
            self.order_by = "brokerName"

        # Rolling window
        self.week_ago = query_params.get("ingestion_time_window")
        self.month_ago = query_params.get("ship_time_window")

        # Filters
        self.cache_filter = (
            "notificationID is NULL AND" if query_params.get("use_cache", True) else ""
        )

    def scan_db(self):
        # REMOVE ACCESORIALS
        scan = f"""
                select distinct id,
                        {self.reference},
                        brokerName,
                        shipperName,
                        laneTableId,
                        brokerTableId,
                        shipperTableId,
                        originCloseTime,
                        destinationCloseTime,
                        destinationArrivalTime,
                        destinationDelayMinutes,
                        cityTableOriginCity,
                        cityTableOriginState,
                        cityTableDestinationCity,
                        cityTableDestinationState,
                        equipmenttype,
                        sm.shipmentIngestionTime,
                        customerDirect,
                        shipmentClass
                from {self.db}.shipments as s
                join (select id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype from {self.db}.lanes) AS l ON s.laneid = l.lanetableid
                join (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                join (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                JOIN (SELECT id as brokerTableId, name as brokerName FROM {self.db}.brokers) AS b ON s.brokerId = b.brokerTableId
                JOIN (SELECT id as shipperTableId, name as shipperName FROM {self.db}.shippers) AS sr ON s.shipperId = sr.shipperTableId
                JOIN (select shipmentId as sid, shipmentIngestionTime from {self.db}.shipmentmetadata) as sm on s.id = sm.sid
                left join (select shipmentId, userId, notificationID from {self.db}.late_customer_direct_notifications) as n on s.id = n.shipmentId AND s.{self.notif_id_join_point} = n.userId
                WHERE sm.shipmentIngestionTime > '{self.week_ago}' AND
                        {self.cache_filter}
                        originCloseTime > '{self.month_ago}' AND
                        {self.user_query} AND
                        customerDirect is not NULL AND
                        destinationDelayMinutes > 0.0 AND
                        shipmentClass = 'canonical'
                ORDER BY {self.order_by},
                        customerDirect,
                        cityTableOriginCity,
                        cityTableOriginState,
                        cityTableDestinationCity,
                        cityTableDestinationState,
                        destinationDelayMinutes;
                """
        return scan

    def update_high_margin_notifs(self, shipment_id, notification_id):
        update = f"""
                INSERT INTO {self.db}.late_customer_direct_notifications (shipmentId, userId, notificationId)
                VALUES ('{shipment_id}', '{self.id}', '{notification_id}');
        """
        return update
