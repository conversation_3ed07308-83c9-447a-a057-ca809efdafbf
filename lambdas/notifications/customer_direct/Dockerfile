FROM common:latest

COPY lambdas/notifications/customer_direct/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
ADD lambdas/notifications/notification_utils.py .
ADD lambdas/notifications/query_generator_global.py .
ADD lambdas/notifications/query.py .
ADD lambdas/notifications/customer_direct .
ADD lambdas/notifications/customer_direct/constants.py .
ADD lambdas/notifications/customer_direct/email_template.py .
ADD lambdas/notifications/customer_direct/query_generator.py .
ADD lambdas/notifications/customer_direct/main.py .

# Set CMD to handler
C<PERSON> ["python", "main.py"]
