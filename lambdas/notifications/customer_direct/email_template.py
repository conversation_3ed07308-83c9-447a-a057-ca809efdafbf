from string import Template

email_template = Template(
    """
                                <html>
                                    <head>
                                        <style>
                                            #shipmenttable {
                                                border-collapse: collapse;
                                                }

                                            #shipmenttable td, #shipmenttable th {
                                                border: 1px solid #ddd;
                                                padding: 10px;
                                            }

                                            #shipmenttable th {
                                                padding-top: 12px;
                                                padding-bottom: 12px;
                                                text-align: left;
                                            }
                                        </style>
                                    </head>
                                    <body>
                                        <p>We have identified late deliveries on customer-direct destinations for $global_customer on the following loads.</p>
                                        <p>
                                            $broker_sentence
                                        </p>
                                        <table id="shipmenttable">
                                            <tr>
                                            <th>$customer_title</th>
                                            <th>Customer Name</th>
                                            <th>Lane</th>
                                            <th>Equip.</th>
                                            <th>Occ.</th>
                                            <th>Ship Date</th>
                                            <th>$reference</th>
                                            <th>Delivery Appt.</th>
                                            <th>Dest. Arrival</th>
                                            <th>Hours Late</th>
                                            </tr>
                                        $ROWS
                                        </table>
                                        <p>Simply reply to this email with any questions or concerns,
                                           or login to our platform https://www.truce.io/app to learn more!</p>
                                        <p>Thanks, <br />
                                        The Truce Team</p>
                                        <br />
                                        You can <a href="https://www.truce.io/app">unsubscribe</a> from notifications in our platform!
                                        <img src="$TRACKING" width="1" height="1" style="display:none;"/>
                                    </body>
                                </html>
                            """
)

row_template = Template(
    """
                                            <tr>
                                                <td>$opp_cust</td>
                                                <td style="font-size: 15px;"><b>$customer</b></td>
                                                <td>$origin_city, $origin_state -> $dest_city, $dest_state</td>
                                                <td>$equipment_type</td>
                                                <td>$occurrence</td>
                                                <td>$ship_date</td>
                                                <td>$shipment_primary_ref</td>
                                                <td>$dest_close</td>
                                                <td>$dest_arrival</td>
                                                <td style="font-size: 15px;"><b>$hours_late</b></td>
                                            </tr>
                    """
)

broker_sentence = "You can try to avoid late deliveries on customer-direct destinations to prevent incurring extraneous fees for your shipper partner."
