import argparse
import datetime
import os
import sys

import boto3
import pytz
import requests
from constants import API_LINK_DEV
from constants import API_LINK_PROD
from constants import BC<PERSON>
from constants import <PERSON><PERSON><PERSON>_TAG
from constants import NOTIFICATION_TYPE
from constants import QUERY_PARAMS
from constants import SEND_<PERSON>MAIL
from constants import SEND<PERSON>
from constants import SUB<PERSON><PERSON>T_DEV
from constants import SUB<PERSON>ECT_PROD
from constants import TRACKING_URL_DEV
from constants import TRACKING_URL_PROD
from constants import TYPE
from constants import TYPE_TAG
from email_template import broker_sentence
from email_template import email_template
from email_template import row_template
from query_generator import QueryGenerator

sys.path.append(os.path.abspath("../"))
from notification_utils import (
    format_datetime_string,
    eliminates_nones,
    minutes_to_hours_minutes,
    get_subscribers,
    format,
    store_lane_occurrences,
    get_notification_occurrences,
    start_occurrence_update_thread,
)
from query import ThreadQuery
from query_generator_global import QueryGenerator<PERSON><PERSON>bal
import copy
from typing import Dict, List
import time


# ----------------------------------------------------------------------
#         WARNING :: RUN WITHOUT --prod DURING DEVELOPMENT
# ----------------------------------------------------------------------
parser = argparse.ArgumentParser()
parser.add_argument(
    "--prod",
    action="store_true",
    help="Run a notification against prod db / subscribers. DO NOT INCLUDE WHEN MANUALLY RUNNING NOTIFICATIONS.",
)

parser.add_argument(
    "--shipper",
    action="store_true",
    help="Run the same notification, but for a shipper",
)

parser.add_argument(
    "--update-counters",
    action="store_true",
    help="Update occurrence counters. Do not include during development to avoid unnecessary inflating counters.",
)


def update_notification_mapping(
    qg: QueryGenerator, client: boto3.client, shipper_id: str, notification_id: str
) -> None:
    """Updates shipment to notification cache."""
    update_thread = ThreadQuery(
        name="update_notif_mapping",
        client=client,
        args=(qg.update_high_margin_notifs(shipper_id, notification_id),),
    )
    update_thread.start()
    update_thread.join()


def format_table_row(
    load: Dict[str, str], is_shipper: bool, lane_counts: Dict[str, int]
) -> str:
    """Constructs row of load table in email from high margin results."""

    origin_city = load.get("cityTableOriginCity", "")
    origin_state = load.get("cityTableOriginState", "")
    dest_city = load.get("cityTableDestinationCity", "")
    dest_state = load.get("cityTableDestinationState", "")
    equipment_type = load.get("equipmenttype", "")

    if not is_shipper:
        primary_reference = load.get("brokerPrimaryReference", "")
        opp_cust = load.get("shipperName", "")
    else:
        primary_reference = load.get("shipperPrimaryReference", "")
        opp_cust = load.get("brokerName", "")

    # Delay metrics
    destination_close_time = load.get("destinationCloseTime", "")
    if destination_close_time != "" and destination_close_time is not None:
        destination_close_time = format_datetime_string(destination_close_time)

    destination_arrival_time = load.get("destinationArrivalTime", "")
    if destination_arrival_time != "" and destination_arrival_time is not None:
        destination_arrival_time = format_datetime_string(destination_arrival_time)

    minutes_late = load.get("destinationDelayMinutes", "")
    hours_late = ""
    if minutes_late != "" and minutes_late is not None:
        hours_late = minutes_to_hours_minutes(minutes_late)

    customer = load.get("customerDirect", "")

    ship_date = load.get("originCloseTime", "")
    if ship_date != "" and ship_date is not None:
        ship_date = ship_date.split(" ")[0]
        y, m, d = ship_date.split("-")
        ship_date = "-".join([m, d, y])

    storage_key = "brokerTableId" if is_shipper else "shipperTableId"
    lane_customer_key = f"{load.get('lanetableid', '')}&{load.get(storage_key, '')}"
    # Count
    occurrence = lane_counts.get(lane_customer_key, 1)

    return row_template.safe_substitute(
        origin_city=eliminates_nones(origin_city),
        origin_state=eliminates_nones(origin_state),
        dest_city=eliminates_nones(dest_city),
        dest_state=eliminates_nones(dest_state),
        equipment_type=eliminates_nones(equipment_type),
        shipment_primary_ref=eliminates_nones(primary_reference),
        opp_cust=eliminates_nones(opp_cust),
        ship_date=eliminates_nones(ship_date),
        customer=eliminates_nones(customer),
        dest_close=eliminates_nones(destination_close_time),
        dest_arrival=eliminates_nones(destination_arrival_time),
        hours_late=eliminates_nones(hours_late),
        occurrence=occurrence,
    )


def send_notification(targets: List[str], content: str, is_prod: bool) -> str:
    """Sends notification email."""

    # TODO(P1): Create subscriber metadata table with timezone.
    current_datetime = datetime.datetime.now(pytz.timezone("US/Central"))
    current_datetime = current_datetime.strftime("%x")
    link = API_LINK_PROD if is_prod else API_LINK_DEV
    subject = SUBJECT_PROD if is_prod else SUBJECT_DEV

    r = requests.put(
        f"{link}/putNotification",
        json={
            "query_params": {
                "targets": targets,
                "email": int(SEND_EMAIL),
                "message": content,
                "subject": subject.format(notification_date=current_datetime),
                "sender": SENDER,
                "type": TYPE,
                "is_dev_env": int(not is_prod),
            }
        },
        timeout=60,
    )
    return r.content


def scan_for_late_customer_directs(
    qg: QueryGenerator, client: boto3.client
) -> List[Dict[str, any]]:
    """Scans for high margin shipments in database."""
    scan_thread = ThreadQuery(
        name="late_customer_directs_scan", client=client, args=(qg.scan_db(),)
    )
    scan_thread.start()
    late_customer_directs = format(scan_thread.join())
    return late_customer_directs


def main():
    args = parser.parse_args()
    is_prod = args.prod
    is_shipper = args.shipper
    update_counters = args.update_counters

    if is_shipper:
        USER_TYPE_WHITELIST = "shipper"
    else:
        USER_TYPE_WHITELIST = "broker"

    # TODO (P1): Add subscriber constraints, whitelist / blacklist to db
    subscribers = {}
    today = datetime.datetime.now().replace(microsecond=0)
    week_ago = today - datetime.timedelta(days=14)
    month_ago = today - datetime.timedelta(days=30)
    client = boto3.client("rds-data")
    subscribers = get_subscribers(is_prod, USER_TYPE_WHITELIST, "customer_direct")
    assert bool(subscribers), "NO SUBSCRIBERS. NOT NOTIFYING."

    for id in subscribers:
        print("Subscribers", id, subscribers[id])

        # Fill query_params
        query_params = copy.deepcopy(QUERY_PARAMS)
        query_params["is_prod"] = is_prod
        query_params["is_shipper"] = is_shipper
        query_params["brokerId"] = QUERY_PARAMS["brokerId"].format(id=id)
        query_params["shipperId"] = QUERY_PARAMS["shipperId"].format(id=id)
        query_params["ingestion_time_window"] = QUERY_PARAMS[
            "ingestion_time_window"
        ].format(week_ago=week_ago)
        query_params["ship_time_window"] = QUERY_PARAMS["ship_time_window"].format(
            month_ago=month_ago
        )
        qg = QueryGenerator(query_params)
        qgg = QueryGeneratorGlobal(query_params)

        # Scan for late customer direct loads
        late_loads = scan_for_late_customer_directs(qg, client)
        if len(late_loads) == 0:
            print("NO VALID OR UNSENT ROWS. NOT NOTIFYING.")
            continue

        storage_key = "brokerTableId" if is_shipper else "shipperTableId"
        lane_to_count = store_lane_occurrences(late_loads, storage_key)

        # Reconcile lane counts
        update_threads = []
        for key in lane_to_count:
            lane_id, customer_id = tuple(key.split("&"))
            curr_occurences = get_notification_occurrences(
                client, qgg, lane_id, customer_id, id, "customer_direct"
            )
            if curr_occurences != []:
                lane_to_count[key] = lane_to_count.get(key) + int(
                    curr_occurences[0]["occurrence"]
                )

            if update_counters:
                update_threads.append(
                    start_occurrence_update_thread(
                        client,
                        qgg,
                        lane_id,
                        customer_id,
                        id,
                        lane_to_count[key],
                        "customer_direct",
                        new_occurrence=(curr_occurences != []),
                    )
                )

        for thread in update_threads:
            thread.join()

        # Construct load table
        rows = ""
        global_customer_name = None
        for load in late_loads:
            if global_customer_name is None:
                key = "brokerName" if not is_shipper else "shipperName"
                global_customer_name = load.get(key, None)
            rows += format_table_row(load, is_shipper, lane_to_count)

        # Create email html
        email_html = email_template.safe_substitute(
            ROWS=rows,
            global_customer=global_customer_name,
            customer_title="Broker" if is_shipper else "Shipper",
            reference="Ref #" if is_shipper else "Broker Ref #",
            broker_sentence="" if is_shipper else broker_sentence,
            TRACKING=TRACKING_URL_PROD if is_prod else TRACKING_URL_DEV,
        )

        email_list = []
        subscriber_emails = subscribers[id]
        # If there are shippers that are subscribed, then combine those emails with the BCC email list
        if subscriber_emails:
            email_list = subscriber_emails + BCC

        # Send emails individually
        for email in email_list:
            try:
                # Replace tags with pixel tracking details
                final_email_html = email_html.replace(EMAIL_TAG, email).replace(
                    TYPE_TAG, NOTIFICATION_TYPE
                )
                notification_insertion = str(
                    send_notification([email], final_email_html, is_prod), "UTF-8"
                )

                # Cache shipments
                for load in late_loads:
                    load_id = load["id"]
                    notification_id = notification_insertion.split("_")[1]
                    update_notification_mapping(qg, client, load_id, notification_id)

            except Exception as e:
                print(f"Failed to send notification: {e}")
                print(f"Response: {notification_insertion}")

            time.sleep(5)


if __name__ == "__main__":
    main()
