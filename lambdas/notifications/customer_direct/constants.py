# ----------------------------------------------------------------------
#                    CUSTOMER DIRECT NOTIFIER - CONSTANTS
# ----------------------------------------------------------------------
import os
import sys


sys.path.append(os.path.abspath("../"))
from notification_utils import NotificationType

# API constants
API_LINK_DEV = "https://ij1yddlun5.execute-api.us-east-1.amazonaws.com/dev-1"
API_LINK_PROD = "https://hz9ylqv3we.execute-api.us-east-1.amazonaws.com/prod-1"

# Email constants
SUBJECT_DEV = (
    "[Notifications][Dev] Truce Late Customer Direct Report - {notification_date}"
)
SUBJECT_PROD = "Truce Late Customer Direct Report - {notification_date}"
TYPE = NotificationType.INSIGHT.value
SENDER = "late_customer_direct_notifier"
SEND_EMAIL = True
BCC = ["<EMAIL>"]

# Query constants
QUERY_PARAMS = {
    "is_prod": False,
    "brokerId": "{id}",
    "shipperId": "{id}",
    "ingestion_time_window": "{week_ago}",
    "ship_time_window": "{month_ago}",
    "use_cache": True,
    "is_shipper": False,
}

# User type whitelist
USER_TYPE_WHITELIST = None

# Pixel tracking constants
TRACKING_URL_DEV = "https://cukxv5x7l3.execute-api.us-east-1.amazonaws.com/test/open?email=[EMAIL]&type=[TYPE]"
TRACKING_URL_PROD = ""

EMAIL_TAG = "[EMAIL]"
TYPE_TAG = "[TYPE]"

NOTIFICATION_TYPE = "customer-direct"
