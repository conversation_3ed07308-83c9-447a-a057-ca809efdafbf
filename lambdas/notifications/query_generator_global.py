# Query generator for all global queries


class QueryGeneratorGlobal:
    def __init__(self, query_params):
        self.db = "mvp_db" if query_params.get("is_prod") else "mvp_db_dev"

    def get_lane_occurrences(self, laneid, brokerid, userid, notiftype):
        lane_occ = f"""
            select * from {self.db}.notification_occurrences
            WHERE laneId='{laneid}' AND
                  brokerId='{brokerid}' AND
                  userId='{userid}' AND
                  notificationType='{notiftype}';
        """
        return lane_occ

    def update_lane_occurrences(
        self, laneid, brokerid, userid, new_count, notiftype, update=False
    ):
        if update:
            update_counter = f"""
                UPDATE {self.db}.notification_occurrences
                SET occurrence = {new_count}
                WHERE laneId='{laneid}' AND
                    brokerId='{brokerid}' AND
                    userId='{userid}' AND
                    notificationType='{notiftype}';
            """
        else:
            update_counter = f"""
                    INSERT INTO {self.db}.notification_occurrences
                    values ('{laneid}', '{brokerid}', '{userid}', '{notiftype}', {new_count});
            """
        return update_counter
