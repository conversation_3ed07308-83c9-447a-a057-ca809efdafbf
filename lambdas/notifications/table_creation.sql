-- Notifications Table
CREATE TABLE mvp_db.notifications (
    notificationId CHAR(36) NOT NULL,
    target VARCHAR(255) NOT NULL,
    notificationType VARCHAR(255),
    sender VARCHAR(255),
    notificationCreationDate DATETIME NOT NULL,
    notificationAccessDate DATETIME,
    status VARCHAR(255),
    emailStatus BOOL,
    subject TEXT(1000),
    message LONGTEXT,
    loadInWeb bool default false
);

-- New Notification Shipment Tracker
CREATE TABLE mvp_db.high_margin_notifications (
    shipmentId CHAR(36),
    notificationId CHAR(36) default NULL,
);

-- New Notification Shipment Tracker
CREATE TABLE mvp_db.low_margin_notifications (
    shipmentId CHAR(36),
    notificationId CHAR(36) default NULL
);

-- Late Customer Direct Shipment Tracker
CREATE TABLE mvp_db.late_customer_direct_notifications (
    shipmentId CHAR(36),
    userId CHAR(36),
    notificationId CHAR(36) default NULL,
    email VARCHAR(100) NOT NULL
);

-- Lane occurence for all notys
CREATE TABLE mvp_db.notification_occurrences (
    laneId CHAR(36),
    brokerId CHAR(36),
    userId CHAR(36),
    notificationType VARCHAR(255),
    occurrence INT DEFAULT 1
);
