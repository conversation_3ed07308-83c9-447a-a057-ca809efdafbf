# Set a base image that includes Lambda runtime API:
# Public docker image: https://gallery.ecr.aws/lambda/python
FROM public.ecr.aws/lambda/python:3.11

# Create the truce directory to hold all files and set it as the working directory
WORKDIR /var/task/truce

# Add the root directory to PYTHONPATH
ENV PYTHONPATH="${PYTHONPATH}:/var/task"

# Copy and install requirements.txt to environment
ADD common common

# Copy and install requirements.txt to environment
COPY setup.py .
RUN pip3 install .
RUN pip3 install -r common/requirements.txt

# Copy and install requirements.txt to environment
COPY lambdas/demo_data_refresh/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY lambdas/demo_data_refresh/constants/dev_constants.py ./constants.py
COPY lambdas/demo_data_refresh/lambda_function.py .

# Set CMD to handler
CMD [ "truce/lambda_function.lambda_handler" ]
