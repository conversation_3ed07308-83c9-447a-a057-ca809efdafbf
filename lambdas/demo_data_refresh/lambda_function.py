import boto3
from constants import <PERSON>ATABASE_NAME
from constants import DEMO_SHIPPER_ID

from common import credentials


def lambda_handler(event: dict, context) -> dict:
    """
    This lambda function refreshes the demo data so that it stays current.

    Initial Steps:
        1. Shift all datetime columns to current day
        2. Start lambda on a schedule which will run every day after that
    """
    CREDENTIALS = credentials.Credentials()
    secret_arn = CREDENTIALS.rds_secret_arn
    cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"
    database_name = DATABASE_NAME
    client = boto3.client("rds-data")

    # 1 shifts a unit forward, -1 shifts a unit backwards
    shift_value = 1
    # Check here for all acceptable units - https://www.w3schools.com/sql/func_mysql_date_add.asp
    shift_unit = "DAY"
    # Id of Demo Shipper (check shipper table in DB for correct id)
    demo_shipper_id = DEMO_SHIPPER_ID

    query_text = f"""
                    UPDATE {database_name}.shipments s
                    JOIN {database_name}.shipmentmetadata sm ON s.id = sm.shipmentid
                    SET
                    s.originOpenTime = DATE_ADD(s.originOpenTime, INTERVAL {shift_value} {shift_unit}),
                    s.originCloseTime = DATE_ADD(s.originCloseTime, INTERVAL {shift_value} {shift_unit}),
                    s.originArrivalTime = DATE_ADD(s.originArrivalTime, INTERVAL {shift_value} {shift_unit}),
                    s.originDepartureTime = DATE_ADD(s.originDepartureTime, INTERVAL {shift_value} {shift_unit}),
                    s.destinationOpenTime = DATE_ADD(s.destinationOpenTime, INTERVAL {shift_value} {shift_unit}),
                    s.destinationCloseTime = DATE_ADD(s.destinationCloseTime, INTERVAL {shift_value} {shift_unit}),
                    s.destinationArrivalTime = DATE_ADD(s.destinationArrivalTime, INTERVAL {shift_value} {shift_unit}),
                    s.destinationDepartureTime =  DATE_ADD(s.destinationDepartureTime, INTERVAL {shift_value} {shift_unit}),
                    s.loadCreationTime = DATE_ADD(s.loadCreationTime, INTERVAL {shift_value} {shift_unit}),
                    s.loadActivationTime = DATE_ADD(s.loadActivationTime, INTERVAL {shift_value} {shift_unit}),
                    s.carrierAssignedTime = DATE_ADD(s.carrierAssignedTime, INTERVAL {shift_value} {shift_unit}),
                    sm.shipmentIngestionTime = DATE_ADD(sm.shipmentIngestionTime, INTERVAL {shift_value} {shift_unit}),
                    sm.lastModifiedTime = CURRENT_TIMESTAMP
                    WHERE s.shipperid = '{demo_shipper_id}';
                """

    include_metadata = True

    try:
        response = client.execute_statement(
            resourceArn=cluster_arn,
            database=database_name,
            secretArn=secret_arn,
            sql=query_text,
            includeResultMetadata=include_metadata,
        )
        print(response)

    except Exception as e:
        print(e)
