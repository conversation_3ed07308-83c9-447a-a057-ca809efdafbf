import base64
import json
from datetime import datetime


def parse_payload(payload: dict) -> dict:
    """
    Create new dict with the desired fields exctracted from the payload provided

    Args:
        payload (dict): payload to parse

    Returns:
        dict: new dict with desired fields
    """
    parsed_payload = {}

    # Add values from payload event to list
    parsed_payload["event_type"] = payload.get("event_type", "NULL")
    parsed_payload["event_datetime_utc"] = payload.get("attributes", {}).get(
        "timestamp_utc", "NULL"
    )
    parsed_payload["event_version"] = payload.get("event_version", "NULL")
    parsed_payload["app_id"] = payload.get("application", {}).get("app_id", "NULL")
    parsed_payload["app_version_name"] = payload.get("application", {}).get(
        "version_name", "NULL"
    )
    parsed_payload["client_id"] = payload.get("client", {}).get("client_id", "NULL")
    parsed_payload["session_id"] = payload.get("session", {}).get("session_id", "NULL")
    parsed_payload["timezone"] = payload.get("attributes", {}).get("timezone", "NULL")
    parsed_payload["component_name"] = payload.get("attributes", {}).get(
        "componentname", "NULL"
    )
    parsed_payload["type"] = payload.get("attributes", {}).get("type", "NULL")
    parsed_payload["target"] = payload.get("attributes", {}).get("target", "NULL")
    parsed_payload["user_id"] = payload.get("attributes", {}).get("user_id", "NULL")
    parsed_payload["user_name"] = payload.get("attributes", {}).get("name", "NULL")
    parsed_payload["user_email"] = payload.get("attributes", {}).get("email", "NULL")
    parsed_payload["endpoint_status"] = payload.get("endpoint", {}).get(
        "endpointstatus", "NULL"
    )
    parsed_payload["endpoint_opt_out"] = payload.get("endpoint", {}).get(
        "optout", "NULL"
    )
    parsed_payload["endpoint_request_id"] = payload.get("endpoint", {}).get(
        "requestid", "NULL"
    )
    parsed_payload["endpoint_demographic_make"] = (
        payload.get("endpoint", {}).get("demographic", {}).get("make", "NULL")
    )
    parsed_payload["endpoint_demographic_model"] = (
        payload.get("endpoint", {}).get("demographic", {}).get("model", "NULL")
    )
    parsed_payload["endpoint_demographic_model_version"] = (
        payload.get("endpoint", {}).get("demographic", {}).get("modelversion", "NULL")
    )
    parsed_payload["endpoint_demographic_app_version"] = (
        payload.get("endpoint", {}).get("demographic", {}).get("appversion", "NULL")
    )
    parsed_payload["endpoint_demographic_platform"] = (
        payload.get("endpoint", {}).get("demographic", {}).get("platform", "NULL")
    )
    parsed_payload["endpoint_effective_date"] = payload.get("endpoint", {}).get(
        "effectivedate", "NULL"
    )
    parsed_payload["endpoint_id"] = payload.get("endpoint", {}).get("id", "NULL")
    parsed_payload["endpoint_cohort_id"] = payload.get("endpoint", {}).get(
        "cohortid", "NULL"
    )

    # Format timestamp so it can be easily read as a datetime column in QuickSight
    if parsed_payload["event_datetime_utc"] != "NULL":
        parsed_payload["event_datetime_utc"] = datetime.fromtimestamp(
            int(parsed_payload["event_datetime_utc"])
        ).strftime("%Y-%m-%dT%H:%M:%SZ")
    else:
        parsed_payload["event_datetime_utc"] = datetime.now().strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )

    return parsed_payload


def lambda_handler(event, context):
    output = []

    # Loop through records and process them
    for record in event["records"]:
        payload = base64.b64decode(record["data"]).decode("utf-8").lower()

        # Parse payload
        parsed_payload_values = parse_payload(json.loads(payload))

        # Construct output record needed by Kineses Data Firehouse
        output_record = {
            "recordId": record["recordId"],
            "result": "Ok",
            "data": base64.b64encode(
                (json.dumps(parsed_payload_values) + "\n").encode("utf-8")
            ).decode("utf-8"),
        }
        output.append(output_record)

    print("Successfully processed {} records.".format(len(event["records"])))

    return {"records": output}
