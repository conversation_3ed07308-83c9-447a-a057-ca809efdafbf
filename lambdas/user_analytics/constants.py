# Database metadata
DATABASE_NAME = "userAnalytics"
TABLE_NAME = "userEvents"

# Compute aggregate metrics over window to date
# Units in d - see https://docs.aws.amazon.com/timestream/latest/developerguide/date-time-functions.html
ANALYTICS_TIMEFRAME = "1d"

# Analytics blacklist : do not consider when computing analytics
ANALYTICS_BLACKIST = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]

# Website pages
PAGES = {
    "BrokerDashboard",
    "ShipperDashboard",
    "LaneDashboard",
    "BrokerDrilldown",
    "ShipperDrilldown",
    "LaneDrilldown",
    "Account",
    "Analytics",
    "Calculator",
    "Notifications",
}

# API constants
API_LINK = "https://ij1yddlun5.execute-api.us-east-1.amazonaws.com/dev-1"

# Email constants
TYPE = "message"
SENDER = "user_analytics_bot"
BCC = []
