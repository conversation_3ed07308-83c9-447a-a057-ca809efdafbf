---
swagger: "2.0"
info:
  description: Basic user analytics web API.
  version: 1.0.0
  title: User Analytics
schemes:
- https
- http
paths:
  /putAnalyticsEvent:
    put:
      tags:
      - user_analytics
      summary: endpoint for user analytics events
      description: Puts a given user website action in timeseries
      operationId: putAnalyticsEvent
      produces:
      - application/json
      parameters:
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/request'
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/response'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/error'
        "401":
          description: invalid user
          schema:
            $ref: '#/definitions/error'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/error'
definitions:
  request:
    type: object
    properties:
      query_params:
        type: object
        description: contains notification data
  response:
    type: object
    properties:
      code:
        type: integer
        format: int32
        description: contains status code
      records:
        type: array
        description: contains all records that matched request criteria
        items:
          type: object
          description: singular record in response array
  error:
    type: object
    properties:
      code:
        type: integer
        format: int32
        description: error status code
      message:
        type: string
        description: error message
