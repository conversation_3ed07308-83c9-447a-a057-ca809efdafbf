import json
import os
import sys

import boto3

sys.path.append(os.path.abspath("../"))
from constants import DATABASE_NAME, TABLE_NAME


def lambda_handler(event: dict, context) -> dict:
    """This lambda handles the insertion of analytics data from the frontend into a Timestream

    :param event: Frontend parameter with index_by (str) and event_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and error message if applicable.
    """

    event_params = event["event_params"]
    client = boto3.client("timestream-write")

    dimensions = [
        {"Name": "email", "Value": event_params.get("email")},
        {"Name": "event_name", "Value": event_params.get("event_name")},
        {"Name": "company", "Value": event_params.get("company")},
        {"Name": "role", "Value": event_params.get("role")},
    ]

    event_attributes = {
        "Dimensions": dimensions,
        "MeasureName": "attributes",
        "MeasureValue": json.dumps(
            event_params.get("custom_event_attributes"), separators=(",", ":")
        ),
        "MeasureValueType": "VARCHAR",
        "Time": event_params.get("event_time"),
    }

    records = [event_attributes]

    try:
        result = client.write_records(
            DatabaseName=DATABASE_NAME,
            TableName=TABLE_NAME,
            Records=records,
            CommonAttributes={},
        )
        print(
            "WriteRecords Status: [%s]" % result["ResponseMetadata"]["HTTPStatusCode"]
        )
    except client.exceptions.RejectedRecordsException as err:
        _print_rejected_records_exceptions(err)
    except Exception as err:
        print("Unknown error: ", err)

    return {
        "statusCode": 200,
        "headers": {
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "OPTIONS,POST,PUT",
        },
        "body": "Successful user analytics event!",
    }


def _print_rejected_records_exceptions(err):
    """
    Prints the records contained in the error response if they are rejected due to
    1. Duplicate entries
    2. Timestamp outside of memory retention duration
    3. Dimensions or Measures exceeding Timestream limits
    """
    print("RejectedRecords: ", err)
    for rr in err.response["RejectedRecords"]:
        print("Rejected Index " + str(rr["RecordIndex"]) + ": " + rr["Reason"])
        if "ExistingVersion" in rr:
            print("Rejected record existing version: ", rr["ExistingVersion"])


def main():
    e = {
        "event_params": {
            "event_name": "pageNav",
            "email": "<EMAIL>",
            "company": "Truce",
            "role": "admin",
            "custom_event_attributes": {"pageSource": "LaneDashboard"},
            "event_time": "1675622846250",
        }
    }

    lambda_handler(e, None)


if __name__ == "__main__":
    main()
