FROM common-lambda:latest

COPY lambdas/user_analytics/constants.py .
COPY lambdas/user_analytics/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY lambdas/user_analytics/jobs/daily_user_report/main.py .
COPY lambdas/user_analytics/jobs/daily_user_report/email_template.py .
COPY lambdas/user_analytics/jobs/daily_user_report/query_generator.py .

# Set CMD to handler
CMD [ "python", "main.py" ]
