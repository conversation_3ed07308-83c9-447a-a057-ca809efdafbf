# Computes daily metric reports
# Aggregate metrics:
# -- Total user logins
# -- Top user
# -- Top company
# Page metrics
# -- Page views by page
# User metrics:
# -- Event Table
# Graphs - TODO(P1):
# -- logins over week
import os
import sys

import boto3
from email_template import email_template
from email_template import event_table_row
from email_template import page_table_row
from query_generator import QueryGeneratorUserAnalytics


sys.path.append(os.path.abspath("../../"))
from constants import PAGES, API_LINK, TYPE, SENDER, BCC
import json
import requests
import datetime
import pytz


def format(response):
    metadata = response["ColumnInfo"]
    records = response["Rows"]

    return [
        {
            metadata[j]["Name"]: (records[i]["Data"][j]["ScalarValue"])
            for j in range(len(metadata))
        }
        for i in range(len(records))
    ]


def run_query(client, query_text):
    response = client.query(QueryString=query_text)
    return format(response)


def get_total_login_count(client, qg):
    response = run_query(client, qg.aggregate_login_count())
    l_count = response[0].get("total_logins", "")
    return l_count


def get_top_user(client, qg):
    response = run_query(client, qg.top_user())
    top_user = max(response, key=lambda x: x["logins"]).get("email")
    return top_user


def get_top_company(client, qg):
    response = run_query(client, qg.top_company())
    top_company = max(response, key=lambda x: x["logins"]).get("company")
    return top_company


def load_row(row):
    try:
        attributes = json.loads(row["measure_value::varchar"])
    except Exception:
        print(f"Could not convert {row}")
        return None

    return attributes


def get_views_per_page(client, qg):
    response = run_query(client, qg.views_per_page())
    page_view_counts = {page: 0 for page in list(PAGES)}
    for row in response:
        attributes = load_row(row)
        if attributes is None:
            continue

        page = attributes["pageSource"]
        count = row["view_count"]
        if page in PAGES:
            page_view_counts[page] = count

    page_table = page_table_row.safe_substitute(
        broker_dashboard_count=page_view_counts["BrokerDashboard"],
        shipper_dashboard_count=page_view_counts["ShipperDashboard"],
        lane_dashboard_count=page_view_counts["LaneDashboard"],
        broker_drilldown_count=page_view_counts["BrokerDrilldown"],
        shipper_drilldown_count=page_view_counts["ShipperDrilldown"],
        lane_drilldown_count=page_view_counts["LaneDrilldown"],
        account_count=page_view_counts["Account"],
        analytics_count=page_view_counts["Analytics"],
        calculator_count=page_view_counts["Calculator"],
        notification_count=page_view_counts["Notifications"],
    )
    return page_table


def get_events_per_user(client, qg):
    response = run_query(client, qg.events_per_user())
    event_counts = {
        event["email"]: {
            "company": event["company"],
            "role": event["role"],
            "logins": 0,
            "views": {f"{page}_views": 0 for page in list(PAGES)},
        }
        for event in response
    }

    for event in response:
        if event["event_name"] == "login":
            event_counts[event["email"]]["logins"] = event_counts[event["email"]][
                "logins"
            ] + int(event["event_count"])

        elif event["event_name"] == "pageView":
            attributes = load_row(event)
            if attributes is None:
                continue

            page = attributes["pageSource"]
            if page in PAGES:
                key = f"{page}_views"
                event_counts[event["email"]]["views"][key] = event_counts[
                    event["email"]
                ]["views"][key] + int(event["event_count"])
        else:
            print(f"Unsupported event type: {event['event_name']}")

    rows = ""
    for email in event_counts:
        db_views = (
            event_counts[email]["views"]["BrokerDashboard_views"]
            + event_counts[email]["views"]["ShipperDashboard_views"]
            + event_counts[email]["views"]["LaneDashboard_views"]
        )

        dd_views = (
            event_counts[email]["views"]["BrokerDrilldown_views"]
            + event_counts[email]["views"]["ShipperDrilldown_views"]
            + event_counts[email]["views"]["LaneDrilldown_views"]
        )

        rows += event_table_row.safe_substitute(
            user=email,
            company=event_counts[email]["company"],
            role=event_counts[email]["role"],
            logins=event_counts[email]["logins"],
            db_views=db_views,
            dd_views=dd_views,
            account_views=event_counts[email]["views"]["Account_views"],
            analytics_views=event_counts[email]["views"]["Analytics_views"],
            calculator_views=event_counts[email]["views"]["Calculator_views"],
            notification_views=event_counts[email]["views"]["Notifications_views"],
        )

    return rows


def send_notification(content: str) -> str:
    """Sends notification email."""

    current_datetime = datetime.datetime.now(pytz.timezone("US/Central"))
    current_datetime = current_datetime.strftime("%x")

    # TODO(P1): Create user analytics mailing list.
    targets = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    r = requests.put(
        f"{API_LINK}/putNotification",
        json={
            "query_params": {
                "targets": targets,
                "email": 1,
                "message": content,
                "subject": f"[User Analytics] Truce User Analytics Report - {current_datetime}",
                "sender": SENDER,
                "type": TYPE,
                "is_dev_env": 1,
                "bcc": BCC,
            }
        },
    )
    return r.content


def main():
    client = boto3.client("timestream-query")
    qg = QueryGeneratorUserAnalytics()

    # TODO (P3): Truce admin analytics
    # qg_admins
    _ = QueryGeneratorUserAnalytics(use_blacklist=False)

    admin = "NON-ADMINISTRATOR"
    l_count = get_total_login_count(client, qg)
    top_user = get_top_user(client, qg)
    top_company = get_top_company(client, qg)
    page_view_counts = get_views_per_page(client, qg)
    event_counts = get_events_per_user(client, qg)

    EMAIL_HTML = email_template.safe_substitute(
        admin=admin,
        l_count=l_count,
        top_user=top_user,
        top_company=top_company,
        PAGE_TABLE=page_view_counts,
        EVENT_TABLE=event_counts,
    )

    send_notification(EMAIL_HTML)


if __name__ == "__main__":
    main()
