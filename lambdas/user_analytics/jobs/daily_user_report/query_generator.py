import os
import sys

sys.path.append(os.path.abspath("../../"))
from constants import DATABASE_NAME, TABLE_NAME, ANALYTICS_TIMEFRAME, ANALYTICS_BLACKIST


class QueryGeneratorUserAnalytics:
    def __init__(self, use_blacklist=True):
        if use_blacklist:
            self.query_blacklist = (
                "AND email not in (" + str(ANALYTICS_BLACKIST)[1:-1] + ")"
            )
        else:
            self.query_blacklist = ""

    def aggregate_login_count(self):
        l_count = f"""
                SELECT count(*) as total_logins FROM "{DATABASE_NAME}"."{TABLE_NAME}"
                WHERE time between ago({ANALYTICS_TIMEFRAME}) and now()
                AND event_name='login'
                {self.query_blacklist}
                """
        return l_count

    def top_user(self):
        top_user = f"""
                    SELECT email, count(*) as logins FROM "{DATABASE_NAME}"."{TABLE_NAME}"
                    WHERE time between ago({ANALYTICS_TIMEFRAME}) and now()
                    AND event_name='login'
                    {self.query_blacklist}
                    GROUP BY email
                    ORDER BY logins DESC
                    """
        return top_user

    def top_company(self):
        top_company = f"""
                        SELECT company, count(*) as logins FROM "{DATABASE_NAME}"."{TABLE_NAME}"
                        WHERE time between ago({ANALYTICS_TIMEFRAME}) and now()
                        AND event_name='login'
                        {self.query_blacklist}
                        GROUP BY company
                        ORDER BY logins DESC
                        """
        return top_company

    def views_per_page(self):
        page_counts = f"""
                        SELECT measure_value::varchar, count(*) as view_count FROM "{DATABASE_NAME}"."{TABLE_NAME}"
                        WHERE time between ago(7d) and now()
                        AND event_name='pageView'
                        {self.query_blacklist}
                        GROUP BY measure_value::varchar
                        ORDER BY view_count DESC
                        """
        return page_counts

    def events_per_user(self):
        event_table = f"""
                        SELECT event_name,
                               role,
                               company,
                               email,
                               measure_name,
                               measure_value::varchar,
                               count(*) as event_count
                        FROM "{DATABASE_NAME}"."{TABLE_NAME}"
                        WHERE time between ago({ANALYTICS_TIMEFRAME}) and now()
                        {self.query_blacklist}
                        GROUP BY event_name, role, company, email, measure_name, measure_value::varchar
                        ORDER BY event_count DESC
                        """
        return event_table
