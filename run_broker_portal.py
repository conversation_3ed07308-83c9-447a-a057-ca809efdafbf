#!/usr/bin/env python3
"""
Run script for the broker portal serverless application from project root.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables from broker_portal_serverless/.env file
env_path = os.path.join(os.path.dirname(__file__), 'broker_portal_serverless', '.env')
load_dotenv(env_path)

def check_environment():
    """Check if all required environment variables are set."""

    required_env_vars = [
        'BUCKET_NAME',
        'DYNAMODB_JOBS_TABLE',
        'DYNAMODB_BROKERS_TABLE',
        'SQS_QUEUE_URL',
        'AWS_DEFAULT_REGION'
    ]

    missing_vars = []

    print("🔧 Environment Variables:")
    print("=" * 30)

    for var in required_env_vars:
        value = os.environ.get(var)
        if value:
            # Mask sensitive values
            if 'KEY' in var or 'SECRET' in var:
                display_value = value[:8] + "..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"✅ {var} = {display_value}")
        else:
            print(f"❌ {var} = NOT SET")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("   Please update broker_portal_serverless/.env file with the correct values")
        return False

    return True

def main():
    """Run the FastAPI application locally for testing."""

    print("🚀 Starting Broker Portal Serverless Application")
    print("=" * 50)

    # Check environment variables
    if not check_environment():
        print("\n❌ Environment setup incomplete. Please fix the .env file and try again.")
        print("   Edit: broker_portal_serverless/.env")
        return

    # Check AWS credentials
    try:
        import boto3
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"\n✅ AWS Account: {identity.get('Account')}")
        print(f"✅ AWS User/Role: {identity.get('Arn')}")
    except Exception as e:
        print(f"\n❌ AWS Credentials Error: {e}")
        print("   Please check your AWS credentials in the .env file")
        return

    # Check if required AWS resources exist
    print("\n🔍 Checking AWS Resources:")
    print("=" * 30)

    try:
        # Check S3 bucket
        s3 = boto3.client('s3')
        bucket_name = os.environ['BUCKET_NAME']
        try:
            s3.head_bucket(Bucket=bucket_name)
            print(f"✅ S3 Bucket exists: {bucket_name}")
        except:
            print(f"⚠️  S3 Bucket not found: {bucket_name}")
            print(f"   Create it with: aws s3 mb s3://{bucket_name}")

        # Check DynamoDB tables
        dynamodb = boto3.client('dynamodb')
        for table_name in [os.environ['DYNAMODB_JOBS_TABLE'], os.environ['DYNAMODB_BROKERS_TABLE']]:
            try:
                dynamodb.describe_table(TableName=table_name)
                print(f"✅ DynamoDB Table exists: {table_name}")
            except:
                print(f"⚠️  DynamoDB Table not found: {table_name}")

        # Check SQS queue
        sqs = boto3.client('sqs')
        queue_url = os.environ['SQS_QUEUE_URL']
        try:
            sqs.get_queue_attributes(QueueUrl=queue_url)
            print(f"✅ SQS Queue exists: {queue_url}")
        except:
            print(f"⚠️  SQS Queue not found: {queue_url}")

    except Exception as e:
        print(f"❌ AWS Resource Check Error: {e}")

    print("\n🌐 Starting FastAPI Server:")
    print("=" * 30)
    print("📍 URL: http://localhost:8000")
    print("📖 Docs: http://localhost:8000/docs")
    print("🔍 Health: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 50)

    # Import and start the server
    try:
        import uvicorn

        # Change to project root directory to ensure proper imports
        project_root = os.path.dirname(os.path.abspath(__file__))
        os.chdir(project_root)

        from broker_portal_serverless.api import app

        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,  # Disable reload for better compatibility
            log_level="info"
        )
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Please install dependencies: pip install -r requirements-broker-portal.txt")
        print("   Make sure you're running from the project root directory")
    except Exception as e:
        print(f"❌ Server Error: {e}")

if __name__ == "__main__":
    main()
