# Repository Setup

## Quick Start

1. Install the software
   1. [ ] [VSCode](https://code.visualstudio.com/)
   2. [ ] [Git](https://github.com/git-guides/install-git)
   3. [ ] [GitHub Desktop](https://desktop.github.com/)
   4. [ ] [GitHub LFS](https://docs.github.com/en/repositories/working-with-files/managing-large-files/installing-git-large-file-storage)
   5. [ ] [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
   6. [ ] [Docker](https://docs.docker.com/get-docker/)
   7. [ ] (Anything else we're missing please add)
2. Ensure you have the credentials file in `common/credentials`
3. Ask <EMAIL> for any questions any time!

### Python

1. [ ] Install [Python 3.11](https://www.python.org/downloads/release/python-3110/)

2. Run `pip install .`
3. Run `pip install -r common/requirements.txt`
4. Run `pip install pre-commit`
5. Run `pre-commit install`
6. Run `pip install black`
7. Run `pip install -r requirements.txt` in the project directory you will work on.

### JavaScript

1. Install [Node.js 14.21.3](https://nodejs.org/en/blog/release/v14.21.3)
2. Run `npm install` in the `frontend/freightgpt/web` directory

## Pull Request Templates

Pull requests may only be submitted if all checkboxes in the description are checked. We have created templates for common pull requests, such as:

- Updates to ingestion
- Price recommendation model updates

When creating a PR, if your PR falls under one of the templates, simply click on the template after creating the PR to populate the description properly.

### Create PR template

Under `.github/PULL_REQUEST_TEMPLATE/` add a new file with whatever you want to include in the template. Then in the file `.github/pull_request_template.md` add a link for the new template. You can copy the one that is there for ingestion and replace the template param in the link with the name of the new template file.

## Run Frontend

```
cd web/
npm run serve
```

## Credentials

You must have a local file  `common/credentials` with the following values.

```
[default]
aws_access_key_id = XXXXXXXXXXX
aws_secret_access_key = XXXXXXXXXXX
region = us-east-1
output = json

RDS_SECRET_ARN=XXXXXXXXXXXX
GCP_API_KEY=XXXXXXXXXXX
CLUSTER_ARN=XXXXXXXXXXXX
```

## Common Code Library

For first time setup and after modifying the common code library, run

```
pip install .
```

In order to use the common library, after making changes run the above command.

## Testing

`python setup.py pytest`

## Pre-Commit/Linting

To install the necessary pre-commit:

`pip install pre-commit`

To add a commit hook for pre-commit:
`pre-commit install`

If you would like to run linting manually on all files:

`pre-commit run --all-files`

# Other `README.md`'s

* [deploy](deploy/README.md)
* [transparency_web](frontend/transparency/web/README.md)
* [transparency_lambda](backend/transparency/lambda/README.md)
* [ingestion](ingestion/README.md)
