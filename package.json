{"name": "truce", "version": "1.0.0", "description": "## quickstart", "main": "index.js", "dependencies": {"@typescript-eslint/eslint-plugin": "^5.4.0", "@types/node": "^20.10.7", "@typescript-eslint/parser": "^5.4.0", "typescript": "^5.3.3"}, "devDependencies": {"@babel/eslint-parser": "^7.22.15", "@vue/cli-plugin-babel": "^5.0.8", "eslint": "^8.49.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "lint-staged": "^14.0.1", "prettier": "^3.0.3"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/arnob-sengupta/truce.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/arnob-sengupta/truce/issues"}, "homepage": "https://github.com/arnob-sengupta/truce#readme", "eslintConfig": {"root": true, "env": {"node": true, "es6": true}, "plugins": ["@typescript-eslint"], "extends": ["plugin:vue/essential", "eslint:recommended", "plugin:prettier/recommended", "plugin:@typescript-eslint/recommended"], "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": 2020, "sourceType": "module"}, "rules": {"no-unused-vars": "off"}}}