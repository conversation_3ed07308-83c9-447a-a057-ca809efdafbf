"""Processes trimmed PCA data and fits / predicts multiple predictive models to classift shipments.

0 = "Primary"
1 = "Spot"
"""
import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.compose import ColumnTransformer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
from sklearn.metrics import classification_report
from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsClassifier
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import OneHotEncoder
from sklearn.preprocessing import StandardScaler


def prep_data():
    """
    Reads in data, coerces column types, and drops NA
    """
    df = pd.read_csv("trimmed_data.csv")
    df["laneId"] = pd.Categorical(df["laneId"])
    df["shipmentRank"] = pd.Categorical(df["shipmentRank"])
    df = df.dropna()
    return df


def create_pipeline(preprocessor):
    """
    Returns dictionary of model pipelines given preprocessor and classifiers of interest
    """
    regression_pipeline = Pipeline(
        [("preprocessor", preprocessor), ("classifier", LogisticRegression())]
    )

    rf_pipeline = Pipeline(
        [
            ("preprocessor", preprocessor),
            (
                "classifier",
                RandomForestClassifier(n_estimators=120, random_state=RANDOM_STATE),
            ),
        ]
    )

    knn_pipeline = Pipeline(
        [
            ("preprocessor", preprocessor),
            ("classifier", KNeighborsClassifier(n_neighbors=5)),
        ]
    )

    kmeans_pipeline = Pipeline(
        [
            ("preprocessor", preprocessor),
            ("clusterer", KMeans(n_clusters=2, random_state=RANDOM_STATE)),
        ]
    )
    return {
        regression_pipeline,
        rf_pipeline,
        knn_pipeline,
        kmeans_pipeline,
    }


def report_results(all_models, x_train, x_test, y_train, y_test):
    """
    Fits and makes predictions with each model,
    Reports each model's accuracy, precision, recall, and predicted class proportions
    """
    for model in all_models:
        model.fit(x_train, y_train)
        y_pred = model.predict(x_test)
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, _, _ = classification_report(
            y_test, y_pred, output_dict=True
        )[str(1)].values()
        class_prop = np.bincount(y_pred) / len(y_pred)
        print(f"Model: {model}")
        print(f"Accuracy: {accuracy:.4f}")
        print(f"Precision: {precision:.4f}")
        print(f"Recall: {recall:.4f}")
        print(f"Proportion of shipment rank predictions: {class_prop}")


if __name__ == "__main__":
    df = prep_data()

    CAT_COLS = ["laneId"]
    QUANT_COLS = ["blt", "clt", "pb", "ltu_sc"]
    Y_COL = ["shipmentRank"]
    TEST_SIZE = 0.30
    RANDOM_STATE = 48

    x_cat = df[CAT_COLS]
    x_quant = df[QUANT_COLS]
    y = df[Y_COL]

    (
        x_cat_train,
        x_cat_test,
        x_quant_train,
        x_quant_test,
        y_train,
        y_test,
    ) = train_test_split(
        x_cat,
        x_quant,
        y,
        test_size=TEST_SIZE,
        random_state=RANDOM_STATE,
    )

    preprocessor = ColumnTransformer(
        [
            ("categorical", OneHotEncoder(), CAT_COLS),
            ("quantitative", StandardScaler(), QUANT_COLS),
        ]
    )

    all_models = create_pipeline(preprocessor)

    x_train = np.concatenate((x_cat_train, x_quant_train), axis=1)
    x_test = np.concatenate((x_cat_test, x_quant_test), axis=1)

    x_train = pd.DataFrame(x_train, columns=CAT_COLS + QUANT_COLS)
    x_test = pd.DataFrame(x_test, columns=CAT_COLS + QUANT_COLS)

    y_train = y_train["shipmentRank"].map({"Primary": 0, "Spot": 1})
    y_test = y_test["shipmentRank"].map({"Primary": 0, "Spot": 1})

    report_results(all_models, x_train, x_test, y_train, y_test)
