"""Principle Component Analysis on SPOT data to identify important features to utilize in models.

"""
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.colors import ListedColormap
from mpl_toolkits.mplot3d import Axes3D
from sklearn.decomposition import PCA
from sklearn.impute import SimpleImputer
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler


def prep_data():
    """
    Reads in data, coerces columns, and adds index column
    """
    df = pd.read_csv("spot_data.csv")
    df["shipmentRank"] = pd.Categorical(df["shipmentRank"])
    df["index"] = df.index
    return df


# Scree plot
def plot_scree(my_pca):
    """
    Creates labeled and formatted scree plot based on PCA output
    """
    plt.bar(range(1, len(my_pca.explained_variance_) + 1), pca.explained_variance_)
    plt.plot(
        range(1, len(my_pca.explained_variance_) + 1),
        np.cumsum(my_pca.explained_variance_),
        c="red",
        label="Cumulative Explained Variance",
    )
    plt.legend(loc="upper left")
    plt.xlabel("Number of Components")
    plt.ylabel("Explained Variance")
    plt.title("Scree Plot")
    plt.show()


# 2 Component PCA Plot
def plot_2d_pca(pca_data):
    """
    Creates 2D plot of PCA results: distribution of data, colored by class, across two components
    """
    sns.lmplot(
        x="PC1", y="PC2", data=pca_data, hue="shipmentRank", fit_reg=False, legend=True
    )
    plt.title("2 Component PCA Graph")
    plt.show()


# 3 Component PCA Plot
def plot_3d_pca(pca_data):
    """
    Creates 3D plot of PCA results: distribution of data, colored by class, across three components
    Dark Blue: Primary
    Light Blue: Spot
    """
    figure = plt.figure()
    axes = Axes3D(figure, auto_add_to_figure=False)
    figure.add_axes(axes)
    cmap = ListedColormap(sns.color_palette("bright", 2).as_hex())
    full_plot = axes.scatter(
        "PC1",
        "PC2",
        "PC3",
        data=pca_data,
        s=2.5,
        c="binaryRank",
        marker="o",
        cmap=cmap,
        alpha=0.75,
    )
    axes.set_xlabel("Principal Component 1")
    axes.set_ylabel("Principal Component 2")
    axes.set_zlabel("Principal Component 3")
    plt.legend(*full_plot.legend_elements(), bbox_to_anchor=(1, 1), loc=2)
    plt.show()


def plot_data_features(feature: str, data: pd.DataFrame):
    """
    Creates feature scatterplot
    X axis is data index
    Y axis is feature (numerical data)
    """
    sns.lmplot(
        x="index",
        y=feature,
        data=data,
        hue="shipmentRank",
        fit_reg=False,
        legend=True,
    )
    plt.title("Feature by Shipment Rank")
    plt.show()


if __name__ == "__main__":
    df = prep_data()

    quant_df = df[
        ["margin_pct", "odm", "ddm", "blt", "clt", "pb", "pb_sc", "ltu_sc"]
    ].copy()

    preprocessing = make_pipeline(SimpleImputer(strategy="mean"), StandardScaler())

    quant_df = preprocessing.fit_transform(quant_df)

    pca = PCA(n_components=3)
    pca_data = pca.fit_transform(quant_df)
    pca_df = pd.DataFrame(data=pca_data, columns=["PC1", "PC2", "PC3"])
    pca_df["shipmentRank"] = df["shipmentRank"]
    pca_df["binaryRank"] = np.where(df["shipmentRank"] == "Primary", 0, 1)

    quant_df = pd.DataFrame(quant_df)

    sns.set()

    plot_scree(pca)
    plot_2d_pca(pca_df)
    plot_3d_pca(pca_df)
    plot_data_features("margin_pct", df)
    plot_data_features("odm", df)
    plot_data_features("ddm", df)
    plot_data_features("blt", df)
    plot_data_features("clt", df)
    plot_data_features("pb", df)
    plot_data_features("pb_sc", df)
    plot_data_features("ltu_sc", df)
