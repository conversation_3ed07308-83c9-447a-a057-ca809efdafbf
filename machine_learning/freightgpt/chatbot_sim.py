import asyncio
import os
import time

from colorama import Fore
from colorama import Style

from common import init_main


PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt.constants import LANGCHAIN_MODEL
from machine_learning.freightgpt.prompt import write_formatted_examples


class ChatBot:
    def send_message(self, msg: str) -> str:
        raise NotImplementedError


def create_langchain_agent():
    from machine_learning.freightgpt.langchain_agent import LangChainAgent

    return LangChainAgent(
        cli_dev_mode=True,
        verbose=True,
        mock=args.eval_mode,
        model=args.model,
        tags=args.tags,
    )


async def chat_loop(async_mode):
    chatbot = create_langchain_agent()

    while True:
        text = input(f"{Fore.BLUE}{Style.BRIGHT}User: ")
        print(f"{Style.RESET_ALL}" + "-" * 120)
        if text.lower() in ["quit", "quit()"]:
            break
        s = time.perf_counter()

        if async_mode:
            chat_response = await chatbot.asend_message(text)
        else:
            chat_response = chatbot.send_message(text)

        print(f"\n{Style.BRIGHT}FreightGPT:{Fore.GREEN}")
        print("\n".join(list(chat_response.split("\n"))))
        print(f"{Style.RESET_ALL}" + "-" * 120)
        print(
            f"{Style.DIM}{Fore.YELLOW}Response time: {time.perf_counter() - s}\n{Style.RESET_ALL}"
        )

        if args.no_convo:
            chatbot = create_langchain_agent()


def chatbot_cli():
    asyncio.run(chat_loop(False))


async def async_chatbot_cli():
    await chat_loop(True)


def main():
    if args.generate_prompt:
        written_prompt = write_formatted_examples()
        print(written_prompt)

    # TODO(P1): Add a user input to check if the user wants to get updated prompt examples.
    os.environ["LANGCHAIN_PROJECT"] = args.langchain_project
    if args.async_mode:
        asyncio.run(async_chatbot_cli())
    else:
        chatbot_cli()


if __name__ == "__main__":
    # Add arguments to run chatbot, async chatbot, tool timers, or tagged examples
    os.environ["ENV"] = "DEV"

    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--async_mode", action="store_true", help="Run chatbot and tools in async mode."
    )
    parser.add_argument(
        "--eval_mode",
        action="store_true",
        help="Run chatbot in eval mode. Used often to generate examples for evaluation dataset.",
    )
    parser.add_argument(
        "--model",
        action="store",
        default=LANGCHAIN_MODEL,  # "ft:gpt-3.5-turbo-0613:truce::8Dj8gwqm"
        help="Model to use for chatbot.",
    )
    parser.add_argument(
        "--no_convo",
        action="store_true",
        help="Resets agent after every message, ensuring no conversation memory.",
    )
    parser.add_argument(
        "--generate_prompt",
        action="store_true",
        help="Generate a new prompt_examples.txt file.",
    )
    # https://smith.langchain.com/o/3d1f3312-0b72-4ba6-a0e1-54792f0c9eb3/projects
    parser.add_argument(
        "--langchain-project",
        action="store",
        default="default",
        help="Langsmith Project to store runs, such as default or fine-tune.",
    )
    parser.add_argument(
        "--tags",
        nargs="+",
        default=[],
        help="Tags to identify Langsmith run, such as 'finetuning-v0.1' Separate by spaces.",
    )
    args = parser.parse_args()

    main()

# To test the chatbot, as it would be in production, run:
# python machine_learning/freightgpt/chatbot_sim.py
# To generate examples for the evaluation dataset, using mock and no memory, run:
# python machine_learning/freightgpt/chatbot_sim.py --eval_mode --no_convo
