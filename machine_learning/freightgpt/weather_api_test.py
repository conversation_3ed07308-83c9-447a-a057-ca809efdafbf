import unittest

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt.weather_api import bearing_to_cardinal_direction


class WeatherAPITest(unittest.TestCase):
    @parameterized.expand(
        [
            (-100, ""),
            (-1, ""),
            (0, "N"),
            (22, "N"),
            (23, "NE"),
            (44, "NE"),
            (45, "NE"),
            (90, "E"),
            (135, "SE"),
            (180, "S"),
            (180.001, "S"),
            (225, "SW"),
            (270, "W"),
            (315, "NW"),
            (360, "N"),
            (None, ""),
        ]
    )
    def test_bearing_to_cardinal_direction(self, bearing: int, direction: str):
        self.assertEqual(
            bearing_to_cardinal_direction(bearing),
            direction,
        )


if __name__ == "__main__":
    unittest.main()
