import datetime
import logging
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Tuple
from typing import Union

import pandas as pd
import polyline
import pytz
from langchain.tools.base import ToolException

from common.google_maps_utils import GoogleMapsUtils
from machine_learning.freightgpt import langchain_utils
from machine_learning.freightgpt.constants import ActiveType
from machine_learning.freightgpt.constants import VisualizationType

MAX_DRIVING_HOURS = 11  # Maximum driving hours per shift
BREAK_HOURS = 10  # Required break hours after maximum driving hours
TRUCKING_TIME_ADJUSTMENT_FACTOR = 1.15  # 15% increase in driving time for trucking


def calculate_trucking_hours(
    driving_hours_needed: float, allowed_hours_left: float = MAX_DRIVING_HOURS
):
    """
    Calculate the total hours of driving and rest required for a given number of hours.

    Args:
        drivings_hours_needed (float): The total hours of driving needed.
        allowed_hours_left (float): The remaining hours of driving available. Defaults to MAX_DRIVING_HOURS.

    Returns:
        float: The total hours of driving and rest required.
    """

    if allowed_hours_left < 0:
        raise ValueError("The remaining driving hours cannot be negative.")
    if driving_hours_needed < 0:
        raise ValueError("The driving hours needed cannot be negative.")
    if allowed_hours_left > MAX_DRIVING_HOURS:
        raise ValueError(
            "The remaining driving hours cannot exceed the maximum driving hours."
        )

    total_hours = 0
    hours_left = driving_hours_needed
    allowed_hours = allowed_hours_left

    # While there are more hours needed than the allowed hours
    while hours_left > allowed_hours:
        # Add the maximum driving hours allowed and the required break hours
        total_hours += allowed_hours + BREAK_HOURS
        hours_left -= allowed_hours
        # Reset the allowed hours to the maximum driving hours
        allowed_hours = MAX_DRIVING_HOURS

    return total_hours + hours_left


from common import init_main, route_segmentation_utils

PROJECT_ROOT = init_main.initialize()


def is_tz_aware(time: pd.Timestamp):
    """Checks if a datetime object is timezone aware."""
    return time.tzinfo is not None and time.tzinfo.utcoffset(time) is not None


def parse_ambiguous_time_object(
    time_obj: Union[datetime.datetime, datetime.time],
    location: str,
    gmaps_utils: GoogleMapsUtils,
) -> Tuple[datetime.datetime, str, bool]:
    """Handles a time object that may or may not have a date or timezone.

    Args:
        time (datetime.datetime or datetime.time): The maybe tz-aware time/datetime object.

    Returns:
        datetime.datetime: The tz-aware datetime object.
        str: The format string for the time.
        bool: Whether the time object is a datetime.
    """
    if isinstance(time_obj, datetime.datetime):
        fmtstr = "%b %d %H:%M %Z"
        is_dt = True
    elif isinstance(time_obj, datetime.time):
        fmtstr = "%H:%M %Z"
        time_obj = datetime.datetime.combine(datetime.datetime.today(), time_obj)
        is_dt = False
    else:
        raise ValueError("Invalid time object type.")

    if not is_tz_aware(time_obj):
        tz = pytz.timezone(gmaps_utils.get_tz_from_address(location))
        time_obj = tz.localize(time_obj)

    return time_obj, fmtstr, is_dt


def get_transit_time(
    origin: str,
    destination: str,
    gmaps_utils: GoogleMapsUtils,
    hours_left_on_clock: int = MAX_DRIVING_HOURS,
    pickup_time: Union[datetime.datetime, datetime.time] = None,
    dropoff_time: Union[datetime.datetime, datetime.time] = None,
    roundtrip: bool = False,
    emission_function: Callable = None,
):
    """
    Calculate the transit time between two locations, including the estimated pickup or dropoff time if applicable.

    The return value:
    - Always includes the estimated transit time
    - Includes dropoff time if pickup time is provided
    - Includes pickup time if dropoff time is provided
    - Includes days before/after if the estimated time crosses a day boundary, and no date is provided
    - Ignores dropoff time if pickup time is provided

    Examples:
    >>> get_transit_time("San Francisco", "Los Angeles")
    {'transit_time': '9 hours'}

    >>> get_transit_time("San Francisco", "Los Angeles", pickup_time=datetime.datetime(2022, 1, 2, 14, 0))
    {'transit_time': '9 hours', 'calculated_dropoff_time': 'Jan 02 23:00 PST'}

    >>> get_transit_time("San Francisco", "Los Angeles", dropoff_time=datetime.datetime(2022, 1, 3, 11, 0))
    {'transit_time': '9 hours', 'calculated_pickup_time': 'Jan 03 02:00 PST'}

    >>> get_transit_time("San Francisco", "Los Angeles", pickup_time=datetime.time(22, 0), dropoff_time=datetime.time(11, 0))
    {'transit_time': '9 hours', 'calculated_dropoff_time': '07:00 PST', 'days_after': 1}

    Args:
        origin (str): The origin location (any input Google Maps can understand).
        destination (str): The destination location (any input Google Maps can understand).
        gmaps_utils (GoogleMapsUtils): An instance of the GoogleMapsUtils class.
        hours_left_on_clock (int): The remaining hours on the driver's clock. Defaults to MAX_DRIVING_HOURS.
        pickup_time (datetime.datetime or datetime.time): The pickup time. Defaults to None.
        dropoff_time (datetime.datetime or datetime.time): The dropoff time. Defaults to None.
        roundtrip (bool): Whether the trip is a roundtrip. Defaults to False.

    Returns:
        dict: A dictionary with the transit time and sometimes estimated pickup and/or dropoff times.
    """
    durations = gmaps_utils.get_driving_time(origin, destination)
    duration_seconds = durations["duration"]
    if not duration_seconds:
        raise ToolException(f"Unable to find route between {origin} and {destination}.")
    logging.info(
        "The driving time from {} to {} is {}.".format(
            origin,
            destination,
            langchain_utils.format_timedelta(
                datetime.timedelta(seconds=duration_seconds)
            ),
        )
    )
    if roundtrip:
        durations = gmaps_utils.get_driving_time(destination, origin)
        duration_seconds += durations["duration"]
        logging.info(
            "The roundtrip driving time is {}.".format(
                langchain_utils.format_timedelta(
                    datetime.timedelta(seconds=duration_seconds)
                )
            )
        )

    duration_seconds *= TRUCKING_TIME_ADJUSTMENT_FACTOR  # Add 15% to the duration to account for trucking time
    logging.info(
        "Adding 15% and 2 hours for loading, the total duration is {}.".format(
            langchain_utils.format_timedelta(
                datetime.timedelta(seconds=duration_seconds)
            )
        )
    )

    transit_time_hrs = calculate_trucking_hours(
        driving_hours_needed=round(duration_seconds / 3600),
        allowed_hours_left=hours_left_on_clock,
    )
    timedelta_enroute = datetime.timedelta(hours=transit_time_hrs)
    logging.info(f"The transit time is {timedelta_enroute}.")
    rounded_formatted_time = langchain_utils.format_timedelta(
        timedelta_enroute, round_up=True
    )
    logging.info(f"Transit time rounded up to {rounded_formatted_time}.")

    formatted_info = {"transit_time": rounded_formatted_time}

    # If a pickup time is provided, return the estimated dropoff time
    # If a dropoff time is provided, return the estimated pickup time
    # formatted_info will have one or the other, but not both
    if pickup_time:
        pickup_time, fmtstr, is_dt = parse_ambiguous_time_object(
            pickup_time, origin, gmaps_utils
        )

        estimated_dropoff_time = pickup_time + timedelta_enroute

        destination_tz = pytz.timezone(gmaps_utils.get_tz_from_address(destination))
        estimated_dropoff_time = estimated_dropoff_time.astimezone(destination_tz)

        formatted_info["calculated_dropoff_time"] = estimated_dropoff_time.strftime(
            fmtstr
        )
        if not is_dt:
            days_after = (estimated_dropoff_time.date() - pickup_time.date()).days
            if days_after > 0:
                formatted_info["days_after"] = days_after

    elif dropoff_time:
        dropoff_time, fmtstr, is_dt = parse_ambiguous_time_object(
            dropoff_time, destination, gmaps_utils
        )

        estimated_pickup_time = dropoff_time - timedelta_enroute

        origin_tz = pytz.timezone(gmaps_utils.get_tz_from_address(origin))
        estimated_pickup_time = estimated_pickup_time.astimezone(origin_tz)

        formatted_info["calculated_pickup_time"] = estimated_pickup_time.strftime(
            fmtstr
        )
        if not is_dt:
            days_before = (dropoff_time.date() - estimated_pickup_time.date()).days
            if days_before > 0:
                formatted_info["days_before"] = days_before

    start_time = (
        pickup_time
        if pickup_time
        else (estimated_pickup_time if dropoff_time else None)
    )

    formatted_info.update(
        get_transit_stops(
            origin=origin,
            destination=destination,
            hours_left_on_clock=hours_left_on_clock,
            transit_time=timedelta_enroute,
            gmaps_utils=gmaps_utils,
            start_time=start_time,
            emission_function=emission_function,
        )
    )

    return formatted_info


def format_segments(
    segments: List[Dict[str, Any]],
    origin: str,
    destination: str,
    start_location: Dict[str, float],
    end_location: Dict[str, float],
    transit_time: datetime.timedelta,
    gmaps_utils: GoogleMapsUtils,
    start_time: datetime.datetime = None,
    emission_function: Callable = None,
) -> Dict[str, Any]:
    """Format the segments of a route into a dictionary.

    Args:
        - segments (List[Dict[str, Any]]): The segments of the route, each containing:
            - step_name (str): Name of the route segment.
            - polyline (str): Encoded polyline string representing the path of the segment.
            - distance_in_step (float): Distance covered in this segment in miles.
            - seconds_enroute (float): Estimated seconds to travel this segment.
            - point (Tuple[float, float]): Latitude and longitude of a notable point in this segment.
        - origin (str): The origin location.
        - destination (str): The destination location.
        - start_location (Dict[str, float]): Origin with keys "lat" and "lng".
        - end_location (Dict[str, float]): Destination with keys "lat" and "lng".
        - transit_time (datetime.timedelta): The estimated transit time as calculated above.
        - gmaps_utils (GoogleMapsUtils): An instance of the GoogleMapsUtils class.
        - start_time (datetime.datetime): The start time of the route as specified by the user/llm. Is assumed to be in the local time of the origin if not timezone aware. Defaults to None.
        - emission_function (Callable): The function to emit the visualization data. Defaults to None.

    Returns:
        - Dict[str, List[str]]: The formatted data.
            - driver_will_run_out_of_hours_near (List[str]): The cities where the driver will run out of hours.

    Emits:
        - VisualizationType.TRANSIT_MARKERS: Emits an event containing the visualization data for transit stops along the route. The event is a dictionary structured as follows:
            - 'origin' (Dict[str, Any]): Detailed information about the starting point, including:
                - 'lat' (float): Latitude of the origin.
                - 'lng' (float): Longitude of the origin.
                - 'city_name' (str): Name of the city at the origin.
                - 'time' (str): Local time at the origin, formatted as "Mon 01 00:00 PST".
            - 'destination' (Dict[str, Any]): Detailed information about the endpoint, including:
                - 'lat' (float): Latitude of the destination.
                - 'lng' (float): Longitude of the destination.
                - 'city_name' (str): Name of the city at the destination.
                - 'time' (str): Local time at the destination, formatted as "Mon 01 00:00 PST".
            - 'markers' (List[Dict[str, Any]]): A list of dictionaries, each representing a marker for a rest stop along the route (excluding origin and destination). Each marker contains:
                - 'lat' (float): Latitude of the rest stop.
                - 'lng' (float): Longitude of the rest stop.
                - 'rest_start_time' (str): The suggested start time for the rest stop, formatted as "Mon 01 00:00 PST".
                - 'rest_end_time' (str): The suggested end time for the rest stop, formatted as "Mon 01 00:00 PST".
                - 'nearest_city' (str): The name of the nearest city to the rest stop, providing context for the location.
    """

    if not segments:
        logging.warning("No segments to format.")
        return []
    formatted_data = {}
    fmtstr = "%b %d, %H:%M"
    # Convert start time to UTC
    if start_time:
        if not is_tz_aware(start_time):
            tz_name = langchain_utils.get_tz_from_lat_lng(
                start_location["lat"], start_location["lng"]
            )
            start_time = pytz.timezone(tz_name).localize(start_time)
        route_start_time_local = start_time.strftime(fmtstr + " %Z")
        running_time_utc = start_time.astimezone(pytz.utc)
    else:
        running_time_utc = datetime.datetime.now(tz=pytz.utc)
        route_start_time_local = langchain_utils.convert_to_local_time(
            running_time_utc, start_location["lat"], start_location["lng"], fmtstr
        )
    end_time = running_time_utc + transit_time
    route_end_time_local = langchain_utils.convert_to_local_time(
        end_time, end_location["lat"], end_location["lng"], fmtstr
    )

    nearest_cities = []
    markers = []
    for segment in segments:
        marker = {}
        markers.append(marker)
        if "polyline" in segment:
            pline = segment["polyline"]
            decoded_polyline = polyline.decode(pline)
            # Use end of segment instead of middle for rest stops
            marker["lat"], marker["lng"] = decoded_polyline[-1]
        running_time_utc += datetime.timedelta(hours=MAX_DRIVING_HOURS)
        marker["rest_start_time"] = langchain_utils.convert_to_local_time(
            running_time_utc, marker["lat"], marker["lng"], fmtstr
        )
        running_time_utc += datetime.timedelta(hours=BREAK_HOURS)
        marker["rest_end_time"] = langchain_utils.convert_to_local_time(
            running_time_utc, marker["lat"], marker["lng"], fmtstr
        )
        try:
            marker["nearest_city"] = gmaps_utils.get_city_state_from_lat_lng(
                marker["lat"], marker["lng"], short_state_name=True
            )
            nearest_cities.append(marker["nearest_city"])
        except Exception as e:
            logging.warning("Error getting city and state from lat and lng: %s" % e)

    event = {
        "origin": {
            "lat": start_location["lat"],
            "lng": start_location["lng"],
            "city_name": origin,
            "time": route_start_time_local,
        },
        "destination": {
            "lat": end_location["lat"],
            "lng": end_location["lng"],
            "city_name": destination,
            "time": route_end_time_local,
        },
        "markers": markers[:-1],  # Remove destination marker
    }

    langchain_utils.emit_event(
        emission_function, event, VisualizationType.TRANSIT_MARKERS
    )

    if len(nearest_cities) > 1:
        formatted_data["driver_will_run_out_of_hours_near"] = nearest_cities[:-1]

    return formatted_data


def get_transit_stops(
    origin: str,
    destination: str,
    hours_left_on_clock: int,
    transit_time: datetime.timedelta,
    gmaps_utils: GoogleMapsUtils,
    start_time: datetime.datetime = None,
    emission_function: Callable = None,
) -> Dict[str, Any]:
    """
    Calculate the location of transit stops between two locations.

    Additionally emits the visualization data for the transit stops.

    Args:
        origin (str): The origin location (any input Google Maps can understand).
        destination (str): The destination location (any input Google Maps can understand).
        hours_left_on_clock (int): The remaining hours on the driver's clock.
        transit_time (datetime.timedelta): The estimated transit time.
        gmaps_utils (GoogleMapsUtils): An instance of the GoogleMapsUtils class. Defaults to a new instance.
        start_time (datetime.datetime): The start time of the route. Defaults to None.
        emission_function (Callable): The function to emit the visualization data. Defaults to None.

    Returns:
        dict: Formatted data for the transit stops.
    """
    route_details = route_segmentation_utils.get_route(
        origin, destination, gmaps_utils=gmaps_utils
    )

    steps = route_details.get("steps", [])

    if not steps:
        logging.error(
            f"Unable to find steps for route between {origin} and {destination}."
        )
        raise ToolException(
            f"Unable to find steps for route between {origin} and {destination}."
        )

    event = langchain_utils.generate_polyline_event(
        ActiveType.ACTIVE,
        origin=origin,
        destination=destination,
        gmaps_utils=gmaps_utils,
        pre_computed_route=route_details,
    )
    langchain_utils.emit_event(emission_function, event, VisualizationType.GMAPS)

    # Convert distance value to trucking hours
    for step in steps:
        step["distance"]["value"] = (
            step["duration"]["value"] / 3600 * TRUCKING_TIME_ADJUSTMENT_FACTOR
        )

    # Add hours left on clock to the first step, to cause early rest stop if needed
    steps[0]["distance"]["value"] += MAX_DRIVING_HOURS - hours_left_on_clock

    # Break the route into even MAX_DRIVING_HOURS segments
    segments = route_segmentation_utils.splice_to_equal_segments(
        steps, MAX_DRIVING_HOURS
    )

    start_location = steps[0].get("start_location", {})
    if "lat" not in start_location or "lng" not in start_location:
        logging.error("Start location is missing lat or lng.")
        raise ValueError("Start location is missing lat or lng.")
    end_location = steps[-1].get("end_location", {})
    if "lat" not in end_location or "lng" not in end_location:
        logging.error("End location is missing lat or lng.")
        raise ValueError("End location is missing lat or lng.")

    return format_segments(
        segments=segments,
        origin=origin,
        destination=destination,
        start_location=start_location,
        end_location=end_location,
        transit_time=transit_time,
        gmaps_utils=gmaps_utils,
        start_time=start_time,
        emission_function=emission_function,
    )


if __name__ == "__main__":
    gmaps_utils = GoogleMapsUtils()

    origin = "Chicago, IL"
    destination = "Miami, FL"

    route_details = route_segmentation_utils.get_route(origin, destination, gmaps_utils)
    steps = route_details.get("steps", [])

    # Convert distance value to trucking hours
    for step in steps:
        step["distance"]["value"] = (
            step["duration"]["value"] / 3600 * TRUCKING_TIME_ADJUSTMENT_FACTOR
        )

    # Add hours left on clock to the first step, to cause early rest stop if needed
    steps[0]["distance"]["value"] += MAX_DRIVING_HOURS - 11

    # Break the route into even MAX_DRIVING_HOURS segments
    segments = route_segmentation_utils.splice_to_equal_segments(
        steps, MAX_DRIVING_HOURS, True
    )

    print(format_segments(segments, steps[0].get("start_location", {}), gmaps_utils))
