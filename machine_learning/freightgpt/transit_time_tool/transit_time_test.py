import unittest

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt.transit_time_tool.transit_time import (
    calculate_trucking_hours,
)


class TransitTimeTest(unittest.TestCase):
    @parameterized.expand(
        [
            # Test cases are structured as (test_name, drivings_hours_needed, allowed_hours_left, round_up, expected_total_hours)
            ("less_than_max_shift", 5, 5),
            ("equal_to_max_shift", 11, 11),
            ("more_than_max_shift", 12, 22),
            ("one_break_needed", 22, 32),
            ("multiple_breaks_needed", 23, 43),
            ("exact_full_shifts", 33, 53),
            ("float_hours", 10.5, 10.5),
            ("float_hours_with_break", 11.5, 21.5),
        ]
    )
    def test_calculate_trucking_hours(
        self, name, drivings_hours_needed, expected_total_hours
    ):
        result = calculate_trucking_hours(drivings_hours_needed)
        self.assertEqual(
            result,
            expected_total_hours,
            msg=f"Failed the test: {name} with parameters: {drivings_hours_needed}. Expected result: {expected_total_hours}",
        )

    @parameterized.expand(
        [
            ("use_remaining_hours_no_break", 2, 2, 2),
            ("use_remaining_hours_break_needed", 13, 2, 23),
            ("float_hours_left", 5, 0.25, 15),
            ("float_hours_left_with_break", 5.25, 0.25, 15.25),
            ("one_hour_left", 5, 1, 15),
        ]
    )
    def test_calculate_trucking_hours_with_hours_on_clock(
        self, name, drivings_hours_needed, driving_hours_left, expected_total_hours
    ):
        result = calculate_trucking_hours(
            driving_hours_needed=drivings_hours_needed,
            allowed_hours_left=driving_hours_left,
        )
        self.assertEqual(
            result,
            expected_total_hours,
            msg=f"Failed the test: {name} with parameters: {drivings_hours_needed}, {driving_hours_left}. Expected result: {expected_total_hours}",
        )

    # Test invalid inputs
    @parameterized.expand(
        [
            # Test cases are structured as (test_name, drivings_hours_needed, driving_hours_left, expected_error)
            ("negative_hours_needed", -5, 11),
            ("negative_hours_left", 5, -11),
            ("remaining_hours_exceeds_max", 5, 12),
        ]
    )
    def test_calculate_trucking_hours_invalid(
        self, name, drivings_hours_needed, driving_hours_left
    ):
        with self.assertRaises(ValueError, msg=f"Failed the test: {name}"):
            calculate_trucking_hours(
                driving_hours_needed=drivings_hours_needed,
                allowed_hours_left=driving_hours_left,
            )


if __name__ == "__main__":
    unittest.main()
