import asyncio
import datetime
import unittest

from common import init_main

# from unittest.mock import patch

PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt import langchain_utils


class TestBreakIntoSegments(unittest.TestCase):
    def test_asyncify(self):
        async def test_coro():
            wrapped = langchain_utils.asyncify(lambda x: x + 1)
            result = await wrapped(1)
            self.assertEqual(result, 2)

            wrapped = langchain_utils.asyncify(lambda x, y: x + y)
            result = await wrapped(1, 2)
            self.assertEqual(result, 3)

            wrapped = langchain_utils.asyncify(lambda x, y=2: x + y)
            result = await wrapped(1)
            self.assertEqual(result, 3)

        asyncio.run(test_coro())

    def test_combine_multiple_truck_costs(self):
        trip1 = {
            "truck_cost_with_fuel": 100,
            # "truck_cost_lower": 90,
            # "truck_cost_upper": 110,
            "distance": 100,
            # "origin_diesel_cost": 2.5,
            # "destination_diesel_cost": 3.0,
        }

        trip2 = {
            "truck_cost_with_fuel": 200,
            # "truck_cost_lower": 190,
            # "truck_cost_upper": 210,
            "distance": 100,
            # "origin_diesel_cost": 3.0,
            # "destination_diesel_cost": 2.5,
        }

        expected_result = {
            "roundtrip_cost_with_fuel": 300,
            # "roundtrip_truck_cost_lower": 280,
            # "roundtrip_truck_cost_upper": 320,
            "roundtrip_distance": 200,
            # "origin_diesel_cost": 2.5,
            # "destination_diesel_cost": 2.5,  # Note this is from the last trip in the list.
        }

        self.assertEqual(
            langchain_utils.combine_multiple_truck_costs([trip1, trip2]),
            expected_result,
        )

    def test_format_timedelta(self):
        # Test case 1: timedelta less than a minute
        td = datetime.timedelta(seconds=30)
        self.assertEqual(langchain_utils.format_timedelta(td), "")

        # Test case 2: timedelta between a minute and an hour
        td = datetime.timedelta(minutes=45)
        self.assertEqual(langchain_utils.format_timedelta(td), "1 hour")

        # Test case 3: timedelta between an hour and a day
        td = datetime.timedelta(hours=5)
        self.assertEqual(langchain_utils.format_timedelta(td), "5 hours")

        # Test case 4: timedelta between a day and a month
        td = datetime.timedelta(days=20)
        self.assertEqual(langchain_utils.format_timedelta(td), "20 days")

        # Test case 5: timedelta between a month and a year
        td = datetime.timedelta(days=400)
        self.assertEqual(
            langchain_utils.format_timedelta(td), "1 year, 1 month, 5 days"
        )

        # Test case 6: timedelta more than a year
        td = datetime.timedelta(days=1000)
        self.assertEqual(langchain_utils.format_timedelta(td), "2 years, 9 months")

        # Test case 6: timedelta more than a year
        td = datetime.timedelta(hours=49)
        self.assertEqual(langchain_utils.format_timedelta(td), "2 days, 1 hour")

        td = datetime.timedelta(hours=1, minutes=30)
        self.assertEqual(langchain_utils.format_timedelta(td), "2 hours")

        td = datetime.timedelta(hours=1, minutes=29)
        self.assertEqual(langchain_utils.format_timedelta(td), "1 hour")

        # Test case 7: granularity = minute
        td = datetime.timedelta(hours=1, minutes=31, seconds=30)
        self.assertEqual(
            langchain_utils.format_timedelta(td, "minute"), "1 hour, 32 minutes"
        )

        # Test case 8: round_up = True
        td = datetime.timedelta(hours=1, minutes=1)
        self.assertEqual(langchain_utils.format_timedelta(td, round_up=True), "2 hours")

        # Test case 9: round_up = True, granularity = minute
        td = datetime.timedelta(hours=1, minutes=31, seconds=1)
        self.assertEqual(
            langchain_utils.format_timedelta(td, "minute", True), "1 hour, 32 minutes"
        )


# Run the tests
if __name__ == "__main__":
    unittest.main()
