import asyncio
import datetime
import logging
import typing

import pandas as pd

from common import route_segmentation_utils
from machine_learning.freightgpt import langchain_utils
from machine_learning.freightgpt import weather_api
from machine_learning.freightgpt.constants import ActiveType
from machine_learning.freightgpt.constants import MIN_WIND_SPEED_MPH
from machine_learning.freightgpt.constants import VisualizationType

HUNDRED_MILES_IN_METERS = 160934


def fetch_weather(segment, timestamp):
    point = segment["point"]
    point_dt = int(
        (
            datetime.datetime.fromtimestamp(timestamp)
            + datetime.timedelta(seconds=segment["seconds_enroute"])
        ).timestamp()
    )
    try:
        return weather_api.get_weather(point[0], point[1], point_dt)
    except Exception as e:
        logging.error("Error getting NWS weather: %s" % e)
        return {}


async_fetch_weather = langchain_utils.asyncify(fetch_weather)


def get_pirateweather_from_points(
    segments: typing.List[typing.Dict[str, typing.Any]], timestamp: int
) -> typing.List[typing.Dict[str, typing.Any]]:
    return [fetch_weather(segment, timestamp) for segment in segments]


async def async_get_pirateweather_from_points(
    segments: typing.Dict[str, typing.Any], timestamp: int
) -> typing.List[typing.Dict[str, typing.Any]]:
    return await asyncio.gather(
        *[async_fetch_weather(segment, timestamp) for segment in segments]
    )


def format_route(
    route: typing.Dict[str, typing.Any],
    pirate_weathers: typing.List[typing.Any],
    forecast_time: datetime.datetime,
    gmaps_utils,
) -> typing.List[typing.Dict[str, typing.Any]]:
    """
    Formats the route and weather data into a usable format.

    Args:
    - route (Dict[str, Any]): The original route data.
    - pirate_weathers (List[Any]): The weather data for each segment.
    - forecast_time (datetime.datetime): The time for which the weather forecast should be retrieved, in UTC.
    - gmaps_utils: The Google Maps utility object.

    Returns:
    - List[Dict[str, Any]]: Summary and segments.
    """

    segments = []

    # Add origin city and state to the route

    for i, segment in enumerate(route["segments"]):
        if pd.isna(pirate_weathers[i]):
            continue
        segment["weather"] = pirate_weathers[i]
        segment["weather"]["warning"] = []
        if "temperature" not in segment["weather"]:
            logging.error(
                "Temperature not found in weather data: %s", segment["weather"]
            )
            continue
        segment["weather"]["temperature"] = int(segment["weather"]["temperature"])
        # Add warnings if temperature is below 32 or wind speed is above 20 mph or wind gust is above 30 mph
        if segment["weather"]["temperature"] <= 32:
            segment["weather"]["warning"].append("Freezing")
        try:
            wind_speed_str = segment["weather"]["windSpeed"]
            wind_speed_mph = int(wind_speed_str.replace(" mph", ""))
            if wind_speed_mph >= MIN_WIND_SPEED_MPH:
                segment["weather"]["warning"].append("Windy")
        except ValueError:
            logging.error(
                "Error parsing wind speed: %s" % segment["weather"]["windSpeed"]
            )

        else:
            del segment["weather"]["windSpeed"]
            # del segment["weather"]["windGust"]
            del segment["weather"]["windBearing"]
        lat, lng = segment["point"]
        try:
            segment["nearest_city"] = gmaps_utils.get_city_state_from_lat_lng(
                lat, lng, short_state_name=True
            )
        except Exception as e:
            logging.warning("Error getting city and state from lat and lng: %s" % e)
        # Adjust forecast time by seconds enroute
        adjusted_forecast_time = forecast_time + datetime.timedelta(
            seconds=segment["seconds_enroute"]
        )
        # Convert seconds_enroute from int to formatted string
        segment["forecast_time"] = langchain_utils.convert_to_local_time(
            adjusted_forecast_time, lat, lng
        )
        # Convert meters in step to miles
        # segment["miles_in_step"] = segment["distance_in_step"] * 0.000621371
        unnecessary_keys = ["point", "polyline", "seconds_enroute", "distance_in_step"]
        for key in unnecessary_keys:
            if key in segment:
                del segment[key]
        if not segment["weather"]["warning"]:
            del segment["weather"]["warning"]
        segments.append(segment)

    return {"route_taken": route["summary"], "segments": segments}


def get_weather_along_route(
    origin: str,
    destination: str,
    gmaps_utils,
    forecast_time: typing.Union[datetime.datetime, int] = None,
    segment_meters: int = 160934,  # 100 miles
) -> typing.Dict[str, typing.Any]:
    """
    Gets weather information for segments along a route from the origin to the destination.

    Args:
    - origin (str): The starting location, a searchable Google Maps location.
    - destination (str): The ending location, a searchable Google Maps location.
    - forecast_time (datetime.datetime): The time for which the weather forecast should be retrieved. Defaults to the current time.
    - segment_meters (int): The desired length for each segment along the route. Defaults to 250,000 meters (2.5 km).

    Returns:
    - Dict[str, Any]: A list of dictionaries, each representing a segment of the route.
        Each dictionary contains:
        - "step_name": The name of the road or step from which the segment was derived.
        - "weather": A dictionary containing:
            - "summary": A brief description of the weather conditions (e.g., "Cloudy", "Rain").
            - "temperature": The temperature in Fahrenheit.
            - "warning": A list of warnings if any (e.g., "Freezing", "Windy").
        - "forecast_time": The time at which the segment is expected to be reached, formatted as a string (HH:MM:SS).
        - "nearest_city": The nearest city to the segment's location, along with its state.

    Example Output:
    [
        {
            "step_name": "I-25 N Santa Fe",
            "weather": {
                "summary": "Cloudy",
                "temperature": 74,
                "warning": []
            },
            "forecast_time": "16:45:31",
            "nearest_city": "Las Vegas, New Mexico"
        },
        ...
    ]

    Raises:
    - ValueError: If the forecast_time is not a datetime.datetime or int.
    """
    import time

    # Convert the forecast time to a UNIX timestamp in seconds
    timestamp = langchain_utils.get_unix_timestamp(forecast_time)
    forecast_time = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)

    a = time.perf_counter()
    route = route_segmentation_utils.get_segmented_route(
        origin, destination, segment_meters, gmaps_utils=gmaps_utils
    )

    logging.info("route got in %.2f seconds" % (time.perf_counter() - a))
    a = time.perf_counter()

    pirate_weathers = get_pirateweather_from_points(route["segments"], timestamp)

    logging.info("pirate_weathers got in %.2f seconds" % (time.perf_counter() - a))
    a = time.perf_counter()

    formatted_route = format_route(route, pirate_weathers, forecast_time, gmaps_utils)

    logging.info("formatted_route got in %.2f seconds" % (time.perf_counter() - a))

    return formatted_route


async def async_get_weather_along_route(
    origin: str,
    destination: str,
    emission_function: typing.Callable,
    gmaps_utils,
    forecast_time: typing.Union[datetime.datetime, int] = None,
    segment_meters: int = HUNDRED_MILES_IN_METERS,
) -> typing.Dict[str, typing.Any]:
    """
    Asynchronously gets weather information for segments along a route from the origin to the destination.
    """

    # Convert the forecast time to a UNIX timestamp in seconds
    timestamp = langchain_utils.get_unix_timestamp(forecast_time)
    forecast_time = datetime.datetime.fromtimestamp(timestamp)

    route = route_segmentation_utils.get_segmented_route(
        origin,
        destination,
        segment_meters,
        gmaps_utils=gmaps_utils,
    )

    # Asynchronously get pirate weather data for segments
    pirate_weathers = await async_get_pirateweather_from_points(
        route["segments"], timestamp
    )

    formatted_route = format_route(route, pirate_weathers, forecast_time, gmaps_utils)

    event = langchain_utils.generate_polyline_event(
        ActiveType.ACTIVE,
        origin=origin,
        destination=destination,
        gmaps_utils=gmaps_utils,
        pre_computed_route=route,
    )
    langchain_utils.emit_event(emission_function, event, VisualizationType.WINDY)

    return formatted_route
