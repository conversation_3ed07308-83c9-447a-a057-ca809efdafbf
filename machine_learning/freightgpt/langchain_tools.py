# List of functions to be used as Langchain Tools in langchain_agent.py
import inspect
import os
import random
import re
import sys
import time
import traceback

import pandas as pd

from common.constants import EquipmentType

start_time = time.perf_counter()
import asyncio
import datetime
import logging
import typing

import pytz
from langchain.chains.llm_math.base import LL<PERSON><PERSON><PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.tools.base import ToolException
from langchain_core.messages.system import SystemMessage
from langchain.pydantic_v1 import BaseModel, Field
from machine_learning.freightgpt.constants import (
    CARRIER_RATING_DICT,
    CARSHIP_DICT,
    VisualizationType,
    GMAPS_UTILS,
)

from machine_learning.freightgpt.transit_time_tool import transit_time
from machine_learning.freightgpt.langchain_utils import (
    stable_random,
    stable_random_choice,
)

print(f"Time to load public imports: {time.perf_counter() - start_time} seconds")
start_time = time.perf_counter()
from common import init_main

PROJECT_ROOT = init_main.initialize()
print(f"Time to load init_main: {time.perf_counter() - start_time} seconds")
start_time = time.perf_counter()
from machine_learning.freightgpt.carrier_tool import carrier_tool

print(f"Time to load carrier_tool: {time.perf_counter() - start_time} seconds")
start_time = time.perf_counter()
from machine_learning.freightgpt import langchain_utils

print(f"Time to load langchain_utils: {time.perf_counter() - start_time} seconds")
start_time = time.perf_counter()
from machine_learning.freightgpt import weather_along_route

print(f"Time to load weather_along_route: {time.perf_counter() - start_time} seconds")
from machine_learning.freightgpt import weather_api

from machine_learning.freightgpt.constants import (
    DEFAULT_TRUCK_MPG,
    LANGCHAIN_MODEL,
)

from machine_learning.freightgpt.constants import MOCK_CURRENT_TIME

from machine_learning.price_recommendation import constants as pricer_constants

from machine_learning.price_recommendation.price_predictor import (
    VertexAutoMLPricePredictor,
)


start_time = time.perf_counter()
# Make sure os.getenv("ENV") is set.
if os.getenv("ENV") is None:
    logging.info("No env set - setting to TEST")
    os.environ["ENV"] = "TEST"
    # raise ValueError("ENV environment variable must be set.")
PRICER_ENDPOINT = (
    VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_FREIGHTGPT_DEV
    if os.getenv("ENV") in {"DEV", "TEST"}
    else VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_FREIGHTGPT_PROD
)

PRICER = VertexAutoMLPricePredictor(PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS)
print(
    f"Time to load pricer {PRICER_ENDPOINT}: {time.perf_counter() - start_time} seconds"
)


# TODO(P1): Is there a way to wrangle the length of this file?
# TODO(P1): #1692 Wrap each tool into its own class.
# We can't use BaseTool, it seems. We need to use StructuredTool.from_function.
def tool_wrapper(
    func: typing.Union[typing.Callable, typing.Coroutine],
    tool_name: str,
    memory: ConversationBufferMemory,
    emit_events: bool = False,
):
    """Wrap func in a try/except block to catch exceptions raise as ToolException.

    This prevents crashes when using the tool in the agent in prod.
    ToolExceptions are special as they are processed by handle_tool_error and are returned to the agent as an observation.
    Also add tool input/output to memory list
    """

    def remember_tool_io(kwargs, tool_result):
        tool_inputs = {k: v for k, v in kwargs.items() if k != "callbacks"}
        tool_inp_str = f"Tool Input '{tool_name}': {str(tool_inputs)}"
        tool_out_str = f"Tool Output '{tool_name}': {str(tool_result)}"
        logging.info(tool_inp_str)
        logging.info(tool_out_str)
        # Whatever the LLM passes to the tool is stored in the memory.
        if memory.chat_memory:
            # Ensure that the tool input and output are after the last human message.
            memory.chat_memory.add_after_human_message(
                SystemMessage(content=tool_inp_str)
            )
            memory.chat_memory.add_after_human_message(
                SystemMessage(content=tool_out_str)
            )

    def handle_exception(e):
        try:
            logging.error(
                f"Error in tool {tool_name}: {e}"
                f'Name "{type(e).__name__}": {e}\n\n'
                f"Details:\n"
                f'\ttype: "{sys.exc_info()[0]}"\n'
                f'\tvalue: "{sys.exc_info()[1]}"\n'
                f"\ttraceback:\n{traceback.format_exc()}"
            )
        except Exception as exc_info_error:
            logging.error(
                f"Error in tool {tool_name}: {e}"
                f'Name "{type(e).__name__}": {e}\n\n'
                f"Details:\n"
                f"Error in getting exc_info: {exc_info_error}"
            )

        # Integration tests still pass on ToolException as langchain handles them gracefully.
        # Force raise Exception to kill test execution.
        if os.getenv("ENV") == "TEST":
            raise Exception(f"Integration test failed with exception {e}.")

        if isinstance(e, ToolException):
            raise e
        raise ToolException(e)

    if asyncio.iscoroutinefunction(func):

        async def wrapped_async(*args, callbacks=None, **kwargs):
            try:
                if emit_events:
                    kwargs["callbacks"] = callbacks
                tool_result = await func(*args, **kwargs)
                remember_tool_io(kwargs, tool_result)
                return tool_result
            except Exception as e:
                handle_exception(e)

        return wrapped_async
    else:

        def wrapped(*args, **kwargs):
            try:
                tool_result = func(*args, **kwargs)
                remember_tool_io(kwargs, tool_result)
                return tool_result
            except Exception as e:
                handle_exception(e)

        return wrapped


class DieselInput(BaseModel):
    state: str = Field(description="Two-letter US State code")


def validate_us_states(states: typing.Set[str]) -> None:
    if invalid_states := states - pricer_constants.US_STATES:
        raise ToolException(
            f"Invalid US state codes: {', '.join(invalid_states)}. We only support US states."
        )


def mock_get_diesel_price_tool(state: str) -> float:
    validate_us_states({state})
    return stable_random(state, 2.5, 3.5, precision=2)


def get_diesel_price_tool(state: str) -> float:
    """Gets diesel price in dollars per gallon for this week using EIA API."""
    # TODO(P1): Consider making diesel price a tool that fails more, and is handled by handle_tool_error.
    # This would require making pricer handle the error as well, as it assumes pd.NA is returned on error.
    validate_us_states({state})
    return PRICER.diesel_price.get_diesel_price(datetime.datetime.now(), state)


class LocationsInput(BaseModel):
    origin: str = Field(description="city, state (two-letter code)")
    destination: str = Field(description="city, state (two-letter code)")


def mock_get_distance_between_locations_tool(origin: str, destination: str) -> float:
    return stable_random(f"{origin}{destination}", 50, 150)


def get_distance_between_locations_tool(
    origin: str, destination: str, use_cache: bool = False
) -> float:
    """Gets distance between two locations in miles using google maps API."""

    # Removing map on distance for now. May add later.

    # emission_function = langchain_utils.extract_emission_function(
    #     callbacks, "add_event_to_queue"
    # )
    # event = langchain_utils.generate_polyline_event(
    #     ActiveType.ACTIVE,
    #     origin=origin,
    #     destination=destination,
    #     gmaps_utils=GMAPS_UTILS,
    # )
    # langchain_utils.emit_event(emission_function, event, VisualizationType.GMAPS)

    try:
        return GMAPS_UTILS.get_distance_miles(origin, destination, use_cache=use_cache)
    except Exception as e:
        logging.error(f"Error in common.google_maps_utils.get_distance_miles: {e}")
        raise ToolException(
            f"Couldn't find a route between {origin} and {destination}."
        ) from e


class TransitTimeInput(BaseModel):
    origin: str = Field(description="city, state (two-letter code)")
    destination: str = Field(description="city, state (two-letter code)")
    hours_left_on_clock: int = Field(
        description="Hours left from FMCSA 11-hour driving limit. This has no bearing on whether driver will make destination on time.",
        default=transit_time.MAX_DRIVING_HOURS,
    )
    pickup_date: str = Field(
        description="""Date of pickup/departure in ISO format YYYY-MM-DD.""",
        default="",
    )
    dropoff_date: str = Field(
        description="""Date of dropoff/arrival in ISO format YYYY-MM-DD.""",
        default="",
    )
    pickup_time: str = Field(
        description="""Time of pickup/departure ISO format HH:MM:SS±HH:MM.""",
        default="",
    )
    dropoff_time: str = Field(
        description="""Time of dropoff/arrival ISO format HH:MM:SS±HH:MM.""",
        default="",
    )
    roundtrip: bool = Field(
        description="If true, calculates round trip transit time.",
        default=False,
    )


def validate_transit_time_input(
    hours_left_on_clock: int,
    pickup_time: datetime.datetime | datetime.time,
    dropoff_time: datetime.datetime | datetime.time,
) -> None:
    if hours_left_on_clock < 0:
        raise ToolException("Hours left on clock cannot be negative.")
    if hours_left_on_clock > transit_time.MAX_DRIVING_HOURS:
        raise ToolException(
            f"Hours left on clock cannot exceed the maximum driving hours: {transit_time.MAX_DRIVING_HOURS}."
        )


def mock_transit_time_tool(
    origin: str,
    destination: str,
    hours_left_on_clock: int = transit_time.MAX_DRIVING_HOURS,
    pickup_date: str = "",
    dropoff_date: str = "",
    pickup_time: str = "",
    dropoff_time: str = "",
    roundtrip: bool = False,
) -> typing.Dict[str, typing.Any]:
    if pickup_date and not pickup_time:
        raise ToolException("Pickup time is required if pickup date is provided.")
    if dropoff_date and not dropoff_time:
        raise ToolException("Dropoff time is required if dropoff date is provided.")
    # Date is optional, so we need to check if it's empty before joining with time.
    # E.g. 2021-01-01T12:00:00-05:00 if both date and time are provided, or 2021-01-01 if only date is provided.
    pickup_datetime = "T".join([i for i in [pickup_date, pickup_time] if i != ""])
    dropoff_datetime = "T".join([i for i in [dropoff_date, dropoff_time] if i != ""])
    pickup_time_or_datetime = langchain_utils.parse_time_or_datetime(pickup_datetime)
    dropoff_time_or_datetime = langchain_utils.parse_time_or_datetime(dropoff_datetime)
    validate_transit_time_input(
        hours_left_on_clock, pickup_time_or_datetime, dropoff_time_or_datetime
    )

    hash_str = f"{origin}{destination}{hours_left_on_clock}{pickup_time_or_datetime}{dropoff_time_or_datetime}{roundtrip}"
    random.seed(stable_random(hash_str, 0, 1000))

    transit_time_hrs = stable_random(hash_str, 10, 40)
    formatted_data = {
        "transit_time": langchain_utils.format_timedelta(
            td_object=datetime.timedelta(hours=transit_time_hrs)
        )
    }
    # Generate random time for pickup/dropoff
    random_time = f"{stable_random(hash_str, 0, 24):02d}:00 PDT"

    if pickup_time_or_datetime:
        formatted_data["calculated_dropoff_time"] = random_time
        if transit_time_hrs > 24:
            formatted_data["days_after"] = 1
    elif dropoff_time_or_datetime:
        formatted_data["calculated_pickup_time"] = random_time
        if transit_time_hrs > 24:
            formatted_data["days_before"] = 1

    cities = [
        "Chicago, IL",
        "Detroit, MI",
        "Dallas, TX",
        "Los Angeles, CA",
    ]
    # Maybe choose some random cities from the list
    if stops := random.sample(cities, stable_random(hash_str, 0, 4)):
        formatted_data["driver_will_run_out_of_hours_near"] = stops

    return formatted_data


def transit_time_tool(
    origin: str,
    destination: str,
    hours_left_on_clock: int = transit_time.MAX_DRIVING_HOURS,
    pickup_date: str = "",
    dropoff_date: str = "",
    pickup_time: str = "",
    dropoff_time: str = "",
    roundtrip: bool = False,
    callbacks=None,
) -> typing.Dict[str, typing.Any]:
    if pickup_date and not pickup_time:
        raise ToolException("Pickup time is required if pickup date is provided.")
    if dropoff_date and not dropoff_time:
        raise ToolException("Dropoff time is required if dropoff date is provided.")
    # Date is optional, so we need to check if it's empty before joining with time.
    # E.g. 2021-01-01T12:00:00-05:00 if both date and time are provided, or 2021-01-01 if only date is provided.
    pickup_datetime = "T".join([i for i in [pickup_date, pickup_time] if i != ""])
    dropoff_datetime = "T".join([i for i in [dropoff_date, dropoff_time] if i != ""])
    pickup_time = langchain_utils.parse_time_or_datetime(pickup_datetime)
    dropoff_time = langchain_utils.parse_time_or_datetime(dropoff_datetime)
    validate_transit_time_input(hours_left_on_clock, pickup_time, dropoff_time)
    return transit_time.get_transit_time(
        origin=origin,
        destination=destination,
        gmaps_utils=GMAPS_UTILS,
        hours_left_on_clock=hours_left_on_clock,
        pickup_time=pickup_time,
        dropoff_time=dropoff_time,
        roundtrip=roundtrip,
        emission_function=langchain_utils.extract_emission_function(
            callbacks, "add_event_to_queue"
        ),
    )


class FuelCostInput(BaseModel):
    origin_city: str = Field(description="Origin City")
    origin_state: str = Field(description="Two-digit US State code")
    destination_city: str = Field(description="Destination City")
    destination_state: str = Field(description="Two-digit US State code")
    roundtrip: bool = Field(
        description="If true, calculates round trip cost.",
        default=False,
    )
    # mpg: float = Field(description="Miles per gallon", default=DEFAULT_TRUCK_MPG)


def validate_fuel_cost_input(
    origin_state: str, destination_state: str, mpg: float = DEFAULT_TRUCK_MPG
):
    validate_us_states({origin_state, destination_state})
    if not isinstance(mpg, (int, float)):
        raise ToolException("Miles per gallon must be a number.")
    if mpg <= 0:
        raise ToolException("Miles per gallon must be greater than 0.")


def mock_get_fuel_cost_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    roundtrip: bool = False,
    mpg: float = DEFAULT_TRUCK_MPG,
) -> float:
    validate_fuel_cost_input(origin_state, destination_state, mpg)
    # Considering diesel price and distance to mock a fuel cost
    origin_diesel_price = mock_get_diesel_price_tool(origin_state)
    mock_distance = mock_get_distance_between_locations_tool(
        f"{origin_city}, {origin_state}", f"{destination_city}, {destination_state}"
    )
    gallons_needed = mock_distance / mpg
    total_fuel_cost = origin_diesel_price * gallons_needed
    return round(total_fuel_cost + total_fuel_cost * roundtrip, 2)


def get_fuel_cost_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    roundtrip: bool = False,
    mpg: float = DEFAULT_TRUCK_MPG,
) -> float:
    """
    Calculates the fuel cost for a trip between two locations.

    Args:
        origin_city (str): The name of the origin city.
        origin_state (str): The name of the origin state.
        destination_city (str): The name of the destination city.
        destination_state (str): The name of the destination state.
        roundtrip (bool, optional): Whether the trip is a roundtrip. Defaults to False.
        mpg (float, optional): The miles per gallon of the vehicle. Defaults to DEFAULT_TRUCK_MPG.

    Returns:
        float: The fuel cost for the trip.
    """
    validate_fuel_cost_input(origin_state, destination_state, mpg)
    distance_miles = get_distance_between_locations_tool(
        f"{origin_city}, {origin_state}",
        f"{destination_city}, {destination_state}",
        use_cache=True,
    )
    return langchain_utils.estimate_fuel_cost(
        origin_state=origin_state,
        distance_miles=distance_miles,
        pricer=PRICER,
        roundtrip=roundtrip,
        mpg=mpg,
    )


class TruckCostInput(BaseModel):
    origin_city: str = Field(description="Origin City")
    origin_state: str = Field(description="Two-digit US State code")
    destination_city: str = Field(description="Destination City")
    destination_state: str = Field(description="Two-digit US State code")
    equipment_type: str = Field(
        description="Equipment type. Must be Reefer or Dry Van.", default="Dry Van"
    )
    roundtrip: bool = Field(
        description="If true, calculates round trip cost.",
        default=False,
    )


def validate_truck_cost_input(
    origin_state: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
):
    validate_us_states({origin_state, destination_state})
    if equipment_type not in ["Dry Van", "Reefer"]:
        raise ToolException("Equipment type must be Reefer or Dry Van.")


def mock_get_todays_truck_cost_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    roundtrip: bool = False,
) -> typing.Dict[str, float]:
    if roundtrip:
        a_to_b, b_to_a = mock_get_todays_truck_cost_tool(
            origin_city,
            origin_state,
            destination_city,
            destination_state,
            equipment_type,
            False,
        ), mock_get_todays_truck_cost_tool(
            destination_city,
            destination_state,
            origin_city,
            origin_state,
            equipment_type,
            False,
        )
        combined_truck_costs = langchain_utils.combine_multiple_truck_costs(
            [a_to_b, b_to_a]
        )
        return combined_truck_costs
    validate_truck_cost_input(origin_state, destination_state, equipment_type)
    base_cost = stable_random(
        f"{origin_city}{origin_state}{destination_city}{destination_state}{equipment_type}",
        1800,
        2200,
        precision=2,
    )
    mock_distance = mock_get_distance_between_locations_tool(
        f"{origin_city}, {origin_state}", f"{destination_city}, {destination_state}"
    )
    return {
        "truck_cost_with_fuel": base_cost,
        "distance": mock_distance,
        # "fuel_cost": mock_get_fuel_cost_tool(
        #     origin_state,
        #     mock_distance,
        # ),
    }


def get_todays_truck_cost_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    roundtrip: bool = False,
    callbacks=None,
) -> typing.Dict[str, float]:
    """Gets todays truck cost based on origin and destination cities. Returns truck cost, distance, diesel cost."""
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )
    if roundtrip:
        a_to_b, b_to_a = get_todays_truck_cost_tool(
            origin_city=origin_city,
            origin_state=origin_state,
            destination_city=destination_city,
            destination_state=destination_state,
            equipment_type=equipment_type,
            roundtrip=False,
            callbacks=callbacks,
        ), get_todays_truck_cost_tool(
            origin_city=destination_city,
            origin_state=destination_state,
            destination_city=origin_city,
            destination_state=origin_state,
            equipment_type=equipment_type,
            roundtrip=False,
            callbacks=callbacks,
        )

        combined_truck_costs = langchain_utils.combine_multiple_truck_costs(
            [a_to_b, b_to_a]
        )
        return combined_truck_costs

    validate_truck_cost_input(origin_state, destination_state, equipment_type)

    return_dict = langchain_utils.price_truck(
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        pricer=PRICER,
        gmaps_utils=GMAPS_UTILS,
        equipment_type=equipment_type,
        emission_function=emission_function,
    )
    # Remove upper and lower bounds for now
    del return_dict["truck_cost_upper"]
    del return_dict["truck_cost_lower"]
    if pd.isna(return_dict["distance"]):
        raise ToolException(
            f"Couldn't find a route between {origin_city}, {origin_state} and {destination_city}, {destination_state}."
        )
    # return_dict["fuel_cost"] = get_fuel_cost_tool(
    #     origin_state=origin_state,
    #     distance_miles=return_dict["distance"],
    # )
    if return_dict["distance"] <= 0:
        raise ToolException(
            "A lane with a different origin and destination must be provided."
        )
    return return_dict


async_truck_cost_fn = langchain_utils.asyncify(get_todays_truck_cost_tool)


# Have a separate function for async to simultaneously call get_todays_truck_cost_tool twice in roundtrip
async def async_get_todays_truck_cost_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    roundtrip: bool = False,
    callbacks=None,
) -> typing.Dict[str, float]:
    if roundtrip:
        a_to_b, b_to_a = await asyncio.gather(
            async_truck_cost_fn(
                origin_city=origin_city,
                origin_state=origin_state,
                destination_city=destination_city,
                destination_state=destination_state,
                equipment_type=equipment_type,
                roundtrip=False,
                callbacks=callbacks,
            ),
            async_truck_cost_fn(
                origin_city=destination_city,
                origin_state=destination_state,
                destination_city=origin_city,
                destination_state=origin_state,
                equipment_type=equipment_type,
                roundtrip=False,
                callbacks=callbacks,
            ),
        )
        combined_truck_costs = langchain_utils.combine_multiple_truck_costs(
            [a_to_b, b_to_a]
        )
        return combined_truck_costs
    return await async_truck_cost_fn(
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        equipment_type=equipment_type,
        roundtrip=False,
        callbacks=callbacks,
    )


class CurrentTimeInput(BaseModel):
    timezone_name: str = Field(description="Timezone name")


def mock_get_current_time_tool(timezone_name: str) -> str:
    if timezone_name not in pytz.all_timezones:
        raise ToolException(f"Invalid timezone {timezone_name}")

    # Localize the MOCK_CURRENT_TIME as a UTC time
    utc_time = pytz.utc.localize(MOCK_CURRENT_TIME)

    # Convert this UTC time to the provided time zone
    timezone = pytz.timezone(timezone_name)
    localized_time = utc_time.astimezone(timezone)

    return localized_time.isoformat()


def get_current_time_tool(timezone_name: str) -> str:
    """
    Returns current time in specified city via pytz time zone database.
    Example time zone is "America/New_York"
    """
    if timezone_name not in pytz.all_timezones:
        raise ToolException(f"Invalid timezone {timezone_name}")
    timezone = pytz.timezone(timezone_name)
    current_time = datetime.datetime.now(timezone)
    return current_time.isoformat()


class MinMaxValuesInput(BaseModel):
    values: typing.List[float] = Field(description="List of values")


def get_minimum_value_tool(values: typing.List[float]) -> float:
    """
    Inputs list of values, returns minimum value from the list.
    Values must be a list of floats, no expressions are allowed.
    Example: [5771.4671875, 5153.095703125]
    """
    return min(values)


def get_maximum_value_tool(values: typing.List[float]) -> float:
    """
    Inputs list of values, returns maximum value from the list.
    Values must be a list of floats, no expressions are allowed.
    Example: [5617.431640625, 5722.459716796875]
    """
    return max(values)


class WeatherInput(BaseModel):
    city: str = Field(description="City name")
    state: str = Field(description="State name")
    # forecast_time: str = Field(description="Time of forecast in ISO format", default="")
    # hours_in_future: int = Field(
    #     description="Hours in the future to forecast", default=0
    # )
    country: str = Field(description="Country name", default="US")
    zipcode: str = Field(description="Zipcode", default="")


def mock_get_city_weather_tool(
    city: str,
    state: str,
    country: str = "US",
    zipcode: str = "",
    forecast_time: str = "",
    hours_in_future: int = 0,
) -> typing.Dict[str, typing.Any]:
    hash_str = city + state + country + zipcode + forecast_time + str(hours_in_future)
    return {
        "summary": "Clear",  # Keep this static for simplicity
        "temperature": stable_random(hash_str, 60, 85),
        "windSpeed": f"{stable_random(hash_str, 5, 15)} mph",
        "windBearing": stable_random_choice(hash_str, weather_api.CARDINAL_DIRECTIONS),
        # "windGust": round(stable_random(hash_str, 10.0, 20.0)),
    }


def get_city_weather_tool(
    city: str,
    state: str,
    country: str = "US",
    zipcode: str = "",
    forecast_time: str = "",
    hours_in_future: int = 0,
) -> typing.Dict[str, typing.Any]:
    """
    Gets the weather data of a given city.
    Returns data in JSON format and different data can be returned based on the key selected.
    Currently configured to return "current" data incl. temperature, wind speed, and visibility.
    Requires city and state but country and zipcode are optional.
    Time is required for a historic request.
    Response has verbal summary (rain/clear) and conditions.
    Invoke GetCurrentTime for one hour time.
    """
    try:
        latitude, longitude = GMAPS_UTILS.get_lat_lng(city, state, country, zipcode)
    except Exception as e:
        logging.error(f"Error in common.google_maps_utils.get_lat_lng: {e}")
        raise ToolException("Couldn't find the city you specified.")
    # Convert forecast_time from string to UNIX
    # Initialize as None to accept current time as default if no time specified
    forecast_timestamp = None
    if forecast_time:
        forecast_timestamp = int(
            datetime.datetime.strptime(forecast_time, "%Y-%m-%dT%H:%M:%S").timestamp()
        )
    elif hours_in_future:
        future_time = datetime.datetime.now() + datetime.timedelta(
            hours=hours_in_future
        )
        forecast_timestamp = int(future_time.timestamp())
    try:
        weather = weather_api.get_weather(latitude, longitude, forecast_timestamp)
    except Exception as e:
        logging.error(f"Error in weather_api.get_weather for {city}, {state}: {e}")
        raise ToolException(e)
    # wind_fields = ["windSpeed", "windBearing", "windGust"]
    # if "windSpeed" in weather and weather["windSpeed"] < MIN_WIND_SPEED_MPH:
    #     for field in wind_fields:
    #         if field in weather:
    #             del weather[field]
    return weather


def mock_get_weather_along_route_tool(
    origin: str, destination: str
) -> typing.Dict[str, typing.Any]:
    hash_str = origin + destination

    weather_options = ["Clear", "Cloudy", "Fog", "Rain"]

    route = f"I-{stable_random(hash_str, 5, 95)}"
    # Randomize number of segments between 2 and 4
    num_segments = stable_random(hash_str, 2, 4)

    cities = [
        "Austin, Texas",
        "Columbus, Texas",
        "Houston, Texas",
        "Corpus Christie, Texas",
    ]

    segments = [
        {
            "step_name": route,
            "weather": {
                "summary": stable_random_choice(hash_str + str(i), weather_options),
                "temperature": stable_random(hash_str + str(i), 70, 85),
                "warning": [],
            },
            "forecast_time": f"{(MOCK_CURRENT_TIME + datetime.timedelta(hours=i)).strftime('%H:%M')} CST",
            "nearest_city": cities[i],
        }
        for i in range(num_segments)
    ]
    return {"route_taken": route, "segments": segments}


def get_weather_along_route_tool(
    origin: str, destination: str
) -> typing.Dict[str, typing.Any]:
    """Gets the weather along the driving route between origin and destination.

    Example:
    get_weather_along_route_tool("New York, NY", "Trenton, NJ")

    Origin and destination are strings that can be in the form "city, state".

    Returns a list of segments with relevant information to the driver.
    """
    return weather_along_route.get_weather_along_route(origin, destination, GMAPS_UTILS)


async def async_get_weather_along_route_tool(
    origin: str, destination: str, callbacks=None
) -> typing.Dict[str, typing.Any]:
    """Gets the weather along the driving route between origin and destination.

    Example:
    async_get_weather_along_route_tool("New York, NY", "Trenton, NJ")

    Origin and destination are strings that can be in the form "city, state".

    Returns a list of segments with relevant information to the driver.
    """
    # Emits an event to the agent to draw a map of the route.
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )
    return await weather_along_route.async_get_weather_along_route(
        origin, destination, emission_function, GMAPS_UTILS
    )


class CarrierInfoInput(BaseModel):
    dot_number: typing.Union[int, str] = Field(
        description="1-8 digit department of transportation number",
        default=None,
    )
    mc_number: typing.Union[int, str] = Field(
        description="1-8 digit number prefixed by MC, MX, or FF", default=None
    )


def validate_dot_number(dot_number: int = None) -> None:
    if pd.isna(dot_number):
        return
    if dot_number and not isinstance(dot_number, int):
        raise ToolException(
            "DOT number must be an integer. If searching by name, invoke CarrierSearch first."
        )
    if dot_number and not (1 <= len(str(dot_number)) <= 8):
        raise ToolException("DOT number must be between 1 and 8 digits.")


def mock_get_carrier_info_tool(
    dot_number: int = None, mc_number: str = None
) -> typing.Dict[str, typing.Any]:
    """Must mirror format from carrier_tool.format_carier_info."""
    validate_dot_number(dot_number)
    hash_str = str(dot_number) + str(mc_number)
    random.seed(stable_random(hash_str, 0, 1000))

    COMPANY_NAMES = [
        "Test Carrier",
        "Express Logistics",
        "Quick Transport",
        "Global Freight",
    ]

    EQUIPMENT_TYPES = EquipmentType._fields
    CARGO_TYPES = [
        "Fresh Produce",
        "Meat",
        "Refrigerated Food",
        "Beverages",
        "Paper Products",
        "Milk",
        "Groceries",
        "Eggs",
    ]

    equipment_types = random.sample(EQUIPMENT_TYPES, stable_random(hash_str, 1, 3))
    cargo_types = random.sample(CARGO_TYPES, stable_random(hash_str, 1, 4))

    # Mock Data
    mock_info = {
        "company_name": stable_random_choice(hash_str, COMPANY_NAMES),
        "equipment_type": equipment_types,
        "dot_number": (
            dot_number if dot_number else stable_random(hash_str, 1000000, 9999999)
        ),
        "email": "<EMAIL>",
        "phone_number": "1234567890",
        "located_in": "Albany, NY",
        "rating": stable_random_choice(hash_str, CARRIER_RATING_DICT.values()),
        "total_trucks": stable_random(hash_str, 1, 50),
        "details": {
            "contact_name": "John Doe",
            "in_business_for": "{} years, {} months, {} days".format(
                stable_random(hash_str, 1, 10),
                stable_random(hash_str, 1, 12),
                stable_random(hash_str, 1, 31),
            ),
            "company_type": stable_random_choice(hash_str, CARSHIP_DICT.values()),
            "mcs_form_update_date": "{}-{:02d}-{:02d}".format(
                stable_random(hash_str, 2000, 2023),
                stable_random(hash_str, 1, 12),
                stable_random(hash_str, 1, 28),
            ),
            "types_of_cargo_transported": cargo_types,
        },
        "mc_number": (
            mc_number
            if mc_number
            else "MC" + str(stable_random(hash_str, 10000, 99999))
        ),
    }

    # Optional fields
    inspections = stable_random(hash_str, 0, 5)
    if inspections > 0:
        mock_info["inspections_in_last_two_years"] = inspections

    crashes = stable_random(hash_str, 0, 5)
    if crashes > 0:
        mock_info["crashes_in_last_two_years"] = crashes

    return mock_info


def get_carrier_info_tool(
    dot_number: int = None, mc_number: str = None, callbacks=None
) -> typing.Dict[str, typing.Any]:
    """Gets information based on DOT or ICC number.

    Example:
    get_carrier_info_tool(dot_number=1234567)
    get_carrier_info_tool(mc_number="MC1234567")
    """
    validate_dot_number(dot_number)
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )
    carrier_info = carrier_tool.fetch_carrier_info(
        dot_number,
        mc_number,
        fetch_inspection=True,
        fetch_crash=True,
        emission_function=emission_function,
    )
    if not carrier_info:
        return {"error": "Carrier not found"}
    if "error" in carrier_info:
        return carrier_info

    census_dict = carrier_info["census_dict"]
    inspection_dict = carrier_info["inspection_dict"]
    crash_dict = carrier_info["crash_dict"]
    full_carrier_info = {**census_dict}

    tot_inspection_key = "inspections_in_last_two_years"
    if tot_inspection_key in inspection_dict:
        full_carrier_info[tot_inspection_key] = inspection_dict[tot_inspection_key]
    tot_crash_key = "crashes_in_last_two_years"
    if tot_crash_key in crash_dict:
        full_carrier_info[tot_crash_key] = crash_dict[tot_crash_key]

    return full_carrier_info


def mock_get_crash_info_tool(
    dot_number: int = None, mc_number: str = None
) -> typing.Dict[str, typing.Any]:
    """Mock function for getting crash information."""
    validate_dot_number(dot_number)
    hash_str = str(dot_number) if dot_number else mc_number

    # Sample Data for Crashes
    STATES = ["IL", "WI", "IN", "MI", "OH"]
    CITIES = ["Chicago", "Milwaukee", "Indianapolis", "Detroit", "Cleveland"]
    LOCATIONS = ["Main St", "Elm St", "Park Ave", "Oak St", "Pine St"]
    EVENTS = [
        "Collision involving motor vehicle in transport",
        "Non collision ran off road",
        "Collision involving fixed object",
    ]
    WEATHER_CONDITIONS = ["Clear", "Rain", "Snow", "Fog", "Severe Crosswinds"]
    ROAD_CONDITIONS = ["Dry", "Wet", "Icy", "Snow-covered", "Slushy"]
    COMPANY_NAMES = [
        "Test Carrier",
        "Express Logistics",
        "Quick Transport",
        "Global Freight",
    ]
    # Generating a random number of sample crashes
    num_crashes = stable_random(hash_str, 1, 5)
    sample_crashes = []
    for i in range(num_crashes):
        crash = {
            "crash_date": "{}-{:02d}-{:02d}".format(
                stable_random(hash_str + str(i), 2022, 2023),
                stable_random(hash_str + str(i), 1, 12),
                stable_random(hash_str + str(i), 1, 28),
            ),
            "state": stable_random_choice(hash_str + str(i), STATES),
            "city": stable_random_choice(hash_str + str(i), CITIES),
            "location": stable_random_choice(hash_str + str(i), LOCATIONS),
            "events": stable_random_choice(hash_str + str(i), EVENTS),
        }

        # Randomly adding weather and road conditions
        if stable_random(hash_str + str(i) + "weather", 0, 1, precision=1) > 0.5:
            crash["weather_condition"] = stable_random_choice(
                hash_str + str(i), WEATHER_CONDITIONS
            )
        if stable_random(hash_str + str(i) + "road", 0, 1, precision=1) > 0.5:
            crash["road_condition"] = stable_random_choice(
                hash_str + str(i), ROAD_CONDITIONS
            )

        sample_crashes.append(crash)

    # Mock Data
    mock_info = {
        "crashes_in_last_two_years": num_crashes,
        "sample_crashes": sample_crashes,
        "dot_number": (
            dot_number if dot_number else stable_random(hash_str, 1000000, 9999999)
        ),
        "company_name": stable_random_choice(hash_str, COMPANY_NAMES),
    }

    return mock_info


def get_crash_info_tool(
    dot_number: int = None, mc_number: str = None, callbacks=None
) -> typing.Dict[str, typing.Any]:
    validate_dot_number(dot_number)
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )

    carrier_info = carrier_tool.fetch_carrier_info(
        dot_number,
        mc_number,
        fetch_inspection=False,
        fetch_crash=True,
        emission_function=emission_function,
    )
    if not carrier_info:
        return {"error": "Carrier not found"}
    if "error" in carrier_info:
        return carrier_info

    return carrier_info["crash_dict"]


def mock_get_inspection_info_tool(
    dot_number: int = None, mc_number: str = None
) -> typing.Dict[str, typing.Any]:
    """Mock function for getting inspection information."""
    validate_dot_number(dot_number)
    hash_str = str(dot_number) if dot_number else mc_number
    random.seed(stable_random(hash_str, 0, 1000))

    # Sample Violation Descriptions
    SAMPLE_VIOLATIONS = [
        "Exceeding speed limit by 15 mph",
        "Failure to secure vehicle equipment",
        "Improper lane change",
        "Overloaded vehicle",
        "Brake system not functioning properly",
        "Missing or defective lights",
        "Failure to maintain log book",
        "Driver not wearing seatbelt",
        "Use of handheld mobile device while driving",
        "Expired vehicle registration",
    ]
    COMPANY_NAMES = [
        "Test Carrier",
        "Express Logistics",
        "Quick Transport",
        "Global Freight",
    ]

    # Selecting a random subset of violations for mock data
    selected_violations = random.sample(
        SAMPLE_VIOLATIONS, stable_random(hash_str, 1, 5)
    )

    # Mock Data
    mock_info = {
        "inspections_in_last_two_years": stable_random(hash_str, 0, 50),
        "driver_violations": stable_random(hash_str, 0, 10),
        "driver_out_of_service_violations": stable_random(hash_str, 0, 5),
        "vehicle_violations": stable_random(hash_str, 0, 10),
        "vehicle_out_of_service_violations": stable_random(hash_str, 0, 5),
        "violation_descriptions": selected_violations,
        "dot_number": (
            dot_number if dot_number else stable_random(hash_str, 1000000, 9999999)
        ),
        "company_name": stable_random_choice(hash_str, COMPANY_NAMES),
    }

    return mock_info


def get_inspection_info_tool(
    dot_number: int = None, mc_number: str = None, callbacks=None
) -> typing.Dict[str, typing.Any]:
    validate_dot_number(dot_number)
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )

    carrier_info = carrier_tool.fetch_carrier_info(
        dot_number,
        mc_number,
        fetch_inspection=True,
        fetch_crash=False,
        emission_function=emission_function,
    )
    if not carrier_info:
        return {"error": "Carrier not found"}
    if "error" in carrier_info:
        return carrier_info

    return carrier_info["inspection_dict"]


class HistoricalPriceInput(BaseModel):
    origin_city: str = Field(description="Origin City")
    origin_state: str = Field(description="Two-digit US State code")
    destination_city: str = Field(description="Destination City")
    destination_state: str = Field(description="Two-digit US State code")
    equipment_type: str = Field(
        description="Equipment type. Must be Reefer or Dry Van.", default="Dry Van"
    )
    number_of_days: int = Field(
        description="Number of days to provide historical data for", default=30
    )
    roundtrip: bool = Field(
        description="Whether or not to calculate for round trip", default=False
    )


def validate_historical_price_input(
    origin_state: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    number_of_days: int = 30,
):
    validate_truck_cost_input(origin_state, destination_state, equipment_type)
    if number_of_days < 14 or number_of_days > 730:
        raise ToolException("Period must be at least two weeks and at most two years.")


def mock_get_historical_price_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    number_of_days: int = 30,
    roundtrip: bool = False,
) -> typing.Dict[str, typing.Dict]:
    hash_str = (
        origin_city
        + origin_state
        + destination_city
        + destination_state
        + equipment_type
        + str(number_of_days)
        + str(roundtrip)
    )
    validate_historical_price_input(
        origin_state, destination_state, equipment_type, number_of_days
    )
    return {
        "low": stable_random(hash_str, 300, 2000, 2),
        "high": stable_random(hash_str, 1500, 5000, 2),
        "average": stable_random(hash_str, 500, 4000, 2),
        "period": langchain_utils.format_timedelta(
            datetime.timedelta(days=number_of_days), granularity="day"
        ),
    }


def get_historical_price_tool(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    equipment_type: str = "Dry Van",
    number_of_days: int = 30,
    roundtrip: bool = False,
    callbacks=None,
) -> typing.Dict[str, typing.Dict]:
    emission_function = langchain_utils.extract_emission_function(
        callbacks, "add_event_to_queue"
    )
    validate_historical_price_input(
        origin_state, destination_state, equipment_type, number_of_days
    )

    result = langchain_utils.batch_price_historical(
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        equipment_type=equipment_type,
        number_of_days=number_of_days,
        roundtrip=roundtrip,
        gmaps_utils=GMAPS_UTILS,
        pricer=PRICER,
        emission_function=emission_function,
    )
    return {
        "average": round(result["average"], 2),
        "high": round(result["high"], 2),
        "low": round(result["low"], 2),
        "period": langchain_utils.format_timedelta(
            datetime.timedelta(days=number_of_days), granularity="day"
        ),
    }


from langchain_openai import ChatOpenAI

LLM_MATH_CHAIN = LLMMathChain.from_llm(
    llm=ChatOpenAI(temperature=0, model=LANGCHAIN_MODEL)
)


class CalculatorInput(BaseModel):
    question: str = Field()


class CarrierSearchInput(BaseModel):
    carrier_name: str = Field(description="Carrier name search string", default="")
    equipment_types: typing.List[str] = Field(
        description="Equipment types hauled, such as ['Dry Van']",
        default=[],
    )
    cargo_hauled: typing.List[str] = Field(
        description="Types of cargo hauled, such as ['Meat']",
        default=[],
    )
    in_business_months_min: int = Field(
        description="Minimum months in business", default=None
    )
    in_business_months_max: int = Field(
        description="Maximum months in business", default=None
    )
    fleet_size_min: int = Field(description="Minimum fleet size", default=10)
    fleet_size_max: int = Field(description="Maximum fleet size", default=300)
    insurance_coverage_min: int = Field(
        description="Minimum insurance coverage amount", default=None
    )
    inspections_min: int = Field(
        description="Minimum number of inspections in the last two years",
        default=None,
    )
    carries_hazmat: bool = Field(
        description="If carrier hauls hazmat or not.", default=False
    )
    safety_ratings: typing.List[str] = Field(
        description="Carrier's safety ratings - invoke with S for satisfactory, C for conditional, U for unsatisfactory, N for no rating",
        default=["S", "N"],
    )
    origin_city: str = Field(
        description="If user specifies 'near city' or asks about a lane origin, use the origin parameters.",
        default=None,
    )
    origin_state: str = Field(
        description="If user specifies 'near city' or asks about a lane origin, use the origin parameters. Must be a two-letter state code.",
        default=None,
    )
    destination_city: str = Field(
        description="If user asks about a lane destination, use the destination parameters.",
        default=None,
    )
    destination_state: str = Field(
        description="If user asks about a lane destination, use the destination parameters. Must be a two-letter state code.",
        default=None,
    )
    near_origin_radius: int = Field(
        description="Radius in miles, leave empty unless user specifies miles",
        default=50,
    )
    carrier_email: str = Field(
        description="Carrier's contact email address", default=None
    )
    carrier_phone: str = Field(
        description="Carrier's contact phone number", default=None
    )


def validate_carrier_search_input(
    in_business_months_min: int,
    in_business_months_max: int,
    fleet_size_min: int,
    fleet_size_max: int,
    insurance_coverage_min: int,
    inspections_min: int,
    origin_state: str,
    destination_state: str,
    safety_ratings: typing.List[str],
    carrier_email: str,
) -> None:
    states = set()
    if origin_state:
        states.add(origin_state)
    if destination_state:
        states.add(destination_state)
    validate_us_states(states)
    email_regex = re.compile(r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)")
    if (in_business_months_min and in_business_months_min < 0) or (
        in_business_months_max and in_business_months_max < 0
    ):
        raise ToolException("Months in business must be positive.")
    if (
        in_business_months_min
        and in_business_months_max
        and in_business_months_min > in_business_months_max
    ):
        raise ToolException("Minimum months in business must be less than maximum.")
    if (fleet_size_min and fleet_size_min < 0) or (
        fleet_size_max and fleet_size_max < 0
    ):
        raise ToolException("Fleet size must be positive.")
    if fleet_size_min and fleet_size_max and fleet_size_min > fleet_size_max:
        raise ToolException("Minimum fleet size must be less than maximum.")
    if insurance_coverage_min and insurance_coverage_min < 0:
        raise ToolException("Insurance coverage min must be positive.")
    if inspections_min and inspections_min < 0:
        raise ToolException("Inspections min must be positive.")

    for entry in safety_ratings:
        if entry.upper() not in ["S", "C", "U", "N"]:
            raise ToolException("Safety rating must be provided as S, C, N or U.")

    if carrier_email and not re.match(email_regex, carrier_email):
        raise ToolException("Email is not properly formatted. Cannot match.")


def mock_get_carrier_search_tool(
    carrier_name: str = "",
    equipment_types: typing.List[str] = [],
    cargo_hauled: typing.List[str] = [],
    in_business_months_min: int = None,
    in_business_months_max: int = None,
    fleet_size_min: int = None,
    fleet_size_max: int = None,
    insurance_coverage_min: int = None,
    inspections_min: int = None,
    carries_hazmat: bool = False,
    safety_ratings: typing.List = ["S", "N"],
    origin_city: str = None,
    origin_state: str = None,
    destination_city: str = None,
    destination_state: str = None,
    near_origin_radius: int = 50,
    carrier_email: str = None,
    carrier_phone: str = None,
) -> typing.Dict[str, typing.Any]:
    passed_args = {**locals()}
    validate_carrier_search_input(
        in_business_months_min=in_business_months_min,
        in_business_months_max=in_business_months_max,
        fleet_size_min=fleet_size_min,
        fleet_size_max=fleet_size_max,
        insurance_coverage_min=insurance_coverage_min,
        inspections_min=inspections_min,
        origin_state=origin_state,
        destination_state=destination_state,
        safety_ratings=safety_ratings,
        carrier_email=carrier_email,
    )
    hash_str = f"{carrier_name}{equipment_types}{cargo_hauled}{in_business_months_min}{in_business_months_max}{fleet_size_min}{fleet_size_max}{insurance_coverage_min}{inspections_min}{carries_hazmat}{safety_ratings}{origin_city}{origin_state}{destination_city}{destination_state}{near_origin_radius}{carrier_email}{carrier_phone}"

    args = inspect.signature(mock_get_carrier_search_tool).parameters

    filters = {
        name: passed_args[name]
        for name in args
        if passed_args[name] is not None and passed_args[name] != args[name].default
    }

    formatted_data = {
        "num_results_given_filters": stable_random(hash_str, 0, 100),
        "using_filters": filters,
    }
    # If these are provided, we would normally have specific_search
    if carrier_name or carrier_email or carrier_phone:
        formatted_data["specific_carrier_info"] = mock_get_carrier_info_tool(
            dot_number=stable_random(hash_str, 1000000, 9999999)
        )
    return formatted_data


from machine_learning.freightgpt.carrier_tool import carrier_search


def get_carrier_search_tool(
    carrier_name: str = "",
    equipment_types: typing.List[str] = [],
    cargo_hauled: typing.List[str] = [],
    in_business_months_min: int = None,
    in_business_months_max: int = None,
    fleet_size_min: int = None,
    fleet_size_max: int = None,
    insurance_coverage_min: int = None,
    inspections_min: int = None,
    carries_hazmat: bool = False,
    safety_ratings: typing.List = ["S", "N"],
    origin_city: str = None,
    origin_state: str = None,
    destination_city: str = None,
    destination_state: str = None,
    near_origin_radius: int = 50,
    carrier_email: str = None,
    carrier_phone: str = None,
    callbacks=None,
) -> typing.Dict[str, typing.Any]:
    validate_carrier_search_input(
        in_business_months_min=in_business_months_min,
        in_business_months_max=in_business_months_max,
        fleet_size_min=fleet_size_min,
        fleet_size_max=fleet_size_max,
        insurance_coverage_min=insurance_coverage_min,
        inspections_min=inspections_min,
        origin_state=origin_state,
        destination_state=destination_state,
        safety_ratings=safety_ratings,
        carrier_email=carrier_email,
    )
    # TODO(P1): #2116 Support outbound and backhaul simultaneously, by moving logic to run_carrier_search
    (
        results,
        is_specific_search,
        expanded_search,
    ), filters = carrier_search.run_carrier_search(
        text_search=carrier_name,
        cargo_or_equipment_hauled=list(set(equipment_types + cargo_hauled)),
        in_business_months_min=in_business_months_min,
        in_business_months_max=in_business_months_max,
        fleet_size_min=fleet_size_min,
        fleet_size_max=fleet_size_max,
        insurance_coverage_min=insurance_coverage_min,
        inspections_min=inspections_min,
        carries_hazmat=carries_hazmat,
        safety_ratings=safety_ratings,
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        near_origin_radius=near_origin_radius,
        carrier_email=carrier_email,
        carrier_phone=carrier_phone,
        backhaul=False,
        tool_store=langchain_utils.extract_tool_store(callbacks),
    )

    # TODO(P0): #2076 Add some carrier results to agent memory
    # TODO(P0): Consider wrapper function
    langchain_utils.emit_event(
        emission_function=langchain_utils.extract_emission_function(
            callbacks, "add_event_to_queue"
        ),
        event={
            "search_results": results,
            "filters": filters,
            "expanded_search": expanded_search,
        },
        viz_type=VisualizationType.CARRIER_SEARCH_RESULTS,
    )

    formatted_data = {
        "num_results_given_filters": len(results),
        "using_filters": filters,
    }

    if is_specific_search and results:
        # The specific info shouldn't be too redundant with the carrier search table
        dot_number = int(results[0]["dot_number"])
        carrier_info = get_carrier_info_tool(dot_number=dot_number)
        formatted_data["specific_carrier_info"] = carrier_info

    return formatted_data


LANGCHAIN_TOOLS = {
    "CarrierSearch": {
        "description": """search for carriers based on various criteria and filters. Only filter exactly by the fields the user specifies.
        Don't list any carriers in the response, only return a response saying 'Here are x number of results with this search criteria.'""",
        "args_schema": CarrierSearchInput,
        "function": get_carrier_search_tool,
        "mock_function": mock_get_carrier_search_tool,
        "frontend_loading_description": "Searching for carriers",
        "emits_event": True,
    },
    "Calculator": {
        "description": """use for freight math (calculating cost per mile, +/- fuel cost or margin). Inputs should always be numbers. Don't use this for minimum""",
        "args_schema": CalculatorInput,
        "function": LLM_MATH_CHAIN.run,
        "async_function": LLM_MATH_CHAIN.arun,
        "frontend_loading_description": "Calculating",
    },
    "WeeklyDieselPrice": {
        "description": "this week's diesel price for a state",
        "args_schema": DieselInput,
        "function": get_diesel_price_tool,
        "mock_function": mock_get_diesel_price_tool,
        "frontend_loading_description": "Getting price in $/gal",
    },
    "Distance": {
        "description": """get distance between two locations""",
        "args_schema": LocationsInput,
        "function": get_distance_between_locations_tool,
        "mock_function": mock_get_distance_between_locations_tool,
        "frontend_loading_description": "Computing driving distance between locations",
        "followup_suggestions": [
            "How many days and hours is the transit time?",
            "What is the Dry Van truck cost?",
            "What is the weather along route?",
            "Can you list carriers that haul this lane?",
            "Tell me more about their crash history.",
            "Tell me more about their inspection history.",
        ],
    },
    "TransitTime": {
        "description": """get transit time between two locations. use to see if a shipment can be made in time.
        If user asks for estimate dropoff/arrival time, invoke this function then reply with the time of arrival, in the timezone of the destination.
        """,
        "args_schema": TransitTimeInput,
        "function": transit_time_tool,
        "mock_function": mock_transit_time_tool,
        "emits_event": True,
        "frontend_loading_description": "Calculating transit time",
        "followup_suggestions": [
            "How many miles is it?",
            "What is the truck cost?",
            "Can you give me a list of carriers that haul this lane?",
            "What is the weather along route?",
        ],
    },
    "RouteFuelCost": {
        "description": """calculate the cost of fuel for a shipment between two cities.
        When calculating fuel along route, run RouteFuelCost. You need not run WeeklyDieselPrice.""",
        "args_schema": FuelCostInput,
        "function": get_fuel_cost_tool,
        "mock_function": mock_get_fuel_cost_tool,
        "frontend_loading_description": "Obtaining diesel cost for this route",
        "followup_suggestions": [
            "What is the diesel price at the origin?",
            "What is the diesel price at the destination?",
            "How much is the all-in truck cost?",
        ],
    },
    "TruckCost": {
        "description": """compute truck cost between two cities with fuel included. always call this first if needed for calculations.
        If more than two cities are referenced in a truck cost, use the truck cost function on each pair.
        When responding, don't mention that the fuel cost is included in the truck cost.""",
        "args_schema": TruckCostInput,
        "function": get_todays_truck_cost_tool,
        "async_function": async_get_todays_truck_cost_tool,
        "mock_function": mock_get_todays_truck_cost_tool,
        "emits_event": True,
        # The frontend relies on this loading description to conditionally display some elements in the feedback window
        "frontend_loading_description": "Predicting truck cost",
        "followup_suggestions": [
            "Add a 15% margin to the truck cost",
            "Find me carriers that haul this lane with this type of equipment.",
            "What is the 90 day historical price on this lane?",
            "How many days and hours is the transit time?",
            "What is the weather along route on this lane?",
            "Remove the route fuel cost from the truck cost.",
        ],
    },
    "HistoricalPrice": {
        "description": """get past truck costs between two cities from a number of days prior to the current date.
        User can specify custom time period up to two years into past. Convert to number of days when calling this function.
        """,
        "args_schema": HistoricalPriceInput,
        "function": get_historical_price_tool,
        "mock_function": mock_get_historical_price_tool,
        "emits_event": True,
        "frontend_loading_description": "Gathering truck prices",
        "followup_suggestions": [
            "What are the historical rates for the past year?",
            "How many days and hours is the transit time?",
            "Find me carriers that haul this lane with this type of equipment.",
        ],
    },
    "CurrentTime": {
        "description": """gets current time and converts it to local time zone""",
        "args_schema": CurrentTimeInput,
        "function": get_current_time_tool,
        "mock_function": mock_get_current_time_tool,
        "frontend_loading_description": "Obtaining current time",
    },
    "CurrentCityWeather": {
        "description": """current or forecast weather for city""",
        "args_schema": WeatherInput,
        "function": get_city_weather_tool,
        "mock_function": mock_get_city_weather_tool,
        "frontend_loading_description": "Getting city weather",
    },
    "WeatherAlongRoute": {
        "description": """step-by-step weather along route, answer with a step-by-step breakdown of weather along a route.""",
        "args_schema": LocationsInput,
        "function": get_weather_along_route_tool,
        "async_function": async_get_weather_along_route_tool,
        "mock_function": mock_get_weather_along_route_tool,
        "emits_event": True,
        "frontend_loading_description": "Getting weather along route",
        "followup_suggestions": [
            "How much is the truck cost?",
            "How long is the transit time?",
            "Search for carriers that haul this lane.",
        ],
    },
    "CarrierInfo": {
        "description": """basic carrier information, based on DOT or ICC number (MC/MX/FF).""",
        "args_schema": CarrierInfoInput,
        "function": get_carrier_info_tool,
        "mock_function": mock_get_carrier_info_tool,
        "frontend_loading_description": "Obtaining carrier information",
        "followup_suggestions": [
            "What is their inspection history?",
            "What is their crash history?",
            "What equipment do they have?",
            "How many trucks do they have?",
            "What is their safety rating?",
        ],
    },
    "CrashInfo": {
        "description": """carrier's crash history in the past 2 years, based on DOT or ICC number (MC/MX/FF).""",
        "args_schema": CarrierInfoInput,
        "function": get_crash_info_tool,
        "mock_function": mock_get_crash_info_tool,
        "frontend_loading_description": "Obtaining crash information",
        "emits_event": True,
        "followup_suggestions": [
            "What is the inspection history?",
            "Tell me more about the carrier.",
        ],
    },
    "InspectionInfo": {
        "description": """carrier's inspection history in the past 2 years, based on DOT or ICC number (MC/MX/FF).""",
        "args_schema": CarrierInfoInput,
        "function": get_inspection_info_tool,
        "mock_function": mock_get_inspection_info_tool,
        "frontend_loading_description": "Obtaining inspection information",
        "emits_event": True,
        "followup_suggestions": [
            "What is the crash history?",
            "Tell me more about the carrier.",
        ],
    },
}


def time_tools():
    from machine_learning.freightgpt import langchain_tools
    import time

    a = time.perf_counter()
    result = langchain_tools.get_distance_between_locations_tool("New York", "Trenton")
    print(f"get_distance_between_locations_tool {time.perf_counter() - a:.2f}")
    print(f"First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_diesel_price_tool("NY")
    print(f"get_diesel_price_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_fuel_cost_tool("NY", 100, mpg=DEFAULT_TRUCK_MPG)
    print(f"get_fuel_cost_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_todays_truck_cost_tool(
        "New York", "NY", "Trenton", "NJ", equipment_type="Dry Van"
    )
    print(f"get_todays_truck_cost_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_roundtrip_cost_tool(
        "New York", "NY", "Trenton", "NJ", equipment_type="Dry Van"
    )
    print(f"get_roundtrip_cost_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_city_weather_tool("NYC", "NY")
    print(f"get_city_weather_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = langchain_tools.get_weather_along_route_tool("New York", "Miami")
    print(f"get_weather_along_route_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")


async def time_async_tools():
    from machine_learning.freightgpt import langchain_tools
    import time

    a = time.perf_counter()
    result = await langchain_tools.async_get_roundtrip_cost_tool(
        "New York", "NY", "Trenton", "NJ", equipment_type="Dry Van"
    )
    print(f"async_get_roundtrip_cost_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")

    a = time.perf_counter()
    result = await langchain_tools.async_get_weather_along_route_tool(
        "New York", "Miami"
    )
    print(f"async_get_weather_along_route_tool {time.perf_counter() - a:.2f}")
    print(f"    First 50 chars of return value: {str(result)[:50]}")


if __name__ == "__main__":
    time_tools()
    asyncio.run(time_async_tools())
