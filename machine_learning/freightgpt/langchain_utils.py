import hashlib
import time

import numpy as np
import pandas as pd
import pytz
import timezonefinder

from common import route_segmentation_utils
from common.google_maps_utils import GoogleMapsUtils

start_time = time.perf_counter()
import asyncio
import datetime
import logging
from typing import Any, Callable, Dict, Iterable, List, Optional, Union
from langchain.tools.base import ToolException
from langchain.callbacks.manager import AsyncCallbackManager

print(
    f"┌───Time to load polyline and constants: {time.perf_counter() - start_time} seconds."
)
from machine_learning.freightgpt.constants import (
    DEFAULT_TRUCK_MPG,
    PRICER_ORIGIN_CLOSE_HOUR_UTC,
    ActiveType,
)
from machine_learning.freightgpt.constants import VisualizationType

start_time = time.perf_counter()
from machine_learning.price_recommendation import price_predictor

print(f"├───Time to load price_predictor: {time.perf_counter() - start_time} seconds.")

start_time = time.perf_counter()
TZ_FINDER = timezonefinder.TimezoneFinder()
print(f"├───Time to load tzfinder: {time.perf_counter() - start_time} seconds")


def stable_random(
    hash_str: str, min_val: float, max_val: float, precision: int = 0
) -> Union[float, int]:
    """Generate a stable random float within a range, based on a hash string.

    The function ensures that the same hash string will always generate the same random value.
    This is useful for generating random values in a deterministic way. The random value is
    between min_val (inclusive) and max_val (inclusive).

    Examples:
    >>> stable_random("world", 0, 10)
        7
    >>> stable_random("hello", 0, 10)
        3
    >>> stable_random("hello", 0, 10, 3)
        3.142

    Args:
        hash_str (str): The string to hash to generate the random value.
        min_val (float): The minimum value of the random float.
        max_val (float): The maximum value of the random float.
        precision (int): The number of decimal places to round the float to (mantissa length).

    Returns:
        float: The random float between min_val and max_val, inclusive.
    """
    md5_hash = int(
        hashlib.md5(hash_str.encode()).hexdigest(), 16
    )  # Convert hexadecimal hash to integer
    random_val = (md5_hash % 1000) / 1000.0
    # Due to rounding, max_val can be reached.
    rounded_val = round(min_val + (max_val - min_val) * random_val, precision)
    return int(rounded_val) if precision == 0 else rounded_val


def stable_random_choice(hash_str: str, options: Iterable[Any]):
    """Select a stable choice from options based on hash_str."""
    index = stable_random(hash_str, 0, len(options) - 1)
    return list(options)[index]


def get_valid_callback_handler(callback_manager: AsyncCallbackManager):
    """Verifies if a callback manager passed to a tool is valid, and if so returns the first handler."""
    # https://api.python.langchain.com/en/latest/callbacks/langchain.callbacks.manager.AsyncCallbackManager.html#langchain.callbacks.manager.AsyncCallbackManager

    callback_handler = None
    try:
        # Type check callback manager passed to tool.
        if callback_manager is None or not isinstance(
            callback_manager, AsyncCallbackManager
        ):
            raise Exception(
                "Improper callback manager passed to the tool. Expected an instance of AsyncCallbackManager."
            )
        # If valid callback, we can grab the handler.
        callback_handler = callback_manager.handlers[0]
    except Exception as e:
        logging.error(f"Cannot use callback: {e}. Cannot emit events from this tool.")

    return callback_handler


def extract_emission_function(
    callback_manager: AsyncCallbackManager, emission_function_name: str
) -> Callable:
    """Verifies if the callback manager passed in to a tool is valid, and if so returns the emission function."""
    emission_function = None
    try:
        # If valid callback, we can grab the emit event function.
        callback_handler = get_valid_callback_handler(callback_manager)
        emission_function = getattr(callback_handler, emission_function_name)
    except Exception as e:
        logging.error(
            f"Cannot extract emission function: {e}. Cannot emit events from this tool."
        )

    return emission_function


def extract_tool_store(callback_manager: AsyncCallbackManager) -> Callable:
    """Verifies if the callback manager passed in to a tool is valid, and if so returns tool store."""
    tool_store = None
    try:
        # If valid callback, we can grab the emit event function.
        callback_handler = get_valid_callback_handler(callback_manager)
        tool_store = getattr(callback_handler, "tool_store")
    except Exception as e:
        logging.error(
            f"Cannot extract tool store: {e}. Cannot use persistent storage with this tool."
        )

    return tool_store


def emit_event(
    emission_function: Callable,
    event: Dict[str, Any],
    viz_type: str,  # VisualizationType named tuple
) -> None:
    """Checks if the emission function is valid and emits the event.

    Args:
        emission_function: A function that emits the polyline event.
        event: The event dictionary emitted by the tool.
        viz_type: The type of visualization for the polyline.
    """
    if emission_function and callable(emission_function):
        try:
            emission_function(event, event_type=viz_type)
        except Exception as e:
            logging.error(f"Cannot emit event: {e}.")
    else:
        logging.warn("Cannot emit event. Emission function is not valid.")


def generate_polyline_event(
    active_type: str,
    origin: str,
    destination: str,
    gmaps_utils: GoogleMapsUtils,
    pre_computed_route: Dict[str, Union[Dict, str]] = None,
) -> Dict[str, Any]:
    """Generates and emits a polyline event.

    Args:
        active_type: The active type for the polyline.
        origin: The origin point of the polyline.
        destination: The destination point of the polyline.
        pre_computed_route: A pre-computed route with polylines. Defaults to None.
            - Must have keys "encoded_polyline", "bounds", and "total_distance".

    Raises:
        ValueError: route_segmentation_utils.get_route failed to get a route.

    """
    route = (
        pre_computed_route
        if pre_computed_route
        else route_segmentation_utils.get_route(
            origin, destination, gmaps_utils=gmaps_utils
        )
    )

    return {
        "active_type": active_type,
        "polyline": route["encoded_polyline"],
        "bounds": route["bounds"],
        "origin_city": origin,
        "destination_city": destination,
        "total_distance": route["total_distance"],
    }


def asyncify(func: Callable) -> Callable:
    """Converts a function to an async function."""

    async def wrapped_async(*args, **kwargs):
        """Wraps a synchronous function `func` into an asynchronous function that can be executed in a separate thread using `asyncio.to_thread`.

        Args:
            func: The synchronous function to be wrapped.

        Returns:
            An asynchronous function that can be executed in a separate thread.

        Example:
            async def wrapped_async(*args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
        """
        return await asyncio.to_thread(func, *args, **kwargs)

    return wrapped_async


def combine_multiple_truck_costs(trips: List[Dict[str, float]]) -> Dict[str, float]:
    """
    Combines the truck costs of multiple trips into a roundtrip cost.

    Args:
    - trips (List[Dict[str, float]]): A list of dictionaries, each containing:
        - "truck_cost": the cost of the trip.
        - "truck_cost_lower": the lower bound of the cost of the trip.
        - "truck_cost_upper": the upper bound of the cost of the trip.
        - "distance": the distance of the trip.
        - "fuel_cost": fuel cost of trip.

    Returns:
    - Dict[str, float]: A dictionary containing:
        - "roundtrip_cost_with_fuel": the cost of the roundtrip.
        - "roundtrip_truck_cost_lower": the lower bound of the cost of the roundtrip.
        - "roundtrip_truck_cost_upper": the upper bound of the cost of the roundtrip.
        - "roundtrip_distance": the distance of the roundtrip.
        - "roundtrip_fuel_cost": fuel cost of the roundtrip.
    """

    if not trips:
        raise ValueError("The trips list cannot be empty.")

    return_dict = {}
    return_dict["roundtrip_cost_with_fuel"] = round(
        sum(trip["truck_cost_with_fuel"] for trip in trips), 2
    )
    # return_dict["roundtrip_truck_cost_lower"] = round(sum(
    #     trip["truck_cost_lower"] for trip in trips
    # ), 2)
    # return_dict["roundtrip_truck_cost_upper"] = round(sum(
    #     trip["truck_cost_upper"] for trip in trips
    # ), 2)
    return_dict["roundtrip_distance"] = round(
        sum(trip["distance"] for trip in trips), 2
    )
    # return_dict["roundtrip_fuel_cost"] = round(
    #     sum(trip["fuel_cost"] for trip in trips), 2
    # )
    return return_dict


def estimate_fuel_cost(
    origin_state: str,
    distance_miles: float,
    pricer: price_predictor.PricePredictor,
    roundtrip: bool = False,
    mpg: float = DEFAULT_TRUCK_MPG,
) -> float:
    """
    Returns total fuel cost for a given shipment by multiplying average diesel price by number of gallons needed.

    Args:
    - origin_state: The state where the shipment originates.
    - distance_miles: The distance of the shipment in miles.
    - roundtrip: Whether the shipment is a roundtrip. Defaults to False.
    - mpg: The average miles per gallon of the truck. Defaults to DEFAULT_TRUCK_MPG.

    Returns:
    - float: The total fuel cost for the shipment, given the distance.
    """
    origin_diesel_price = pricer.diesel_price.get_diesel_price(
        datetime.datetime.now(), origin_state
    )
    if pd.isna(origin_diesel_price):
        logging.error(
            f"Couldn't find diesel price for {origin_state} on {datetime.datetime.now()}."
        )
        raise ToolException("Could not find route fuel cost. Please try again later.")
    gallons_needed = distance_miles / mpg
    total_fuel_cost = origin_diesel_price * gallons_needed
    return round(total_fuel_cost + total_fuel_cost * roundtrip, 2)


def price_truck(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    pricer: price_predictor.PricePredictor,
    gmaps_utils: GoogleMapsUtils,
    equipment_type: str = "Dry Van",
    emission_function: Callable = None,
) -> Dict[str, float]:
    """
    Uses price_predictor to predict the truck cost from origin to destination (one leg).
    Can emit map event.

    Returns:
    - Dict[str, float]: A dictionary containing:
        - "truck_cost_with_fuel": the cost of the trip from A to B.
        - "truck_cost_lower": the lower bound of the cost of the trip from A to B.
        - "truck_cost_upper": the upper bound of the cost of the trip from A to B.
        - "distance": the distance of the trip from A to B.
        - "origin_diesel_cost": the diesel cost at A.
        - "destination_diesel_cost": the diesel cost at B.

    Raises:
        - Exception: If the bounds are too large, then we don't trust the prediction.
    """
    origin_country = destination_country = "US"
    # origin_zip = pricer.gmaps_utils.get_zip_from_city_state(
    #     origin_city, origin_state, origin_country
    # )
    # destination_zip = pricer.gmaps_utils.get_zip_from_city_state(
    #     destination_city, destination_state, destination_country
    # )

    # Today's date in UTC time zone, with a time of PRICER_ORIGIN_CLOSE_HOUR_UTC:00:00, but tz-naive
    origin_close_time = (
        pd.Timestamp.now(tz="UTC")
        .replace(hour=PRICER_ORIGIN_CLOSE_HOUR_UTC, minute=0, second=0, microsecond=0)
        .tz_localize(None)
    )
    prices = pricer.predict(
        price_predictor.PricePredictorInput(
            origin_city=origin_city,
            origin_state=origin_state,
            origin_country=origin_country,
            # origin_zip=origin_zip,
            destination_city=destination_city,
            destination_state=destination_state,
            destination_country=destination_country,
            # destination_zip=destination_zip,
            origin_close_time=origin_close_time,
            equipment_type=equipment_type,
            data_source="rds_pricing_full",
        )
    )
    # Rename the keys
    if pd.isna(pricer.augmented_input.distance_miles):
        raise ToolException(
            f"Could not find a route between {origin_city}, {origin_state} and {destination_city}, {destination_state}."
        )
    prices["truck_cost_with_fuel"] = round(prices.pop("value"), 2)
    prices["truck_cost_lower"] = round(prices.pop("lower_bound"), 2)
    prices["truck_cost_upper"] = round(prices.pop("upper_bound"), 2)
    prices["distance"] = round(pricer.augmented_input.distance_miles)
    fuel_cost = estimate_fuel_cost(
        origin_state=origin_state,
        distance_miles=prices["distance"],
        pricer=pricer,
        roundtrip=False,
    )
    # prices["origin_diesel_cost"] = pricer.augmented_input.origin_diesel_price
    # prices["destination_diesel_cost"] = pricer.augmented_input.destination_diesel_price

    # bound_size = prices["truck_cost_upper"] - prices["truck_cost_lower"]
    # bound_ratio = bound_size / prices["truck_cost"] if prices["truck_cost"] else pd.NA

    # if bound_size > 3500 or bound_ratio > 1.2:
    #     # If the bounds are too large, then we don't trust the prediction
    #     raise Exception("Unable to provide reasonable price estimate.")

    event = generate_polyline_event(
        ActiveType.ACTIVE,
        origin=f"{origin_city}, {origin_state}",
        destination=f"{destination_city}, {destination_state}",
        gmaps_utils=gmaps_utils,
    )

    event["all_in_rate"] = prices["truck_cost_with_fuel"]
    event["equipment_type"] = equipment_type
    event["fuel_cost"] = fuel_cost

    batch_price_historical_data = batch_price_historical(
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        equipment_type=equipment_type,
        pricer=pricer,
        gmaps_utils=gmaps_utils,
        number_of_days=30,
    )
    event["batch_price_historical_data"] = batch_price_historical_data

    emit_event(
        emission_function=emission_function,
        event=event,
        viz_type=VisualizationType.PRICER_POLYLINE,
    )

    return prices


def parse_time_or_datetime(
    time_str: str,
) -> Union[datetime.datetime, datetime.time, None]:
    """
    Parses a string representing a time or a datetime.

    If the string is not a valid time or datetime, raises a ToolException, ensuring that the time is in ISO format.

    Args:
    - time_str: A string representing a time or a datetime.

    Returns:
    - Union[datetime.datetime, datetime.time]: A datetime or time object.
    - None: If the time_str is falsy.
    """
    if time_str:
        try:
            return datetime.time.fromisoformat(time_str)
        except ValueError:
            try:
                return pd.Timestamp(time_str)
            except ValueError:
                raise ToolException(
                    f"Invalid time format: {time_str}, must be ISO format YYYY-MM-DDTHH:MM:SS±HH:MM or HH:MM:SS±HH:MM"
                )
    return None


def get_unix_timestamp(dt_obj: Union[datetime.datetime, int, float, None]) -> int:
    """
    Convert the given UTC dt to a UNIX timestamp in seconds.

    Args:
    - forecast_time (Union[datetime.datetime, int]): The time for which the forecast is required.
      If it's a datetime object, it will be converted to a UNIX timestamp.
      If it's an int, it's assumed to be already in UNIX timestamp format.
      If None, the current time is used.

    Returns:
    - int: UNIX timestamp representing the forecast_time
    """
    if dt_obj is None:
        return int(datetime.datetime.now().timestamp())

    if isinstance(dt_obj, datetime.datetime):
        return int(dt_obj.timestamp())
    if isinstance(dt_obj, int):
        return dt_obj
    if isinstance(dt_obj, float):
        return int(dt_obj)
    raise ValueError("dt_obj must be a datetime.datetime or int.")


def get_tz_from_lat_lng(lat: float, lng: float) -> str:
    """
    Gets the timezone from latitude and longitude.

    Args:
    - lat (float): The latitude.
    - lng (float): The longitude.

    Returns:
    - str: The timezone name.
    """
    try:
        return TZ_FINDER.timezone_at(lat=lat, lng=lng)
    except Exception as e:
        logging.warning(
            f"Error getting timezone from lat {lat} and lng {lng}: {str(e)}"
        )
        return ""


def convert_to_local_time(
    dt: datetime.datetime,
    latitude: float,
    longitude: float,
    fmtstr: str = "%m/%d %H:%M",
) -> Optional[str]:
    """
    Converts a UTC time to local time based on latitude and longitude and formats it.

    Args:
    - dt (datetime): The datetime in UTC.
    - latitude (float): The latitude for the timezone calculation.
    - longitude (float): The longitude for the timezone calculation.
    - include_date (bool): Whether to include the date in the formatted time. Defaults to False.

    Returns:
    - str: The forecast time converted to local time and formatted.
    """
    # Ensure forecast_time is aware of its timezone
    dt = dt.replace(tzinfo=pytz.utc)

    # Determine the local timezone from latitude and longitude
    local_timezone_str = get_tz_from_lat_lng(latitude, longitude)

    # If timezone is found, convert time to that timezone
    if not local_timezone_str:
        local_timezone_str = "UTC"

    local_time = dt.astimezone(pytz.timezone(local_timezone_str))

    # Use tzname() to get the timezone name (e.g., CST or CDT)
    timezone_name = local_time.tzname()

    # Formatting the time with the correct timezone name
    return local_time.strftime(fmtstr) + " " + timezone_name


def format_timedelta(
    td_object: datetime.timedelta, granularity: str = "hour", round_up: bool = False
) -> str:
    """Converts a timedelta object into a human-readable string.

    Example usage:
    >>> td = datetime.timedelta(days=400)
    >>> format_timedelta(td)
    '1 year, 1 month, 5 days'

    >>> td = datetime.timedelta(hours=49, minutes=30)
    >>> format_timedelta(td)
    '2 days, 1 hour'

    >>> td = datetime.timedelta(hours=1, minutes=30)
    >>> format_timedelta(td, granularity="minute")
    '1 hour, 30 minutes'

    Args:
    - td_object: A timedelta object.
    - granularity: A string representing the granularity of the output.
    Returns:
    - A human-readable string representing the timedelta such as '1 year, 3 months, 2 days'.
    """
    if not isinstance(granularity, str):
        raise ValueError(
            f"Invalid type: {type(granularity)}, for granularity: {granularity}. Must be a string."
        )

    if td_object.total_seconds() < 0:
        raise ValueError("The timedelta object cannot be negative.")

    # Define the time units in seconds
    time_units_in_seconds = {
        "year": 60 * 60 * 24 * 365,
        "month": 60 * 60 * 24 * 30,
        "day": 60 * 60 * 24,
        "hour": 60 * 60,
        "minute": 60,
        "second": 1,
    }

    # Ensure the granularity is one of our time units
    if granularity not in time_units_in_seconds:
        raise ValueError(f"Invalid granularity: {granularity}")

    # Get the total seconds of the timedelta object (e.g. 119880 seconds)
    total_seconds = td_object.total_seconds()
    # Calculate the number of units of the given granularity (e.g. 33.3 hours)
    num_units = total_seconds / time_units_in_seconds[granularity]
    # Round up if specified
    if round_up:
        num_units += 0.5
    # Round the units to the nearest integer (e.g. 33 hours)
    rounded_units = round(num_units)
    # Calculate the total seconds after rounding (e.g. 118800 seconds)
    seconds = rounded_units * time_units_in_seconds[granularity]

    # Build the human-readable string (e.g. ["1 day", "9 hours"])
    result = []
    for name, count in time_units_in_seconds.items():
        value = seconds // count
        if value:
            seconds -= value * count
            if value == 0:
                continue
            if value > 1:
                name += "s"
            result.append(f"{value} {name}")

    # Join the human-readable strings with a comma and space (e.g. "1 day, 9 hours")
    return ", ".join(result)


def batch_price_historical(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    pricer: price_predictor.PricePredictor,
    gmaps_utils: GoogleMapsUtils,
    equipment_type: str = "Dry Van",
    roundtrip: bool = False,
    number_of_days: int = 30,
    emission_function: Callable = None,
) -> Dict[str, Union[float, str]]:
    """
    Uses price_predictor to predict the truck cost from origin to destination (one leg).
    Can emit map event.

    Args:
    - origin_city: origin city
    - origin_state: origin state
    - destination_city: destination city
    - destination_state: destination state
    - pricer: price predictor object
    - equipment_type: equipment type
    - roundtrip: whether the price is for a roundtrip
    - number_of_days: number of days to retrieve historical price for
    - emission_function: event emission function

    Returns:
    - Dict[str, float]: A dictionary containing:
        - "average": average price,
        - "high": highest price in period,
        - "low": lowest price in period,
        - "period": duration of period in number of days,
        - "time_series": list of data points as (time, price),
    """
    if roundtrip:
        a_to_b, b_to_a = batch_price_historical(
            origin_city=origin_city,
            origin_state=origin_state,
            destination_city=destination_city,
            destination_state=destination_state,
            equipment_type=equipment_type,
            number_of_days=number_of_days,
            gmaps_utils=gmaps_utils,
            pricer=pricer,
        ), batch_price_historical(
            origin_city=destination_city,
            origin_state=destination_state,
            destination_city=origin_city,
            destination_state=origin_state,
            equipment_type=equipment_type,
            number_of_days=number_of_days,
            gmaps_utils=gmaps_utils,
            pricer=pricer,
        )

        series = []
        prices = []
        for leg1, leg2 in zip(a_to_b["time_series"], b_to_a["time_series"]):
            # Discard combined time as they should be the same
            _, price = np.add(leg1, leg2)
            timestamp = leg1[0]
            prices.append(price)
            series.append([timestamp, price])

        emit_event(
            emission_function,
            {
                "origin_city": origin_city,
                "destination_city": destination_city,
                "roundtrip": roundtrip,
                "time_series": series,
            },
            VisualizationType.GRAPH_SERIES,
        )

        return {
            "average": np.mean(prices),
            "high": max(prices),
            "low": min(prices),
            "period": number_of_days,
        }

    # Precompute miles to avoid multiple calls to Google Maps API
    distance_miles = gmaps_utils.get_distance_miles(
        f"{origin_city}, {origin_state}",
        f"{destination_city}, {destination_state}",
        use_cache=False,
    )
    batch = []
    origin_close_times = []
    for i in reversed(range(1, number_of_days + 1)):
        origin_close_time = pd.Timestamp.today() - pd.Timedelta(days=i)
        origin_close_times.append(origin_close_time.timestamp())
        batch.append(
            {
                "origin_city": origin_city,
                "origin_state": origin_state,
                "origin_country": "US",
                "destination_city": destination_city,
                "destination_state": destination_state,
                "destination_country": "US",
                # "data_source": "",
                "equipment_type": equipment_type,
                "origin_close_time": origin_close_time,
                "distance_miles": distance_miles,
            }
        )

    prices = pricer.batch_predict(batch)

    price_vals = [price["value"] for price in prices if not pd.isna(price["value"])]
    if len(price_vals) == 0:
        raise ToolException("Could not retrieve any valid historical batch prices")

    downsampled_time_prices = []
    mean_convolved_times = mean_convolve_1d(origin_close_times)
    mean_convolved_prices = mean_convolve_1d(price_vals)

    for timestamp, price in zip(mean_convolved_times, mean_convolved_prices):
        downsampled_time_prices.append([timestamp, price])

    emit_event(
        emission_function,
        {
            "origin_city": origin_city,
            "destination_city": destination_city,
            "roundtrip": roundtrip,
            "time_series": downsampled_time_prices,
        },
        VisualizationType.GRAPH_SERIES,
    )

    return {
        "average": np.mean(price_vals),
        "high": max(price_vals),
        "low": min(price_vals),
        "period": number_of_days,
        "time_series": downsampled_time_prices,
    }


def mean_convolve_1d(
    series: List[float],
    desired_length: int = 30,
):
    """
    Splits a list into desired_length number of evenly sized subarrays.
    Returns a list containing the averages of each subarray.
    Args:
        series: a list of floats without nans
        target_count: target number of resulting values
    Returns:
    - List[float] an array of mean values of length desired_length
    """
    if len(series) <= desired_length:
        return series
    split_vals = np.array_split(series, desired_length)
    averages = [np.mean(group) for group in split_vals]
    return averages
