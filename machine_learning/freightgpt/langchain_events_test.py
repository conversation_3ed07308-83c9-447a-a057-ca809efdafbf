import unittest

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt import langchain_events


class TestLangchainMessageType(unittest.TestCase):
    # Test various messages and types against the boolean result, including a type mismatch and if the key "type" is not present.
    @parameterized.expand(
        [
            ({"type": "ai"}, "ai", True),
            ({"type": "human"}, "hooman", False),
            ({"roop": "pal"}, "pal", False),
        ]
    )
    def test_is_langchain_message_of_type(self, message, message_type, result):
        self.assertEqual(
            langchain_events.is_langchain_message_of_type(message, message_type), result
        )


class TestSetLastEventActive(unittest.TestCase):
    # Same event type (A)
    @parameterized.expand(
        [
            (
                [
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                ],
                "type",
                "A",
                False,
                [
                    {"event": {"type": "A", "data": {"active_type": "inactive"}}},
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                ],
            ),
        ]
    )
    def test_same_event_type_only(
        self, events, event_key, event_type, retain_run_active, result_events_list
    ):
        self.assertEqual(
            langchain_events.set_last_event_active(
                events, event_key, event_type, retain_run_active=retain_run_active
            ),
            result_events_list,
        )

    # Multiple event types (A, B, C)
    @parameterized.expand(
        [
            (
                [
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                    {"event": {"type": "C", "data": {"active_type": "active"}}},
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                    {"event": {"type": "B", "data": {"active_type": "active"}}},
                ],
                "type",
                "A",
                False,
                [
                    {"event": {"type": "A", "data": {"active_type": "inactive"}}},
                    {"event": {"type": "A", "data": {"active_type": "inactive"}}},
                    {"event": {"type": "C", "data": {"active_type": "active"}}},
                    {"event": {"type": "A", "data": {"active_type": "active"}}},
                    {"event": {"type": "B", "data": {"active_type": "active"}}},
                ],
            ),
        ]
    )
    def test_multiple_event_types(
        self, events, event_key, event_type, retain_run_active, result_events_list
    ):
        self.assertEqual(
            langchain_events.set_last_event_active(
                events, event_key, event_type, retain_run_active=retain_run_active
            ),
            result_events_list,
        )

    # Multiple event types, and uses the retain_run_active flag.
    # Messages that contain the same message_id in succession should retain their active status.
    @parameterized.expand(
        [
            (
                [
                    {
                        "event": {
                            "type": "A",
                            "data": {"active_type": "active"},
                        },
                        "message_id": 5678,
                    },
                    {
                        "event": {
                            "type": "A",
                            "data": {"active_type": "active"},
                        },
                        "message_id": 1234,
                    },
                    {"event": {"type": "C", "data": {"active_type": "active"}}},
                    {
                        "event": {"type": "A", "data": {"active_type": "active"}},
                        "message_id": 1234,
                    },
                    {"event": {"type": "B", "data": {"active_type": "active"}}},
                ],
                "type",
                "A",
                True,
                [
                    {
                        "event": {
                            "type": "A",
                            "data": {"active_type": "inactive"},
                        },
                        "message_id": 5678,
                    },
                    {
                        "event": {
                            "type": "A",
                            "data": {"active_type": "active"},
                        },
                        "message_id": 1234,
                    },
                    {"event": {"type": "C", "data": {"active_type": "active"}}},
                    {
                        "event": {"type": "A", "data": {"active_type": "active"}},
                        "message_id": 1234,
                    },
                    {"event": {"type": "B", "data": {"active_type": "active"}}},
                ],
            ),
        ]
    )
    def test_retain_last_run(
        self, events, event_key, event_type, retain_run_active, result_events_list
    ):
        self.assertEqual(
            langchain_events.set_last_event_active(
                events, event_key, event_type, retain_run_active=retain_run_active
            ),
            result_events_list,
        )


class TestMergeFilterChatHistoryEvents(unittest.TestCase):
    # No events at all.
    @parameterized.expand(
        [
            (
                [{"data": {"id": 1234, "content": "hello"}, "type": "ai"}],
                [],
                [{"data": {"id": 1234, "content": "hello"}, "type": "ai"}],
            )
        ]
    )
    def test_no_events(self, chat_history, events, clean_combined_history):
        self.assertEqual(
            langchain_events.merge_and_filter_chat_history_with_events(
                chat_history, events
            ),
            clean_combined_history,
        )

    # Single chat message and single associated event.
    @parameterized.expand(
        [
            (
                [{"data": {"id": 1234, "content": "hello"}, "type": "ai"}],
                [{"event": {"type": "windy"}, "message_id": 1234}],
                [
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                    {"event": {"type": "windy"}, "message_id": 1234},
                ],
            )
        ]
    )
    def test_single_message_single_event(
        self, chat_history, events_history, clean_combined_history
    ):
        self.assertEqual(
            langchain_events.merge_and_filter_chat_history_with_events(
                chat_history, events_history
            ),
            clean_combined_history,
        )

    # Multiple chat messages and multiple events associated with each.
    # Two runs: 1234 and 5678, each of which has an associated set of events
    # Presence of a system message for run 1234, which should be skipped.
    @parameterized.expand(
        [
            (
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"data": {"id": 5678, "content": "hello"}, "type": "ai"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "system"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                ],
                [
                    {"event": {"type": "windy"}, "message_id": 5678},
                    {"event": {"type": "windy"}, "message_id": 1234},
                    {"event": {"type": "windy"}, "message_id": 1234},
                    {"event": {"type": "gmaps"}, "message_id": 1234},
                ],
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"data": {"id": 5678, "content": "hello"}, "type": "ai"},
                    {"event": {"type": "windy"}, "message_id": 5678},
                    {"data": {"id": 1234, "content": "hello"}, "type": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                    {"event": {"type": "windy"}, "message_id": 1234},
                    {"event": {"type": "windy"}, "message_id": 1234},
                    {"event": {"type": "gmaps"}, "message_id": 1234},
                ],
            )
        ]
    )
    def test_multiple_messages_multiple_events(
        self, chat_history, events_history, clean_combined_history
    ):
        self.assertEqual(
            langchain_events.merge_and_filter_chat_history_with_events(
                chat_history, events_history
            ),
            clean_combined_history,
        )

    # Test a mismatch in order between IDs in the events list and IDs in the chat history - particularly if the chat history becomes jumbled.
    # Note: This should NEVER happen. We operate under the assumption that consecutive messages have the same run id
    # and things are always stored chronologically.
    # A break in chronological order in either array will cause issues system-wide.
    # TODO (P1): handle case when order of messages or events goes out of sync?
    # In this case, only the last event should be assigned.
    @parameterized.expand(
        [
            (
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "system"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "human"},
                    {"data": {"id": 5678, "content": "hello"}, "type": "ai"},
                ],
                [
                    {"event": {"type": "gmaps"}, "message_id": 5678},
                    {"event": {"type": "windy"}, "message_id": 1234},
                ],
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "human"},
                    {"data": {"id": 5678, "content": "hello"}, "type": "ai"},
                    {"event": {"type": "gmaps"}, "message_id": 5678},
                ],
            )
        ]
    )
    def test_chat_event_id_order_mismatch(
        self, chat_history, events_history, clean_combined_history
    ):
        self.assertEqual(
            langchain_events.merge_and_filter_chat_history_with_events(
                chat_history, events_history
            ),
            clean_combined_history,
        )

    # Test bad keys for robustness, while retaining chronological order.
    # Note: this should also really not happen and should be protected against in other parts of the system.
    # The presence of an event for run 5678 will cause no events to be rendered, hence we start with run 1234.
    # TODO (P1): Ensure system is robust against malformed keys.
    # In this case, only the event with correct keys should be appended.
    @parameterized.expand(
        [
            (
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"roop": {"id": 5678, "content": "hello"}, "type": "ai"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "system"},
                    {"data": {"id": 1234, "content": "hello"}, "pal": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                ],
                [
                    {"roop": {"type": "gmaps"}, "message_id": 1234},
                    {"event": {"type": "gmaps"}, "message_id": 1234},
                    {"event": {"type": "windy"}, "pal": 1234},
                ],
                [
                    {"data": {"id": 5678, "content": "hello"}, "type": "human"},
                    {"roop": {"id": 5678, "content": "hello"}, "type": "ai"},
                    {"data": {"id": 1234, "content": "hello"}, "pal": "human"},
                    {"data": {"id": 1234, "content": "hello"}, "type": "ai"},
                    {"event": {"type": "gmaps"}, "message_id": 1234},
                ],
            )
        ]
    )
    def test_bad_keys(self, chat_history, events_history, clean_combined_history):
        self.assertEqual(
            langchain_events.merge_and_filter_chat_history_with_events(
                chat_history, events_history
            ),
            clean_combined_history,
        )


# Run the tests
if __name__ == "__main__":
    unittest.main()
