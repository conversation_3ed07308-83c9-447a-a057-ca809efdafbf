{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Duplicate call to initialize(). Previously initialized in /Users/<USER>/anaconda3/envs/truce/lib/python3.11/site-packages/common/constants.py at line 14.\n", "│   ┌───Time to load aiplatform: 0.5133240419672802 seconds.\n"]}], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "\n", "from common import google_maps_utils\n", "from machine_learning.price_recommendation.price_predictor import (\n", "    VertexAutoMLPricePredictor,\n", ")\n", "import os\n", "\n", "os.environ[\"ENV\"] = \"DEV\"\n", "\n", "PRICER_ENDPOINT = VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_FREIGHTGPT_DEV\n", "GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()\n", "PRICER = VertexAutoMLPricePredictor(PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain.tools.base import ToolException"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["required_columns = [\n", "    \"origin_city\",\n", "    \"origin_state\",\n", "    \"destination_city\",\n", "    \"destination_state\",\n", "    \"equipment_type\",\n", "]\n", "\n", "optional_columns = [\n", "    \"origin_close_date\",\n", "    \"distance_miles\",\n", "    \"origin_zip\",\n", "    \"destination_zip\",\n", "]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from common.constants import EQUIPMENT_TYPE_DICT, EquipmentType\n", "\n", "LOCAL_EQUIPMENT_TYPE_DICT = None\n", "\n", "def get_equipment_type_dict(df):\n", "\n", "    unique_equipment_types = df['equipment_type'].unique()\n", "\n", "    prompt_template = ChatPromptTemplate.from_template(\n", "        \"\"\"\n", "        You are a professional freight equipment handler.\n", "        Learn how to map equipment types using the dictionary {equipment_type_dict} and \n", "        general knowledge about freight equipments and their shorthands.\n", "        Then map the following set of equipment types: {raw_equipment_types}\n", "        to {equipment_types}.\n", "        Provide the mapping in the format 'raw_equipment_type -> equipment_type'.\n", "        If there is no suitable match, return 'raw_equipment_type -> None'.\n", "        Each mapping should be on a new line.\n", "        There should not be quotes or any other character surronding raw and mapped equipment type.\n", "        \"\"\"\n", "    )\n", "\n", "    llm = ChatOpenAI(model='gpt-4o', temperature=0)\n", "\n", "    llm_chain = prompt_template | llm | StrOutputParser()\n", "\n", "    mapping_text = llm_chain.invoke(\n", "        input = {\n", "            \"raw_equipment_types\": unique_equipment_types, \n", "            \"equipment_types\": EquipmentType, \n", "            \"equipment_type_dict\": EQUIPMENT_TYPE_DICT, \n", "        }\n", "    )\n", "\n", "    mapping = {}\n", "    \n", "    # Parse the mapping\n", "    for line in mapping_text.split('\\n'):\n", "        if '->' in line:\n", "            original, template = line.split('->')\n", "            original = original.strip()\n", "            template = template.strip()\n", "            mapping[original] = template if template.lower() != 'none' else None\n", "\n", "    return mapping\n", "\n", "def map_equipment_type(equipment_type: str) -> str:\n", "\n", "    if pd.isna(equipment_type) or not equipment_type:\n", "        return pd.NA\n", "    elif equipment_type in LOCAL_EQUIPMENT_TYPE_DICT:\n", "        return LOCAL_EQUIPMENT_TYPE_DICT[equipment_type]\n", "    else:\n", "        print(f\"Equipment type {equipment_type} not found in dictionary\")\n", "        return pd.NA"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from machine_learning.price_recommendation import preprocessing\n", "from machine_learning.freightgpt.constants import PRICER_ORIGIN_CLOSE_HOUR_UTC\n", "\n", "def preprocess_columns(df):\n", "    global LOCAL_EQUIPMENT_TYPE_DICT\n", "\n", "    LOCAL_EQUIPMENT_TYPE_DICT = get_equipment_type_dict(df)\n", "\n", "    df[\"origin_city\"] = df[\"origin_city\"].str.title()\n", "    df[\"destination_city\"] = df[\"destination_city\"].str.title()\n", "    df[\"origin_state\"] = df[\"origin_state\"].str.upper()\n", "    df[\"destination_state\"] = df[\"destination_state\"].str.upper()\n", "\n", "    df[\"origin_country\"] = df[\"origin_state\"].apply(preprocessing.derive_country_from_state)\n", "    df[\"destination_country\"] = df[\"destination_state\"].apply(preprocessing.derive_country_from_state)\n", "\n", "    df[\"equipment_type\"] = df[\"equipment_type\"].apply(map_equipment_type)\n", "\n", "    def fill_origin_close_date(row):\n", "        if \"origin_close_date\" not in row or pd.isna(row[\"origin_close_date\"]):\n", "            return pd.Timestamp.now(tz=\"UTC\").date().strftime(\"%Y-%m-%d\")\n", "\n", "        return preprocessing.build_timestamp(\n", "            row[\"origin_close_date\"], time_str=f\"{PRICER_ORIGIN_CLOSE_HOUR_UTC}:00:00\"\n", "        )\n", "\n", "\n", "    # Check if all of the column is NA\n", "    if \"origin_close_date\" not in df.columns or df[\"origin_close_date\"].isna().all():\n", "        print(\"Origin close date is missing, filling with current date.\")\n", "        df[\"origin_close_time\"] = (\n", "            pd.Timestamp.now(tz=\"UTC\")\n", "            .replace(hour=PRICER_ORIGIN_CLOSE_HOUR_UTC, minute=0, second=0, microsecond=0)\n", "            .tz_localize(None)\n", "        )\n", "    else:\n", "        df[\"origin_close_time\"] = df.apply(fill_origin_close_date, axis=1)\n", "\n", "\n", "def filter_valid_rows(df):\n", "\n", "    # Filter NAs, we want required columns and countries\n", "    df = df.dropna(subset=required_columns + [\"origin_country\", \"destination_country\"])\n", "\n", "    # Only price US to US\n", "    df = df[(df[\"origin_country\"] == \"US\") & (df[\"destination_country\"] == \"US\")]\n", "\n", "    # State should be a 2-digit code\n", "    df = df[df['origin_state'].apply(lambda x: isinstance(x, str) and len(x) == 2)]\n", "    df = df[df['destination_state'].apply(lambda x: isinstance(x, str) and len(x) == 2)]\n", "\n", "    # Distance miles should be non-negative\n", "    if 'distance_miles' in df.columns:\n", "        df = df[(df['distance_miles'].isna()) | (df['distance_miles'] >= 0)]\n", "\n", "    return df\n", "\n", "\n", "def get_data_dict(row):\n", "    d = {}\n", "    for col in required_columns:\n", "        d[col] = row[col]\n", "    for col in optional_columns + [\"origin_close_time\"]:\n", "        # Ignore zips for now\n", "        if \"zip\" in col or \"origin_close_date\" in col:\n", "            continue\n", "        if col in row:\n", "            d[col] = row[col]\n", "    return d"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_mapping(original_headers, sample_data, template_headers, issues=None):\n", "    \n", "    # Create the prompt for the LLM\n", "    prompt_template = ChatPromptTemplate.from_template(\n", "        \"\"\"\n", "        Given the following original column headers: {original_headers}\n", "        and a sample of the data: {sample_data}\n", "        Map them to the following template column headers: {template_headers}\n", "        Provide the mapping in the format 'original_column -> template_column'.\n", "        If there is no suitable match, return 'original_column -> None'.\n", "        The mapping must be one to one. Multiple columns should not be matched with a single template header.\n", "        Each mapping should be on a new line.\n", "        There should not be quotes or any other character surronding original and template column names.\n", "        If there are any issues with the mapping, they are: {issues}\n", "        Please provide an updated mapping considering these issues.\n", "        \"\"\"\n", "    )\n", "\n", "    llm = ChatOpenAI(model='gpt-4o', temperature=0.1)\n", "\n", "    llm_chain = prompt_template | llm | StrOutputParser()\n", "\n", "    mapping_text = llm_chain.invoke(\n", "        input = {\n", "            \"original_headers\": original_headers, \n", "            \"template_headers\": template_headers, \n", "            \"sample_data\": sample_data, \n", "            \"issues\": issues\n", "        }\n", "    )\n", "    \n", "    mapping = {}\n", "    \n", "    # Parse the mapping\n", "    for line in mapping_text.split('\\n'):\n", "        if '->' in line:\n", "            original, template = line.split('->')\n", "            original = original.strip()\n", "            template = template.strip()\n", "            if template.lower() != 'none':\n", "                mapping[original] = template\n", "\n", "    display(mapping)\n", "    \n", "    return mapping\n", "\n", "\n", "def validate_columns(df):\n", "\n", "    columns_set = set(df.columns)\n", "\n", "    # Required columns should be present and not be completely empty\n", "    for col in required_columns:\n", "        if col not in columns_set:\n", "            raise ValueError(f\"Required column {col} is not mapped!\")\n", "        if df[col].isna().all():\n", "            raise ValueError(f\"Required column {col} is completely empty!\")\n", "        \n", "    for col in ['origin', 'destination']:\n", "        if f\"{col}_country\" not in columns_set:\n", "            raise ValueError(f\"{col}_country column is not mapped implying an issue with {col}_state column!\")\n", "        if df[f\"{col}_country\"].isna().all():\n", "            raise ValueError(f\"{col}_country column is completely empty implying an issue with {col}_state column!\")\n", "        \n", "\n", "    # State should be a 2-digit code\n", "    if not df['origin_state'].apply(lambda x: isinstance(x, str) and len(x) == 2).any():\n", "        raise ValueError(\"Origin state column must contain 2-digit state codes!\")\n", "    if not df['destination_state'].apply(lambda x: isinstance(x, str) and len(x) == 2).any():\n", "        raise ValueError(\"Destination state column must contain 2-digit state codes!\")\n", "    \n", "    \n", "\n", "\n", "def convert_csv(input_csv, output_csv):\n", "    df = pd.read_csv(input_csv)\n", "    original_headers = df.columns.tolist()\n", "    sample_data = df.head(5).to_dict(orient='records')\n", "    \n", "    template_headers = required_columns + optional_columns\n", "\n", "    issue = None\n", "\n", "    attempt = 1\n", "    while (attempt==1 or issue) and attempt <= 3:\n", "        column_mapping = get_mapping(original_headers, sample_data, template_headers, issue)\n", "        new_df = df.rename(columns=column_mapping)\n", "        template_columns_matched = list(column_mapping.values())\n", "        new_df = new_df[template_columns_matched]\n", "        display(new_df.head())\n", "        try:\n", "            preprocess_columns(new_df)\n", "            validate_columns(new_df)\n", "            issue = None\n", "        except KeyE<PERSON><PERSON> as key:\n", "            issue = f\"Required column {key} is not mapped!\"\n", "            print(f\"Issue: {issue}\")\n", "            if attempt < 3:\n", "                print(\"Trying again...\\n\")\n", "        except ValueError as err:\n", "            issue = err\n", "            print(f\"Issue: {issue}\")\n", "            if attempt < 3:\n", "                print(\"Trying again...\\n\")\n", "        except Exception as err:\n", "            issue = err\n", "            print(f\"Issue: {issue}\")\n", "            if attempt < 3:\n", "                print(\"Trying again...\\n\")\n", "\n", "        attempt += 1\n", "\n", "    if issue:\n", "        raise ToolException(f\"Failed to match columns in the uploaded csv file: {issue}\")\n", "    \n", "    # Save the converted DataFrame to a new CSV file\n", "    new_df.to_csv(output_csv, index=False)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Type of Shipment': 'equipment_type',\n", " 'Pickup City': 'origin_city',\n", " 'Pickup State': 'origin_state',\n", " 'Pickup Zip': 'origin_zip',\n", " 'Consignee City': 'destination_city',\n", " 'Consignee State': 'destination_state',\n", " 'Consignee Zip': 'destination_zip',\n", " 'Miles Customer': 'distance_miles',\n", " 'Pick Appt Date': 'origin_close_date'}"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>equipment_type</th>\n", "      <th>origin_city</th>\n", "      <th>origin_state</th>\n", "      <th>origin_zip</th>\n", "      <th>destination_city</th>\n", "      <th>destination_state</th>\n", "      <th>destination_zip</th>\n", "      <th>distance_miles</th>\n", "      <th>origin_close_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DRAY</td>\n", "      <td>TACOMA</td>\n", "      <td>WA</td>\n", "      <td>98421</td>\n", "      <td>PORTLAND</td>\n", "      <td>OR</td>\n", "      <td>97211</td>\n", "      <td>143.0</td>\n", "      <td>01/08/24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DRAY</td>\n", "      <td>TACOMA</td>\n", "      <td>WA</td>\n", "      <td>98421</td>\n", "      <td>PORTLAND</td>\n", "      <td>OR</td>\n", "      <td>97211</td>\n", "      <td>143.0</td>\n", "      <td>01/09/24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>V</td>\n", "      <td>LEWISTON</td>\n", "      <td>ID</td>\n", "      <td>83501</td>\n", "      <td>VANCOUVER</td>\n", "      <td>WA</td>\n", "      <td>98661</td>\n", "      <td>352.0</td>\n", "      <td>01/02/24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DRAY</td>\n", "      <td>SEATTLE</td>\n", "      <td>WA</td>\n", "      <td>98134</td>\n", "      <td>WOODLAND</td>\n", "      <td>WA</td>\n", "      <td>98674</td>\n", "      <td>147.0</td>\n", "      <td>01/09/24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DRAY</td>\n", "      <td>SEATTLE</td>\n", "      <td>WA</td>\n", "      <td>98134</td>\n", "      <td>WOODLAND</td>\n", "      <td>WA</td>\n", "      <td>98674</td>\n", "      <td>147.0</td>\n", "      <td>01/10/24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  equipment_type origin_city origin_state origin_zip destination_city   \n", "0           DRAY      TACOMA           WA      98421         PORTLAND  \\\n", "1           DRAY      TACOMA           WA      98421         PORTLAND   \n", "2              V    LEWISTON           ID      83501        VANCOUVER   \n", "3           DRAY     SEATTLE           WA      98134         WOODLAND   \n", "4           DRAY     SEATTLE           WA      98134         WOODLAND   \n", "\n", "  destination_state destination_zip  distance_miles origin_close_date  \n", "0                OR           97211           143.0          01/08/24  \n", "1                OR           97211           143.0          01/09/24  \n", "2                WA           98661           352.0          01/02/24  \n", "3                WA           98674           147.0          01/09/24  \n", "4                WA           98674           147.0          01/10/24  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example usage\n", "input_csv = 'raw_data.csv'\n", "output_csv = 'output.csv'\n", "\n", "convert_csv(input_csv, output_csv)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>equipment_type</th>\n", "      <th>origin_city</th>\n", "      <th>origin_state</th>\n", "      <th>origin_zip</th>\n", "      <th>destination_city</th>\n", "      <th>destination_state</th>\n", "      <th>destination_zip</th>\n", "      <th>distance_miles</th>\n", "      <th>origin_close_date</th>\n", "      <th>origin_country</th>\n", "      <th>destination_country</th>\n", "      <th>origin_close_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Container</td>\n", "      <td>Tacoma</td>\n", "      <td>WA</td>\n", "      <td>98421</td>\n", "      <td>Portland</td>\n", "      <td>OR</td>\n", "      <td>97211</td>\n", "      <td>143.0</td>\n", "      <td>01/08/24</td>\n", "      <td>US</td>\n", "      <td>US</td>\n", "      <td>2024-01-08 17:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Container</td>\n", "      <td>Tacoma</td>\n", "      <td>WA</td>\n", "      <td>98421</td>\n", "      <td>Portland</td>\n", "      <td>OR</td>\n", "      <td>97211</td>\n", "      <td>143.0</td>\n", "      <td>01/09/24</td>\n", "      <td>US</td>\n", "      <td>US</td>\n", "      <td>2024-01-09 17:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> Van</td>\n", "      <td>Lewiston</td>\n", "      <td>ID</td>\n", "      <td>83501</td>\n", "      <td>Vancouver</td>\n", "      <td>WA</td>\n", "      <td>98661</td>\n", "      <td>352.0</td>\n", "      <td>01/02/24</td>\n", "      <td>US</td>\n", "      <td>US</td>\n", "      <td>2024-01-02 17:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Container</td>\n", "      <td>Seattle</td>\n", "      <td>WA</td>\n", "      <td>98134</td>\n", "      <td>Woodland</td>\n", "      <td>WA</td>\n", "      <td>98674</td>\n", "      <td>147.0</td>\n", "      <td>01/09/24</td>\n", "      <td>US</td>\n", "      <td>US</td>\n", "      <td>2024-01-09 17:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Container</td>\n", "      <td>Seattle</td>\n", "      <td>WA</td>\n", "      <td>98134</td>\n", "      <td>Woodland</td>\n", "      <td>WA</td>\n", "      <td>98674</td>\n", "      <td>147.0</td>\n", "      <td>01/10/24</td>\n", "      <td>US</td>\n", "      <td>US</td>\n", "      <td>2024-01-10 17:00:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  equipment_type origin_city origin_state origin_zip destination_city   \n", "0      Container      Tacoma           WA      98421         Portland  \\\n", "1      Container      Tacoma           WA      98421         Portland   \n", "2        Dry Van    Lewiston           ID      83501        Vancouver   \n", "3      Container     Seattle           WA      98134         Woodland   \n", "4      Container     Seattle           WA      98134         Woodland   \n", "\n", "  destination_state destination_zip  distance_miles origin_close_date   \n", "0                OR           97211           143.0          01/08/24  \\\n", "1                OR           97211           143.0          01/09/24   \n", "2                WA           98661           352.0          01/02/24   \n", "3                WA           98674           147.0          01/09/24   \n", "4                WA           98674           147.0          01/10/24   \n", "\n", "  origin_country destination_country    origin_close_time  \n", "0             US                  US  2024-01-08 17:00:00  \n", "1             US                  US  2024-01-09 17:00:00  \n", "2             US                  US  2024-01-02 17:00:00  \n", "3             US                  US  2024-01-09 17:00:00  \n", "4             US                  US  2024-01-10 17:00:00  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total Rows: 6384\n", "Valid Rows: 4893\n"]}], "source": ["df = pd.read_csv(output_csv)\n", "total_rows = len(df)\n", "df = filter_valid_rows(df)\n", "valid_rows = len(df)\n", "\n", "display(df.head())\n", "\n", "print(f\"Total Rows: {total_rows}\")\n", "print(f\"Valid Rows: {valid_rows}\")\n", "\n", "batch_predict_inputs = df.apply(get_data_dict, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'origin_city': 'Tacoma',\n", " 'origin_state': 'WA',\n", " 'destination_city': 'Portland',\n", " 'destination_state': 'OR',\n", " 'equipment_type': 'Container',\n", " 'distance_miles': 143.0,\n", " 'origin_close_time': '2024-01-08 17:00:00'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["batch_predict_inputs[0]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["prices = PRICER.batch_predict(batch_predict_inputs)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["idx = 0"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'origin_city': 'Tacoma',\n", " 'origin_state': 'WA',\n", " 'destination_city': 'Portland',\n", " 'destination_state': 'OR',\n", " 'equipment_type': 'Container',\n", " 'distance_miles': 143.0,\n", " 'origin_close_time': '2024-01-08 17:00:00'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["batch_predict_inputs[idx]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'value': 419.4093933105469,\n", " 'lower_bound': 394.4642333984375,\n", " 'upper_bound': 397.9297790527344}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["prices[idx]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "truce", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}