{"functions": [{"name": "Calculator", "description": "useful for when you need to answer questions about math. Inputs should always be numbers. Don't use this for minimum.", "parameters": {"type": "object", "required": ["__arg1"], "properties": {"__arg1": {"type": "string", "title": "__arg1"}}}}, {"name": "WeeklyDieselPrice", "description": "useful for when you need to get this week's diesel price for a state.", "parameters": {"type": "object", "required": ["state"], "properties": {"state": {"type": "string", "title": "State", "description": "Two-digit US State code"}}}}, {"name": "Distance", "description": "useful for when you need to get the distance between two locations. - useful for CPM (cost per mile) calculations.", "parameters": {"type": "object", "required": ["origin", "destination"], "properties": {"origin": {"type": "string", "title": "Origin", "description": "strings that can be in the form city, state"}, "destination": {"type": "string", "title": "Destination", "description": "strings that can be in the form city, state"}}}}, {"name": "RouteFuelCost", "description": "useful for when you need to calculate the cost of fuel for a shipment, both one-way and roundtrip. Returns the total cost of fuel needed to cover a distance or over a lane. Calculate this number and subtract it from the truck cost or roundtrip cost if prompted to do so (e.g. calculate truck cost from a to b and subtract fuel costs).", "parameters": {"type": "object", "required": ["origin_state", "destination_state", "distance_miles"], "properties": {"mpg": {"type": "number", "title": "Mpg", "default": 6.5, "description": "Miles per gallon"}, "origin_state": {"type": "string", "title": "Origin State", "description": "Two-digit US State code"}, "distance_miles": {"type": "number", "title": "Distance Miles", "description": "Distance in miles"}, "destination_state": {"type": "string", "title": "Destination State", "description": "Two-digit US State code"}}}}, {"name": "OneWayTruckCost", "description": "useful for when you need to answer questions about how much a truck costs between two cities.", "parameters": {"type": "object", "required": ["origin_city", "origin_state", "destination_city", "destination_state"], "properties": {"origin_city": {"type": "string", "title": "Origin City", "description": "Origin City"}, "origin_state": {"type": "string", "title": "Origin State", "description": "Two-digit US State code"}, "equipment_type": {"type": "string", "title": "Equipment Type", "default": "Dry Van", "description": "Equipment type. Must be Reefer or Dry Van."}, "destination_city": {"type": "string", "title": "Destination City", "description": "Destination City"}, "destination_state": {"type": "string", "title": "Destination State", "description": "Two-digit US State code"}}}}, {"name": "RoundtripTruckCost", "description": "useful for when you need to calculate the roundtrip (or round trip) cost of a shipment. This will return the sum of truck costs between two cities, once in either direction. This will always be invoked when a prompt includes a round trip, a roundtrip, or a truck headed to a destination and back. This should be completed prior to calculating margin or subtracting expenses, if those are prompted. If more than two cities are referenced in a round trip, use the roundtripcost function on each two cities in the order prompted.", "parameters": {"type": "object", "required": ["origin_city", "origin_state", "destination_city", "destination_state"], "properties": {"origin_city": {"type": "string", "title": "Origin City", "description": "Origin City"}, "origin_state": {"type": "string", "title": "Origin State", "description": "Two-digit US State code"}, "equipment_type": {"type": "string", "title": "Equipment Type", "default": "Dry Van", "description": "Equipment type. Must be Reefer or Dry Van."}, "destination_city": {"type": "string", "title": "Destination City", "description": "Destination City"}, "destination_state": {"type": "string", "title": "Destination State", "description": "Two-digit US State code"}}}}, {"name": "CurrentTime", "description": "useful for when you need to get the current time and convert it to that of a local time zone.", "parameters": {"type": "object", "required": ["timezone_name"], "properties": {"timezone_name": {"type": "string", "title": "Timezone Name", "description": "Timezone name"}}}}, {"name": "MinimumValue", "description": "useful for when you need to find a minimum or smallest value out of two or more options.", "parameters": {"type": "object", "required": ["values"], "properties": {"values": {"type": "array", "items": {"type": "number"}, "title": "Values", "description": "List of values"}}}}, {"name": "MaximumValue", "description": "useful for when you need to find a maximum or largest value out of two or more options.", "parameters": {"type": "object", "required": ["values"], "properties": {"values": {"type": "array", "items": {"type": "number"}, "title": "Values", "description": "List of values"}}}}, {"name": "CityWeather", "description": "CityWeather(*args, **kwargs) - useful for when you need to get the weather or weather forecast. Call this function with the name of the relevant city when prompted to do so. Call this function multiple times when multiple cities are mentioned, such as when asked for the weather on a lane from one city to another.", "parameters": {"type": "object", "title": "WeatherInput", "required": ["city", "state"], "properties": {"city": {"type": "string", "title": "City", "description": "City name"}, "state": {"type": "string", "title": "State", "description": "State name"}, "country": {"type": "string", "title": "Country", "default": "US", "description": "Country name"}, "zipcode": {"type": "string", "title": "Zipcode", "default": "", "description": "Zipcode"}}}}, {"name": "WeatherAlongRoute", "description": "WeatherAlongRoute(*args, **kwargs) - useful for when you need to answer questions about weather along a route, answer with a step-by-step breakdown of weather along a route. For example, invoke with {\"origin\": \"New York, NY\", \"destination\": \"Los Angeles, CA\"}", "parameters": {"type": "object", "title": "LocationsInput", "required": ["origin", "destination"], "properties": {"origin": {"type": "string", "title": "Origin", "description": "strings that can be in the form city, state"}, "destination": {"type": "string", "title": "Destination", "description": "strings that can be in the form city, state"}}}}, {"name": "CarrierInfo", "parameters": {"type": "object", "title": "CarrierInfoInput", "properties": {"dot_number": {"type": "integer", "title": "Dot Number", "description": "1-8 digit department of transportation number"}, "icc_number": {"type": "string", "title": "Icc Number", "description": "1-8 digit number prefixed by MC, MX, or FF"}}}, "description": "CarrierInfo(*args, **kwargs) - useful for when you need to answer questions about a carrier.\n                For example, invoke with {\"dot_number\": 1234567} or {\"icc_number\": \"1234567\"} or {\"icc_number\": \"MC1234567\"}"}, {"name": "CrashInfo", "parameters": {"type": "object", "title": "CarrierInfoInput", "properties": {"dot_number": {"type": "integer", "title": "Dot Number", "description": "1-8 digit department of transportation number"}, "icc_number": {"type": "string", "title": "Icc Number", "description": "1-8 digit number prefixed by MC, MX, or FF"}}}, "description": "CrashInfo(*args, **kwargs) - useful for when you need to answer questions about a carrier's crash history in the past 2 years.\n                For example, invoke with {\"dot_number\": 1234567} or {\"icc_number\": \"1234567\"} or {\"icc_number\": \"MC1234567\"}"}, {"name": "InspectionInfo", "parameters": {"type": "object", "title": "CarrierInfoInput", "properties": {"dot_number": {"type": "integer", "title": "Dot Number", "description": "1-8 digit department of transportation number"}, "icc_number": {"type": "string", "title": "Icc Number", "description": "1-8 digit number prefixed by MC, MX, or FF"}}}, "description": "InspectionInfo(*args, **kwargs) - useful for when you need to answer questions about a carrier's inspection history in the past 2 years.\n                For example, invoke with {\"dot_number\": 1234567} or {\"icc_number\": \"1234567\"} or {\"icc_number\": \"MC1234567\"}"}]}