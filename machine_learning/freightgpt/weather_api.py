# Seconds in minute, hour, day
import datetime
import functools
import logging
from typing import Dict
from typing import Union

import requests
from dateutil.parser import parse

from machine_learning.freightgpt.constants import PIRATEWEATHER_API_URL


NUM_SECONDS_PER_MINUTE = 60
NUM_SECONDS_PER_HOUR = 3600
NUM_SECONDS_PER_DAY = 86400
CARDINAL_DIRECTIONS = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]


def bearing_to_cardinal_direction(wind_bearing: int) -> str:
    """
    Converts a wind bearing in degrees to a cardinal direction.

    Args:
        wind_bearing (int): The wind bearing in degrees (0 to 359).

    Returns:
        str: The cardinal direction (N, NE, E, SE, S, SW, W, NW) corresponding to the wind bearing.
             If the wind_bearing is None, it returns "N/A".
    """
    if wind_bearing is None or wind_bearing < 0 or wind_bearing > 360:
        return ""

    # Every 45 degrees is a new cardinal direction.
    # 22.5 is half of 45, so we add 22.5 to the wind_bearing to get the correct index.
    # Example: 0 degrees is N, 22.5 degrees is NNE, 45 degrees is NE, etc.
    # We use modulo 8 to wrap around the list.
    return CARDINAL_DIRECTIONS[int((wind_bearing + 22.5) / 45.0) % 8]


# Async and yield?
@functools.lru_cache(maxsize=8)
def get_pirate_weather(
    latitude: float, longitude: float, forecast_time: int = None
) -> Dict[str, Union[str, int]]:
    """Gets the weather data of a given lat/lng.

    arg `forecast_time` is a UNIX timestamp in seconds. (.timestamp() on a datetime object)
    """
    if forecast_time is None:
        forecast_time = datetime.datetime.now().timestamp()

    api_time = min(forecast_time, datetime.datetime.now().timestamp())
    url = PIRATEWEATHER_API_URL.format(
        latitude=latitude, longitude=longitude, api_time=api_time
    )
    response = requests.get(url)
    if response.status_code != 200:
        logging.error("Error: %s", response.status_code)
        logging.error("Request: %s", url)
        logging.error("Response: %s", response.text)
        raise ValueError("PirateWeatherAPI Request Failed: ", url)

    raw_response = response.json()
    time_delta_seconds = forecast_time - api_time

    # We set this to hour since minutely data is often incomplete.
    if time_delta_seconds <= NUM_SECONDS_PER_HOUR:  # time_delta_seconds <= 0:
        key = "currently"
        forecast_data = raw_response["currently"]
    else:
        if time_delta_seconds <= NUM_SECONDS_PER_HOUR:
            key = "minutely"
            interval_time = NUM_SECONDS_PER_MINUTE
        elif time_delta_seconds <= 48 * NUM_SECONDS_PER_HOUR:
            key = "hourly"
            interval_time = NUM_SECONDS_PER_HOUR
        elif time_delta_seconds <= 6 * NUM_SECONDS_PER_DAY:
            key = "daily"
            interval_time = NUM_SECONDS_PER_DAY
        else:
            raise ValueError("Forecast time is too far in the future.")

        forecast_time = round(forecast_time / interval_time) * interval_time
        # TODO(P1): Figure out the correct indexing later
        # index = int((forecast_time - api_time) / interval_time)

        # data = data[key]["data"][index]

        # print("forecast_time:", forecast_time)
        # print(data["time"])
        # assert data["time"] == forecast_time

        # Search through data[key] for where "time" == "forecast_time"
        for forecast in raw_response[key]["data"]:
            if forecast["time"] == forecast_time:
                forecast_data = forecast
                break

    if not forecast_data:
        raise ValueError("No forecast data found.")

    # Possible fields:
    # time,summary,icon,nearestStormDistance,nearestStormBearing,precipIntensity,precipProbability,precipIntensityError,precipType,
    # temperature,apparentTemperature,dewPoint,humidity,pressure,windSpeed,windGust,windBearing,cloudCover,uvIndex,visibility,ozone
    return_data = {}
    # summary fields:
    # Clear,Partly Cloudy,Mostly Cloudy,Cloudy,Overcast,Fog,Breezy,Windy,Dry,Humid,Light Rain,Rain,Heavy Rain,Drizzle,Light Snow,Snow,Heavy Snow,Sleet,Flurries,Light Sleet,Wintry Mix
    return_data["summary"] = forecast_data["summary"]
    return_data["temperature"] = round(forecast_data["temperature"])
    return_data["windSpeed"] = round(forecast_data["windSpeed"])
    return_data["windBearing"] = bearing_to_cardinal_direction(
        forecast_data["windBearing"]
    )
    return_data["windGust"] = round(forecast_data["windGust"])
    return return_data


@functools.lru_cache(maxsize=8)
def get_nws_weather(
    latitude: float, longitude: float, forecast_time: int = None
) -> dict:
    """Gets the weather data of a given latitude and longitude using the NWS API."""
    if forecast_time is None:
        forecast_time = int(datetime.datetime.now().timestamp())

    # Get the grid location for latitude and longitude
    points_url = f"https://api.weather.gov/points/{latitude},{longitude}"

    points_response = requests.get(points_url)
    if points_response.status_code != 200:
        logging.error(f"Failed to retrieve grid data: {points_response.status_code}")
        raise ValueError("NWS API Request Failed for Grid Data")

    try:
        grid_data = points_response.json()
        forecast_url = grid_data["properties"]["forecastHourly"]
    except (ValueError, KeyError) as e:
        logging.error(f"Error parsing grid data: {e}")
        raise ValueError("NWS API Response Parsing Failed for Grid Data")

    # Fetch the hourly forecast
    forecast_response = requests.get(forecast_url)
    if forecast_response.status_code != 200:
        logging.error(
            f"Failed to retrieve forecast data: {forecast_response.status_code}"
        )
        logging.error(f"Request: {forecast_url}, Response: {forecast_response.text}")
        raise ValueError("NWS API Request Failed for Forecast Data")
    try:
        forecast_data = forecast_response.json()
    except ValueError as e:
        logging.error(f"Error parsing forecast data: {e}")
        raise ValueError("NWS API Response Parsing Failed for Forecast Data")
    # Extract relevant data for the requested time
    for forecast in forecast_data.get("properties", {}).get("periods", []):
        try:
            forecast_time_datetime = parse(
                forecast["startTime"]
            )  # Convert from ISO 8601 string to datetime
            forecast_time_stamp = int(forecast_time_datetime.timestamp())
            short_forecast = forecast["shortForecast"]
            temperature = forecast["temperature"]
            wind_speed = forecast["windSpeed"]
            wind_direction = forecast["windDirection"]
        except (KeyError, ValueError) as e:
            logging.error(f"Error parsing forecast data: {e}")
            continue

        if abs(forecast_time_stamp - forecast_time) < NUM_SECONDS_PER_HOUR:
            return {
                "summary": short_forecast,
                "temperature": temperature,
                "windSpeed": wind_speed,
                "windBearing": wind_direction,
            }

    # TODO(P1): #2068 Add email warning for when weather API goes down

    raise ValueError("No appropriate forecast data found.")


def get_weather(latitude: float, longitude: float, forecast_time: int = None) -> dict:
    """Gets the weather data of a given latitude and longitude using the NWS API."""
    MAX_RETRIES = 3
    for retry in range(MAX_RETRIES):
        try:
            return get_nws_weather(latitude, longitude, forecast_time)
        except ValueError as e:
            logging.error(
                "NWS API failed on %s/%s times, with error: %s",
                retry + 1,
                MAX_RETRIES,
                e,
            )
    return {}


if __name__ == "__main__":
    new_york = (40.7128, -74.0060)
    # print(get_nws_weather(37.7749, -122.4194))
    print(get_nws_weather(*new_york))

    # new_york = (40.7128, -74.0060)
    # print(get_pirate_weather(37.7749, -122.4194))
    # print(get_pirate_weather(*new_york))

    # # Get the sunnyvale forecast for now and the next 12 hours, hour by hour
    # sunnyvale = (40.7128, -74.0060)  # (37.3688, -122.0363)
    # print(get_nws_weather(*sunnyvale))
    # for i in range(1, 13):
    #     forecast_time = datetime.datetime.now() + datetime.timedelta(hours=i)
    #     forecast = get_nws_weather(*sunnyvale, forecast_time=forecast_time.timestamp())
    #     print(f"{forecast_time}: {forecast}")

    # # Get the next 7 days
    # for i in range(1, 8):
    #     forecast_time = datetime.datetime.now() + datetime.timedelta(days=i)
    #     forecast = get_nws_weather(*sunnyvale, forecast_time=forecast_time.timestamp())
    #     print(f"{forecast_time}: {forecast}")
