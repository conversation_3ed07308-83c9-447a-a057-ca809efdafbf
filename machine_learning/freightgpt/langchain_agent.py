import asyncio
import logging
import os
from typing import AsyncIterable
from typing import <PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>

from aiobotocore.client import AioBaseClient
from langchain.agents import AgentType
from langchain.agents import initialize_agent
from langchain.callbacks.tracers import LangChainTracer
from langchain.memory import ConversationBufferMemory
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.prompts import MessagesPlaceholder
from langchain.tools import StructuredTool
from langchain_core.messages.system import SystemMessage
from langchain_openai import ChatOpenAI

from backend.freightgpt.main_server.langsmith_feedback_manager import (
    LangsmithFeedbackClient,
)
from common import dynamodb_utils
from common import mail
from machine_learning.freightgpt import langchain_tools
from machine_learning.freightgpt.async_stream_handler import send_data
from machine_learning.freightgpt.async_stream_handler import send_error_event
from machine_learning.freightgpt.async_stream_handler import StreamCallback
from machine_learning.freightgpt.chatbot_sim import Chat<PERSON>ot
from machine_learning.freightgpt.constants import LANGCHAIN_MODEL
from machine_learning.freightgpt.constants import LOG_TEMPLATE_URL
from machine_learning.freightgpt.constants import MAX_TOKEN_LIMIT
from machine_learning.freightgpt.constants import WARNING_TOKEN_LIMIT
from machine_learning.freightgpt.custom_langchain_memory import (
    CustomDynamoDBChatHistory,
)
from machine_learning.freightgpt.langchain_utils import asyncify
from machine_learning.freightgpt.prompt import PROMPT


class LangChainAgent(ChatBot):
    def __init__(
        self,
        streaming: bool = False,
        model: str = LANGCHAIN_MODEL,
        cli_dev_mode: bool = False,
        mock: bool = False,
        verbose: bool = False,
        user_id=None,
        conversation_id=None,
        tags=None,
        chats_table_name: str = None,
        events_table_name: str = None,
        mail_api: mail.MailAPI = mail.MailAPI(gmail=False),
        langsmith_feedback_client: LangsmithFeedbackClient = None,
        project_name: str = None,
    ) -> None:
        self.model = model
        self.conversation_title = ""
        self.verbose = verbose
        self.tags = [] if tags is None else tags
        self.tool_store = {}
        self.langsmith_feedback_client = langsmith_feedback_client

        if not cli_dev_mode:
            self.log_url = LOG_TEMPLATE_URL.format(
                log_group="freightgpt-" + os.getenv("ENV").lower(),
                user_id=user_id,
                conversation_id=conversation_id,
            )
            self.mail_api = mail_api
        self.llm = ChatOpenAI(temperature=0, model=self.model, streaming=streaming)

        self.prompt_tokens = self.llm.get_num_tokens(PROMPT)

        # Connect agent's memory to DynamoDB
        chat_memory = (
            CustomDynamoDBChatHistory(
                table_name=chats_table_name,
                session_id=conversation_id,
                key={"user_id": user_id, "conv_id": conversation_id},
            )
            if not cli_dev_mode and chats_table_name and not os.getenv("DRY_MODE")
            else CustomDynamoDBChatHistory(dry_mode=True)
        )

        self.memory = ConversationBufferMemory(
            memory_key="memory",
            input_key="input",
            output_key="output",
            return_messages=True,
            chat_memory=chat_memory,
        )
        # Events storage
        self.events_history = (
            dynamodb_utils.DynamoDBListEntry(
                events_table_name, key={"conv_id": conversation_id}
            )
            if not cli_dev_mode and events_table_name and not os.getenv("DRY_MODE")
            else None
        )

        tools = []
        for tool_name in langchain_tools.LANGCHAIN_TOOLS:
            tool = langchain_tools.LANGCHAIN_TOOLS[tool_name]
            if mock and "mock_function" in tool:
                tool_fn = tool["mock_function"]
            else:
                tool_fn = tool["function"]

            async_fn = (
                tool["async_function"]
                if "async_function" in tool
                else asyncify(tool_fn)
            )

            tools.append(
                StructuredTool.from_function(
                    func=langchain_tools.tool_wrapper(
                        func=tool_fn,
                        tool_name=tool_name,
                        memory=self.memory,
                    ),
                    coroutine=langchain_tools.tool_wrapper(
                        func=async_fn,
                        tool_name=tool_name,
                        memory=self.memory,
                        emit_events=tool.get("emits_event"),
                    ),
                    name=tool_name,
                    description=tool["description"],
                    handle_tool_error=True,
                    args_schema=tool["args_schema"],
                )
            )

        agent_kwargs = {
            "system_message": SystemMessage(content=PROMPT),
            # DO NOT REMOVE, this enables memory in the langchain agent.
            "extra_prompt_messages": [MessagesPlaceholder(variable_name="memory")],
        }

        # TODO(P0): #1808 initialize_agent is deprecated, use create_openai_tools_agent instead.
        self.agent = initialize_agent(
            tools,
            self.llm,
            agent=AgentType.OPENAI_FUNCTIONS,
            # return_intermediate_steps=True,
            agent_kwargs=agent_kwargs,
            memory=self.memory,
            max_execution_time=90,
            early_stopping_method="generate",
            max_iterations=10,
            handle_parsing_errors=True,
            verbose=verbose,
            tags=self.tags,
        )

        self.tracer = LangChainTracer(project_name=project_name)

    def create_conversation_title(self) -> bool:
        """Generates conversation title based on message."""

        chat_memory = self.agent.memory.chat_memory
        is_dynamo_memory = isinstance(chat_memory, CustomDynamoDBChatHistory)

        # If a convo title is already set, do not generate one.
        stored_convo_title = (
            chat_memory.get_convo_summary() if is_dynamo_memory else None
        )
        if self.conversation_title or stored_convo_title:
            # If an existing convo title has been pulled, set this as the convo title.
            if stored_convo_title:
                self.conversation_title = stored_convo_title
            return False

        memory_variables = self.agent.memory.load_memory_variables({})

        formatted_memory = ""
        for message in memory_variables.get("memory", []):
            formatted_memory += f"{message.type}: {message.content}\n"

        prompt = ChatPromptTemplate.from_template(
            "Create a simple title no more than 5 words for the following conversation:\n\n{memory}"
        )

        title_chain = prompt | ChatOpenAI(temperature=0, model=self.model)
        self.conversation_title = title_chain.invoke(
            input={"memory": formatted_memory},
            config={"tags": self.tags + ["titler"], "callbacks": [self.tracer]},
        ).content

        # Upload to dynamo
        if is_dynamo_memory:
            chat_memory.set_convo_summary(self.conversation_title, force_update=True)

        return True

    def limit_tokens(
        self,
        msg: str,
    ) -> Tuple[bool, int]:
        """Limits the number of tokens in the message to prevent OpenAI token limit errors.

        We limit the number of tokens in the message by iteratively removing the oldest
        messages in the chat memory.

        Args:
            - msg (str): The message to check for token limit.
            - aioboto3_ws_client (AioBaseClient): An aioboto3 WebSocket client instance used to send messages.
            - connection_id (str): The connection ID for the WebSocket over which the message will be sent.
        Returns:
            -Tuple[bool, int]: True if removing messages to under limit was successful, False otherwise, and current token number.
        """
        # Takes <0.01 seconds
        # Get the number of tokens in the prompt, chat memory, and message.
        msg_tokens = self.llm.get_num_tokens(msg)
        num_tokens = (
            self.prompt_tokens
            + msg_tokens
            + self.llm.get_num_tokens(str(self.agent.memory.chat_memory))
        )

        # Takes <0.05 seconds
        while num_tokens > MAX_TOKEN_LIMIT:
            logging.warn(f"{num_tokens} exceeded token limit. Shortening message.")
            # If there are no more messages to remove, this message cannot be sent.
            # This can happen if the user sends a very long message, so give up on limiting and return False.
            if len(self.agent.memory.chat_memory.messages) <= 1:
                return False, num_tokens

            removed_msg = self.agent.memory.chat_memory.messages.pop(0).content
            num_tokens = (
                self.prompt_tokens
                + msg_tokens
                + self.llm.get_num_tokens(str(self.agent.memory.chat_memory))
            )
            print(f"Removed message: {removed_msg}")
            print(f"Now at {num_tokens} tokens.")

        return True, num_tokens

    def send_message(self, msg: str) -> str:
        under_token_limit, _ = self.limit_tokens(msg)
        if not under_token_limit:
            raise ValueError("Message violates token limit.")

        # For CLI dev mode to test async tools.
        from langchain_community.callbacks import get_openai_callback

        # Print openai tokens used
        with get_openai_callback() as cb:
            result = self.agent.run(msg)
            if self.verbose:
                print(cb)

        if not self.conversation_title:
            self.create_conversation_title()

        return result

    async def asend_message(self, msg: str) -> str:
        under_token_limit, _ = self.limit_tokens(msg)
        if not under_token_limit:
            raise ValueError("Message violates token limit.")

        # For CLI dev mode to test async tools.
        # TODO(P0): #1809 arun is deprecated, use ainvoke which returns a dict instead.
        return await self.agent.arun(msg)

    async def send_message_stream(
        self,
        message: str,
        aioboto3_ws_client: AioBaseClient = None,
        connection_id: str = "",
    ) -> AsyncIterable[str]:
        """
        Runs langchain agent, and yields each token.
        Args:
            message: input message to pass to agent.
            aioboto3_ws_client (AioBaseClient): An aioboto3 WebSocket client instance used to send messages.
            connection_id (str): The connection ID for the WebSocket over which the message will be sent.
        Returns:
            Async iterable (via yield) that can be passed to a FastAPI
            StreamingResponse object.

        """

        # Check token limit
        under_token_limit, current_token_number = self.limit_tokens(message)
        if not under_token_limit:
            logging.error("Token limit violated")
            await send_error_event(aioboto3_ws_client, connection_id)
            raise ValueError("Message violates token limit.")

        # If token number aboe warning limit, send conversation token warning.
        if current_token_number > WARNING_TOKEN_LIMIT:
            logging.warning("Conversation is above token warning limit.")
            token_warning_event = {
                "event": {
                    "type": "conversation_token_warning",
                    "data": current_token_number,
                }
            }
            await send_data(
                connection_id=connection_id,
                data=token_warning_event,
                aiboto3_ws_client=aioboto3_ws_client,
            )

        self.stream_callback = StreamCallback(
            self.log_url,
            tool_store=self.tool_store,
            parent_run_id_emitter=(
                self.agent.memory.chat_memory.set_parent_run_id
                if not os.getenv("DRY_MODE")
                else None
            ),
            mail_api=self.mail_api,
            user_message=message,
            langsmith_feedback_client=self.langsmith_feedback_client,
        )
        logging.info(f"user_message: {message}")

        async def terminate_on_full_chain(fn: Awaitable, async_callback):
            """
            Wrap an awaitable with a event to signal when its done or an exception is raised.
            Do this instead of relying on the callback's signal, which will terminate early.
            """
            try:
                await fn

            except Exception as e:
                logging.error(f"Caught exception: {e}")

                await async_callback.drain_queue_and_signal_done()
                raise Exception("Agent Error")

            finally:
                # Signal the aiter to stop.
                logging.info("Stopping Aiter!")
                await async_callback.drain_queue_and_signal_done()

        # TODO(P0): #1809 arun is deprecated, use ainvoke which returns a dict instead.
        # Begin a task that runs in the background.
        chain_task = asyncio.create_task(
            terminate_on_full_chain(
                self.agent.arun(message, callbacks=[self.stream_callback, self.tracer]),
                self.stream_callback,
            ),
        )

        ai_message = ""
        # Yield tokens
        async for token in self.stream_callback.aiter():
            # Use server-sent-events to stream the response
            ai_message += f"{token}"
            yield f"{token}"

        await chain_task
        logging.info("ai_message: " + ai_message)


def main():
    chatbot = LangChainAgent()

    print(chatbot.send_message("roundtrip sf ny"))


async def amain(msg: str = "What is the price from SF to LA?"):
    chatbot = LangChainAgent(streaming=True)
    async for token in chatbot.send_message_stream(msg):
        print(token)


# We fail at 25 concurrent complicated queries such as What is the round trip price from chicago to atlanta? Add a 12% margin, and remove $0.43 per mile for fuel. What was the margin amount in $?
async def run_multiple_amains():
    # Create 10 coroutine tasks for amain()
    tasks = [amain() for _ in range(20)]

    # Run all tasks concurrently and wait for their completion
    await asyncio.gather(*tasks)


if __name__ == "__main__":
    # Run all tasks concurrently
    # asyncio.run(run_multiple_amains())
    main()
