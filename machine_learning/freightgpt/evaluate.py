from common import google_sheets_utils
from common import init_main
from machine_learning.freightgpt.constants import LANGCHAIN_MODEL

PROJECT_ROOT = init_main.initialize()

from machine_learning.freightgpt.langchain_agent import LangChainAgent
from langchain.agents.agent import AgentExecutor
from langsmith.evaluation import evaluate, LangChainStringEvaluator
from langchain_core.messages.system import SystemMessage
from langchain_core.messages.human import HumanMessage
from langchain_core.messages.ai import AIMessage
from langchain_openai import ChatOpenAI
import langsmith

from typing import List, Dict


# https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0
EXAMPLES_SHEET_ID = "1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo"
MESSAGE_TYPES = {"system": SystemMessage, "human": HumanMessage, "ai": AIMessage}


def fill_memory(agent: AgentExecutor, memory: List[Dict[str, str]]) -> None:
    """
    Fills the agent's memory with a series of messages provided in a list.

    This function iterates through the list of messages and adds each one to the
    agent's chat memory based on its type. The messages can be of type 'system',
    'ai', or 'human', each corresponding to a specific kind of message.

    Args:
        agent: The agent whose memory is to be filled.
        memory: A list of message dictionaries. Each dictionary must contain the
            following keys:
            - 'type': A string indicating the type of the message. It can be 'system',
              'ai', or 'human'.
            - 'content': A string containing the message content.
    """
    for message in memory:
        MessageType = MESSAGE_TYPES[message["type"]]
        agent.memory.chat_memory.add_message(MessageType(content=message["content"]))


def predict(inputs: Dict[str, str]) -> Dict[str, str]:
    """Call your model or pipeline with the given inputs and return the predictions."""
    agent = LangChainAgent(mock=True, cli_dev_mode=True).agent
    fill_memory(agent, inputs.get("memory", []))
    result = agent.invoke(inputs)
    return {"output": result["output"]}


def tagged_examples(
    model: str,
    dataset: str,
    sheets_to_langsmith: bool,
):
    """
    Loads a dataset, initializes various evaluation and language model components,
    and runs an evaluation on the dataset. Optionally uploads data from sheets to Langsmith.

    Args:
    model (str): Name of the model to be evaluated.
    dataset (str): Name of the langsmith. dataset to be evaluated. https://smith.langchain.com/o/3d1f3312-0b72-4ba6-a0e1-54792f0c9eb3/datasets
    sheets_to_langsmith (bool): If True, uploads data from a Google Sheet to Langsmith. Default is False.
    """

    gsheets_utils = google_sheets_utils.GoogleSheetsUtils()

    # Langsmith client
    client = langsmith.Client()
    # https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0
    if sheets_to_langsmith:
        examples_df = gsheets_utils.load_sheet_into_dataframe(
            EXAMPLES_SHEET_ID, "Examples", skiprows=1
        )
        df = examples_df[examples_df["Desired Response"] != ""]
        client.upload_dataframe(df, dataset, ["Prompt"], ["Desired Response"])

    # Define the LLM that determines whether the responses are correct, concise, etc.
    # Comment out evaluation metrics that you don't want to run.
    eval_llm = ChatOpenAI(model=model, temperature=0)
    # qa = LangChainStringEvaluator("qa", config={"llm": eval_llm})
    cot_qa = LangChainStringEvaluator(
        "cot_qa",
        config={"llm": eval_llm},
        prepare_data=lambda run, example: {
            "prediction": run.outputs["output"],
            "reference": example.outputs["output"],
            "input": example.inputs["input"],
        },
    )
    # conciseness = LangChainStringEvaluator("criteria", config={ "criteria": "conciseness"})

    evaluate(predict, data=dataset, evaluators=[cot_qa], experiment_prefix="exp")


def main():
    tagged_examples(
        model=args.model,
        dataset=args.dataset,
        sheets_to_langsmith=args.sheets_to_langsmith,
    )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run the evaluation on a model.")
    parser.add_argument(
        "--model",
        type=str,
        default=LANGCHAIN_MODEL,  # "ft:gpt-3.5-turbo-0613:truce::8Dj8gwqm"
        help="Model to use for chatbot.",
    )
    parser.add_argument(
        "--sheets_to_langsmith",
        action="store_true",
        help="Uploads examples from a Google Sheet to Langsmith.",
    )
    parser.add_argument(
        "--dataset",
        type=str,
        default="eval-v1.1",  # https://smith.langchain.com/o/3d1f3312-0b72-4ba6-a0e1-54792f0c9eb3/datasets
        help="Name of the dataset to be evaluated, or where the sheet will be uploaded to.",
    )
    args = parser.parse_args()
    main()

# To run the evaluation on a model, run:
# python machine_learning/freightgpt/evaluate.py
# To upload examples from a Google Sheet to Langsmith, run:
# python machine_learning/freightgpt/evaluate.py --sheets_to_langsmith --dataset <new_dataset_name>
