import datetime

from common import cloudwatch_log_utils
from common import credentials
from common import google_maps_utils
from common.constants import construct_named_tuple


CREDENTIALS = credentials.Credentials()

GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()

# Due to UTC 1700 being the most popular close hour in training data.
PRICER_ORIGIN_CLOSE_HOUR_UTC = 17
DEFAULT_TRUCK_MPG = 7.0
LANGCHAIN_MODEL = (
    "gpt-4o-2024-08-06"  # Previous versions: "gpt-4o-2024-05-13", "gpt-3.5-turbo-0125"
)
MAX_TOKEN_LIMIT = 20000
WARNING_TOKEN_LIMIT = 6000
MOCK_CURRENT_TIME = datetime.datetime(2023, 1, 1, 12, 0, 0)
PIRATEWEATHER_API_URL = (
    f"https://api.pirateweather.net/forecast/{CREDENTIALS.pirate_weather_api_key}"
    + "/{latitude},{longitude},{api_time}"
)

# Logging
LOG_TEMPLATE_URL = cloudwatch_log_utils.LOG_TEMPLATE_URL.format(
    log_group="{log_group}", log_stream_id="{user_id}%2F{conversation_id}"
)


VisualizationType = construct_named_tuple(
    "VisualizationType",
    [
        "gmaps",
        "pricer_polyline",
        "windy",
        "polygon",
        "graph_series",
        "carrier_search_results",
        "transit_markers",
    ],
    upper=True,
)

ActiveType = construct_named_tuple("ActiveType", ["active", "inactive"], upper=True)


# Weather
MIN_WIND_GUST_SPEED_MPH = 30
MIN_WIND_SPEED_MPH = 20

# From https://openweathermap.org/api/road-risk#descr
ROAD_STATE_DICT = {
    0: "No report",
    1: "Dry",
    2: "Moist",
    3: "Moist and chemically treated",
    4: "Wet",
    5: "Wet and chemically treated",
    6: "Ice",
    7: "Frost",
    8: "Snow",
    9: "Snow/Ice watch",
    10: "Snow/Ice warning",
    11: "Wet above freezing",
    12: "Wet below freezing",
    13: "Absorption",
    14: "Absorption at dewpoint",
    15: "Dew",
    16: "Black ice warning",
    17: "Other",
    18: "Slush",
}

####################################################################
#         carrier_tools.py
####################################################################


CRASH_EVENT = {
    1: "Non collision ran off road",
    2: "Non collision jackknife",
    3: "Non collision overturn (rollover)",
    4: "Non collision downhill runaway",
    5: "Non collision cargo loss or shift",
    6: "Non collision explosion or fire",
    7: "Non collision separation of units",
    8: "Non collision cross median/centerline",
    9: "Non collision equipment failure (brake failure, blown tires, etc.)",
    10: "Non collision other",
    11: "Non collision unknown",
    12: "Collision involving pedestrian",
    13: "Collision involving motor vehicle in transport",
    14: "Collision involving parked motor vehicle",
    15: "Collision involving train",
    16: "Collision involving pedalcycle",
    17: "Collision involving animal",
    18: "Collision involving fixed object",
    19: "Collision with work zone maintenance equipment",
    20: "Collision with other movable object (Substitutes for previous 'Collision involving other object')",
    21: "Collision with unknown movable object",
    98: "Other",
}

CRASH_ROAD_SURFACE_CONDITION = {
    1: "Dry",
    2: "Wet",
    3: "Water (standing, moving)",
    4: "Snow",
    5: "Slush",
    6: "Ice",
    7: "Sand, Mud, Dirt, Oil or Gravel",
    8: "Other",
    9: "Unknown or Blank",
}

IGNORE_CRASH_ROAD_CONDITIONS = {
    "Dry",
    "Other",
    "Unknown or Blank",
}

CRASH_WEATHER_CONDITION = {
    1: "No Adverse Condition",
    2: "Rain",
    3: "Sleet, Hail",
    4: "Snow",
    5: "Fog",
    6: "Blowing Sand, Soil, Dirt, or Snow",
    7: "Severe Crosswinds",
    8: "Other",
    9: "Unknown or Blank",
}

IGNORE_CRASH_WEATHER_CONDITIONS = {
    "No Adverse Condition",
    "Other",
    "Unknown or Blank",
}

CARRIER_DBA_BLACKLIST = [
    "OWNER",
    "NO",
    "NONE",
    "TRANSPORTATION",
    "Y",
    "CONSTRUCTION",
    "UNISHIPPERS",
    "TRUCKING",
    "LANDSCAPING",
    "FARM",
    "FARMER",
    "FARMING",
    "TOWING",
    "LLC",
    "GENERAL CONTRACTOR",
    "PRIVATE",
    "CARRIER",
    "NOT APPLICABLE",
    "CONTRACTOR",
    "TRANSPORT",
    "PRESIDENT",
]

CARRIER_RATING_DICT = {
    "S": "Satisfactory",
    "C": "Conditional",
    "U": "Unsatisfactory",
}

CARSHIP_DICT = {
    "C": "Carrier",
    "S": "Shipper",
    "B": "Broker",
    "R": "Registrant",
    "F": "Freight Forwarder",
    "T": "Cargo Tank",
}

CARGO_TRANSPORTED_TYPES = {
    "genfreight": "General Freight",
    "household": "Household Goods",
    "metalsheet": "Metal: Sheets, Coils, Rolls",
    "motorveh": "Motor Vehicles",
    "drivetow": "Driveaway/Towaway",
    "logpole": "Logs, Poles, Beams, Lumber",
    "bldgmat": "Building Materials",
    "mobilehome": "Mobile Homes",
    "machlrg": "Machinery, Large Objects",
    "produce": "Fresh Produce",
    "liqgas": "Liquids/Gases",
    "intermodal": "Intermodal Containers",
    "passengers": "Passengers",
    "oilfield": "Oilfield Equipment",
    "livestock": "Livestock",
    "grainfeed": "Grain, Feed, Hay",
    "coalcoke": "Coal/Coke",
    "meat": "Meat",
    "garbage": "Garbage, Refuse, Trash",
    "usmail": "U.S. Mail",
    "chem": "Chemicals",
    "drybulk": "Commodities Dry Bulk",
    "coldfood": "Refrigerated Food",
    "beverages": "Beverages",
    "paperprod": "Paper Products",
    "utility": "Utility",
    "farmsupp": "Farm Supplies",
    "construct": "Construction",
    "waterwell": "Water-Well",
}

# https://docs.google.com/document/d/1zbV33RwTxOoc6rQ-C11ehg3o_iPkQErav5SskG55sXM/edit#heading=h.ud2ysuiko3wc
CARGO_TYPE_TO_EQUIPMENT_TYPE = {
    "genfreight": "Dry Van",
    "household": "Dry Van",
    "paperprod": "Dry Van",
    "beverages": "Dry Van",
    "bldgmat": "Flatbed",
    "mobilehome": "Flatbed",
    "construct": "Flatbed",
    "farmsupp": "Flatbed",
    "drivetow": "Tow",
    "liqgas": "Tanker",
    "oilfield": "Tanker",
    "produce": "Reefer",
    "coldfood": "Reefer",
    "meat": "Reefer",
    "intermodal": "Intermodal",
    "livestock": "Livestock Trailer",
    "grainfeed": "Grain Trailer",
    "garbage": "Garbage",
    "usmail": "Mail",
    "chem": "Chemicals",
    "refrigeratedfood": "Reefer",
    "milk,groceries,eggs": "Reefer",
}

# Source of truth: https://transition.fcc.gov/oet/info/maps/census/fips/fips.txt
# Secondary source of truth: https://www.bls.gov/respondents/mwr/electronic-data-interchange/appendix-d-usps-state-abbreviations-and-fips-codes.htm
# Generated from https://gist.github.com/dantonnoriega/bf1acd2290e15b91e6710b6fd3be0a53
# US_STATE_FIPS = pd.read_csv("us-state-ansi-fips.csv")
# state_data = US_STATE_FIPS.set_index('stusps').T.to_dict(orient='dict')
# processed_state_data = {
#     key.strip(): {'name': value['stname'], 'fips': f"{value['st']:02d}"}
#     for key, value in state_data.items()
# }
STATE_FIPS_DICT = {
    "AL": {"name": "Alabama", "fips": "01"},
    "AK": {"name": "Alaska", "fips": "02"},
    "AZ": {"name": "Arizona", "fips": "04"},
    "AR": {"name": "Arkansas", "fips": "05"},
    "CA": {"name": "California", "fips": "06"},
    "CO": {"name": "Colorado", "fips": "08"},
    "CT": {"name": "Connecticut", "fips": "09"},
    "DE": {"name": "Delaware", "fips": "10"},
    "DC": {"name": "District of Columbia", "fips": "11"},
    "FL": {"name": "Florida", "fips": "12"},
    "GA": {"name": "Georgia", "fips": "13"},
    "HI": {"name": "Hawaii", "fips": "15"},
    "ID": {"name": "Idaho", "fips": "16"},
    "IL": {"name": "Illinois", "fips": "17"},
    "IN": {"name": "Indiana", "fips": "18"},
    "IA": {"name": "Iowa", "fips": "19"},
    "KS": {"name": "Kansas", "fips": "20"},
    "KY": {"name": "Kentucky", "fips": "21"},
    "LA": {"name": "Louisiana", "fips": "22"},
    "ME": {"name": "Maine", "fips": "23"},
    "MD": {"name": "Maryland", "fips": "24"},
    "MA": {"name": "Massachusetts", "fips": "25"},
    "MI": {"name": "Michigan", "fips": "26"},
    "MN": {"name": "Minnesota", "fips": "27"},
    "MS": {"name": "Mississippi", "fips": "28"},
    "MO": {"name": "Missouri", "fips": "29"},
    "MT": {"name": "Montana", "fips": "30"},
    "NE": {"name": "Nebraska", "fips": "31"},
    "NV": {"name": "Nevada", "fips": "32"},
    "NH": {"name": "New Hampshire", "fips": "33"},
    "NJ": {"name": "New Jersey", "fips": "34"},
    "NM": {"name": "New Mexico", "fips": "35"},
    "NY": {"name": "New York", "fips": "36"},
    "NC": {"name": "North Carolina", "fips": "37"},
    "ND": {"name": "North Dakota", "fips": "38"},
    "OH": {"name": "Ohio", "fips": "39"},
    "OK": {"name": "Oklahoma", "fips": "40"},
    "OR": {"name": "Oregon", "fips": "41"},
    "PA": {"name": "Pennsylvania", "fips": "42"},
    "RI": {"name": "Rhode Island", "fips": "44"},
    "SC": {"name": "South Carolina", "fips": "45"},
    "SD": {"name": "South Dakota", "fips": "46"},
    "TN": {"name": "Tennessee", "fips": "47"},
    "TX": {"name": "Texas", "fips": "48"},
    "UT": {"name": "Utah", "fips": "49"},
    "VT": {"name": "Vermont", "fips": "50"},
    "VA": {"name": "Virginia", "fips": "51"},
    "WA": {"name": "Washington", "fips": "53"},
    "WV": {"name": "West Virginia", "fips": "54"},
    "WI": {"name": "Wisconsin", "fips": "55"},
    "WY": {"name": "Wyoming", "fips": "56"},
    "PR": {"name": "Puerto Rico", "fips": "72"},  # From secondary source
    "VI": {"name": "Virgin Islands", "fips": "78"},  # From secondary source
}


FMCSA_HEADERS = [
    "docket_number",
    "dot_number",
    "mx_type",
    "rfc_number",
    "common_stat",
    "contract_stat",
    "broker_stat",
    "common_app_pend",
    "contract_app_pend",
    "broker_app_pend",
    "common_rev_pend",
    "contract_rev_pend",
    "broker_rev_pend",
    "property_chk",
    "passenger_chk",
    "hhg_chk",
    "private_auth_chk",
    "enterprise_chk",
    "min_cov_amount",
    "cargo_req",
    "bond_req",
    "bipd_file",
    "cargo_file",
    "bond_file",
    "undeliverable_mail",
    "dba_name",
    "legal_name",
    "bus_street_po",
    "bus_colonia",
    "bus_city",
    "bus_state_code",
    "bus_ctry_code",
    "bus_zip_code",
    "bus_telno",
    "bus_fax",
    "mail_street_po",
    "mail_colonia",
    "mail_city",
    "mail_state_code",
    "mail_ctry_code",
    "mail_zip_code",
    "mail_telno",
    "mail_fax",
]

####################################################################
#         carrier_search.py
####################################################################
OPENSEARCH_INDEX_NAME = "carrier-name-search"

OPENSEARCH_PREPROCESSING_STOP_WORDS = {
    "shan",
    "yourself",
    "being",
    "won't",
    "didn",
    "under",
    "here",
    "re",
    "whom",
    "are",
    "how",
    "but",
    "ll",
    "shouldn",
    "which",
    "weren't",
    "who",
    "about",
    "after",
    "do",
    "of",
    "has",
    "ours",
    "having",
    "once",
    "his",
    "its",
    "herself",
    "isn",
    "because",
    "at",
    "you're",
    "her",
    "to",
    "ma",
    "it",
    "own",
    "for",
    "did",
    "he",
    "what",
    "isn't",
    "your",
    "both",
    "then",
    "am",
    "should",
    "hers",
    "you'd",
    "on",
    "some",
    "don't",
    "shouldn't",
    "further",
    "should've",
    "their",
    "didn't",
    "other",
    "she",
    "needn't",
    "needn",
    "that'll",
    "same",
    "doesn't",
    "him",
    "theirs",
    "against",
    "such",
    "couldn't",
    "been",
    "mightn't",
    "why",
    "they",
    "not",
    "my",
    "if",
    "just",
    "couldn",
    "the",
    "won",
    "wouldn",
    "itself",
    "out",
    "you",
    "there",
    "above",
    "she's",
    "our",
    "off",
    "haven't",
    "mightn",
    "yourselves",
    "wasn't",
    "myself",
    "wouldn't",
    "again",
    "below",
    "each",
    "now",
    "himself",
    "be",
    "an",
    "or",
    "more",
    "me",
    "from",
    "these",
    "doing",
    "and",
    "until",
    "them",
    "themselves",
    "mustn't",
    "by",
    "only",
    "haven",
    "hadn",
    "wasn",
    "ourselves",
    "most",
    "before",
    "aren",
    "mustn",
    "when",
    "is",
    "you've",
    "between",
    "those",
    "few",
    "will",
    "through",
    "up",
    "it's",
    "into",
    "very",
    "hadn't",
    "that",
    "all",
    "ve",
    "were",
    "with",
    "no",
    "can",
    "we",
    "hasn't",
    "too",
    "shan't",
    "over",
    "yours",
    "this",
    "you'll",
    "where",
    "any",
    "doesn",
    "weren",
    "as",
    "during",
    "had",
    "in",
    "hasn",
    "was",
    "ain",
    "does",
    "down",
    "aren't",
    "have",
    "so",
    "than",
    "nor",
    "while",
}

# Column -> type, fields (if keyword tokenization)
OPENSEARCH_INDEX_COLUMN_MAPPINGS = {
    "mappings": {
        "properties": {
            "icc_number": {"type": "text"},
            "null_icc_number": {"type": "integer"},
            "carrier_name_formatted": {"type": "text"},
            "carrier_search_term_tokens": {"type": "text"},
            "carrier_search_term_nows": {"type": "text"},
            "cargo_transported": {
                "type": "text",
                "fields": {"keyword": {"type": "keyword", "ignore_above": 256}},
            },
            "equipment_type_transported": {
                "type": "text",
                "fields": {"keyword": {"type": "keyword", "ignore_above": 256}},
            },
            "num_trucks": {"type": "integer"},
            "located_in_geopoint": {"type": "geo_point"},
            "location": {"type": "text"},
            "in_business_for_days": {"type": "integer"},
            "email": {"type": "text"},
            "phone_number": {"type": "keyword"},
            "inspection_crash_counties": {"type": "text"},
            "destination_states": {"type": "text"},
            "num_inspections_last_two_years": {"type": "integer"},
            "num_crashes_last_two_years": {"type": "integer"},
            "min_insurance_coverage_amount": {"type": "integer"},
            "safety_rating": {"type": "text"},
            "hazmat": {"type": "text"},
        }
    }
}

####################################################################
#         unused constants
####################################################################


EQUIPMENT_TYPES = {
    "dry van": "Dry Van",
    "reefer": "Reefer",
    "flatbed": "Flatbed",
    "power only": "Power Only",
    "container": "Container",
    "straight truck": "Dry Van",
    "pup": "Pup",
    "van": "Dry Van",
    "v": "Dry Van",
    "dry": "Dry Van",
    "van - van": "Dry Van",
    "r": "Reefer",
    "p": "Power Only",
    "straight trucks": "Dry Van",
    "rc": "Reefer",
    "tv": "Dry Van",
    "tw": "Reefer",
    "reefer, van": "Reefer",
    "vr": "Dry Van",
    "powerloop": "Power Only",
    'dry van 53"': "Dry Van",
    "tord": "Dry Van",
}

EXTENDED_EQUIPMENT_TYPES = {
    "Step Deck",
    "Double Drop",
    "RGN",
    "Hotshot",
    "Van Double",
    "Conestoga",
    "Removable Gooseneck",
    "Lowboy",
    "Stretch RGN",
    "Van Refrigerated",
    "Auto Carrier",
    "Dump Trailer",
    "Tanker",
    "Hopper Bottom",
    "Pneumatic",
    "Walking Floor",
    "Livestock Carrier",
    "Log Trailer",
}

SHIPMENT_MODES = {
    # From your provided SHIPMENT_MODE_DICT
    "TL": "TL",
    "LTL": "LTL",
    "PARTIAL": "PARTIAL",
    "SMALL PARCEL": "SMALL PARCEL",
    "IMDL": "IMDL",
    "AIR": "AIR",
    "OCEAN": "OCEAN",
    "DRAYAGE": "DRAYAGE",
    "tl": "TL",
    "ltl": "LTL",
    "partial": "PARTIAL",
    "small parcel": "SMALL PARCEL",
    "imdl": "IMDL",
    "air": "AIR",
    "ocean": "OCEAN",
    "drayage": "DRAYAGE",
    "truckload": "TL",
    "truck": "TL",
    "partial load": "PARTIAL",
    "vr": "TL",
    "r": "TL",
    "v": "TL",
    "tord": "TL",
    "ftl": "TL",
}

EXTENDED_SHIPMENT_MODES = {
    # From your provided SHIPMENT_MODE_DICT
    "TL": "TL",
    "LTL": "LTL",
    "PARTIAL": "PARTIAL",
    "SMALL PARCEL": "SMALL PARCEL",
    "IMDL": "IMDL",
    "AIR": "AIR",
    "OCEAN": "OCEAN",
    "DRAYAGE": "DRAYAGE",
    "tl": "TL",
    "ltl": "LTL",
    "partial": "PARTIAL",
    "small parcel": "SMALL PARCEL",
    "imdl": "IMDL",
    "air": "AIR",
    "ocean": "OCEAN",
    "drayage": "DRAYAGE",
    "truckload": "TL",
    "truck": "TL",
    "partial load": "PARTIAL",
    "vr": "TL",
    "r": "TL",
    "v": "TL",
    "tord": "TL",
    "ftl": "TL",
}
