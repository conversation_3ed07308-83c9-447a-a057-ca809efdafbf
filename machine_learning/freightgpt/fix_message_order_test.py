import unittest
from typing import Dict
from typing import List

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()

from machine_learning.freightgpt import fix_message_order


class TestReorderMessages(unittest.TestCase):
    def fill_memory_default(
        self, memory: List[Dict[str, str]], message_order: List[str]
    ):
        "Fills memory list to contain messages according to the given message order"
        counter = {"human": 0, "ai": 0, "system": 0}
        for message_type in message_order:
            counter[message_type] += 1
            memory.append(
                {
                    "type": message_type,
                    "content": f"{message_type} message {counter[message_type]}",
                }
            )

    @parameterized.expand(
        [
            (
                ["system", "system", "human", "ai"],
                ["human", "system", "system", "ai"],
            ),  # Case with one block of system, human, and ai messages in expected order
            (
                ["system", "system", "human", "ai", "system", "system", "human", "ai"],
                ["human", "system", "system", "ai", "human", "system", "system", "ai"],
            ),  # Case with multiple blocks of system, human, and ai messages in expected order
            (
                ["human", "ai"],
                ["human", "ai"],
            ),  # Case with only Human and AI messages
            (
                [],
                [],
            ),  # Case with no messages in the memory
        ]
    )
    def test_human_system_messages_reordering_(
        self, message_order: List[str], correct_message_order: List[str]
    ):
        """Tests cases with valid sequence of messages in input"""

        example_input_json = {"input": "some input", "memory": []}
        self.fill_memory_default(example_input_json["memory"], message_order)

        example_input_json_modified = {"input": "some input", "memory": []}
        self.fill_memory_default(
            example_input_json_modified["memory"], correct_message_order
        )

        self.assertEqual(
            fix_message_order.reorder_messages(example_input_json),
            example_input_json_modified,
        )

    @parameterized.expand(
        [
            (["system", "system", "ai"],),  # Case with AI message before Human message
            (["ai", "ai"],),  # Case with multiple AI messages placed together
            (
                [
                    "human",
                    "human",
                ],
            ),  # Case with multiple Human messages placed together
        ]
    )
    def test_incorrect_input_(self, message_order: List[str]):
        """Tests cases with invalid sequence of messages in input"""

        example_input_json = {"input": "some input", "memory": []}
        self.fill_memory_default(example_input_json["memory"], message_order)

        with self.assertRaises(ValueError):
            fix_message_order.reorder_messages(example_input_json)


# Run the tests
if __name__ == "__main__":
    unittest.main()
