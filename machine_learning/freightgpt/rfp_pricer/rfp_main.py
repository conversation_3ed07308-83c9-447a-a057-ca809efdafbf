# Main logic flows for rfp backend endpoints to map + validate and price files.
import logging
import os

import pandas as pd

from common import init_main

PROJECT_ROOT = init_main.initialize()
from machine_learning.freightgpt.rfp_pricer.constants import S3_CLIENT
from machine_learning.freightgpt.rfp_pricer.constants import REQUIRED_COLUMNS
from machine_learning.freightgpt.rfp_pricer.constants import OPTIONAL_COLUMNS
from machine_learning.freightgpt.rfp_pricer.constants import ERROR_COLUMN
from machine_learning.freightgpt.rfp_pricer.constants import VALID_EQUIPMENT_TYPES
from machine_learning.freightgpt.rfp_pricer.constants import FILE_TO_DF
from machine_learning.freightgpt.rfp_pricer.constants import DF_TO_FILE
from machine_learning.freightgpt.rfp_pricer.constants import MAX_MAPPING_ATTEMPTS
from machine_learning.freightgpt.rfp_pricer.constants import NUMBER_OF_ROWS_LIMIT
from machine_learning.freightgpt.rfp_pricer import rfp_file_validation
from machine_learning.freightgpt.rfp_pricer import rfp_pricing_utils
from machine_learning.freightgpt.rfp_pricer import rfp_llm_mapping
from machine_learning.freightgpt.rfp_pricer import rfp_path_helper


from typing import List, Dict, Tuple, Union
from common.s3_cache_file import S3
import pprint


def get_column_and_equipment_mapping_multi_attempt(
    df: pd.DataFrame,
) -> Tuple[Dict[str, str], Dict[str, str]]:
    """
    Given a Dataframe, maps its columns to a predefined template, preprocesses and validates the DataFrame,
    and returns the mapping if successful.

    Args:
        df: The DataFrame to map and validate.

    Returns:
        column_mapping: A dictionary where keys are original column headers and values are their corresponding template headers,
                 or None if no suitable match is found based on the template.
        equipment_mapping: A dictionary where keys are the original equipment types and the values are the corresponding template values,
                 or None if no mapping is found.
    """

    original_headers = df.columns.tolist()
    sample_data = df.head(3).to_dict(orient="records")

    # Truncate sample data to keep it short
    for sample in sample_data:
        for key, value in sample.items():
            if isinstance(value, str):
                sample[key] = value[:10]

    template_headers = REQUIRED_COLUMNS + OPTIONAL_COLUMNS

    errors_by_column = {}

    for attempt in range(MAX_MAPPING_ATTEMPTS):
        column_mapping = rfp_llm_mapping.get_column_mapping_with_LLM(
            original_headers, sample_data, template_headers, errors_by_column
        )
        mapped_df = df.rename(columns=column_mapping)
        template_columns_matched = list(column_mapping.values())
        mapped_df = mapped_df[template_columns_matched]
        equipment_mapping = rfp_llm_mapping.get_equipment_type_mapping_with_LLM(
            mapped_df
        )
        errors_by_column = rfp_llm_mapping.preprocess_columns(
            mapped_df, equipment_mapping
        )
        if not errors_by_column:
            errors_by_column = rfp_file_validation.validate_columns(mapped_df)
        if errors_by_column:
            logging.error(
                f"********** LLM Column Mapping Issues (Attempt {attempt+1}) **********\n {pprint.pformat(errors_by_column)}"
            )
        else:
            return column_mapping, equipment_mapping

    logging.error(
        f"Failed to map columns and equipment types after {MAX_MAPPING_ATTEMPTS} attempts."
    )
    return column_mapping, equipment_mapping



def price_validated_file(
    path_helper: rfp_path_helper.RfpFilePathHelper,
    message: Dict[str, Union[str, Dict[str, Union[int, float, List[float]]]]],
) -> Dict[str, Union[str, int, Dict[str, int]]]:
    """
    Processes a input file (csv/excel) to validate rows, apply pricing, and store the results in an output file.
    This is the main business logic behind the uvicorn handler endpoint, which is invoked by frontend.

    Args:
        message (dict): A dictionary containing the following keys:
            - 'mappedFilename' (str): The filename of the input file with the mapped data.
            - 'pricingType' (str): The type of pricing to apply (e.g., spot, contract).
            - 'startDate' (str): The start date for the pricing period.
            - 'endDate' (str): The end date for the pricing period.
            - 'markup' (dict): A dictionary containing the following keys:
                - 'targetMarkupPercentage' (float): The target markup percentage for calculating the margin.
                - 'marginBound' (list): The lower and upper bounds for the margin.
            - 'fuel' (dict): A dictionary containing the following keys:
                - 'fuelSurchargeCents' (int): The fuel surcharge in cents per mile.

    Returns:
        dict: A dictionary containing the following keys:
            - 'pricedFilename' (str): The filename of the output file with the priced data.
            - 'errors' (dict): A list of validation errors found with count in the input data.
            - 'lanesGiven' (int): The number of initial rows in the input file.
            - 'lanesPriced' (int): The number of rows sucessfully priced in the output file.
            - 'fatalError' (str or None): The fatal error message if an error occurred, otherwise None.

    Example:
        Input: {
            "mappedFilename": "s3://bucket/mapped_data.csv",
            "pricingType": "spot",
            "startDate": "2023-01-01",
            "endDate": "2023-12-31",
            "markup": {
                "targetMarkupPercentage": 10,
                "marginBound": [50, 100]
            },
            "fuel": {
                "fuelSurchargeCents": 50
            }
        }
        Output: {
            "pricedFilename": "s3://bucket/priced_data.csv",
            "errors": {
                "Equipment type is missing or unmapped.": 5,
                "Required field origin_city is missing.": 2
            },
            "lanesGiven": 100,
            "lanesPriced": 93,
            "fatalError": None
        }
    """

    s3_url_priced = None
    readable_errors_with_count = {}
    total_rows = 0
    priced_rows = 0

    # Extracting necessary information from the message dictionary
    pricing_type = message.get("pricingType", "spot")
    start_date = pd.Timestamp(message.get("startDate"))
    end_date = pd.Timestamp(message.get("endDate"))
    markup = message.get("markup", {})
    target_markup_percentage = markup.get("targetMarkupPercentage", 0.0)
    margin_bound = markup.get("marginBound", [0.0, float("inf")])
    # fuel = message.get("fuel", {})  # Unused for now
    # fuel_surcharge_cents = fuel.get("fuelSurchargeCents", 0)  # Unused for now

    # Reading the mapped file into a DataFrame
    try:
        df = FILE_TO_DF[path_helper.file_extension](path_helper.get_local_file_name('mapped'))
    except Exception as err:
        logging.error(f"Error reading mapped file {path_helper.get_local_file_name('mapped')}: {err}")
        raise ValueError(f"Error reading mapped file {path_helper.get_local_file_name('mapped')}.")

    # Add Error column to the df and initialize it to pd.NA
    df[ERROR_COLUMN] = pd.NA

    # Identifying the invalid rows and storing the validation errors
    try:
        readable_errors_with_count = rfp_file_validation.identify_invalid_rows(df)
    except Exception as err:
        logging.error(f"Error identifying invalid rows: {err}")
        raise ValueError("Error identifying invalid rows.")

    # Pricing the rows based on the specified pricing type, start date, and end date
    try:
        prices = rfp_pricing_utils.batch_price(
            df, pricing_type, start_date, end_date
        )
    except Exception as err:
        logging.error(f"Error pricing rows: {err}")
        raise ValueError("Error pricing rows.")

    # Reading the original raw file into a DataFrame and resetting the index for consistency
    try:
        org_df = FILE_TO_DF[path_helper.file_extension](path_helper.get_local_file_name('raw'))
        org_df.reset_index(drop=True, inplace=True)
    except Exception as err:
        logging.error(f"Error reading input file {path_helper.get_local_file_name('raw')}: {err}")
        raise ValueError("Error reading input file.")

    org_shape = org_df.shape

    # Extracting the error messages
    error_messages = df[ERROR_COLUMN]

    # Working with the original dataframe for the rest of the process
    df = org_df

    # Merging the prices with the DataFrame
    try:
        df["Truck Cost"] = prices
    except Exception as err:
        logging.error(f"Error merging prices: {err}")
        raise ValueError("Error merging prices.")

    # Calculating and storing the margin, truck cost with margin, fuel cost, and linehaul cost in the DataFrame
    try:
        if target_markup_percentage > 0:
            rfp_pricing_utils.calculate_and_store_margin_and_fuel_costs(
                df,
                target_markup_percentage,
                margin_bound,
                # fuel_surcharge_cents,
            )
    except Exception as err:
        logging.error(f"Error calculating margins and fuel costs: {err}")
        raise ValueError("Error calculating margins and fuel costs.")

    # Improve final spreadsheet readability
    def format_price_column(column_name: str) -> None:
        """Formats the given column as a price column by adding '$' sign and rounding the value to two decimal places."""
        if column_name in df.columns:
            df[column_name] = df[column_name].apply(
                lambda x: f"${x:.2f}" if pd.notna(x) else x
            )

    try:
        format_price_column("Truck Cost")
        format_price_column("Margin")
        format_price_column("Truck Cost with Margin")
        # format_price_column("fuel_cost")
        # format_price_column("linehaul_cost")

    except Exception as err:
        logging.error(f"Error formatting price columns: {err}")
        raise ValueError("Error formatting price columns.")

    # Add error messages to the DataFrame
    df[ERROR_COLUMN] = error_messages

    # Counting total number of rows and number of rows successfully priced
    total_rows = df.shape[0]
    priced_rows = total_rows - df[ERROR_COLUMN].notna().sum()

    # Check if the final dataframe has the expected number of columns
    fin_shape = df.shape
    assert (
        fin_shape[1] == org_shape[1] + 2 or fin_shape[1] == org_shape[1] + 4
    ), "Number of columns in the final dataframe is not as expected."

    # Writing the output DataFrame to the local output file
    try:
        DF_TO_FILE[path_helper.file_extension](df, path_helper.get_local_file_name('priced'), index=False)
    except Exception as err:
        logging.error(f"Error writing priced file {path_helper.get_local_file_name('priced')}: {err}")
        raise ValueError("Error writing priced file.")

    # # Deleting the local input and output files
    # try:
    #     os.remove(local_raw_file)
    #     os.remove(local_mapped_file)
    #     os.remove(local_priced_file)
    # except Exception as err:
    #     logging.error(f"Error deleting local files: {err}")
    #     raise ValueError("Error deleting local files.")

    results = {
        "pricedFilename": s3_url_priced,
        "errors": readable_errors_with_count,
        "lanesGiven": int(total_rows),
        "lanesPriced": int(priced_rows),
    }

    return results


def map_and_validate_source_file(
    path_helper: rfp_path_helper.RfpFilePathHelper,
    message: Dict[str, Union[str, Dict[str, str]]],
) -> Dict[
    str, Union[str, Dict[str, Union[str, bool]], List[str], Dict[str, List[str]]]
]:
    """
    Maps and validates columns in a raw input file (csv/excel), and stores the validated results in an output file.
    This is the main business logic behind the uvicorn handler endpoint, which is invoked by frontend.

    Args:
        message (Dict): A dictionary containing the following keys:
            - 'sourceFilename' (str): The filename of the raw input file.
            - 'userSpecifiedColumnMapping' (dict, optional): User-specified mapping for columns.
            - 'userSpecifiedEquipmentTypeMapping' (dict, optional): User-specified mapping for equipment types.

    Returns:
        Dict: A dictionary containing the following keys:
            - 'mappedFilename' (str): The filename of the output file with the validated data.
            - 'columnMapping' (dict): The mapping of required and optional columns.
                - 'sourceCol': The original column name.
                - 'required': True if the column is required, False otherwise.
                - 'error': The error message if an error occurred, otherwise None.
            - 'sourceColumns' (list): A list of column names from the input file.
            - 'equipmentTypeMapping' (dict): The mapping of equipment types.
            - 'sourceEquipmentTypes' (list): A list of equipment types from the input data.
            - 'fatalError' (str or None): The fatal error message if an error occurred, otherwise None.

    Example:
        Input: {
            "sourceFilename": "s3://bucket/raw_data.csv",
            "userSpecifiedColumnMapping": {
                "original_col1": "template_col1",
                "original_col2": "template_col2"
            },
            "userSpecifiedEquipmentTypeMapping": {
                "equipment1": "Dry Van",
                "equipment2": "Reefer"
            }
        }
        Output: {
            "mappedFilename": "s3://bucket/mapped_data.csv",
            "columnMapping": {
                "template_col1": {
                    "sourceCol": "original_col1",
                    "required": True,
                    "error": None
                },
                "template_col2": {
                    "sourceCol": "original_col2",
                    "required": False,
                    "error": None
                }
            },
            "sourceColumns": ["original_col1", "original_col2"],
            "equipmentTypeMapping": {
                "Dry Van": ["equipment1"],
                "Reefer": ["equipment2"],
                "Other": []
            },
            "sourceEquipmentTypes": ["equipment1", "equipment2"],
            "fatalError": None
        }
    """

    # Initialize variables to store output filename, errors, and mappings
    column_mapping = {}
    equipment_mapping = {}
    reverse_column_mapping = {}
    source_columns = []
    errors_by_column = {}

    # Extracting the necessary information from the 'message' dictionary
    user_specified_column_mapping = message.get("userSpecifiedColumnMapping", {})
    user_specified_equipment_type_mapping = message.get(
        "userSpecifiedEquipmentTypeMapping", {}
    )

    # Reading the input file into a pandas DataFrame and resetting the index to maintain consistency
    try:
        df = FILE_TO_DF[path_helper.file_extension](path_helper.get_local_file_name('raw'))
        df.reset_index(drop=True, inplace=True)
    except Exception as err:
        logging.error(f"Error reading input file {path_helper.get_local_file_name('raw')}: {err}")
        raise ValueError("Error reading input file.")

    # Checking if the number of rows in the input file exceeds the limit
    if df.shape[0] > NUMBER_OF_ROWS_LIMIT:
        raise ValueError(
            f"Number of rows in the input file exceeds the limit of {NUMBER_OF_ROWS_LIMIT}."
        )

    # Extracting the source columns from the DataFrame and mapping them to template columns
    source_columns = list(df.columns)
    column_mapping, equipment_mapping = {}, {}
    try:
        if user_specified_column_mapping:
            column_mapping = user_specified_column_mapping
        else:
            (
                column_mapping,
                equipment_mapping,
            ) = get_column_and_equipment_mapping_multi_attempt(df)

        df.rename(columns=column_mapping, inplace=True)
        template_columns_matched = list(column_mapping.values())
        df = df[template_columns_matched]
    except Exception as err:
        logging.error(f"Error mapping columns: {err}")
        raise ValueError("Error mapping columns.")

    # Mapping the equipment types based on user-specified mapping or using LLM algorithm
    try:
        if user_specified_equipment_type_mapping:
            equipment_mapping = user_specified_equipment_type_mapping
        elif not equipment_mapping:
            equipment_mapping = rfp_llm_mapping.get_equipment_type_mapping_with_LLM(
                df
            )
    except Exception as err:
        logging.error(f"Error mapping equipment types: {err}")
        raise ValueError("Error mapping equipment types.")

    # Preprocessing the columns and capturing any errors encountered
    try:
        errors_by_column = rfp_llm_mapping.preprocess_columns(df, equipment_mapping)
    except Exception as err:
        logging.error(f"Error preprocessing columns: {err}")
        raise ValueError("Error preprocessing columns.")

    # Validating the columns and capturing any errors encountered
    try:
        if not errors_by_column:
            errors_by_column = rfp_file_validation.validate_columns(df)
    except Exception as err:
        logging.error(f"Error validating columns: {err}")
        raise ValueError("Error validating columns.")

    # Logging the errors encountered during preprocessing and validation steps
    for col, error in errors_by_column.items():
        logging.warning(f"{col}: {error}")

    # Generating the reverse column mapping for reference
    reverse_column_mapping = {v: k for k, v in column_mapping.items()}

    # Writing the output DataFrame to the local output file
    try:
        DF_TO_FILE[path_helper.file_extension](df, path_helper.get_local_file_name('mapped'), index=False)
    except Exception as err:
        logging.error(f"Error writing mapped file {path_helper.get_local_file_name('mapped')}: {err}")
        raise ValueError("Error writing mapped file.")

    # Deleting the local input and output files
    try:
        os.remove(path_helper.get_local_file_name('raw'))
        os.remove(path_helper.get_local_file_name('mapped'))
    except Exception as err:
        logging.error(f"Error deleting local files: {err}")
        raise ValueError("Error deleting local files.")

    column_mapping_with_errors = {}
    for col in REQUIRED_COLUMNS:
        column_mapping_with_errors[col] = {
            "sourceCol": reverse_column_mapping.get(col, None),
            "required": True,
            "error": errors_by_column.get(col, None),
        }
    for col in OPTIONAL_COLUMNS:
        column_mapping_with_errors[col] = {
            "sourceCol": reverse_column_mapping.get(col, None),
            "required": False,
            "error": errors_by_column.get(col, None),
        }

    reverse_equipment_mapping = {k: [] for k in VALID_EQUIPMENT_TYPES}
    reverse_equipment_mapping["Other"] = []
    for equipment, equipment_type in equipment_mapping.items():
        if equipment_type in VALID_EQUIPMENT_TYPES:
            reverse_equipment_mapping[equipment_type].append(equipment)
        else:
            reverse_equipment_mapping["Other"].append(equipment)

    results = {
        "mappedFilename": path_helper.get_local_file_name("mapped"),
        "columnMapping": column_mapping_with_errors,
        "sourceColumns": source_columns,
        "equipmentTypeMapping": reverse_equipment_mapping,
        "sourceEquipmentTypes": list(equipment_mapping.keys()),
    }

    return results


def run_file_pricing(message: Dict[str, Union[str, Dict[str, Union[int, float, List[float]]]]]) -> Dict[str, Union[str, int, Dict[str, int]]]:
    """
    Wraps the file pricing operation with the required S3 operations
    1. Downloads a raw file from an S3 URL.
    2. Maps and validates the source file using the provided message and path helper.
    3. Uploads the mapped file to an S3 bucket.
    4. Handles any errors that occur during the process and logs them.
    Args:
        message (Dict[str, Union[str, Dict[str, Union[int, float, List[float]]]]]): 
            A dictionary containing the source filename and other request information.
    Returns:
        Dict[str, Union[str, int, Dict[str, int]]]: 
            A dictionary containing the result of the mapping operation. If a fatal error occurs, 
            the dictionary will include a "fatalError" key with the error message.
    """
    fatal_error = None
    try:
        s3_url_mapped = message.get("mappedFilename")
        path_helper = rfp_path_helper.RfpFilePathHelper(s3_url_mapped)

        # Download the raw file from S3 bucket
        try:
            S3.download_file_from_url(path_helper.get_s3_path('raw'), path_helper.get_local_file_name('raw'), S3_CLIENT)
        except Exception as err:
            raise ValueError(f"Error downloading input file {path_helper.get_s3_path('raw')}.")

        # Download the mapped file from S3 bucket
        try:
            S3.download_file_from_url(s3_url_mapped, path_helper.get_local_file_name('mapped'), S3_CLIENT)
        except Exception as err:
            raise ValueError(f"Error downloading mapped file {s3_url_mapped}.")

        # Price local files    
        result = price_validated_file(
            path_helper=path_helper,
            message=message
        )

        # Upload the output file to the S3 bucket
        try:
            S3.upload_file_to_url(path_helper.get_local_file_name('priced'), path_helper.get_local_file_name('priced'), S3_CLIENT)
        except Exception as err:
            raise ValueError("Error uploading priced file.")

    except Exception as err:
        fatal_error = str(err)
        logging.error(f"Fatal error: {str(err)}")

    if fatal_error: result["fatalError"] = fatal_error
    return result


def run_file_mapping(message: Dict[str, Union[str, Dict[str, Union[int, float, List[float]]]]]) -> Dict[str, Union[str, int, Dict[str, int]]]:
    """
    Wraps the file mapping operation with the required S3 operations
    1. Downloads a raw file from an S3 URL.
    2. Maps and validates the source file using the provided message and path helper.
    3. Uploads the mapped file to an S3 bucket.
    4. Handles any errors that occur during the process and logs them.
    Args:
        message (Dict[str, Union[str, Dict[str, Union[int, float, List[float]]]]]): 
            A dictionary containing the source filename and other request information.
    Returns:
        Dict[str, Union[str, int, Dict[str, int]]]: 
            A dictionary containing the result of the mapping operation. If a fatal error occurs, 
            the dictionary will include a "fatalError" key with the error message.
    """
    s3_url_source = message.get("sourceFilename")
    path_helper = rfp_path_helper.RfpFilePathHelper(s3_url_source)
    fatal_error = None
    try:
        # Download the raw file from s3
        try:
            S3.download_file_from_url(s3_url_source, path_helper.get_local_file_name('raw'), S3_CLIENT)
        except Exception as err:
            raise ValueError("Error downloading input file.")
        
        result = map_and_validate_source_file(
            path_helper=path_helper,
            message=message
        )
        # Upload the output file to the S3 bucket
        try:
            S3.upload_file_to_url(path_helper.get_local_file_name('mapped'), path_helper.get_local_file_name('mapped'), S3_CLIENT)
        except Exception as err:
            raise ValueError("Error uploading mapped file.")
    
    except Exception as err:
        fatal_error = str(err)
        logging.error(f"Fatal error: {str(err)}")
    
    if fatal_error: result["fatalError"] = fatal_error
    return result


def main():
    validation_test_url = "s3://fgpt-rfp-files/e428a418-d031-70de-eb94-2a5c2d75066d/raw_1729182007931LIDLInboundRFQNovember2024-February2025-Internal.xlsx"
    pathhelper = rfp_path_helper.RfpFilePathHelper(validation_test_url)
    try:
        S3.download_file_from_url(validation_test_url, pathhelper.get_local_file_name('raw'), S3_CLIENT)
    except Exception:
        raise ValueError("Error downloading input file.")
    results = map_and_validate_source_file(
        path_helper=pathhelper,
        message={
            "sourceFilename": validation_test_url,
        },
    )
    pprint.pprint(results)

    # pricing_test_url = "s3://fgpt-rfp-files/e428a418-d031-70de-eb94-2a5c2d75066d/mapped_1729182007931LIDLInboundRFQNovember2024-February2025-Internal.xlsx"
    # results = price_validated_file(
    #     path_helper=RfpFilePathHelper(pricing_test_url),
    #     message={
    #         "mappedFilename": "s3://fgpt-rfp-files/e428a418-d031-70de-eb94-2a5c2d75066d/mapped_1729182007931LIDLInboundRFQNovember2024-February2025-Internal.xlsx",
    #         "pricingType": "spot",
    #         "startDate": "2023-01-01",
    #         "endDate": "2023-01-31",
    #         "markup": {
    #             "targetMarkupPercentage": 10,
    #             "marginBound": [100, 500],
    #         },
    #     }
    # )
    # pprint.pprint(results)


if __name__ == "__main__":
    main()
