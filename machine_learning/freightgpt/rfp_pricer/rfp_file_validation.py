# Validate the columns of the RFP DataFrame and identify invalid rows based on specified criteria.
from typing import Dict

import pandas as pd

from machine_learning.freightgpt.rfp_pricer.constants import ERROR_COLUMN
from machine_learning.freightgpt.rfp_pricer.constants import HUMAN_READABLE_COLUMN_NAME
from machine_learning.freightgpt.rfp_pricer.constants import REQUIRED_COLUMNS
from machine_learning.freightgpt.rfp_pricer.constants import VALID_EQUIPMENT_TYPES


def validate_columns(df: pd.DataFrame) -> Dict[str, str]:
    """
    Validates the columns of the given DataFrame against specified criteria, including required columns presence,
    non-empty required columns, presence of derived country columns, and correct state column formats.

    Args:
        df: The DataFrame to validate.

    Returns:
        errors_by_column: Dictionary containing the error encountered for each column (if any).
    """

    errors_by_column = {}

    columns_set = set(df.columns)

    # Required columns should be present and not be empty
    for col in REQUIRED_COLUMNS:
        if col not in columns_set:
            errors_by_column[
                col
            ] = f"Required column '{HUMAN_READABLE_COLUMN_NAME[col]}' is not mapped."
        if df[col].isna().all():
            errors_by_column[
                col
            ] = f"Required column '{HUMAN_READABLE_COLUMN_NAME[col]}' is empty."

    for col_pre in ("origin", "destination"):
        # Origin and destination countries should be present and not be empty
        if f"{col_pre}_country" not in columns_set:
            errors_by_column[
                f"{col_pre}_state"
            ] = f"'{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_country']}' column could not be mapped implying an issue with '{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' column."
        if df[f"{col_pre}_country"].isna().all():
            errors_by_column[
                f"{col_pre}_state"
            ] = f"'{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_country']}' column is empty implying an issue with '{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' column."

        # State should be a two-digit code
        if (
            not df[f"{col_pre}_state"]
            .apply(lambda x: isinstance(x, str) and len(x) == 2)
            .any()
        ):
            errors_by_column[
                f"{col_pre}_state"
            ] = f"'{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' column must contain two-digit state codes."

    return errors_by_column


def identify_invalid_rows(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies rows in the DataFrame that contain invalid data based on specified criteria.
    - Presence of required columns and non-NA values in them.
    - Origin and destination countries must be US.
    - State columns must contain 2-digit codes.
    - Non-negative distance in miles if available.

    Args:
        df: The DataFrame to identify invalid rows in.

    Returns:
        readable_errors_with_count: Dictionary containing readable errors with count.
    """

    readable_errors_with_count = {}

    def write_error_message_and_count_rows(
        condition: pd.core.series.Series, error_message: str
    ) -> None:
        """
        Writes the error message to the DataFrame for the given condition and logs the count of rows with the error.

        Args:
            condition: The condition to check for the error.
            error_message: The error message to log for the condition.
        """
        df.loc[condition, ERROR_COLUMN] = df.loc[condition, ERROR_COLUMN].apply(
            lambda x: f"{x}; {error_message}" if pd.notna(x) else error_message
        )
        readable_errors_with_count[error_message] = int(condition.sum())

    # Identify NAs, we want required columns and countries
    for col in REQUIRED_COLUMNS:
        write_error_message_and_count_rows(
            df[col].isna(),
            f"Required field '{HUMAN_READABLE_COLUMN_NAME[col]}' is missing",
        )

    for col_pre in ("origin", "destination"):
        # Only price US to US
        write_error_message_and_count_rows(
            df[f"{col_pre}_country"].isna(),
            f"Unable to map '{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_country']}' field implying an issue with '{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' field",
        )
        write_error_message_and_count_rows(
            df[f"{col_pre}_country"] != "US",
            f"'{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' is not in the US",
        )

        # State should be a two-digit code
        write_error_message_and_count_rows(
            df[f"{col_pre}_state"].apply(
                lambda x: not (isinstance(x, str) and len(x) == 2)
            ),
            f"'{HUMAN_READABLE_COLUMN_NAME[f'{col_pre}_state']}' field is not a two-digit state code",
        )

    # Equipment Type should be in VALID_EQUIPMENT_TYPES
    write_error_message_and_count_rows(
        df["equipment_type"].apply(lambda x: x not in VALID_EQUIPMENT_TYPES),
        "Equipment type is not 'Dry Van' or 'Reefer'",
    )

    # Distance miles should be non-negative
    if "distance_miles" in df.columns:
        write_error_message_and_count_rows(
            (df["distance_miles"].notna()) & (df["distance_miles"] < 0),
            f"'{HUMAN_READABLE_COLUMN_NAME['distance_miles']}' field is negative",
        )

    return readable_errors_with_count
