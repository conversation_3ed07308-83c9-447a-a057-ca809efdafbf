import logging

from machine_learning.freightgpt.rfp_pricer.constants import FILE_TO_DF

VALID_PREFIXES = ["raw", "mapped", "priced"]

class RfpFilePathHelper:
    """
    A helper class for managing and manipulating file paths for the RFP pricer.
    Attributes:
        base_filename (str): The base name of the file without the path.
        base_filepath (str): The directory path of the file (including the extension).
        file_extension (str): The file extension.
    Methods:
        __init__(path: str):
            Initializes the RfpFilePathHelper instance by parsing the provided file path.
        get_s3_path(prefix: str) -> str:
            Constructs and returns the S3 path for the file with the given prefix.
        get_local_file_name(prefix: str) -> str:
            Constructs and returns the local file name for the file with the given prefix.
    """
    
    base_filename, base_filepath, file_extension = "", "", ""

    def __init__(self, path: str):
        try:
            split_path = path.split("/")
            self.base_filepath = "/".join(split_path[:-1])
            self.base_filename = split_path[-1]
            self.file_extension = path.split(".")[-1]

            if self.file_extension not in FILE_TO_DF:
                raise ValueError(f"Unsupported file format: {self.file_extension}")

            if (
                self.base_filename.startswith("raw_")
                or self.base_filename.startswith("mapped_")
                or self.base_filename.startswith("priced_")
            ):
                self.base_filename = "_".join(self.base_filename.split("_")[1:])

        except Exception as err:
            logging.error(f"Error parsing mapped file name: {err}")
            raise ValueError("Error parsing mapped file name.")

    def get_s3_path(self, prefix):
        assert prefix in VALID_PREFIXES, "File prefix is invalid"
        return (
            f"{self.base_filepath}/{prefix}_{self.base_filename}"
        )

    def get_local_file_name(self, prefix):
        assert prefix in VALID_PREFIXES, "File prefix is invalid"
        return f"local_{prefix}_{self.base_filename}"
