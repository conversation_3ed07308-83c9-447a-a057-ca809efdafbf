#  Utilities to enable usage of pricer with rfp spreadsheets.
import logging

import pandas as pd

from common import init_main
from machine_learning.freightgpt.rfp_pricer.constants import CONTRACT_PRICER
from machine_learning.freightgpt.rfp_pricer.constants import ERROR_COLUMN
from machine_learning.freightgpt.rfp_pricer.constants import OP<PERSON>ONAL_COLUMNS
from machine_learning.freightgpt.rfp_pricer.constants import REQUIRED_COLUMNS

PROJECT_ROOT = init_main.initialize()

from machine_learning.freightgpt.langchain_tools import (
    PRICER,
)

from machine_learning.freightgpt.constants import GMAPS_UTILS

from machine_learning.price_recommendation.scripts.contract_price_predict import (
    batch_contract_price_historical,
)

from typing import List, Dict, Tuple
import threading


def get_data_dict_spot(row: pd.Series) -> Dict[str, str]:
    """
    Extracts data from a pandas Series row into a dictionary for the spot pricer.

    Args:
        row: A pandas Series representing a row of data.

    Returns:
        data_dict: A dictionary containing extracted data from the row.
    """

    data_dict = {
        col: row[col]
        for col in REQUIRED_COLUMNS
        + OPTIONAL_COLUMNS
        + ["origin_close_time", "origin_country", "destination_country"]
        if col in row and "zip" not in col
    }

    data_dict["index"] = row.name
    data_dict["pricing_type"] = "spot"

    return data_dict


def batch_price(
    df: pd.DataFrame,
    pricing_type: str,
    start_date: pd.Timestamp,
    end_date: pd.Timestamp,
) -> List[float]:
    """
    Performs batch pricing prediction using a PRICER object based on data extracted from the DataFrame.

    Args:
        df: The DataFrame containing data for pricing prediction.
        pricing_type: The type of pricing to apply (e.g., spot, contract).
        start_date: The start date for the pricing period.
        end_date: The end date for the pricing period.

    Returns:
        prices: A list of prices predicted for each record in the DataFrame.
    """

    if pricing_type == "spot":
        prices = [pd.NA] * df.shape[0]
        batch_predict_inputs = (
            df.drop(df[df[ERROR_COLUMN].notna()].index)
            .apply(get_data_dict_spot, axis=1)
            .tolist()
        )
        results = PRICER.batch_predict(batch_predict_inputs)
        for record, result in zip(batch_predict_inputs, results):
            if pd.isna(result["value"]):
                df.at[record["index"], ERROR_COLUMN] = "Error pricing record."
            else:
                prices[record["index"]] = result["value"]

    elif pricing_type == "contract":

        def price_record(
            index: int,
            row: pd.Series,
            start_date: pd.Timestamp,
            end_date: pd.Timestamp,
            prices_param: List[float],
        ):
            """
            Retrieves the historical contract price for a given record.

            Args:
                index (int): The index of the record.
                row (pd.Series): The row containing the record data.
                start_date (pd.Timestamp): The start date for the historical price.
                end_date (pd.Timestamp): The end date for the historical price.
                prices (List[float]): The list to store the retrieved prices.

            Returns:
                None: This function does not return any value. It updates the prices list with the result.
            """
            try:
                result = batch_contract_price_historical(
                    origin_city=row["origin_city"],
                    origin_state=row["origin_state"],
                    destination_city=row["destination_city"],
                    destination_state=row["destination_state"],
                    pricer=CONTRACT_PRICER,
                    gmaps_utils=GMAPS_UTILS,
                    equipment_type=row["equipment_type"],
                    start_date=start_date,
                    end_date=end_date,
                )
            except Exception as err:
                logging.error(f"Error pricing record {index}: {err}")
                result = pd.NA
                df.at[index, ERROR_COLUMN] = "Error pricing record."
            prices_param[index] = result

        prices = [pd.NA] * df.shape[0]
        threads = []
        for index, row in df.iterrows():
            if pd.isna(row["Error"]):
                thread = threading.Thread(
                    target=price_record, args=(index, row, start_date, end_date, prices)
                )
                thread.start()
                threads.append(thread)

        for thread in threads:
            thread.join()

    elif pd.isna(pricing_type):
        raise ValueError("Pricing type is not specified.")

    else:
        raise ValueError(f"Unsupported pricing type: {pricing_type}")

    logging.info(f"Records priced successfully: {len(prices)}")
    return prices


def calculate_and_store_margin_and_fuel_costs(
    df: pd.DataFrame,
    target_markup_percentage: float,
    margin_bound: Tuple[float, float],
    # fuel_surcharge_cents: int,
) -> None:
    """
    Calculates and stores the margin, truck cost with margin, fuel cost, and linehaul cost in the given DataFrame.

    Args:
        df: The DataFrame containing the data.
        target_markup_percentage: The target markup percentage for calculating the margin.
        margin_bound: The lower and upper bounds for the margin in dollars.
        fuel_surcharge_cents: The fuel surcharge in cents for calculating the fuel cost.
    """

    # Calculate margin
    df["Margin"] = df["Truck Cost"] * target_markup_percentage / 100
    df["Margin"] = df["Margin"].clip(lower=margin_bound[0], upper=margin_bound[1])

    # Calculate truck cost with margin
    df["Truck Cost with Margin"] = df["Truck Cost"] + df["Margin"]

    # Calculate fuel cost
    # df["fuel_cost"] = df["distance_miles"] * fuel_surcharge_cents / 100

    # Calculate linehaul cost
    # df["linehaul_cost"] = df["Truck Cost with Margin"] - df["fuel_cost"]
