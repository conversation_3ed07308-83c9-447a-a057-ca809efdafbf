# Use LLM to map user-provided column headers to template headers and equipment types to expected values.
import logging
import threading

import pandas as pd
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import Chat<PERSON><PERSON>ptT<PERSON>plate
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>

from common import init_main
from common.constants import EQUIPMENT_TYPE_DICT
from common.constants import EquipmentType

PROJECT_ROOT = init_main.initialize()

from machine_learning.freightgpt.rfp_pricer.constants import (
    COLUMN_MAPPING_LLM_TEMPERATURE,
    EQUIPMENT_MAPPING_LLM_TEMPERATURE,
    HUMAN_READABLE_COLUMN_NAME,
    PROMPT_MAP_COLUMNS,
    PROMPT_MAP_EQUIPMENT_TYPE,
)
from machine_learning.price_recommendation import preprocessing
from machine_learning.freightgpt.constants import LANGCHAIN_MODEL

from machine_learning.freightgpt.constants import GMAPS_UTILS

from typing import List, Dict, Union, Tuple, Callable


def pack_origin_destination_city_state(
    origin_city: str, origin_state: str, destination_city: str, destination_state: str
) -> Tuple[str, str]:
    """
    Packs origin and destination city and state into a tuple of two strings.

    Args:
        origin_city: The origin city.
        origin_state: The origin state.
        destination_city: The destination city.
        destination_state: The destination state.

    Returns:
        packed_tuple: A tuple containing the origin and destination city and state.
    """

    return (
        f"{origin_city}, {origin_state}",
        f"{destination_city}, {destination_state}",
    )


def get_distance_miles_dict(df: pd.DataFrame) -> Dict[Tuple[str, str], float]:
    """
    Calculates the distance in miles between origin and destination cities in a DataFrame.

    Args:
        df: DataFrame containing the following columns:
            - origin_city
            - origin_state
            - destination_city
            - destination_state


    Returns:
        distance_miles_dict: A dictionary mapping pairs of origin and destination cities to their corresponding distances in miles.

    Example:
        Input:
            pd.DataFrame({
                "origin_city": ["New York", "Los Angeles"],
                "origin_state": ["NY", "CA"],
                "destination_city": ["Chicago", "Houston"],
                "destination_state": ["IL", "TX"]
            })

        Output:
            {
                ("New York, NY", "Chicago, IL"): 790.0,
                ("Los Angeles, CA", "Houston, TX"): 1370.0
            }
    """

    # Combine city and state into a single string for GMAPS_UTILS.get_distance_miles
    source_destination_pairs = df.apply(
        lambda row: pack_origin_destination_city_state(
            row["origin_city"],
            row["origin_state"],
            row["destination_city"],
            row["destination_state"],
        ),
        axis=1,
    ).unique()

    def fetch_distance(
        origin: str,
        destination: str,
        distance_miles_dict_param: Dict[Tuple[str, str], float],
    ) -> None:
        """
        Fetches the distance in miles between the given origin and destination.

        Args:
            origin: The origin location in the format "city, state".
            destination: The destination location in the format "city, state".
            distance_miles_dict: A dictionary to store the distance in miles between origin and destination.

        Returns:
            None: This function does not return any value. It updates the distance_miles_dict with the calculated distance.
        """
        try:
            result = GMAPS_UTILS.get_distance_miles(
                origin=origin,
                destination=destination,
                use_cache=False,
            )
        except Exception as err:
            logging.warning(
                f"Error fetching distance between '{origin}' and '{destination}': {err}"
            )
            result = pd.NA
        if pd.notna(result):
            distance_miles_dict_param[(origin, destination)] = round(result)

    distance_miles_dict = {}

    threads = []
    for origin, destination in source_destination_pairs:
        thread = threading.Thread(
            target=fetch_distance, args=(origin, destination, distance_miles_dict)
        )
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

    return distance_miles_dict


def get_equipment_type_mapping_with_LLM(df: pd.DataFrame) -> Dict[str, str]:
    """
    Generates a dictionary mapping unique equipment types from the DataFrame to their corresponding template values.

    Args:
        df: A DataFrame containing all the records to be priced.

    Returns:
        mapping: A dictionary where the keys are the original equipment types and the values are
                 the corresponding template values or None if no mapping is found.
    """

    unique_equipment_types = df["equipment_type"].unique().tolist()

    prompt_template = ChatPromptTemplate.from_template(PROMPT_MAP_EQUIPMENT_TYPE)
    llm = ChatOpenAI(
        model=LANGCHAIN_MODEL, temperature=EQUIPMENT_MAPPING_LLM_TEMPERATURE
    )
    llm_chain = prompt_template | llm | JsonOutputParser()
    mapping = llm_chain.invoke(
        input={
            "raw_equipment_types": str(unique_equipment_types),
            "equipment_types": str(EquipmentType),
            "equipment_type_dict": str(EQUIPMENT_TYPE_DICT),
        }
    )

    return mapping


def get_column_mapping_with_LLM(
    original_headers: List[str],
    sample_data: List[Dict[str, str]],
    template_headers: List[str],
    issues: Dict[str, str] = {},
) -> Dict[str, str]:
    """
    Generates a mapping dictionary based on provided column headers and sample data,
    using a template for guidance and considering any specified issues.

    Args:
        original_headers: List of original column headers to be mapped.
        sample_data: Sample data dictionary containing actual values for the columns.
        template_headers: List of template column headers to map the original headers to.
        issues: Any issues identified with the previous iteration of this mapping process.

    Returns:
        mapping: A dictionary where keys are original column headers and values are their corresponding template headers,
                 or None if no suitable match is found based on the template.
    """

    prompt_template = ChatPromptTemplate.from_template(PROMPT_MAP_COLUMNS)
    llm = ChatOpenAI(model=LANGCHAIN_MODEL, temperature=COLUMN_MAPPING_LLM_TEMPERATURE)
    llm_chain = prompt_template | llm | JsonOutputParser()
    mapping = llm_chain.invoke(
        input={
            "original_headers": str(original_headers),
            "template_headers": str(template_headers),
            "sample_data": str(sample_data),
            "issues": str(issues),
        }
    )

    return mapping


def map_equipment_type(
    equipment_type: str, local_equipment_type_dict: Dict[str, str]
) -> Union[str, pd._libs.missing.NAType]:
    """
    Maps a given equipment type to its corresponding value using a local dictionary.
    Used with the DataFrame.apply() function.

    Args:
        equipment_type: The raw equipment type to be mapped.
        local_equipment_type_dict: The dictionary containing the mapping of equipment types.

    Returns:
        mapped_type: The mapped equipment type value or pd.NA if the input is invalid or not found in the dictionary.
    """

    if pd.isna(equipment_type) or not equipment_type:
        return pd.NA
    elif equipment_type in local_equipment_type_dict:
        return local_equipment_type_dict[equipment_type]
    else:
        logging.warning(f"Equipment type '{equipment_type}' not found in dictionary")
        return pd.NA


def preprocess_columns(
    df: pd.DataFrame, local_equipment_type_dict: Dict[str, str]
) -> None:
    """
    Preprocesses the columns of the given DataFrame, standardizing city and state formats,
    deriving country information, mapping equipment types, and handling missing dates.

    Args:
        df: The DataFrame to preprocess.
        local_equipment_type_dict: The dictionary containing the mapping of equipment types.

    Returns:
        errors_by_column: Dictionary containing the error encountered for each column (if any).
    """

    errors_by_column = {}

    def safe_df_operation(
        source_column: str,
        operation: Callable,
        operation_args: Tuple[Dict[str, str]] = None,
        target_column: str = "",
    ) -> None:
        """
        Safely applies an operation on a DataFrame column, catching and logging any exceptions.

        Args:
            source_column (str): The column to apply the operation on.
            operation (Callable): The operation to apply on the column.
            operation_args (Tuple[Dict[str, str]], optional): Additional arguments to pass to the operation. Defaults to None.
            target_column (str, optional): The column to store the result of the operation. If None, the result will be stored in the source column. Defaults to "".
        """

        if not target_column:
            target_column = source_column

        try:
            df[target_column] = df[source_column].apply(operation, args=operation_args)
        except Exception as err:
            logging.warning(f"Error preprocessing column '{target_column}': {err}")
            errors_by_column[
                source_column
            ] = f"Error preprocessing column '{HUMAN_READABLE_COLUMN_NAME[target_column]}'."

    # Standardize city and state formats
    safe_df_operation("origin_city", str)  # Convert to string
    safe_df_operation("origin_city", str.strip)  # Strip whitespace
    safe_df_operation("origin_city", str.title)  # Capitalize first letter

    safe_df_operation("destination_city", str)  # Convert to string
    safe_df_operation("destination_city", str.strip)  # Strip all whitespace
    safe_df_operation("destination_city", str.title)  # Capitalize first letter

    safe_df_operation("origin_state", str)  # Convert to string
    safe_df_operation("origin_state", str.strip)  # Strip all whitespace
    safe_df_operation("origin_state", str.upper)  # Convert to uppercase

    safe_df_operation("destination_state", str)  # Convert to string
    safe_df_operation("destination_state", str.strip)  # Strip all whitespace
    safe_df_operation("destination_state", str.upper)  # Convert to uppercase

    # Derive country information from state
    safe_df_operation(
        source_column="origin_state",
        target_column="origin_country",
        operation=preprocessing.derive_country_from_state,
    )

    safe_df_operation(
        source_column="destination_state",
        target_column="destination_country",
        operation=preprocessing.derive_country_from_state,
    )

    # TODO(P2): #2377 Always use GMAPS distance
    try:
        # Calculate distance in miles between origin and destination cities
        distance_miles_dict = get_distance_miles_dict(df)
        if "distance_miles" in df.columns:
            df["distance_miles"] = df.apply(
                lambda row: (
                    row["distance_miles"]
                    if pd.notna(row["distance_miles"]) and row["distance_miles"] >= 0
                    else distance_miles_dict.get(
                        pack_origin_destination_city_state(
                            row["origin_city"],
                            row["origin_state"],
                            row["destination_city"],
                            row["destination_state"],
                        )
                    )
                ),
                axis=1,
            )
        else:
            df["distance_miles"] = df.apply(
                lambda row: distance_miles_dict.get(
                    pack_origin_destination_city_state(
                        row["origin_city"],
                        row["origin_state"],
                        row["destination_city"],
                        row["destination_state"],
                    )
                ),
                axis=1,
            )
    except Exception as err:
        logging.warning(f"Error calculating distance_miles: {err}")
        errors_by_column[
            "distance_miles"
        ] = f"Error calculating '{HUMAN_READABLE_COLUMN_NAME['distance_miles']}' field."

    # Map equipment types
    safe_df_operation(
        "equipment_type",
        map_equipment_type,
        operation_args=(local_equipment_type_dict,),
    )

    try:
        df["origin_close_time"] = pd.Timestamp.now(tz="UTC").date().strftime("%Y-%m-%d")
    except Exception as err:
        logging.warning(f"Error filling origin_close_time: {err}")
        errors_by_column[
            "origin_close_time"
        ] = f"Error filling column '{HUMAN_READABLE_COLUMN_NAME['origin_close_time']}'."

    return errors_by_column
