import boto3
import pandas as pd

from machine_learning.freightgpt.constants import GMAPS_UTILS
from machine_learning.price_recommendation.price_predictor import (
    VertexAutoMLPricePredictor,
)

S3_CLIENT = boto3.client("s3")

CONTRACT_PRICER = VertexAutoMLPricePredictor(
    VertexAutoMLPricePredictor.CONTRACT_PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS
)


# def format_excel_export(
#     df: pd.DataFrame, output_filename: str, index: bool = False
# ) -> None:
#     """
#     Exports a pandas DataFrame to an Excel file with automatically adjusted column widths for readability.

#     Parameters:
#     -----------
#     df : pd.DataFrame
#         The DataFrame to be exported to Excel.
#     output_filename : str
#         The path and filename of the output Excel file (e.g., 'output.xlsx').
#     index : bool, optional, default=False
#         Whether to include the DataFrame index in the Excel output.

#     Returns:
#     --------
#     None
#         The function writes the Excel file to the specified location.

#     Notes:
#     ------
#     - The column widths are automatically adjusted based on the maximum length of the data entries and column headers.
#     - The 'xlsxwriter' engine is used to allow for setting custom column widths.
#     """

#     def calculate_column_widths(df):
#         # Calculate approximate column width from entries
#         widths = []
#         for col in df.columns:
#             max_len = max(df[col].astype(str).map(len).max(), len(col))
#             widths.append(max_len + 1)  # Add extra space for readability
#         return widths

#     # Export to Excel with adjusted column widths using xlsxwriter
#     with pd.ExcelWriter(output_filename, engine="xlsxwriter") as writer:
#         df.to_excel(writer, index=index, sheet_name="Sheet1")

#         # Access the workbook and worksheet objects
#         worksheet = writer.sheets["Sheet1"]

#         # Calculate and set column widths
#         widths = calculate_column_widths(df)
#         for i, width in enumerate(widths):
#             worksheet.set_column(i, i, width)  # Set the width for each column


REQUIRED_COLUMNS = [
    "origin_city",
    "origin_state",
    "destination_city",
    "destination_state",
    "equipment_type",
]

OPTIONAL_COLUMNS = [
    "distance_miles",
    "origin_zip",
    "destination_zip",
]

ERROR_COLUMN = "Error Message"

HUMAN_READABLE_COLUMN_NAME = {
    "origin_city": "Origin City",
    "origin_state": "Origin State",
    "origin_zip": "Origin Zip",
    "origin_country": "Origin Country",
    "destination_city": "Destination City",
    "destination_state": "Destination State",
    "destination_zip": "Destination Zip",
    "destination_country": "Destination Country",
    "equipment_type": "Equipment Type",
    "origin_close_time": "Origin Close Time",
    "distance_miles": "Distance (Miles)",
    "truck_cost": "Truck Cost",
    "linehaul_cost": "Linehaul Cost",
    "fuel_cost": "Fuel Cost",
    "margin": "Margin",
    "truck_cost_with_margin": "Truck Cost with Margin",
}

VALID_EQUIPMENT_TYPES = {"Dry Van", "Reefer"}

MAX_MAPPING_ATTEMPTS = 3

FILE_TO_DF = {
    "csv": pd.read_csv,
    "xls": pd.read_excel,
    "xlsx": pd.read_excel,
}

DF_TO_FILE = {
    "csv": pd.DataFrame.to_csv,
    "xls": pd.DataFrame.to_excel,
    "xlsx": pd.DataFrame.to_excel,
}

PROMPT_MAP_EQUIPMENT_TYPE = """
                            Learn how to map equipment types using the dictionary {equipment_type_dict} and
                            general knowledge about freight equipments and their shorthands.
                            Then map the following set of equipment types: {raw_equipment_types}
                            to {equipment_types}.
                            Provide the mapping in json format as <raw_equipment_type>: <equipment_type> (without any other text).
                            If there is no suitable match for a raw_equipment_type, map it to null i.e. <raw_equipment_type>: null.
                            """

EQUIPMENT_MAPPING_LLM_TEMPERATURE = 0

PROMPT_MAP_COLUMNS = """
                        Given the following original column headers: {original_headers}
                        and a sample of the data: {sample_data}
                        Map them to the following template column headers: {template_headers}
                        Provide the mapping in json format as <original_header>: <template_header> (without any other text).
                        If there is no suitable match for a original column, do not include that column in the mapping.
                        The mapping must be one to one. Multiple columns should not be matched with a single template header.
                        If there are any issues with the previous mapping, they are: {issues}.
                        If there is an issue with a particular template column, map some other original column to that template column.
                        """

COLUMN_MAPPING_LLM_TEMPERATURE = 0.1

NUMBER_OF_ROWS_LIMIT = 100000
