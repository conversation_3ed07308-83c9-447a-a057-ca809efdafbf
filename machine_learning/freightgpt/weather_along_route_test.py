import unittest

from parameterized import parameterized

from common import init_main

# from unittest.mock import patch

PROJECT_ROOT = init_main.initialize()
from common import route_segmentation_utils


class TestBreakIntoSegments(unittest.TestCase):
    @parameterized.expand(
        [
            (
                "Head <b>north</b> on <b>3rd St NW</b> toward <b>Copper Ave NW</b>",
                "north 3rd St NW Copper Ave NW",
            ),
            (
                "Turn <b>right</b> at the 2nd cross street onto <b>Tijeras Ave NW</b>",
                "Tijeras Ave NW",
            ),
            (
                "Continue onto <b>Dr <PERSON> Jr Ave NE</b>",
                "Dr <PERSON> Jr Ave NE",
            ),
            ("Turn <b>left</b> onto <b>Oak St NE</b>", "Oak St NE"),
            (
                'Merge onto <b>I-25 N</b> via the ramp on the <b>left</b> to <b>Santa Fe</b><div style="font-size:0.9em">Entering Colorado</div>',
                "I-25 N Santa Fe",
            ),
            ("Normal text without HTML tags", ""),  # Edge case: No HTML tags
            (
                "<b>Only</b> <b>HTML</b> <b>tags</b>",
                "Only HTML tags",
            ),  # Edge case: No directions like left/right
            ("", ""),  # Edge case: Empty string
            ("<b></b>", ""),  # Edge case: Empty HTML tags
            (
                "Multiple <b>tags</b> <b></b> with <b>empty</b> <b></b> tags",
                "tags empty",
            ),  # Edge case: Mixed empty and non-empty tags
        ]
    )
    def test_extract_location_names(self, html_instructions, expected_locations):
        self.assertEqual(
            route_segmentation_utils.extract_location_names(html_instructions),
            expected_locations,
        )

    # TODO(P0): #1445 Unit test langchain_utils.splice_to_equal_segments.
    # @patch('langchain_utils.polyline')  # Replace 'your_module' with the actual module where 'polyline' is defined.
    # @patch('langchain_utils.plot_on_us_map')  # Replace 'your_module' with the actual module where 'plot_on_us_map' is defined.
    # def test_splice_to_equal_segments(self, mock_plot_on_us_map, mock_polyline):
    #     # Setup mock for polyline
    #     mock_polyline.encode.return_value = 'encoded_polyline'
    #     mock_polyline.decode.return_value = [(0, 0), (1, 1)]

    #     # https://developers.google.com/maps/documentation/utilities/polylineutility

    #     # Test case 1: Segment length 100
    #     steps = [
    #         {"distance": {"value": 50}, "duration": {"value": 100}, "html_instructions": "dummy", "polyline": {"points": "dummy"}},
    #         {"distance": {"value": 100}, "duration": {"value": 200}, "html_instructions": "dummy", "polyline": {"points": "dummy"}},
    #     ]

    #     expected_result = [
    #         {"step_name": None, "polyline": 'encoded_polyline', "distance_in_step": 50, "seconds_enroute": 100, "point": (1, 1)},
    #         {"step_name": None, "polyline": 'encoded_polyline', "distance_in_step": 20, "seconds_enroute": 140, "point": (1, 1)},
    #         {"step_name": None, "polyline": 'encoded_polyline', "distance_in_step": 90, "seconds_enroute": 280, "point": (1, 1)},
    #     ]

    #     result = langchain_utils.splice_to_equal_segments(steps, 70)
    #     self.assertEqual(result, expected_result)

    #     # Test case 2: Edge case, empty list of steps
    #     self.assertEqual(langchain_utils.splice_to_equal_segments([], 100), [])

    # def test_break_into_segments(self):
    #     # Sample data with step 8, 150, 112, 30
    #     steps = [
    #         {
    #             "distance": {"value": 8},
    #             "duration": {"value": 16},
    #         },
    #         {
    #             "distance": {"value": 150},
    #             "duration": {"value": 300},
    #         },
    #         {
    #             "distance": {"value": 112},
    #             "duration": {"value": 224},
    #         },
    #         {
    #             "distance": {"value": 30},
    #             "duration": {"value": 60},
    #         },
    #     ]

    #     # For segment length 30
    #     segments_30 = langchain_utils.break_into_segments(steps, 30)
    #     self.assertEqual(len(segments_30), 10)
    #     # For segment length 301
    #     segments_301 = langchain_utils.break_into_segments(steps, 301)
    #     self.assertEqual(len(segments_301), 0)  # As total distance is less than 301

    #     # For segment length 100
    #     segments_100 = langchain_utils.break_into_segments(steps, 100)
    #     self.assertEqual(len(segments_100), 3)

    #     # Assertions for the first segment
    #     self.assertEqual(segments_100[0]["step_index"], 1)
    #     self.assertEqual(segments_100[0]["distance_in_step"], 92)
    #     self.assertEqual(segments_100[0]["duration_in_step"], 184)

    #     # Assertions for the second segment
    #     self.assertEqual(segments_100[1]["step_index"], 2)
    #     self.assertEqual(segments_100[1]["distance_in_step"], 42)
    #     self.assertEqual(segments_100[1]["duration_in_step"], 84)

    #     # Assertions for the third segment
    #     self.assertEqual(segments_100[2]["step_index"], 3)
    #     self.assertEqual(segments_100[2]["distance_in_step"], 30)
    #     self.assertEqual(segments_100[2]["duration_in_step"], 60)

    # def test_break_into_segments_small(self):
    #     # Sample data with step 8, 150, 112, 30
    #     steps = [
    #         {
    #             "distance": {"value": 100},
    #             "duration": {"value": 16},
    #         },
    #         {
    #             "distance": {"value": 50},
    #             "duration": {"value": 100},
    #         },
    #         {
    #             "distance": {"value": 20},
    #             "duration": {"value": 40},
    #         },
    #         {
    #             "distance": {"value": 30},
    #             "duration": {"value": 60},
    #         },
    #     ]

    #     # For segment length 30
    #     segments_30 = langchain_utils.break_into_segments(steps, 30)
    #     self.assertEqual(len(segments_30), 6)


# Run the tests
if __name__ == "__main__":
    unittest.main()
