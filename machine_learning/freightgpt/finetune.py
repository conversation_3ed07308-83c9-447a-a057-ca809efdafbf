import json
import os
import re

import pandas as pd
from tqdm import tqdm

from common import init_main
from machine_learning.freightgpt.constants import LANGCHAIN_MODEL

PROJECT_ROOT = init_main.initialize()


from machine_learning.freightgpt.prompt import PROMPT
from machine_learning.freightgpt.langchain_agent import <PERSON><PERSON>hainAgent
from machine_learning.freightgpt.langchain_tools import LANGCHAIN_TOOLS
from common import google_sheets_utils


def format_corrected_data(row):
    """Generate the correctly formatted data structure from a DataFrame row.

    High level order of messages is:
    1. Prompt
    2. User query
    3. Memory (including tool i/o for this query)
    4. LLM response
    """

    # Convert the string representation of the list from the CSV into an actual list
    memory = json.loads(row["input_memory"])

    formatted_messages = [
        {"role": "system", "content": PROMPT},
        {"role": "user", "content": row["input_input"]},
    ]

    # Convert the previous interactions into the desired format
    for item in memory:
        # Change "human" role to "user" and "ai" role to "assistant"
        role = "user" if item["type"] == "human" else "assistant"
        message = {"role": role, "content": item["content"]}

        if match := re.match(r"Tool (Input|Output) '(\w+)': (.*)", item["content"]):
            tool_type, function_name, arguments = match.groups()
            # Convert single quotes in arguments to double quotes for valid JSON parsing
            arguments = arguments.replace("'", '"')
            if tool_type == "Input":
                # Function call should be done by assistant
                message["role"] = "assistant"
                message["function_call"] = {
                    "name": function_name,
                    "arguments": json.dumps(json.loads(arguments)),
                }
                # Remove content since function_call is present
                del message["content"]
            else:
                message["role"] = "function"
                message["name"] = function_name
                try:
                    message["content"] = json.dumps(json.loads(arguments))
                except json.decoder.JSONDecodeError:
                    message["content"] = arguments

        formatted_messages.append(message)

    # Append the primary output (output_output) to the formatted messages list
    formatted_messages.append({"role": "assistant", "content": row["output_output"]})

    functions_path = os.path.join(
        PROJECT_ROOT, "machine_learning", "freightgpt", "tool_functions.json"
    )
    functions = json.loads(open(functions_path).read())

    # Get set of function names
    function_names = {fn["name"] for fn in functions["functions"]}
    expected_function_names = set(LANGCHAIN_TOOLS.keys())
    assert (
        function_names == expected_function_names
    ), f"Functions in tool_functions.json do not match LANGCHAIN_TOOLS in langchain_tools.py. Missing functions: {expected_function_names - function_names}"

    return {
        "messages": formatted_messages,
        "functions": functions["functions"],  # Add your functions here
    }


def kv_csv_to_jsonl(kv_filename: str, output_filename: str) -> None:
    df = pd.read_csv(kv_filename)

    """Generate a list of formatted examples from the DataFrame."""
    examples_to_add = df.apply(format_corrected_data, axis=1).tolist()

    # Display the first few examples for verification
    # print(json.dumps(examples_to_add[3], indent=4))
    # Write examples to a JSONL file

    with open(output_filename, "w") as f:
        for example in examples_to_add:
            f.write(json.dumps(example) + "\n")


def fine_tune(training_file: str):
    # TODO(P1): To use after training file is generated. doesn't work yet.
    import os
    import openai

    openai.api_key = os.getenv("OPENAI_API_KEY")
    openai.File.create(file=open(training_file, "rb"), purpose="fine-tune")
    openai.FineTuningJob.create(training_file=training_file, model=LANGCHAIN_MODEL)


# https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0
EXAMPLES_SHEET_ID = "1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo"


def main():
    if args.csv_to_jsonl:
        print("Converting CSV to JSONL for fine-tuning.")
        input_csv = args.input_csv or "fine-tuning-v0.5-fixed.csv"
        input_filename = os.path.join(
            PROJECT_ROOT, "machine_learning", "freightgpt", "data", input_csv
        )
        output_jsonl = "finetune_train.jsonl"
        output_filename = os.path.join(
            PROJECT_ROOT, "machine_learning", "freightgpt", "data", output_jsonl
        )
        kv_csv_to_jsonl(input_filename, output_filename)
    elif args.run_agent_on_finetune_sheet:
        print("Running fine-tuning examples, to upload to langsmith.")
        os.environ["LANGCHAIN_PROJECT"] = args.langchain_project
        gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
        finetune_df = gsheets_utils.load_sheet_into_dataframe(
            EXAMPLES_SHEET_ID, "Fine-Tuning Examples"
        )
        inputs = finetune_df["Input"].tolist()

        for input_msg in tqdm(inputs):
            chatbot = LangChainAgent(
                tags=args.tags,
                cli_dev_mode=True,
                mock=True,
            )
            chatbot.send_message(input_msg)
    else:
        print(
            "Nothing happened. Specify --csv-to-jsonl or --run-agent-on-finetune-sheet."
        )


if __name__ == "__main__":
    import argparse

    argparse = argparse.ArgumentParser()
    argparse.add_argument(
        "--run-agent-on-finetune-sheet",
        action="store_true",
        help="Run the agent on the examples in the fine-tuning sheet.",
    )
    argparse.add_argument(
        "--tags",
        nargs="+",
        default=[],
        help="Tags to identify Langsmith run, such as 'finetuning-v0.1' Separate by spaces.",
    )
    argparse.add_argument(
        "--langchain-project",
        action="store",
        default="default",
        help="Langsmith Project to store runs, such as default or fine-tune.",
    )
    argparse.add_argument(
        "--csv-to-jsonl",
        action="store_true",
        help="Convert CSV to JSONL for fine-tuning.",
    )
    argparse.add_argument(
        "--input-csv",
        action="store",
        default="",
        help="Input CSV file to convert to JSONL for fine-tuning.",
    )
    args = argparse.parse_args()

    main()

# How to use:
# 1. Log langsmith runs to base your dataset off of. (Use the --tags flag to identify them)
#     a. Make sure the chatbot is using mock functions.
# 2. Look up the tagged examples in langsmith and send them to annotation queue.
# 3. Mark the examples as correct, and write any additional corrections.
# 4. Add the examples to a KV dataset in langsmith, and export them as a CSV.
# 5. Manually fix the .csv file based on your notes from the annotation queue, probably in Projects view.
# 6. Convert the CSV to JSONL using the kv_csv_to_jsonl function.
# 7. Fine-tune the model using the JSONL file.

# python .\machine_learning\freightgpt\finetune.py --csv-to-jsonl
# python .\machine_learning\freightgpt\finetune.py --csv-to-jsonl --input-csv fine-tuning-v0.5-fixed.csv
