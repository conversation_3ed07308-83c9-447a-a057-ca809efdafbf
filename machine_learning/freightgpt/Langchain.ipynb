{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "import os\n", "import dotenv\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "from machine_learning.freightgpt.constants import LANGCHAIN_MODEL\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "chat_model = ChatOpenAI(temperature=0, model=LANGCHAIN_MODEL)\n", "chat_model.predict(\"what is temperature in chatgpt parameter?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import typing\n", "import random\n", "\n", "def get_distance_between_locations(origin: str, destination: str) -> float:\n", "    \"\"\"Gets distance between two locations in miles using google maps API.\"\"\"\n", "\n", "    return random.random()\n", "\n", "\n", "def get_diesel_price(state: str) -> float:\n", "    \"\"\"Gets diesel price in dollars per gallon for today using EIA API.\"\"\"\n", "    import time\n", "    time.sleep(1)\n", "    return random.random()\n", "\n", "\n", "def get_todays_truck_cost(\n", "    origin_city: str, origin_state: str, destination_city: str, destination_state: str\n", ") -> typing.Dict[str, float]:\n", "    return {\n", "        \"origin_city\": origin_city,\n", "        \"origin_state\": origin_state,\n", "        \"destination_city\": destination_city,\n", "        \"destination_state\": destination_state,\n", "        \"truck_cost\": random.random() * 1000,\n", "        \"distance\": random.random() * 1000,\n", "        \"diesel_cost\": random.random() * 5,\n", "    }\n", "\n", "\n", "# https://python.langchain.com/docs/modules/agents/tools/tool_input_validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PREFIX = \"\"\"You are FreightGPT. You are a chat agent focused on answering any freight-related queries the user may have.\n", "\n", " You can help users with general freight questions, basic freight math, and current freight pricing.\n", "\n", " You have the power of the get_truck_cost function, which can give you a price estimate for today's market rate for a given lane.\n", " \n", "     The user may only specify a lane as a pair of origin and destination cities. a round trip between a,b requires calling this api twice, once with a,b and once with b,a\n", "\n", "        For example:\n", "        i have a load going from chicago to miami What is my round trip cost?\n", "        agent: calls this api twice, once with chicago, miami and once with miami, chicago.\n", "        \n", "        deadhead miles are miles driven without a load, so if you drive from a to b and then from c to d, the deadhead miles are the miles from b to c.\n", "        \n", "        For example:\n", "        i have a load going from chicago to miami, then tampa to downers grove illinois. What is my trip cost, and how many deadhead miles are there?\n", "        agent: calls this api three times\n", "        Invoking: `TruckCost` with `{'origin_city': 'Chicago', 'origin_state': 'IL', 'destination_city': 'Miami', 'destination_state': 'FL'}`\n", "        Invoking: `TruckCost` with `{'origin_city': 'Tampa', 'origin_state': 'FL', 'destination_city': 'Downers Grove', 'destination_state': 'IL'}`\n", "        Invoking: `TruckCost` with `{'origin_city': 'Miami', 'origin_state': 'FL', 'destination_city': 'Tampa', 'destination_state': 'FL'}`\n", "        \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentType, initialize_agent\n", "from langchain.tools import BearlyInterpreterTool\n", "\n", "\n", "bearly_tool = BearlyInterpreterTool(api_key=\"bearly-sk-aP3nRBJvwWlVxObZq8V6yySgM7Q\")\n", "bearly_tool.as_tool()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "from io import BytesIO\n", "import re\n", "from uuid import uuid4\n", "from langchain.agents import initialize_agent, Tool\n", "from langchain.agents import AgentType\n", "from langchain_openai import ChatOpenAI\n", "from langchain import LLMMathChain\n", "from langchain.utilities import SerpAPIWrapper\n", "from langchain.tools import StructuredTool\n", "\n", "# from langchain.agents import AgentType, initialize_agent\n", "# from langchain.tools import BearlyInterpreterTool\n", "\n", "# https://github.com/shroominic/codeinterpreter-api/blob/main/src/codeinterpreterapi/session.py#L128\n", "\n", "\n", "search = SerpAPIWrapper()\n", "llm = ChatOpenAI(temperature=0, model=LANGCHAIN_MODEL)\n", "llm_math_chain = LLMMathChain.from_llm(\n", "    llm=ChatOpenAI(temperature=0, model=LANGCHAIN_MODEL), verbose=True\n", ")\n", "tools = [\n", "    StructuredTool.from_function(\n", "        func=get_diesel_price,\n", "        coroutine=get_diesel_price,\n", "        name=\"<PERSON>\",\n", "        description=\"\"\"useful for when you need to get today's diesel price for a state.\"\"\",\n", "        handle_tool_error=True,\n", "    ),\n", "    StructuredTool.from_function(\n", "        func=get_distance_between_locations,\n", "        coroutine=get_distance_between_locations,\n", "        name=\"Distance\",\n", "        description=\"\"\"useful for when you need to get the driving distance between two locations.\n", "        - useful for CPM (cost per mile) calculations.\n", "        - useful for subtracting fuel per mile between two cities.\"\"\",\n", "        handle_tool_error=True,\n", "    ),\n", "    StructuredTool.from_function(\n", "        func=get_todays_truck_cost,\n", "        coroutine=get_todays_truck_cost,\n", "        name=\"TruckCost\",\n", "        description=\"\"\"useful for when you need to answer questions about how much a truck costs between two cities.\n", "        When the user asks for a roundtrip, you must call this function twice, once with the origin and destination, and once with the destination and origin.\"\"\",\n", "        handle_tool_error=True,\n", "    ),\n", "    # Tool(\n", "    #     name=\"Calculator\",\n", "    #     func=llm_math_chain.arun,\n", "    #     coroutine=llm_math_chain.arun,\n", "    #     handle_tool_error=True,\n", "    #     description=\"\"\"useful for when you need to answer questions about math. Inputs should always be numbers.\"\"\"\n", "    #     # -  to add two truck costs. For example, in a roundtrip: (origin, destination) + (destination, origin).\"\"\",\n", "    # ),\n", "    Tool(\n", "        name=\"Search\",\n", "        func=search.arun,\n", "        coroutine=search.arun,\n", "        description=\"useful for when you need to answer questions about current events\",\n", "    ),\n", "    # bearly_tool.as_tool(),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import MessagesPlaceholder\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain_core.messages.system import SystemMessage\n", "\n", "agent_kwargs = {\n", "    \"system_message\": SystemMessage(content=PREFIX),\n", "    # 'format_instructions': FORMAT_INSTRUCTIONS,\n", "    # 'suffix': SUFFIX,\n", "    \"extra_prompt_messages\": [MessagesPlaceholder(variable_name=\"memory\")],\n", "}\n", "memory = ConversationBufferMemory(memory_key=\"memory\", return_messages=True)\n", "\n", "agent = initialize_agent(\n", "    tools,\n", "    llm,\n", "    agent=AgentType.OPENAI_FUNCTIONS,\n", "    verbose=True,\n", "    agent_kwargs=agent_kwargs,\n", "    memory=ConversationBufferMemory(\n", "        memory_key=\"memory\",\n", "        input_key=\"input\",\n", "        output_key=\"output\",\n", "        return_messages=True,\n", "    ),\n", "    return_intermediate_steps=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = agent(\n", "    {\n", "        \"input\": \"\"\"chi-town to motor city truck cost?, add 13% margin and give total cost for 28 trucks\"\"\"\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\n", "    \"Create a simple title no more than 5 words for the following conversation:\\n\\n{memory}\"\n", ")\n", "model = ChatOpenAI()\n", "chain = prompt | model\n", "\n", "memory_variables = agent.memory.load_memory_variables({})\n", "formatted_memory = \"\"\n", "for message in memory_variables.get(\"memory\", []):\n", "    formatted_memory += f\"{message.type}: {message.content}\\n\"\n", "chain.invoke({\"memory\": formatted_memory}).content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# https://blog.langchain.dev/structured-tools/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wrap tool in a class\n", "This wraps all our tools in a StructuredTool class, cleaning up a lot fo the code and obsoleting many of the util functions.\n", "\n", "This is a clean refactor we should do [#1692](https://github.com/truce-logistics/truce/issues/1692) but, I'm holding off\n", "because I'm not sure if it will interfere with tool parallelization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.tools import StructuredTool\n", "from abc import ABC, ABCMeta, abstractmethod\n", "from typing import Coroutine, Optional, Callable, Any, Awaitable, List, Type\n", "from langchain_core.callbacks import AsyncCallbackManagerForToolRun\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class AbstractLangchainTool(StructuredTool):\n", "    \"\"\"\n", "    Abstract base class for Langchain tools. Subclasses must implement the function method and args_schema property.\n", "\n", "    Subclasses can optionally implement:\n", "    - mock_function: If provided, it will be used when mock=True is passed to the constructor.\n", "    - async_function: If provided, it will be used for asynchronous operations.\n", "    \"\"\"\n", "\n", "    emits_event: bool = Field(\n", "        default=False, description=\"Indicates if the tool emits an event\"\n", "    )\n", "    frontend_loading_description: str = Field(\n", "        default=\"\", description=\"Description for frontend loading\"\n", "    )\n", "    followup_suggestions: List[str] = Field(\n", "        default=[], description=\"List of follow-up suggestions\"\n", "    )\n", "\n", "    def __init__(\n", "        self,\n", "        name: str,\n", "        description: str,\n", "        emits_event: bool = False,\n", "        mock: bool = False,\n", "        frontend_loading_description: str = \"\",\n", "        followup_suggestions: List[str] = [],\n", "    ):\n", "        super().__init__(\n", "            name=name,\n", "            description=description,\n", "            verbose=True,\n", "            handle_tool_error=True,\n", "            # callbacks?\n", "        )\n", "        self.emits_event = emits_event\n", "        self.frontend_loading_description = frontend_loading_description\n", "        self.followup_suggestions = followup_suggestions\n", "\n", "        # Set the func and coroutine based on the mock flag and whether async_function is defined\n", "        self.func = (\n", "            self.mock_function\n", "            if mock and hasattr(self, \"mock_function\")\n", "            else self.function\n", "        )\n", "\n", "        if hasattr(self, \"async_function\"):\n", "            self.coroutine = self.async_function\n", "\n", "    # We must implement args_schema, func, coroutine\n", "\n", "    @staticmethod\n", "    @abstractmethod\n", "    def function(*args, **kwargs):\n", "        pass\n", "\n", "    # def _arun(\n", "    #     self,\n", "    #     *args: Any,\n", "    #     run_manager: AsyncCallbackManagerForToolRun | None = None,\n", "    #     **kwargs: Any\n", "    # ) -> Coroutine[Any, Any, str]:\n", "    #     return super()._arun(*args, run_manager=run_manager, **kwargs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import random\n", "from pydantic import Field\n", "\n", "\n", "class DieselInput(BaseModel):\n", "    state: str = Field(description=\"Two-digit US State code\")\n", "\n", "\n", "class DieselPriceTool(AbstractLangchainTool):\n", "    args_schema: Type[BaseModel] = DieselInput  # Class-level attribute\n", "\n", "    @staticmethod\n", "    def function(state: str) -> float:\n", "        # Your implementation\n", "        import time\n", "        time.sleep(5)\n", "        return random.random() * 5\n", "\n", "    # @staticmethod\n", "    # def function(state: str) -> float:\n", "    #     \"\"\"Gets diesel price in dollars per gallon for this week using EIA API.\"\"\"\n", "    #     validate_us_states({state})\n", "    #     # Implementation to get the actual diesel price\n", "    #     # Placeholder implementation\n", "    #     return PRICER.diesel_price.get_diesel_price(datetime.now(), state)\n", "\n", "    # @staticmethod\n", "    # def mock_function(state: str) -> float:\n", "    #     \"\"\"Mock function to simulate fetching diesel prices.\"\"\"\n", "    #     validate_us_states({state})\n", "    #     return stable_random(state, 2.5, 3.5)\n", "\n", "\n", "# Example usage of tool class\n", "\n", "diesel_price_tool = DieselPriceTool(\n", "    name=\"WeeklyDieselPrice\",\n", "    description=\"This week's diesel price for a state\",\n", "    frontend_loading_description=\"Diesel price in $/gal at a state\",\n", "    mock=False,\n", "    emits_event=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parallel tool usage, but outside chatbot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.convert_to_openai import (\n", "    format_tool_to_openai_tool,\n", ")\n", "from operator import itemgetter\n", "from typing import Union\n", "\n", "from langchain.output_parsers import JsonOutputToolsParser\n", "from langchain_community.tools.convert_to_openai import (\n", "    format_tool_to_openai_tool,\n", ")\n", "from langchain_core.runnables import (\n", "    <PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    RunnableMap,\n", "    RunnablePassthrough,\n", ")\n", "from langchain_openai import ChatOpenAI\n", "\n", "\n", "tools = [diesel_price_tool]\n", "model = ChatOpenAI(model=LANGCHAIN_MODEL)\n", "model_with_tools = model.bind(tools=[format_tool_to_openai_tool(t) for t in tools])\n", "tool_map = {tool.name: tool for tool in tools}\n", "\n", "\n", "def call_tool(tool_invocation: dict) -> Union[str, Runnable]:\n", "    \"\"\"Function for dynamically constructing the end of the chain based on the model-selected tool.\"\"\"\n", "    tool = tool_map[tool_invocation[\"type\"]]\n", "    return RunnablePassthrough.assign(output=itemgetter(\"args\") | tool)\n", "\n", "\n", "call_tool_list = RunnableLambda(call_tool).map()\n", "chain = model_with_tools | JsonOutputToolsParser() | call_tool_list\n", "\n", "chain.invoke(\"diesel price all states in northeast\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Title LLM"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}