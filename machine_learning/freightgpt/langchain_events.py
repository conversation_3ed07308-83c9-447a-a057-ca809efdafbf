import typing

from machine_learning.freightgpt.constants import ActiveType
from machine_learning.freightgpt.constants import VisualizationType


def is_langchain_message_of_type(message: typing.Dict, message_type: str) -> bool:
    """Used to determine if a message is of a specific type: system, human, or ai."""
    try:
        return message["type"] == message_type
    except KeyError:
        return False


def merge_and_filter_chat_history_with_events(
    chat: typing.List[typing.Dict],
    events: typing.List[typing.Dict],
    events_filter: typing.List[str] = list(VisualizationType) + ["error"],
) -> typing.List[typing.Dict]:
    """Merge chat history and events.

    Merges a list of chat history and a list of events into a single list. The chat history and events are represented as dictionaries. The function iterates through the chat history and appends each message to the combined history. If a message is an AI message, it also appends any events associated with that message. The resulting combined history is returned as a list of dictionaries.

    Args:
        chat (List[Dict]): The list of chat history.
        events (List[Dict]): The list of events.

    Returns:
        List[Dict]: The merged chat history and events.

    """
    clean_combined_history = []
    events_pointer, chats_pointer = 0, 0
    while chats_pointer < len(chat):
        message = chat[chats_pointer]
        message_id = message.get("data", {}).get("id")

        # Skip system messages
        if is_langchain_message_of_type(message, "system"):
            chats_pointer += 1
            continue

        # Append message.
        clean_combined_history.append(message)

        # If AI message, increment events pointer with all associated events (same message id).
        # Add events after the AI message.
        if is_langchain_message_of_type(message, "ai") and message_id is not None:
            while (
                events_pointer < len(events)
                and events[events_pointer].get("message_id") == message_id
            ):
                if events[events_pointer].get("event", {}).get("type") in events_filter:
                    clean_combined_history.append(events[events_pointer])
                events_pointer += 1

        chats_pointer += 1

    return clean_combined_history


def set_last_event_active(
    events: typing.List,
    event_key: str,
    event_type: str,
    retain_run_active: bool = False,
) -> typing.List[typing.Dict]:
    """
    Sets the last event with a specific key and type as active and marks all other events with the same key and type as inactive.

    Args:
        events (List[Dict]): A list of events.
        event_key (str): The key to identify the event.
        event_type (str): The type of the event.
        retain_run_active (bool): only deactivates events from a previous run

    Returns:
        List[Dict]: The modified list of events with the last active event.

    Example:
        events = [
            {"event": {"key": "A", "type": "type1", "active_type":"ACTIVE"}},
            {"event": {"key": "B", "type": "type2"}},
            {"event": {"key": "A", "type": "type1", "active_type" :"ACTIVE"}},
            {"event": {"key": "C", "type": "type3"}},
        ]
        set_last_event_active(events, "A", "type1")

        Output:
        [
            {"event": {"key": "A", "type": "type1", "active_type": "ACTIVE"}},
            {"event": {"key": "B", "type": "type2"}},
            {"event": {"key": "A", "type": "type1", "active_type": "INACTIVE"}},
            {"event": {"key": "C", "type": "type3"}},
        ]
    """
    last_active = {}

    # Iterate backwards
    for i in reversed(range(len(events))):
        if (
            isinstance(events[i]["event"]["data"], dict)
            and events[i]["event"].get(event_key) == event_type
        ):
            # If first encounter of active, do not change.
            # If retain_run_active, then we will also leave messages that have the same run id alone.
            if not last_active or (
                retain_run_active
                and last_active.get("message_id") == events[i].get("message_id")
            ):
                last_active = events[i]

            # Set to inactive
            else:
                events[i]["event"]["data"]["active_type"] = ActiveType.INACTIVE

    return events
