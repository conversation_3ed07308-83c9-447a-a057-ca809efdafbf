# Adding additional functionality to Langchain's DynamoDBChatMessageHistory
# https://github.com/langchain-ai/langchain/blob/6a358311283f54f3bddff63f199997c8e59997a2/libs/langchain/langchain/memory/chat_message_histories/dynamodb.py#L20
import datetime
import logging
import uuid
from decimal import Decimal
from typing import Dict
from typing import List
from typing import Optional

import botocore
from boto3.session import Session
from langchain.schema import messages as langchain_messages
from langchain_community.chat_message_histories import DynamoDBChatMessageHistory


# TODO(P0): Delete this class in order to decouple DynamoDB from memory management. Manage DynamoDB ourselves.
# In other words, don't use DynamoDBChatMessageHistory, use BaseChatMessageHistory instead.
class CustomDynamoDBChatHistory(DynamoDBChatMessageHistory):
    """
    A custom class for managing chat history in a DynamoDB table, extending DynamoDBChatMessageHistory.

    This class provides functionality for storing chat messages in a DynamoDB table, with additional
    features like setting a parent run ID for message grouping.

    Attributes:
        parent_run_id (str): A unique identifier / UUID that unifies system, human, and AI messages
            within a single session or 'run'.

    Methods:
        set_parent_run_id(id): Sets the parent run ID for the current instance.
        add_message(message): Appends a chat message to the DynamoDB record.

    Args:
        table_name (Optional[str]): The name of the DynamoDB table.
        session_id (Optional[str]): The session ID for the chat history.
        endpoint_url (Optional[str]): The endpoint URL for DynamoDB.
        primary_key_name (str): The name of the primary key in the DynamoDB table.
        key (Optional[Dict[str, str]]): The primary key-value pair for accessing DynamoDB.
        boto3_session (Optional[Session]): The boto3 session for DynamoDB access.
        kms_key_id (Optional[str]): The ID of the AWS KMS key used for encryption.
        dry_mode (bool): A flag to indicate whether to run in dry mode (default: False).

    Inherits:
        DynamoDBChatMessageHistory: The base class for managing chat message history in DynamoDB.

    Chat structure:
    [
      {
        "content": "What is the price of a dry van from NYC to Chicago?"
        "id": "2d34ac65-a797-48d4-b1e9-6d653dd90480"
        "type": "human"
      },
      {
        "content": "The cost of a Dry Van from New York, NY to Chicago, IL is $1,130.56."
        "id": "2d34ac65-a797-48d4-b1e9-6d653dd90480"
        "type": "ai"
      }
    ]
    """

    def __init__(
        self,
        table_name: Optional[str] = None,
        session_id: Optional[str] = None,
        endpoint_url: Optional[str] = None,
        primary_key_name: str = "SessionId",
        key: Optional[Dict[str, str]] = None,
        boto3_session: Optional[Session] = None,
        kms_key_id: Optional[str] = None,
        dry_mode: bool = False,
    ):
        # Messages to be added after the next human message.
        self.message_queue: List[langchain_messages.BaseMessage] = []
        # Messages stored in local memory.
        self.local_msgs: List[langchain_messages.BaseMessage] = []
        # When running in dry mode, no operations will affect DynamoDB.
        self.dry_mode: bool = dry_mode
        # The LLM generated summary of the current conversation.
        self.curr_convo_summary = ""

        if dry_mode:
            logging.info("Running in dry mode: No operations will affect DynamoDB.")
            return

        if not table_name:
            raise ValueError("Table name must be provided.")
        if not session_id:
            raise ValueError("Session ID must be provided.")
        super().__init__(
            table_name,
            session_id,
            endpoint_url,
            primary_key_name,
            key,
            boto3_session,
            kms_key_id,
        )

        # Single id unifies system, human, and ai messages.
        self.parent_run_id = ""
        self.curr_convo_summary = ""
        self.creation_timestamp = datetime.datetime.now().timestamp()

        # Messages stored in cloud DynamoDB.
        self.dynamo_stored_msgs = self.get_dynamo_messages()

        # Messages in local machine memory.
        self.local_msgs = langchain_messages.messages_from_dict(self.dynamo_stored_msgs)

    def _get_messages(self) -> List[langchain_messages.BaseMessage]:
        return self.local_msgs

    def _set_messages(self, value) -> None:
        self.local_msgs = value

    def _del_messages(self) -> None:
        del self.local_msgs

    messages = property(
        fget=_get_messages,
        fset=_set_messages,
        fdel=_del_messages,
        doc="The list of messages in the chat history.",
    )

    def set_parent_run_id(self, id: str):
        """
        Sets the last parent run ID.

        Args:
            id: The "run" id - or parent id associated with a single run through the chain.
        """
        self.parent_run_id = id

    def get_convo_summary(self):
        """Return the current set convo summary."""
        return self.curr_convo_summary

    def set_convo_summary(self, summary: str, force_update=False):
        """Sets the summary text of the current conversation.

        If `force_update` is True,
        it also attempts to update the corresponding record in a database table with the new summary
        and the conversation history. If not, It will be updated when new messages are added.

        Args:
            summary (str): The summary text for the current conversation.
            force_update (bool, optional): A flag to indicate whether to forcibly update the
                                           database with the new summary. Defaults to False.
        """
        self.curr_convo_summary = summary

        if self.dry_mode or not force_update:
            logging.info("Skipping DynamoDB update.")
            return

        try:
            self.table.put_item(
                Item={
                    **self.key,
                    "Summary": self.curr_convo_summary,
                    "History": self.dynamo_stored_msgs,
                    "CreationTimestamp": Decimal(str(self.creation_timestamp)),
                }
            )

        except botocore.exceptions.ClientError as err:
            logging.error("Failed to update summary with %s", err)

    def get_dynamo_messages(self) -> None:
        if self.dry_mode:
            logging.info("Skipping DynamoDB retrieval in dry mode.")
            return []

        response = None
        try:
            # TODO(P0): Don't get item /every/ time .chat_memory.messages is accessed
            response = self.table.get_item(Key=self.key)
        except botocore.exceptions.ClientError as error:
            logging.error("Failed to retrieve messages with %s", error)

        if response and "Item" in response:
            items = response["Item"].get("History", [])
            self.curr_convo_summary = response["Item"].get("Summary", "")
            self.creation_timestamp = response["Item"].get("CreationTimestamp", "")
        else:
            items = []
            self.creation_timestamp = datetime.datetime.now().timestamp()

        return items

    def add_after_human_message(self, message: langchain_messages.BaseMessage) -> None:
        """Queue a message to be added after the next human message.

        This often a good way to add system messages with tool i/o
        between the human message and AI response.

        Args:
            message (BaseMessage): The message to be added.

        Raises:
            ValueError: If a human message is queued after a human message."""
        if isinstance(message, langchain_messages.HumanMessage):
            raise ValueError(
                "Cannot queue human message after a human message. Use add_message instead."
            )
        self.message_queue.append(message)

    def _add_messages_to_memory(
        self, messages: List[langchain_messages.BaseMessage]
    ) -> None:
        """Add a list of messages to the memory."""
        self.local_msgs.extend(messages)

        if self.dry_mode:
            logging.info("Skipping DynamoDB update in dry mode.")
            return

        # Langchain format -> Dynamo Format
        messages_dicts = [
            langchain_messages._message_to_dict(message) for message in messages
        ]

        for message in messages_dicts:
            # Create message id.
            message["data"]["id"] = (
                self.parent_run_id if self.parent_run_id else str(uuid.uuid4())
            )
            self.dynamo_stored_msgs.append(message)

        try:
            self.table.put_item(
                Item={
                    **self.key,
                    "Summary": self.curr_convo_summary,
                    "History": self.dynamo_stored_msgs,
                    "CreationTimestamp": Decimal(str(self.creation_timestamp)),
                }
            )
        except botocore.exceptions.ClientError as err:
            logging.error(err)

    def add_message(self, message: langchain_messages.BaseMessage) -> None:
        """Append the message to the record in DynamoDB"""
        message_list = [message]

        if isinstance(message, langchain_messages.HumanMessage):
            message_list.extend(self.message_queue)
            self.message_queue = []

        self._add_messages_to_memory(message_list)


if __name__ == "__main__":
    test = CustomDynamoDBChatHistory(
        table_name="ChatStoreDev",
        session_id="test",
        key={
            "user_id": "integration_test_user",
            "conv_id": "508ccd95-eadf-42d4-a99d-f00cc71847ce",
        },
    )
    # print(test.messages)
