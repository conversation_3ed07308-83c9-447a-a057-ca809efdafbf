# FreightGPT

The main scripts are:
- `chatbot_sim.py`
- `carrier_tool.py`
- `finetune.py`
- `evaluate.py`

## Chatbot
This is the main chatbot CLI to interact with the LLM behind FreightGPT. It is typically run in sync mode, but can be run in async mode as well. The chatbot is run with the following command:

```bash
python machine_learning/freightgpt/chatbot_sim.py
```

Note that if `prompt.py` is updated, it will have to be run manually in order for changes to reflect in the chatbot.

### Tools

Tools can be invoked by the model to run calculations or retrieve information. They will be found in
`langchain_tools.py`

Generally, these will have to be defined:
- The tool function
- A mock function for the tool
- A class describing the parameters that can be passed into the tool (and optionally a function to validate this object)

These tools commonly call utilities defined in `langchain_utils.py`


### Langsmith

The conversations can automatically be stored in langsmith, and there are various CLI tags to facilitate organization within langsmith.
```
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=<API_KEY>
```

You can ask <PERSON><PERSON> for the API key.

## Fine-Tuning

We generate the dataset to fine-tune the LLM using this script. At a high level, the process is:

1. Write examples in the [spreadsheet](https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=868202115)
2. Run `python machine_learning/freightgpt/finetune.py --langchain-project fine-tune ` to log the examples in langsmith under the fine-tune project. I strongly recommend adding `--tags vX.XX` as well to make it easier to find the examples later.
3. Look up the tagged examples in langsmith and send them to annotation queue.
4. Mark the examples as correct, and write any additional corrections.
5. Add the examples to a KV dataset in langsmith, and export them as a CSV.
6. Manually fix the .csv file based on your notes from the annotation queue, probably in Projects view.
7. Convert the CSV to JSONL using the `python machine_learning/freightgpt/finetune.py --csv-to-jsonl`.
8. Fine-tune the model using the JSONL file in the [OpenAI UI](https://platform.openai.com/finetune).

[Google Doc](https://docs.google.com/document/d/1wYGu-W1WrFdoIyOT7SxooroNcjPTtm5XQ_Os58tp4Tc/edit#heading=h.esp5dgft1q9)

## Carrier Tool
This is a script to load FMCSA carrier data into DynamoDB.

## Evaluate
This is a script to evaluate the performance of LLM on our [eval dataset](https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0).

```bash
python machine_learning/freightgpt/evaluate.py --model <MODEL_NAME>
```

## Deployment
https://docs.google.com/document/d/1Hdv_8vtDn5J0HSAP-j2pt8UXLDRBX4DiNbiuWuqzsRo/edit
