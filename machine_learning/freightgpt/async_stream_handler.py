"""
Handles all async operations for the LangChain agent and uvicorn server.

- Implements callback for LangChain asyncronous run. Extends AsyncIteratorCallbackHandler to implement new functionality
on tool and llm invocations.
- Includes websocket functionality for sending events or token back to the user via websocket.
- Includes functionality for running a query and streaming the response back to the caller.
"""
import ast
import asyncio
import json
import logging
import os
import random
import sys
import typing
from typing import Any
from typing import AsyncIterator
from typing import cast
from typing import Dict
from typing import Literal
from typing import Union
from uuid import UUID

import aioboto3
import botocore
from aiobotocore.client import AioBaseClient
from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
from langchain.schema.output import LLMResult

from backend.freightgpt.main_server.langsmith_feedback_manager import (
    LangsmithFeedbackClient,
)
from common import ecr_job_util
from common import mail
from machine_learning.freightgpt.constants import VisualizationType
from machine_learning.freightgpt.langchain_events import set_last_event_active
from machine_learning.freightgpt.langchain_tools import LANGCHAIN_TOOLS


websocket_api = "https://wps3vtux75.execute-api.us-east-1.amazonaws.com/beta-1"
error_message = "Apologies, I am running into issues. Please try a new request."


class AsyncWordIterator:
    def __init__(self, string):
        self._words = string.split()  # Split the string into words
        self._index = 0

    def __aiter__(self):
        return self

    async def __anext__(self):
        await asyncio.sleep(0.1)  # Just to simulate some async delay.

        if self._index < len(self._words):
            value = self._words[self._index]
            self._index += 1
            if self._index != len(self._words):
                return value + " "
            return value
        else:
            raise StopAsyncIteration


class StreamCallback(AsyncIteratorCallbackHandler):
    """
    Overrides AsyncIteratorCallbackHandler.
    Source code:
    https://api.python.langchain.com/en/latest/_modules/langchain/callbacks/streaming_aiter.html#AsyncIteratorCallbackHandler

    Changes:
      - Log tool invocations, inputs, and results.
      - Contains tool store (to match callback lifecycle) to pass or preserve information from tools.
      - Overwrites on_llm_end handler to prevent early termination.
      - Adds additional constraints to on_llm_new_token before adding token to prevent extraneous output.
      - Increments chain counter on chain start and decrements on chain end, to only log when we are on the last chain.
      - Overwrites aiter to prevent early termination from done event before yield
      - Logs uncaught chain errors.
      - Includes the user message initially sent to the agent to be sent on chain errors.
    """

    def __init__(
        self,
        log_url: str,
        tool_store: Dict[Any, Any] = None,
        parent_run_id_emitter: typing.Callable = None,
        mail_api: mail.MailAPI = mail.MailAPI(gmail=False),
        user_message: str = "",
        langsmith_feedback_client: LangsmithFeedbackClient = None,
    ) -> None:
        super().__init__()
        self.chain_counter = 0
        self.parent_run_id_emitter = parent_run_id_emitter
        self.run_event_queued = False
        self.mail_api = mail_api
        self.log_url = log_url
        self.tool_store = tool_store
        self.user_message = user_message
        self.langsmith_feedback_client = langsmith_feedback_client

    async def drain_queue_and_signal_done(self):
        """Space queue iterations to prevent token overload."""
        while not self.queue.empty():
            await asyncio.sleep(1)

        self.done.set()

    # Log tool starts in dev mode.
    async def on_tool_start(
        self,
        serialized: Dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: list[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        """Run when tool starts running."""
        tool_name = serialized["name"]
        if invoked_tool := LANGCHAIN_TOOLS.get(tool_name):
            if invoked_tool.get("frontend_loading_description"):
                self.add_event_to_queue(
                    invoked_tool["frontend_loading_description"],
                    event_type="loading_description",
                )
            if followup_suggestions := invoked_tool.get("followup_suggestions"):
                if len(followup_suggestions) > 3:
                    followup_suggestions = random.sample(followup_suggestions, 3)

                self.add_event_to_queue(
                    followup_suggestions,
                    event_type="followup_suggestions",
                )

    # Increments chain counter to determine number of chains
    # running. If chain counter is 1, we want to stream.
    async def on_chain_start(
        self,
        serialized: Dict[str, Any],
        inputs: Dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: list[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        """Run when chain starts running."""
        self.chain_counter += 1

    # Decrements chain counter as chains end.
    async def on_chain_end(
        self,
        outputs: Dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: list[str] | None = None,
        **kwargs: Any,
    ) -> None:
        """Run when chain ends running."""

        self.chain_counter -= 1

    # Save the current parent run id.
    async def on_llm_start(
        self,
        serialized: Dict[str, Any],
        prompts: list[str],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: list[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        """Run when LLM starts running."""
        parent_run_id_str = str(parent_run_id)
        logging.info(f"Run {parent_run_id_str}")

        # If an emission function is provided, run it on the parent run id at the start of the llm run.
        if self.parent_run_id_emitter:
            self.parent_run_id_emitter(parent_run_id_str)

        # Also send parent run id to client.
        if not self.run_event_queued:
            self.add_event_to_queue({"run_id": parent_run_id_str}, event_type="run_id")
            self.run_event_queued = True

    # Overwrite to prevent early termination of aiter.
    # on_llm_end sends done event early
    # TODO (P1): raise issue on langchain github on early termination
    async def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        pass

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        # On new token: if chain counter is 1 (i.e. only one result left to stream)
        # and token is not None or empty string, add to queue to stream it.
        if (token is not None and token != "") and (self.chain_counter == 1):
            self.queue.put_nowait(token)

    # Re-writes aiter and removes early break with done event.
    # This changes the function to terminate when done or queue is empty.
    # TODO (P1): raise issue on langchain github on early termination
    async def aiter(self) -> AsyncIterator[str]:
        while not self.queue.empty() or not self.done.is_set():
            # Wait for the next token in the queue.
            done, other = await asyncio.wait(
                [
                    # NOTE: If you add other tasks here, update the code below,
                    # which assumes each set has exactly one task each
                    asyncio.ensure_future(self.queue.get()),
                    asyncio.ensure_future(self.done.wait()),
                ],
                return_when=asyncio.FIRST_COMPLETED,
            )

            # Cancel the other task
            if other:
                other.pop().cancel()

            # Extract the value of the first completed task
            token = cast(Union[str, Literal[True]], done.pop().result())

            # Otherwise, the extracted value is a token, which we yield
            if token is not True:
                yield token

    # Logs chain errors that are not caught.
    async def on_chain_error(
        self,
        error: Exception | KeyboardInterrupt,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: list[str] | None = None,
        **kwargs: Any,
    ) -> None:
        """Run when chain errors."""
        logging.error("Chain error: %s", error)

        # If integration test encounters chain error, exit process.
        if os.getenv("ENV") == "TEST":
            sys.exit(1)

        try:
            langsmith_url = self.langsmith_feedback_client.read_run(run_id).url
        except Exception as e:
            logging.error("Unable to retrieve Langsmith URL due to error:\n%s", e)
            langsmith_url = ""

        ecr_job_util.failure_email(
            e=error,
            mail_api=self.mail_api,
            dev=os.getenv("ENV") != "PROD",
            job_name="FreightGPT Chain",
            mail_body=f"\tLog: \n{self.log_url} \n\n Langsmith URL: \n{langsmith_url} \n\n User Message: \n{self.user_message}\n",
        )

    def add_event_to_queue(
        self, event_data: Dict[str, Dict[str, typing.Any]], event_type: str = "default"
    ):
        self.queue.put_nowait({"event": {"type": event_type, "data": event_data}})


# TODO (P0): robustify / error handle, especially returning and using success status
async def send_message_to_websocket(
    ag_async_client: aioboto3.Session.client,
    connection_id: str,
    data: Dict,
    sleep: float = None,
):
    """
    Sends message to api gateway websocket connection.

    Args:
        ag_async_client: aioboto3 apigatewaymanagementapi client.
        connection_id: the corresponding id to the client's websocket connection.
        data: data to send to the websocket.
        sleep: time to sleep after the send. Used to create gaps in streaming.

    Returns:
        success as bool.
    """
    # Post to websocket connection
    try:
        await ag_async_client.post_to_connection(
            ConnectionId=connection_id, Data=json.dumps(data)
        )
        if sleep:
            await asyncio.sleep(sleep)

    except botocore.exceptions.ClientError as e:
        logging.error(f"Failed to publish to websocket API: {e}")


def convert_token(token: str) -> Dict | str:
    """Tokens are always relayed as strings. If an object is passed in, attempt to parse."""
    try:
        converted_token = ast.literal_eval(token)

        if isinstance(converted_token, int) or isinstance(converted_token, float):
            converted_token = token

    except (ValueError, SyntaxError):
        converted_token = token

    return converted_token


async def stream_response(
    chatbot: "langchain_agent.LangChainAgent",  # noqa: F821
    message: str,
    connection_id: str,
    session: aioboto3.Session,
    mail_api: mail.MailAPI,
):
    """
    Sends message to agent, and streams the response back.
    Args:
        chatbot: current langchain agent.
        message: message or query from client.
        connection_id: the corresponding id to the client's websocket connection.
        session: aioboto3 session.
        conversation_id: the current conversation id.

    Returns: None
    """
    async with session.client(
        service_name="apigatewaymanagementapi", endpoint_url=websocket_api
    ) as api_ws_client:
        try:
            async for token in chatbot.send_message_stream(
                message, aioboto3_ws_client=api_ws_client, connection_id=connection_id
            ):
                # If token is a string, it is wrapped into an object with key "token"
                # Otherwise it's assumed that it's a dictionary of non token type and sent as is.
                token_to_send = convert_token(token)
                if not isinstance(token_to_send, dict):
                    token_to_send = {"token": token_to_send}

                # Capture events for publishing
                elif isinstance(token_to_send, dict) and "event" in token_to_send:
                    if chatbot.events_history:
                        chatbot.events_history.add_item(
                            {
                                "message_id": chatbot.memory.chat_memory.parent_run_id,
                                **token_to_send,
                            }
                        )

                await send_message_to_websocket(
                    api_ws_client, connection_id, token_to_send, sleep=0.001
                )

            # Emit conversation summary if one generated
            if chatbot.create_conversation_title():
                await send_message_to_websocket(
                    api_ws_client,
                    connection_id,
                    {"event": {"type": "summary", "data": chatbot.conversation_title}},
                )

            if chatbot.events_history:
                # Temporary measure for WindyMaps: set only most recent to active.
                events = set_last_event_active(
                    events=chatbot.events_history.items,
                    event_key="type",
                    event_type=VisualizationType.WINDY,
                    retain_run_active=True,
                )

                # Write new events
                await chatbot.events_history.async_add_items_upload(
                    events, reset_items=True
                )

        except Exception as error:
            logging.error(f"Fatal error encountered in response stream: {error}")

            if chatbot.events_history and chatbot.memory.chat_memory.parent_run_id:
                error_event = send_error_event(
                    api_ws_client=api_ws_client,
                    connection_id=connection_id,
                    parent_run_id=chatbot.memory.chat_memory.parent_run_id,
                )

                await chatbot.events_history.async_add_items_upload(
                    chatbot.events_history.items + [error_event], reset_items=False
                )

            if os.getenv("ENV") != "TEST":
                ecr_job_util.failure_email(
                    e=error,
                    mail_api=mail_api,
                    dev=os.getenv("ENV") != "PROD",
                    job_name="FreightGPT Stream",
                    mail_body=f"log:\n{chatbot.log_url}",
                )

    await send_close_event(session, connection_id)


async def send_error_event(
    aioboto3_ws_client: AioBaseClient, connection_id: str, parent_run_id: str = ""
) -> Dict[str, str | Dict[str, str]]:
    """
    Asynchronously sends an error event message over a WebSocket connection using aioboto3.

    Parameters:
        aioboto3_ws_client (AioBaseClient): An aioboto3 WebSocket client instance used to send messages.
        connection_id (str): The connection ID for the WebSocket over which the message will be sent.
        parent_run_id (str, optional): An optional ID representing the parent run of the error event.

    Returns:
        Dict[str, Union[str, Dict[str, str]]]: A dictionary containing the error event message that was sent.
    """
    error_event = {
        "event": {"type": "error", "data": error_message},
        "message_id": parent_run_id,
    }

    await send_message_to_websocket(
        aioboto3_ws_client,
        connection_id,
        error_event,
    )

    return error_event


async def send_data(
    connection_id: str,
    data: Dict,
    session: aioboto3.Session = None,
    aiboto3_ws_client: AioBaseClient = None,
) -> None:
    """
    Sends JSON data to client through the websocket. Must provide either an aiboto3 session or a websocket client.
    If both session and client are provided, will use client.
    Args:
        session: aioboto3 session
        connection_id: the corresponding id to the client's websocket connection.
        data: data to send down websocket.
        aiboto3_ws_client: can use pre-existing aiboto3 client instead of creating a new one.
    Returns: None
    """
    if not session and not aiboto3_ws_client:
        raise ValueError(
            "Must provide either an aioboto3 session or a websocket client."
        )

    if aiboto3_ws_client:
        await send_message_to_websocket(aiboto3_ws_client, connection_id, data)
    else:
        async with session.client(
            service_name="apigatewaymanagementapi", endpoint_url=websocket_api
        ) as api_ws_client:
            await send_message_to_websocket(api_ws_client, connection_id, data)


async def send_close_event(
    session: aioboto3.Session,
    connection_id: str,
) -> None:
    """
    Sends close event to client so that client terminates connection.
    Args:
        session: aioboto3 session
        connection_id: the corresponding id to the client's websocket connection.
    Returns: None
    """
    await send_data(
        connection_id=connection_id, data={"close": connection_id}, session=session
    )
