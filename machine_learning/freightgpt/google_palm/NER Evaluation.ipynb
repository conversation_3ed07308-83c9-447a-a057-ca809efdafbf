{"cells": [{"cell_type": "markdown", "id": "7e992498-d521-4a23-a558-672b5b90b572", "metadata": {}, "source": ["# Generate Evaluation Pairings for FreightGPT"]}, {"cell_type": "code", "execution_count": 1, "id": "1e213419-0411-4c1c-ac19-387a449a01c6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2/2 [00:00<00:00,  5.15it/s]\n"]}], "source": ["import pandas as pd\n", "from common import query_utils\n", "\n", "QUERY_STR = \"\"\" SELECT\n", "                DISTINCT city, state, country\n", "                FROM\n", "                mvp_db_dev.cities;\"\"\"\n", "\n", "# Use the utility function to get data in batches\n", "cities_df = query_utils.get_data_in_batches(\"mvp_db_dev\", 1000, QUERY_STR)"]}, {"cell_type": "code", "execution_count": 24, "id": "dcaa179b-c417-41d8-8716-b2aba490df69", "metadata": {}, "outputs": [], "source": ["class PricePredictionInput:\n", "    def __init__(\n", "        self,\n", "        origin_city,\n", "        origin_state,\n", "        destination_city,\n", "        destination_state,\n", "        origin_country=None,\n", "        origin_zip=None,\n", "        destination_country=None,\n", "        destination_zip=None,\n", "        origin_open_time=None,\n", "        origin_close_time=None,\n", "        destination_open_time=None,\n", "        destination_close_time=None,\n", "        distance_miles=None,\n", "        equipment_type=None,\n", "        weight=None,\n", "    ):\n", "        self.origin_city = origin_city\n", "        self.origin_state = origin_state\n", "        self.origin_country = origin_country\n", "        self.origin_zip = origin_zip\n", "        self.destination_city = destination_city\n", "        self.destination_state = destination_state\n", "        self.destination_country = destination_country\n", "        self.destination_zip = destination_zip\n", "        self.origin_open_time = origin_open_time\n", "        self.origin_close_time = origin_close_time\n", "        self.destination_open_time = destination_open_time\n", "        self.destination_close_time = destination_close_time\n", "        self.distance_miles = distance_miles\n", "        self.equipment_type = equipment_type\n", "        self.weight = weight\n", "\n", "    def compare(self, other):\n", "        return vars(self) == vars(other)"]}, {"cell_type": "code", "execution_count": 19, "id": "22f19a58-a86a-4220-baed-3d9120a1f77f", "metadata": {}, "outputs": [], "source": ["STATE_CODE_TO_NAME = pd.read_csv(\n", "    \"data/na_states_provinces.csv\", index_col=\"State/Province\"\n", ")"]}, {"cell_type": "code", "execution_count": 21, "id": "4595a6cf-85ec-4e97-a2ed-a6d68c062137", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: typo in /home/<USER>/.conda/envs/default/lib/python3.9/site-packages (0.1.5)\n"]}], "source": ["!pip install typo"]}, {"cell_type": "code", "execution_count": 22, "id": "c174d5f4-dcb6-4728-9ffb-b964c9b58bb9", "metadata": {}, "outputs": [], "source": ["import random\n", "import typo\n", "\n", "\n", "# Add this function to randomly introduce typos\n", "def add_typo(sentence):\n", "    typo_funcs = [\n", "        \"char_swap\",\n", "        \"missing_char\",\n", "        \"extra_char\",\n", "        \"nearby_char\",\n", "        \"similar_char\",\n", "        \"skipped_space\",\n", "        \"random_space\",\n", "        \"repeated_char\",\n", "        \"unichar\",\n", "    ]\n", "    typo_func = random.choice(typo_funcs)\n", "\n", "    s = typo.<PERSON><PERSON><PERSON><PERSON>(sentence)\n", "    sentence = getattr(s, typo_func)()\n", "\n", "    return sentence"]}, {"cell_type": "code", "execution_count": 25, "id": "a0db7f55-cb91-4196-9bb1-1d52f3166b53", "metadata": {}, "outputs": [], "source": ["import random\n", "import numpy as np\n", "\n", "from constants import EQUIPMENT_TYPES\n", "\n", "CITY_SHORTHANDS = {\n", "    \"Los Angeles\": [\"LA\", \"LAX\"],\n", "    \"San Francisco\": [\"SF\"],\n", "    \"New York City\": [\"NYC\", \"NY\"],\n", "    \"Chicago\": [\"Chi-Town\"],\n", "    \"Houston\": [\"H-Town\", \"HOU\"],\n", "    \"Dallas\": [\"Big D\", \"DAL\"],\n", "    \"Seattle\": [\"Emerald City\", \"SEA\"],\n", "    \"Denver\": [\"Mile High\", \"DEN\"],\n", "    \"Atlanta\": [\"ATL\"],\n", "    \"Phoenix\": [\"PHX\"],\n", "    \"Philadelphia\": [\"<PERSON>ly\", \"PHL\"],\n", "    \"Indianapolis\": [\"Indy\", \"IND\"],\n", "    \"Detroit\": [\"Motor City\", \"DET\"],\n", "    \"Columbus\": [\"C-bus\", \"CLB\"],\n", "    \"Nashville\": [\"Music City\", \"NAS\"],\n", "    \"Memphis\": [\"Bluff City\", \"MEM\"],\n", "    \"Portland\": [\"PDX\", \"POR\", \"Rip City\"],\n", "    \"Jacksonville\": [\"<PERSON>\", \"JAX\"],\n", "    \"Kansas City\": [\"KC\", \"KAN\"],\n", "    \"Charlotte\": [\"QC\", \"CHA\"],\n", "    \"Boston\": [\"Beantown\", \"BOS\"],\n", "    \"Las Vegas\": [\"Sin City\", \"LV\"],\n", "    \"Miami\": [\"MIA\", \"Magic City\"],\n", "    \"Minneapolis\": [\"Twin Cities\", \"MSP\"],\n", "    \"Pittsburgh\": [\"The 'Burgh\", \"PIT\"],\n", "    \"New Orleans\": [\"NOLA\", \"The Big Easy\"],\n", "    \"San Antonio\": [\"Alamo City\", \"SA\"],\n", "    \"Salt Lake City\": [\"SLC\"],\n", "    \"Milwaukee\": [\"MKE\", \"Cream City\"],\n", "    \"Oakland\": [\"OAK\"],\n", "    \"Omaha\": [\"OMA\", \"Big O\"],\n", "    \"Cleveland\": [\"CLE\"],\n", "    \"Syracuse\": [\"The 'Cuse\", \"SYR\"],\n", "}\n", "\n", "date_formats = [\"%m/%d/%y\", \"%B %d, %Y\", \"%Y-%m-%d\", \"%d-%m-%Y\", \"%A, %B %d, %Y\"]\n", "\n", "# Define a list of phrases that a human might use to ask for a price\n", "price_phrases = [\n", "    \"What's the cost from {origin_city_input} to {destination_city_input}\",\n", "    \"Can you give me the price to {destination_city_input} from {origin_city_input}\",\n", "    \"I need to know the price from {origin_city_input} to {destination_city_input}\",\n", "    \"{origin_city_input} to {destination_city_input}. What's that gonna cost me\",\n", "    \"How much does it cost from {origin_city_input} to {destination_city_input}\",\n", "    \"Price? {origin_city_input} to {destination_city_input}\",\n", "    \"What's the price for a shipment from {origin_city_input} to {destination_city_input}\",\n", "    \"How much would it cost to move goods to {destination_city_input} from {origin_city_input}\",\n", "    \"I'm shipping to {destination_city_input} from {origin_city_input}, what would that run me\",\n", "    \"Can you tell me the cost to go from {origin_city_input} to {destination_city_input}\",\n", "    \"What's the freight cost from {origin_city_input} to {destination_city_input}\",\n", "    \"I need a quote for a shipment to {destination_city_input} from {origin_city_input}\",\n", "    \"What's the estimated shipping cost from {origin_city_input} to {destination_city_input}\",\n", "    \"What would be the freight charges from {origin_city_input} to {destination_city_input}\",\n", "    \"Could you estimate the cost for a trip to {destination_city_input} from {origin_city_input}\",\n", "    \"Give me the shipping price from {origin_city_input} to {destination_city_input}\",\n", "    \"I need the rate for moving stuff from {origin_city_input} to {destination_city_input}\",\n", "    \"What's the charge for transporting goods from {origin_city_input} to {destination_city_input}\",\n", "    \"Can you quote the price for delivery to {destination_city_input} from {origin_city_input}\",\n", "    \"How much to ship from {origin_city_input} to {destination_city_input}\",\n", "    \"What are the freight rates from {origin_city_input} to {destination_city_input}\",\n", "    \"What's the cost estimate for a delivery from {origin_city_input} to {destination_city_input}\",\n", "    \"Can you calculate the cost from {origin_city_input} to {destination_city_input}\",\n", "    \"What's the delivery cost from {origin_city_input} to {destination_city_input}\",\n", "    \"Give me a cost estimate from {origin_city_input} to {destination_city_input}\",\n", "    \"How much would you charge for a delivery from {origin_city_input} to {destination_city_input}\",\n", "    \"Can you give me the price for a trip to {destination_city_input} from {origin_city_input}\",\n", "    \"What's the cost if I want to ship from {origin_city_input} to {destination_city_input}\",\n", "    \"I'm planning to ship from {origin_city_input} to {destination_city_input}, how much will it cost\",\n", "    \"{origin_city_input} -> {destination_city_input}\",\n", "    \"{origin_city_input} to {destination_city_input}\",\n", "    \"Cost to {destination_city_input} from {origin_city_input}\",\n", "]\n", "\n", "\n", "dataset = []\n", "\n", "shorthanded_cities_df = cities_df[cities_df[\"city\"].isin(list(CITY_SHORTHANDS.keys()))]\n", "\n", "\n", "def shorthand_city(city: str, p=0.9):\n", "    if city in CITY_SHORTHANDS and random.random() < p:\n", "        return random.choice(CITY_SHORTHANDS[city])\n", "    return city\n", "\n", "\n", "def name_state(state_code: str, p=0.1):\n", "    if random.random() < p:\n", "        return STATE_CODE_TO_NAME.loc[state_code][\"Name\"].title()\n", "    return state_code\n", "\n", "\n", "for _ in range(50):  # change the number based on how many data you want to generate\n", "    if random.random() < 0.1:\n", "        origin_city_state, destination_city_state = shorthanded_cities_df.sample(\n", "            2\n", "        ).to_dict(\"records\")\n", "    else:\n", "        origin_city_state, destination_city_state = cities_df.sample(2).to_dict(\n", "            \"records\"\n", "        )\n", "\n", "    origin_city_output = origin_city_state[\"city\"]\n", "    origin_state_output = origin_city_state[\"state\"]\n", "    destination_city_output = destination_city_state[\"city\"]\n", "    destination_state_output = destination_city_state[\"state\"]\n", "\n", "    origin_city_input = shorthand_city(origin_city_output)\n", "    destination_city_input = shorthand_city(destination_city_output)\n", "    origin_state_input = name_state(origin_state_output)\n", "    destination_state_input = name_state(destination_state_output)\n", "\n", "    if random.random() < 0.9:\n", "        origin_city_input = origin_city_input + \", \" + origin_state_input\n", "    if random.random() < 0.9:\n", "        destination_city_input = destination_city_input + \", \" + destination_state_input\n", "\n", "    # Construct the sentence\n", "    sentence = random.choice(price_phrases).format(\n", "        origin_city_input=origin_city_input,\n", "        destination_city_input=destination_city_input,\n", "    )\n", "    output = PricePredictionInput(\n", "        origin_city_output,\n", "        origin_state_output,\n", "        destination_city_output,\n", "        destination_state_output,\n", "    )\n", "\n", "    if random.random() < 0.5:\n", "        # Randomly select an equipment type and capitalize randomly\n", "        equipment_type_output = random.choice(list(EQUIPMENT_TYPES.keys()))\n", "        equipment_type_input = (\n", "            EQUIPMENT_TYPES[equipment_type_output].title()\n", "            if random.random() < 0.5\n", "            else EQUIPMENT_TYPES[equipment_type_output]\n", "        )\n", "        if random.random() < 0.1:\n", "            sentence += \" {}\".format(equipment_type_input)\n", "        else:\n", "            sentence += \" for a {}\".format(equipment_type_input)\n", "        output.equipment_type = equipment_type_output\n", "\n", "    # if random.random() < 0.5:\n", "    #     # Randomly generate a date within next one year\n", "    #     date = (datetime.now() + <PERSON><PERSON><PERSON>(days=random.randint(1, 365)))\n", "    #     date_input = date.strftime(random.choice(date_formats))\n", "    #     date_output = date.strftime(\"%m/%d/%y\")\n", "    #     sentence += \" on {}\".format(date_input)\n", "    #     output.origin_close_time = equipment_type_output\n", "\n", "    sentence += random.choice([\"\", \"?\", \".\"])\n", "    sentence = add_typo(sentence)\n", "\n", "    dataset.append({\"input\": sentence, \"output\": output})\n", "\n", "# Create DataFrame from the dataset\n", "df = pd.DataFrame(dataset)"]}, {"cell_type": "code", "execution_count": 26, "id": "774010a9-e4ca-4bec-be27-b2dc4206813a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([Could you estimate the cost for a trip to Newman, CA from Los Molinos, CA?,\n", "       What's the cots if I want to ship from Cream City, WI to Twin Cities, MN,\n", "       I need to know the price from Antioch, CA to Mkcinney, TX,\n", "       What wouldd be the freight charges from Bedford Heights, OH to Earth City, MO for a Container,\n", "       Give me the shipping price from East Grand Forks, MN to Johnstown, CO for a Dry  Van?,\n", "       Could you estimate the cost for a trip to Lakemoor, Illnois from Grand Rapids, MN for a Container?,\n", "       I'm pl anning to ship from Othello, WA to Wooster, OH, how much will it cost,\n", "       I need the rate for moving stuff from Santa Clara, CA to S San Francisc, CA for a Dry Van?,\n", "       I'm shipping<PERSON> Fullerton, CA from Decatur, AL, what would that run me for a Reefer.,\n", "       What's the charge for transporting goods from Hazelwod to North Richland Hills, Texas?,\n", "       Can you calculate the cost from South Brunswick Township, New Jersey to Wilder, KY  for a Dry Van?,\n", "       What's the cost if I want to s hip from Los Lunas, NM to Commerce, CA.,\n", "       <PERSON> rovidence, RI to Downers Grove, IL for a Dry Van,\n", "       Price? Hebron, OH to Brookhaven, Missi ssippi,\n", "       What's the charge for transporting goods from Mosic, PA to Carlisle, PA for a Power Only.,\n", "       What's the estimated shippingcost from Winchester, KY to Harrisburg, PA for a Reefer.,\n", "       KC, MO ->The Big Easy, LA?,\n", "       Can you give methe price to Port Orange, FL from Little Rock, AR for a Dry Van.,\n", "       I neeed a quote for a shipment to Kearney, NE from Salem,\n", "       Can you give me the price for a trip to Wodlawn, OH from Camas, WA for a Dry Van.,\n", "       I need to know the price from Yuma, AZ to Cape Girrdeau, MO for a Container.,\n", "       I need to know the price from Male Grove, MN to Macon, GA?,\n", "       Addison, Illinois to Brook Par, OH. What's that gonna cost me?,\n", "       Give me the shipping price from Patterson, CA to Pliansboro, NJ.,\n", "       Give me the shipping price from South San Francsico, CA to La Mirada, CA,\n", "       What's the freignht cost from Bakersfield, CA to Hereford, Texas,\n", "       Givve me a cost estimate from Freeport, TX to Cinnaminson, NJ for a Dry Van.,\n", "       Dixon, IL -> ATL, GA for a DryVan,\n", "       Whwt's the delivery cost from Post Falls, ID to Grand Prairie, TX for a Reefer.,\n", "       I'm shipping to Chi-Town, IL form Moonachie, NJ, what would that run me for a <PERSON>er,\n", "       How much would you charge for a delivery from Baton Rouge, LA to Attalla, AL.,\n", "       Give me the shipping price from Idaho Falls, ID to Ventura, A for a Dry Van,\n", "       How umch would it cost to move goods to Mc Calla from Pleasant Grove, CA?,\n", "       What arde the freight rates from South Brunswick, NJ to Bremerton, WA for a Container?,\n", "       Give me a cost estimate from Jeferson, GA to Sealy, TX?,\n", "       What's the cost estimate for a delivery from Boardman, OR to Pery, NY for a Reefer?,\n", "       Torrance, CA to Lelland, NC?,\n", "       How much  to ship from Merced, CA to Keasbey, NJ,\n", "       Can you quote the price for delivery to Glendale, WI f rom Tremont, PA?,\n", "       What's the estimated shipling cost from Sunnyside, WA to Maplewood, Minnesota.,\n", "       <PERSON>w Apw, MI -> <PERSON><PERSON><PERSON>, CA for a Pup,\n", "       I'm planning to ship from Gurnee, IL to Netcong, NJ, how much wil it cost?,\n", "       Give me a cost estimate from Caapitol Heights, MD to Alachua, FL for a Reefer?,\n", "       What are the freight rates from Latham to Thorndale, PA or a Dry Van,\n", "       Whar's the cost from Nashua, NH to Caldwell, ID for a Dry Van.,\n", "       I ned to know the price from Manchester, CT to OAK, CA?,\n", "       How much to ship from North Bergen, NJ to Framuingham for a Dry Van.,\n", "       How much does it cost from Healdsburg, California to Montgometry, NY?,\n", "       How much to ship from Tullahomma, Tennessee to St Louis, MO Dry Van,\n", "       Could you setimate the cost for a trip to Foster City, CA from Caln, PA.],\n", "      dtype=object)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"input\"].values"]}, {"cell_type": "code", "execution_count": 3, "id": "e67c10da-0298-46ef-9069-27b234fac55f", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_1244/1683099733.py\u001b[0m in \u001b[0;36m<cell line: 58>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     56\u001b[0m \u001b[0;31m# Instantiate model and call evaluation\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     57\u001b[0m \u001b[0;31m# Assuming 'model' is your defined and trained model.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 58\u001b[0;31m \u001b[0mevaluate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdataset\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["# Define Dataset\n", "import datetime\n", "\n", "# Define Dataset\n", "dataset = [\n", "    {\n", "        \"input\": \"Give me the price from SF to LA.\",\n", "        \"output\": PricePredictionInput(\n", "            \"San Francisco\",\n", "            \"CA\",\n", "            \"Los Angeles\",\n", "            \"CA\",\n", "            datetime.datetime.now().strftime(\"%X\"),\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Boston to NYC for reefer on 07/24/24\",\n", "        \"output\": PricePredictionInput(\n", "            \"Boston\", \"MA\", \"New York City\", \"NY\", \"07/24/24\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Chattanooga to DC for reefer on 07/30/23\",\n", "        \"output\": PricePredictionInput(\n", "            \"Chattanooga\", \"TN\", \"Washington DC\", \"DC\", \"07/30/23\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"What would it cost to ship from Dallas, TX to Las Vegas, NV?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Dallas\", \"TX\", \"Las Vegas\", \"NV\", datetime.datetime.now().strftime(\"%X\")\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Estimate the price for a delivery from Miami to Chicago for a dry van on 07/31/24\",\n", "        \"output\": PricePredictionInput(\n", "            \"Miami\", \"FL\", \"Chicago\", \"IL\", \"07/31/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Can you tell me the price from Denver to Seattle for a reefer on 08/01/24?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Denver\", \"CO\", \"Seattle\", \"WA\", \"08/01/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"What's the cost from Phoenix to Philadelphia for dry van on 12/25/24?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Phoenix\", \"AZ\", \"Philadelphia\", \"PA\", \"12/25/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Cost from SF to LA now.\",\n", "        \"output\": PricePredictionInput(\n", "            \"San Francisco\",\n", "            \"CA\",\n", "            \"Los Angeles\",\n", "            \"CA\",\n", "            datetime.datetime.now().strftime(\"%X\"),\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Boston to NYC. Reefer. 07/24/24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Boston\", \"MA\", \"New York City\", \"NY\", \"07/24/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Chattanooga to DC. Reefer. 07/30/24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Chattanooga\",\n", "            \"TN\",\n", "            \"Washington DC\",\n", "            \"DC\",\n", "            \"07/30/24\",\n", "            equipment_type=\"Reefer\",\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Price? Dallas, TX to Las Vegas, NV.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Dallas\", \"TX\", \"Las Vegas\", \"NV\", datetime.datetime.now().strftime(\"%X\")\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Miami to Chicago. Dry Van. 07/31/24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Miami\", \"FL\", \"Chicago\", \"IL\", \"07/31/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Denver to Seattle. Reefer. 08/01/24?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Denver\", \"CO\", \"Seattle\", \"WA\", \"08/01/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Phoenix to Philadelphia. Dry van. 12/25/24?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Phoenix\", \"AZ\", \"Philadelphia\", \"PA\", \"12/25/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Cost from SF to LA now.\",\n", "        \"output\": PricePredictionInput(\n", "            \"San Francisco\",\n", "            \"CA\",\n", "            \"Los Angeles\",\n", "            \"CA\",\n", "            datetime.datetime.now().strftime(\"%X\"),\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Boston to NYC. Reefer. July 24, 24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Boston\", \"MA\", \"New York City\", \"NY\", \"07/24/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Chattanooga to DC. Reefer. 07/30/24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Chattanooga\",\n", "            \"TN\",\n", "            \"Washington DC\",\n", "            \"DC\",\n", "            \"07/30/24\",\n", "            equipment_type=\"Reefer\",\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Price? Dallas, TX to Las Vegas, NV. Now.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Dallas\", \"TX\", \"Las Vegas\", \"NV\", datetime.datetime.now().strftime(\"%X\")\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Miami to Chicago. Dry Van. July 31st, 24.\",\n", "        \"output\": PricePredictionInput(\n", "            \"Miami\", \"FL\", \"Chicago\", \"IL\", \"07/31/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Denver to Seattle. Reefer. Aug 1 2024?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Denver\", \"CO\", \"Seattle\", \"WA\", \"08/01/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Phoenix to Philadelphia. Dry van. Xmas day 24?\",\n", "        \"output\": PricePredictionInput(\n", "            \"Phoenix\", \"AZ\", \"Philadelphia\", \"PA\", \"12/25/24\", equipment_type=\"Dry Van\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"SF to LA, like, right now.\",\n", "        \"output\": PricePredictionInput(\n", "            \"San Francisco\",\n", "            \"CA\",\n", "            \"Los Angeles\",\n", "            \"CA\",\n", "            datetime.datetime.now().strftime(\"%X\"),\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Boston to NYC with a reefer on 07-24-2024\",\n", "        \"output\": PricePredictionInput(\n", "            \"Boston\", \"MA\", \"New York City\", \"NY\", \"07/24/24\", equipment_type=\"Reefer\"\n", "        ),\n", "    },\n", "    {\n", "        \"input\": \"Chattanooga to DC for a reefer 7/30/24\",\n", "        \"output\": PricePredictionInput(\n", "            \"Chattanooga\",\n", "            \"TN\",\n", "            \"Washington DC\",\n", "            \"DC\",\n", "            \"07/30/24\",\n", "            equipment_type=\"Reefer\",\n", "        ),\n", "    },\n", "]\n", "\n", "\n", "import pandas as pd\n", "\n", "# Create DataFrame from the dataset\n", "df = pd.DataFrame(dataset)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}