import vertexai
from vertexai.preview.language_models import ChatModel
from vertexai.preview.language_models import InputOutputTextPair

from common import google_sheets_utils
from machine_learning.freightgpt.chatbot_sim import ChatBot
from machine_learning.freightgpt.google_palm import request_extractor
from machine_learning.price_recommendation import price_predictor

# https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0
EXAMPLES_SHEET_ID = "1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo"


class PaLMForChat(ChatBot):
    def __init__(self) -> None:
        vertexai.init(project="truce-330420", location="us-central1")
        self.chat_model = ChatModel.from_pretrained("chat-bison@001")
        self.parameters = {
            "temperature": 0.2,
            "max_output_tokens": 256,
            "top_p": 0.8,
            "top_k": 40,
        }
        self.initialize_model()
        self.extractor = request_extractor.PaLMForText()
        self.pricer = price_predictor.VertexAutoMLPricePredictor()

    def augment_input_text(self, api_response: str, user_text: str) -> str:
        return f"""Truce Pricing API Response: {api_response}\n\nUser Text: {user_text}\n\nOutput Text:\n"""

    def initialize_model(self):
        gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
        # Load examples from Freight GPT Examples sheet
        # https://docs.google.com/spreadsheets/d/1gihUhLp6m4kb-ZqeajZIESUEur4NmzaSvNmCcKYYYqo/edit#gid=0
        examples_df = gsheets_utils.load_sheet_into_dataframe(
            EXAMPLES_SHEET_ID, "Examples", skiprows=1
        )

        context = gsheets_utils.load_sheet_into_dataframe(
            EXAMPLES_SHEET_ID, "PaLM Prompt"
        )["Context"].values[0]
        examples = [
            InputOutputTextPair(
                input_text=self.augment_input_text(r["API Response"], r["Prompt"]),
                output_text=r["Response"],
            )
            for _, r in examples_df.iterrows()
        ]
        self.chat = self.chat_model.start_chat(
            context=context,
            examples=examples,
        )

    def send_message(self, msg: str) -> str:
        if msg.lower() == "reinit":
            self.initialize_model()
            self.last_api_response = "Model reinitialized. Ready to chat."
            return "Reinitialized the model. Ready to chat."
        self.last_api_response = request_extractor.extractor_handler(
            msg, self.extractor, self.pricer
        )
        input_text = self.augment_input_text(
            api_response=self.last_api_response, user_text=msg
        )
        return self.chat.send_message(input_text, **self.parameters).text


# This is what we use for PaLMForChat
def chatbot_handler(request: dict, chatbot: ChatBot) -> str:
    # TODO(P1): Make statusCode int not str.
    if request is None:
        return {"statusCode": "400", "error": "No request found"}
    if "message" not in request:
        return {"statusCode": "400", "error": "No field 'text' found in request"}

    try:
        chat_response = chatbot.send_message(request["message"])
    except Exception as e:
        return {
            "statusCode": "500",
            "body": {"message": str(e), "responseType": "ERROR"},
        }

    if isinstance(chatbot, PaLMForChat):
        if str(chatbot.last_api_response) in chat_response:
            return {
                "statusCode": "200",
                "body": {
                    "responseType": "MARKDOWN",
                    "message": "We accidentally revealed our API response:\n"
                    + chat_response,
                },
            }
        # Temporarily add api response to the chat response.
        chat_response += (
            "\n\n[Developer Mode]\n`" + str(chatbot.last_api_response) + "`"
        )

    return {
        "statusCode": "200",
        "body": {
            "responseType": "MARKDOWN",
            "message": chat_response,
        },
    }


# May want to add this to chatbot_sim.py

# if isinstance(chatbot, PaLMForChat):
#     print("response:", chatbot.last_api_response)
