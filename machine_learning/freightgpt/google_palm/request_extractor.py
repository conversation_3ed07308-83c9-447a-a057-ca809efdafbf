import datetime
import json
import logging
import typing

import vertexai
from vertexai.language_models import TextGenerationModel

from machine_learning.freightgpt.google_palm import inference_api
from machine_learning.price_recommendation import price_predictor


FEW_SHOT_EXAMPLES = """Text: Give me a cost estimate from Issaquah, EWA to Lake Worth
JSON: {
    \"origin_city\": \"Issaquah\",
    \"origin_state\": \"WA\",
    \"destination_city\": \"Lake Worth\",
    \"destination_state\": \"FL\"
}

Text: What\'s the estimated shipping cost from Butte, MT to Erlanger, KY for a Pup

JSON: {
    \"origin_city\": \"Butte\",
    \"origin_state\": \"MT\",
    \"destination_city\": \"Erlanger\",
    \"destination_state\": \"KY\",
    \"equipment_type\": \"Pup\"
}


Text: Price? Grensburg, PA to Lexington, KY

JSON: {
    \"origin_city\": \"Greensburg\",
    \"origin_state\": \"PA\",
    \"destination_city\": \"Lexington\",
    \"destination_state\": \"KY\"
}


Text: What are the freight rates from Madison Heights to Columbus, OH.

JSON: {
    \"origin_city\": \"Madison Heights\",
    \"origin_state\": \"MI\",
    \"destination_city\": \"Columbus\",
    \"destination_state\": \"OH\"
}


Text: Can you calculate the cost from Trinity, AL tl Jackson, Tennessee for a dry van.

JSON: {
    \"origin_city\": \"Trinity\",
    \"origin_state\": \"AL\",
    \"destination_city\": \"Jackson\",
    \"destination_state\": \"TN\",
    \"equipment_type\": \"Dry Van\"
}


Text: Give me a cost estimate from Pleasant Prairie, WI to Mechanicsburg, PA for a reefer.

JSON: {
    \"origin_city\": \"Pleasant Prairie\",
    \"origin_state\": \"WI\",
    \"destination_city\": \"Mechanicsburg\",
    \"destination_state\": \"PA\",
    \"equipment_type\": \"Reefer\"
}


Text: I need to know the price from Dayville, Connectiicut to Ocala, FL.

JSON: {
    \"origin_city\": \"Dayville\",
    \"origin_state\": \"CT\",
    \"destination_city\": \"Ocala\",
    \"destination_state\": \"FL\"
}


Text: I need the rate for moving stuff from Cliftn, NJ to Caledonia, MI?

JSON: {
    \"origin_city\": \"Clifton\",
    \"origin_state\": \"NJ\",
    \"destination_city\": \"Caledonia\",
    \"destination_state\": \"MI\"
}


Text: Can you cal culate the cost from Hermitage to Reno, TX Dry Van

JSON: {
    \"origin_city\": \"Hermitage\",
    \"origin_state\": \"PA\",
    \"destination_city\": \"Reno\",
    \"destination_state\": \"TX\",
    \"equipment_type\": \"Dry Van\"
}


Text: I\'m shipping to Williamsport, MD from Anaheim, CA, what would that un me?

JSON: {
    \"origin_city\": \"Anaheim\",
    \"origin_state\": \"CA\",
    \"destination_city\": \"Williamsport\",
    \"destination_state\": \"MD\"
}


Text: What\'s the cost from C-bus, iN to Jax, FL.

JSON: {
    \"origin_city\": \"Columbus\",
    \"origin_state\": \"IN\",
    \"destination_city\": \"Jacksonville\",
    \"destination_state\": \"FL\"
}


Text: What\'s the cost ftom Hanover Township, Pennsylvania to Cloverdale, CA for a Flatbed?

JSON: {
    \"origin_city\": \"Hanover Township\",
    \"origin_state\": \"PA\",
    \"destination_city\": \"Cloverdale\",
    \"destination_state\": \"CA\",
    \"equipment_type\": \"Flatbed\"
}

Text: Fuck you

JSON: {
    "responseType": "ERROR",
    "message": "Cannot parse API request."
}

Text: asdf

JSON: {
    "responseType": "ERROR",
    "message": "Cannot parse API request."
}

Text: What\'s the cost ftom Hanover Township

JSON: {
    "responseType": "ERROR",
    "message": "Missing destination."
}

Text: What\'s the cost to NYC

JSON: {
    "responseType": "ERROR",
    "message": "Missing origin."
}

Text: How much is a roundtrip from columbus to chicago

JSON: {
    \"origin_city\": \"Columbus\",
    \"origin_state\": \"OH\",
    \"destination_city\": \"Chicago\",
    \"destination_state\": \"IL\"
}
"""


class NERModel:
    def extract_price_predictor_request(
        self, text: str
    ) -> price_predictor.PricePredictorInput:
        raise NotImplementedError


class PaLMForText(NERModel):
    def __init__(self):
        vertexai.init(project="truce-330420", location="us-central1")
        self.parameters = {
            "temperature": 0.2,
            "max_output_tokens": 1024,
            "top_p": 0.8,
            "top_k": 40,
        }
        self.model = TextGenerationModel.from_pretrained("text-bison@001")

    def predict(self, text: str) -> str:
        response = self.model.predict(
            """Extract the technical specifications from the text below in a JSON format.

        """
            + FEW_SHOT_EXAMPLES
            + """

        Text: """
            + str(text)
            + """

        JSON:
        """,
            **self.parameters,
        )
        return response.text

    def extract_price_predictor_request(
        self, text: str
    ) -> typing.Union[str, dict, price_predictor.PricePredictorInput]:
        response = self.predict(text)
        try:
            output = json.loads(response)
        except Exception as e:
            logging.error(e)
            logging.error(f"Error: Unable to parse response {response}.")
            return response
        if (
            "origin_city" not in output
            or "origin_state" not in output
            or "destination_city" not in output
            or "destination_state" not in output
        ):
            logging.error(
                "Missing minimum fields of origin_city, origin_state, destination_city, destination_state"
            )
            return output
        predictor_input = price_predictor.PricePredictorInput(
            output["origin_city"],
            output["origin_state"],
            output["destination_city"],
            output["destination_state"],
        )
        for k in output:
            predictor_input.__setattr__(k, output[k])
        return predictor_input


def extractor_handler(
    input_text: str,
    extractor: NERModel,
    pricer: price_predictor.PricePredictor,
):
    prediction = extractor.extract_price_predictor_request(input_text)
    response = {
        "status": "200",
    }
    if isinstance(prediction, str):
        response["body"] = {
            "responseType": "ERROR",
            "message": f"Unable to get minimum fields, the text agent returned the following: {prediction}",
        }
        return response
    if isinstance(prediction, dict):
        response["body"] = {
            "responseType": "ERROR",
            "message": f"Unable to get minimum fields, we parsed the following: {prediction}",
        }
        return response
    if isinstance(prediction, price_predictor.PricePredictorInput):
        prediction_dict = prediction.to_serializable_dict()
        prediction_dict["origin_close_day"] = datetime.datetime.now().strftime(
            "%Y-%m-%d"
        )
        price_predictor_response = inference_api.price_prediction_handler(
            prediction_dict, pricer
        )
        if price_predictor_response["statusCode"] != 200:
            response["body"] = {
                "responseType": "ERROR",
                "message": f"We parsed the following PricePredictorInput: {prediction}, and got the following error: {price_predictor_response}",
            }
            return response

        price_predictions = price_predictor_response["body"]
        prediction_dict["mlPrice"] = price_predictions["value"]
        response["body"] = {
            "responseType": "PRICING",
            "data": prediction_dict,
        }
        return response
    else:
        response["body"] = {
            "responseType": "ERROR",
            "message": f"Weird error, but, we parsed the following: {prediction}",
        }
        return response
