import datetime
import os

import pandas as pd
import pytz

from common.constants import PROJECT_ROOT
from common.google_maps_utils import GoogleMapsUtils
from machine_learning.price_recommendation import price_predictor

# These are UTC times. In central time, they are 10am and 1am.
DEFAULT_LOCAL_APPOINTMENT_TIMES = {
    "origin_open": "15:00:00",
    "origin_close": "15:00:00",
    "destination_open": "06:00:00",
    "destination_close": "06:00:00",
}


# Consider using Pipelines object from Feature Engineering.ipynb.


def build_and_normalize_timestamp(
    field: str, event: dict, gmaps_utils: GoogleMapsUtils
) -> pd.Timestamp:
    """Builds a timestamp from the given field and normalizes it to UTC.

    Args:
        field: The field to build the timestamp from. Can be one of
            "origin_open", "origin_close", "destination_open", "destination_close".
        event: The event dictionary. Has keys like "origin_open_day", "origin_open_time",
                "origin_zip".
        gmaps_utils: The GoogleMapsUtils object.

    Returns:
        The normalized timestamp in UTC.
    """
    if field + "_day" not in event:
        return pd.NA

    date_str = event[field + "_day"]
    if field + "_time" in event:
        time_str = event[field + "_time"]
    else:
        time_str = DEFAULT_LOCAL_APPOINTMENT_TIMES[field]

    local_time = pd.Timestamp(
        datetime.datetime.strptime(date_str + " " + time_str, "%Y-%m-%d %H:%M:%S")
    )

    field_prefix = field.split("_")[0]
    tz = gmaps_utils.get_tz_from_zip(
        event[field_prefix + "_zip"], event[field_prefix + "_country"]
    )
    return pytz.utc.normalize(tz.localize(local_time)).replace(tzinfo=None)


def price_prediction_handler(event, pricer: price_predictor.PricePredictor):
    assert "origin_city" in event
    assert "origin_state" in event
    assert "destination_city" in event
    assert "destination_state" in event
    assert "origin_close_day" in event

    # s = time.perf_counter()
    # print("INIT SPEED", time.perf_counter() - s)
    if "origin_country" not in event:
        event["origin_country"] = "US"
    if "destination_country" not in event:
        event["destination_country"] = "US"
    if "origin_zip" not in event:
        # TODO(P0): #1277 See why this is randomized.
        event["origin_zip"] = pricer.gmaps_utils.get_zip_from_city_state(
            event["origin_city"], event["origin_state"], event["origin_country"]
        )
    if "destination_zip" not in event:
        event["destination_zip"] = pricer.gmaps_utils.get_zip_from_city_state(
            event["destination_city"],
            event["destination_state"],
            event["destination_country"],
        )

    check_fields = ["city", "state", "country", "zip"]
    # Check if all origin fields are the same as destination fields.
    if all(["origin_" + f] == event["destination_" + f] for f in check_fields):
        return {"statusCode": 422, "body": "Origin and destination are the exact same."}

    origin_open_time = build_and_normalize_timestamp(
        "origin_open", event, pricer.gmaps_utils
    )
    origin_close_time = build_and_normalize_timestamp(
        "origin_close", event, pricer.gmaps_utils
    )
    destination_open_time = build_and_normalize_timestamp(
        "destination_open", event, pricer.gmaps_utils
    )
    destination_close_time = build_and_normalize_timestamp(
        "destination_close", event, pricer.gmaps_utils
    )

    # pretty print event
    # print("EVENT")
    # for k, v in event.items():
    #     print(k, v)

    # print("ORIGIN OPEN TIME", origin_open_time)
    # print("ORIGIN CLOSE TIME", origin_close_time)
    # print("DESTINATION OPEN TIME", destination_open_time)
    # print("DESTINATION CLOSE TIME", destination_close_time)

    # s = time.perf_counter()
    predicted_cogs_total = pricer.predict(
        price_predictor.PricePredictorInput(
            origin_city=event["origin_city"],
            origin_state=event["origin_state"],
            origin_country=event["origin_country"],
            origin_zip=event["origin_zip"],
            destination_city=event["destination_city"],
            destination_state=event["destination_state"],
            destination_country=event["destination_country"],
            destination_zip=event["destination_zip"],
            origin_open_time=origin_open_time,
            origin_close_time=origin_close_time,
            destination_open_time=destination_open_time,
            destination_close_time=destination_close_time,
        )
    )
    # print("prediCTION SPEED", time.perf_counter() - s)

    # Don't block response on saving cache files
    # import threading

    # threading.Thread(target=pricer.save_and_upload_cache_files).start()

    return {"statusCode": 200, "body": predicted_cogs_total}


def evaluate_scrapers():
    # Make sure to generate a new ml_scrape_full.csv file from Feature Engineering.ipynb.
    import numpy as np

    # Plot histogarm
    import matplotlib.pyplot as plt

    ml_scrape_df = pd.read_csv(
        os.path.join(
            PROJECT_ROOT,
            "machine_learning",
            "price_recommendation",
            "data",
            "ml_scrape_full.csv",
        )
    )

    ml_scrape_df["origin_zip"] = ml_scrape_df["origin_zip"].astype(str)
    ml_scrape_df["destination_zip"] = ml_scrape_df["destination_zip"].astype(str)

    labels = np.array([r["OUTPUT_cogs_total"] for r in ml_scrape_df.to_dict("records")])

    rds_data_df = pd.read_csv(
        os.path.join(
            PROJECT_ROOT,
            "machine_learning",
            "price_recommendation",
            "data",
            "rds_pricing_full.csv",
        )
    )

    mystery_df = pd.read_csv(
        os.path.join(
            PROJECT_ROOT,
            "machine_learning",
            "price_recommendation",
            "data",
            "mystery_full.csv",
        )
    )
    saved_predictions_path = os.path.join(
        PROJECT_ROOT, "machine_learning", "price_recommendation", "data"
    )

    # Only use the following columns

    # Convert destination_zip and origin_zip to ints

    LESS_COLS_NOZIP_ENDPOINT = "7714457254476382208"
    pricer = price_predictor.VertexAutoMLPricePredictor(LESS_COLS_NOZIP_ENDPOINT)
    results = pricer.batch_predict(ml_scrape_df.to_dict("records"))
    # TODO(P2): Load predictions_lower and predictions_upper for analysis.
    np.save(
        os.path.join(saved_predictions_path, "predictions_lower.npy"),
        np.array([r["lower_bound"] for r in results]),
    )
    np.save(
        os.path.join(saved_predictions_path, "predictions.npy"),
        np.array([r["value"] for r in results]),
    )
    np.save(
        os.path.join(saved_predictions_path, "predictions_upper.npy"),
        np.array([r["upper_bound"] for r in results]),
    )

    predictions = np.load(os.path.join(saved_predictions_path, "predictions.npy"))

    errors = labels - predictions
    mae = np.mean(np.abs(errors))
    print(mae)
    boards = ml_scrape_df["board"].unique()

    # Do the same prediction, removing a number of fetaures
    plt.figure(figsize=(20, 15))

    # Plot errors
    error_bins = np.linspace(-1000, 1000, 50)
    plt.subplot(3, 3, 1)
    plt.hist(errors, bins=error_bins)
    plt.title("Errors")

    value_bins = np.linspace(0, 5000, 50)
    # Plot labels vs. predictions
    plt.subplot(3, 3, 2)
    plt.hist(labels, bins=value_bins, alpha=0.5, label="Labels", color="blue")
    plt.hist(predictions, bins=value_bins, alpha=0.5, label="Predictions", color="red")
    plt.title("Labels vs Predictions")
    plt.legend()

    # Comparison of normalized labels
    plt.subplot(3, 3, 3)
    plt.hist(labels, bins=50, alpha=0.5, label="Labels", density=True)
    plt.hist(
        rds_data_df["OUTPUT_cogs_total"],
        bins=50,
        alpha=0.5,
        label="RDS Labels",
        density=True,
    )
    plt.title("Comparison of Normalized Labels")
    plt.legend()

    # Plot mystery labels
    plt.subplot(3, 3, 4)
    plt.hist(mystery_df["OUTPUT_cogs_total"], bins=value_bins)
    plt.title("Mystery Labels")

    # Plot RDS labels
    plt.subplot(3, 3, 5)
    plt.hist(rds_data_df["OUTPUT_cogs_total"], bins=value_bins)
    plt.title("RDS Labels")

    # Your boards
    for idx, board in enumerate(boards):
        ax = plt.subplot(3, 3, 7 + idx)  # 7th, 8th, and 9th positions for the boards
        mask = ml_scrape_df["board"] == board
        errors = ml_scrape_df.loc[mask, "OUTPUT_cogs_total"] - predictions[mask]
        positive_errors = errors[errors >= 0]
        negative_errors = errors[errors < 0]
        ax.hist(
            [negative_errors, positive_errors],
            bins=error_bins,
            color=["red", "blue"],
            label=["Negative Errors", "Positive Errors"],
            stacked=True,
        )
        ax.set_title(f"Error Distribution for {board}")
        ax.legend()
        print(f"MAE for {board}: {np.mean(np.abs(errors))}")

    # Show all plots in a single window
    plt.tight_layout()
    plt.show()


def main():
    # event = {
    #     "origin_city": "Huntley",
    #     "origin_state": "IL",
    #     "origin_country": "US",
    #     # "origin_zip": "60142",
    #     "destination_city": "Grand Rapids",
    #     "destination_state": "MI",
    #     "destination_country": "US",
    #     # "destination_zip": "49501",
    #     # "origin_open_time": "08:00:00",
    #     # "origin_open_day": "2023-07-11",
    #     "origin_close_time": "19:00:00",
    #     "origin_close_day": "2023-07-11",
    #     # "destination_open_time": "08:00:00",
    #     # "destination_open_day": "2023-07-11",
    #     # "destination_close_time": "17:00:00",
    #     # "destination_close_day": "2023-07-11",
    # }
    # s = time.perf_counter()
    # pricer = price_predictor.VertexAutoMLPricePredictor()
    # print(price_prediction_handler(event, pricer))
    # print(time.perf_counter() - s)

    # # event = {
    # #     "originCity": "Huntley",
    # #     "originState": "IL",
    # #     "originCountry": "US",
    # #     "originZip": "60142",
    # #     "destinationCity": "Grand Rapids",
    # #     "destinationState": "MI",
    # #     "destinationCountry": "US",
    # #     "destinationZip": "49501",
    # #     "originOpenTime": datetime.datetime(2023, 7, 11, 8, 0, 0),
    # #     "originCloseTime": datetime.datetime(2023, 7, 11, 17, 0, 0),
    # #     "destinationOpenTime": datetime.datetime(2023, 7, 11, 8, 0, 0),
    # #     "destinationCloseTime": datetime.datetime(2023, 7, 11, 17, 0, 0),
    # #     "equipmentType": "Dry Van",
    # #     "weight": 10000,
    # # }
    # # print(pricer.batch_predict([event, event, event]))

    # dictionary = {
    #     "id": "0002db48-fd99-4f0b-9fec-ea753e52f775",
    #     "brokerId": "076e9914-9bb2-4204-9acf-78f9af68eb2e",
    #     "laneId": "69474650-fbf3-47df-bce8-6088b58d9ad0",
    #     "shipperId": "111d145e-2ac6-4585-ac5e-fe22095cbc7f",
    #     "brokerPrimaryReference": "6000729436",
    #     "shipperPrimaryReference": "157677029",
    #     "stopCount": 2.0,
    #     "distanceMiles": 1152.2996427604,
    #     "cogsLineHaul": 2300.0,
    #     "cogsAccessorial": 0.0,
    #     "cogsFuel": "",
    #     "cogsTotal": 2300.0,
    #     "revenueLineHaul": 1843.2,
    #     "revenueFuel": 564.63,
    #     "revenueAccessorial": "",
    #     "revenueTotal": 2407.83,
    #     "originWarehouse": "53 ODELL BREWING",
    #     "destinationWarehouse": "RBD - MONARCH BEVERAGE",
    #     "shipmentStatus": "Delivered",
    #     "shipmentRank": "Primary",
    #     "weight": 38023.92,
    #     "originOpenTime": "2023-03-31 17:00:00",
    #     "originCloseTime": "2023-03-31 17:00:00",
    #     "originArrivalTime": "2023-03-31 17:00:00",
    #     "originDepartureTime": "2023-03-31 18:30:00",
    #     "destinationOpenTime": "2023-04-03 11:30:00",
    #     "destinationCloseTime": "2023-04-03 11:30:00",
    #     "destinationArrivalTime": "2023-04-03 11:30:00",
    #     "destinationDepartureTime": "2023-04-03 12:30:00",
    #     "loadCreationTime": "2023-03-22 17:37:44",
    #     "loadActivationTime": "2023-03-22 18:11:14",
    #     "carrierAssignedTime": "2023-03-30 16:55:08",
    #     "score": 94.0,
    #     "blt": 68.8127777778,
    #     "clt": 69.3711111111,
    #     "preBook": 10.0811111111,
    #     "shipmentClass": "canonical",
    #     "pendingUpdate": "{}",
    #     "originDelayMinutes": 0.0,
    #     "destinationDelayMinutes": 0.0,
    #     "carrierScore": 100.0,
    #     "brokerScore": 95.0,
    #     "performanceScore": 99.0,
    #     "costScore": 86.0,
    #     "shipmentIngestionTime": "",
    #     "originZipId": "743dc178-0183-45f6-a93b-d6e604625cff",
    #     "destinationZipId": "7b86a024-99f2-4155-ac33-174ad6abd96d",
    #     "ltuScore": 100.0,
    #     "prebookScore": 90.0,
    #     "otpScore": 100.0,
    #     "otdScore": 100.0,
    #     "customerDirect": "",
    #     "mlPrice": 1202.89,
    #     "mlPriceUpperBound": 2421.28,
    #     "mlPriceLowerBound": 952.697,
    #     "modelName": "rds-mystery-subset-v1",
    #     "modelSource": "VertexAutoMLPricePredictor",
    #     "mlPricedTime": "2023-07-25 21:47:21",
    #     "originCityId": "478b395f-7e72-4248-88c9-4b1f7a0761be",
    #     "destinationCityId": "4cfa0dd5-2d96-4833-8548-9bd4c6221fd5",
    #     "equipmentType": "Reefer",
    #     "shipmentMode": "TL",
    #     "originCity": "Fort Collins",
    #     "originCountry": "US",
    #     "originState": "CO",
    #     "destinationCity": "Indianapolis",
    #     "destinationCountry": "US",
    #     "destinationState": "IN",
    #     "originZip": "80524",
    #     "destinationZip": "46236",
    #     "OUTPUT_cogs_total": 2300.0,
    # }

    # batch_predictions = pricer.batch_predict([dictionary])

    # print(batch_predictions)

    # idx = "000177ce-3ffb-4c5d-aa37-6831db75abbb"
    # s3_ml_data = S3("truce-ml-data")
    # # ml_df_filename = "data/pricing_ml_df_v0.csv"
    # ml_df_filename = "data/pricing_ml_df_2023-05-10_18-58-13.csv"
    # s3_ml_data.pull(ml_df_filename, merge=False)
    # ml_df = pd.read_csv(ml_df_filename, index_col="id")
    # r = ml_df.loc[idx]
    # origin_city = r["origin_city"]
    # origin_state = r["origin_state"]
    # origin_country = r["origin_country"]
    # origin_zip = r["origin_zip"]
    # destination_city = r["destination_city"]
    # destination_state = r["destination_state"]
    # destination_country = r["destination_country"]
    # destination_zip = r["destination_zip"]
    # origin_close_time = pd.Timestamp(
    #     datetime.datetime(
    #         r["origin_close_year"],
    #         r["origin_close_month"],
    #         r["origin_close_day"],
    #         r["origin_close_hour"],
    #     )
    # )
    # destination_close_time = pd.Timestamp(
    #     datetime.datetime(
    #         int(r["destination_close_year"]),
    #         int(r["destination_close_month"]),
    #         int(r["destination_close_day"]),
    #         int(r["destination_close_hour"]),
    #     )
    # )

    # # The values from the prediction API is slightly off from Y_pred... to investigate

    # pricer = price_predictor.PricePredictor()

    # predicted_cogs_total = pricer.truce_pricing_api(
    #     origin_city,
    #     origin_state,
    #     origin_country,
    #     origin_zip,
    #     destination_city,
    #     destination_state,
    #     destination_country,
    #     destination_zip,
    #     origin_close_time,
    #     destination_close_time,
    # )
    # actual_cogs_total = r["OUTPUT_cogs_total"]
    # print(
    #     "predicted cogs total",
    #     predicted_cogs_total,
    #     "actual cogs total",
    #     actual_cogs_total,
    # )

    # LESS_COLS_NOZIP_ENDPOINT = "7714457254476382208"
    # pricer = price_predictor.VertexAutoMLPricePredictor()  # LESS_COLS_NOZIP_ENDPOINT)
    # x = price_predictor.PricePredictorInput(
    #     destination_city="Grand Rapids",
    #     destination_state="MI",
    #     destination_country="US",
    #     destination_zip="49501",
    #     origin_city="Huntley",
    #     origin_state="IL",
    #     origin_country="US",
    #     origin_zip="60142",
    #     origin_close_time=pd.Timestamp("2023-07-11 15:00:00"),
    #     equipment_type="Dry Van",
    # )
    # print("Dry van price", pricer.predict(x))
    # x.equipment_type = "Reefer"
    # print("Reefer price", pricer.predict(x))

    evaluate_scrapers()


if __name__ == "__main__":
    main()
