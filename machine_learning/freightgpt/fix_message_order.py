import copy
from typing import Dict
from typing import List
from typing import Union

import langsmith

from common import init_main

PROJECT_ROOT = init_main.initialize()


def reorder_messages(
    example_input_json: Dict[str, Union[str, List[Dict[str, str]]]]
) -> Dict[str, Union[str, List[Dict[str, str]]]]:
    """
    Reorders the messages in the example input to follow the order Human -> System (0 or more) -> AI.

    Args:
        example_input_json: The example input containing a list of messages to be reordered.

    Returns:
        example_input_json_modified: The example input with reordered list of messages.
    """
    system_message_buffer = []
    reordered_messages = []
    last_non_system_message = {}

    for message in example_input_json.get("memory", []):
        if message["type"] == "system":
            system_message_buffer.append(message)
        elif message["type"] == "human":
            if last_non_system_message.get("type") == "human":
                raise ValueError(
                    "Invalid input: Multiple Human messages cannot be placed consecutively. Please check the input and ensure correct message order."
                )
            reordered_messages.append(message)
            if system_message_buffer:
                reordered_messages.extend(system_message_buffer)
                system_message_buffer.clear()
            last_non_system_message = message
        elif message["type"] == "ai":
            if not last_non_system_message or last_non_system_message["type"] == "ai":
                raise ValueError(
                    "Invalid input: AI message cannot occur before Human message. Please check the input and ensure correct message order."
                )
            if system_message_buffer:
                reordered_messages.extend(system_message_buffer)
                system_message_buffer.clear()
            reordered_messages.append(message)
            last_non_system_message = message

    example_input_json_modified = copy.deepcopy(example_input_json)
    example_input_json_modified["memory"] = reordered_messages

    return example_input_json_modified


def add_examples_langsmith(org_dataset: str, new_dataset: str) -> None:
    """
    Adds examples from one LangSmith dataset to another after reordering messages.

    This function retrieves examples from the original dataset, reorders the messages
    in each example, and then creates examples in the new dataset with the
    reordered messages.

    Args:
        org_dataset: The name of the original dataset in LangSmith
                     to retrieve examples from.
        new_dataset: The name of the new dataset in LangSmith to create
                     modified examples in.
    """
    client = langsmith.Client()
    examples = client.list_examples(dataset_name=org_dataset)
    for example in examples:
        example_input_json_modified = reorder_messages(example.inputs)
        client.create_example(
            dataset_name=new_dataset,
            inputs=example_input_json_modified,
            outputs=example.outputs,
        )


def main():
    add_examples_langsmith(
        org_dataset="prod-examples-for-conv-eval", new_dataset="conv-eval-2"
    )


if __name__ == "__main__":
    main()
