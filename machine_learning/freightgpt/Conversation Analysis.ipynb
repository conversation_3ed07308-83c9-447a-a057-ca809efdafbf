{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "\n", "import langsmith\n", "\n", "client = langsmith.Client()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "# Analyze the last 14 days of runs\n", "start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=14)\n", "\n", "runs = list(\n", "    client.list_runs(\n", "        project_name=\"freightgpt-prod\",\n", "        run_type=\"llm\",\n", "        start_time=start_time,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "deny_list = {\"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\"}\n", "extraneous_tags = {\"openai-functions\", \"desktop\", \"mobile\"}\n", "traces = {}\n", "runs_by_user = collections.defaultdict(list)\n", "from tqdm.notebook import tqdm\n", "\n", "for run in tqdm(runs):\n", "    # Check that it's a final output by seeing if there's text\n", "    generations = run.outputs[\"generations\"]\n", "    assert len(generations) == 1\n", "    if generations[0][\"generation_info\"][\"finish_reason\"] != \"stop\":\n", "        continue\n", "\n", "    # Only titler seems to have run-level tags\n", "    if run.tags:\n", "        continue\n", "\n", "    # There should only be one final output per trace\n", "    assert run.trace_id not in traces\n", "    # <PERSON> doesn't have tags, so we need to look at the trace\n", "    # This is the bottleneck, takes about .5 seconds per trace\n", "    trace = client.read_run(run.trace_id)\n", "    tags = set(trace.tags)\n", "\n", "    if not tags:\n", "        continue\n", "\n", "    # Check the intersection of tags and deny_list is empty\n", "    if deny_list & tags:\n", "        # print(f\"Skipping {run.trace_id} because of {deny_list & tags}\")\n", "        continue\n", "\n", "    user = tags - extraneous_tags\n", "    if len(user) != 1:\n", "        # print(f\"Skipping {run.trace_id} because of {user}\")\n", "        continue\n", "\n", "    assert len(user) == 1\n", "    user = user.pop()\n", "\n", "    traces[run.trace_id] = run\n", "    runs_by_user[user].append(run)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for user in runs_by_user:\n", "    print(user, len(runs_by_user[user]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "\n", "fn_counts = collections.defaultdict(int)\n", "for run in traces.values():\n", "    for i in run.inputs[\"messages\"]:\n", "        if additional_kwargs := i[\"kwargs\"].get(\"additional_kwargs\"):\n", "            fn_name = additional_kwargs[\"function_call\"][\"name\"]\n", "            fn_counts[fn_name] += 1\n", "fn_counts\n", "# Display fn counts as a histogram\n", "import matplotlib.pyplot as plt\n", "\n", "bars = plt.bar(fn_counts.keys(), fn_counts.values())\n", "plt.xticks(rotation=90)\n", "# Add the count above each bar\n", "for bar in bars:\n", "    yval = bar.get_height()\n", "    plt.text(\n", "        bar.get_x() + bar.get_width() / 2.0, yval, int(yval), ha=\"center\", va=\"bottom\"\n", "    )  # ha='center' to horizontally align the text\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import collections\n", "\n", "# First, collect all unique function names and initialize the bottoms to 0 for each\n", "all_fn_names = set()\n", "for runs in runs_by_user.values():\n", "    for run in runs:\n", "        for msg in run.inputs[\"messages\"]:\n", "            if additional_kwargs := msg[\"kwargs\"].get(\"additional_kwargs\"):\n", "                fn_name = additional_kwargs[\"function_call\"][\"name\"]\n", "                all_fn_names.add(fn_name)\n", "\n", "fn_name_list = sorted(all_fn_names)  # Sort to maintain order\n", "bottoms = {fn_name: 0 for fn_name in fn_name_list}  # Initialize bottoms\n", "\n", "# Get a color map with enough colors\n", "colors = plt.cm.get_cmap(\"tab20\", len(runs_by_user))\n", "\n", "# Start plotting\n", "plt.figure(figsize=(15, 8))  # Increase figure size to better fit\n", "\n", "for i, (user, runs) in enumerate(\n", "    sorted(runs_by_user.items())\n", "):  # Sort the users if necessary\n", "    fn_counts = collections.defaultdict(int)\n", "    # Calculate function invocation counts for this user\n", "    for run in runs:\n", "        for msg in run.inputs[\"messages\"]:\n", "            if additional_kwargs := msg[\"kwargs\"].get(\"additional_kwargs\"):\n", "                fn_name = additional_kwargs[\"function_call\"][\"name\"]\n", "                fn_counts[fn_name] += 1\n", "\n", "    # Only continue if the user has any invocations\n", "    if sum(fn_counts.values()) == 0:\n", "        continue\n", "\n", "    # Normalize `i` to be between 0 and 1\n", "    normalized_i = i / len(runs_by_user)\n", "\n", "    # Collect the counts in the same order as `fn_name_list`\n", "    counts = [fn_counts[fn_name] for fn_name in fn_name_list]\n", "\n", "    # Plot this user's bar portions\n", "    plt.bar(\n", "        fn_name_list,\n", "        counts,\n", "        bottom=[bottoms[fn_name] for fn_name in fn_name_list],\n", "        color=colors(normalized_i),\n", "        label=user,\n", "    )\n", "\n", "    # Update the bottoms for the next user\n", "    bottoms = {\n", "        fn_name: bottoms[fn_name] + count\n", "        for fn_name, count in zip(fn_name_list, counts)\n", "    }\n", "\n", "for fn_name in fn_name_list:\n", "    x_pos = fn_name_list.index(fn_name)  # Get the x position for this function name\n", "    y_pos = bottoms[fn_name]  # Get the total height of the stack for this function name\n", "    if y_pos > 0:  # Only display label if the stack height is non-zero\n", "        plt.text(\n", "            x_pos, y_pos, str(y_pos), ha=\"center\", va=\"bottom\"\n", "        )  # Adjust vertical alignment to 'bottom'\n", "\n", "# Set up the plot\n", "plt.xticks(rotation=90)\n", "plt.legend()\n", "# Show the plot\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}