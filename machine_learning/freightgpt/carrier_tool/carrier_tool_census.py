# Uses main DynamoDB table: CarrierFMCSA
# Uses secondary indexes: ICC1DocketIndex, ICC2DocketIndex, ICC3DocketIndex
# This pulls the census data, and gives dot_number.
import datetime
import functools
import logging
import re
import typing

import boto3
import pandas as pd
import titlecase
from boto3.dynamodb.conditions import Attr
from boto3.dynamodb.conditions import Key

from machine_learning.freightgpt import langchain_utils
from machine_learning.freightgpt.constants import CARGO_TRANSPORTED_TYPES
from machine_learning.freightgpt.constants import CARGO_TYPE_TO_EQUIPMENT_TYPE
from machine_learning.freightgpt.constants import CARRIER_DBA_BLACKLIST
from machine_learning.freightgpt.constants import CARRIER_RATING_DICT
from machine_learning.freightgpt.constants import CARSHIP_DICT

DYNAMO_DB = boto3.resource("dynamodb")
CARRIER_TABLE = DYNAMO_DB.Table("CarrierFMCSA")


def load_by_icc_number_dynamo(
    icc_prefix: str, icc_number: int
) -> typing.Dict[str, typing.Any]:
    """
    Loads carrier information from the CARRIER_TABLE based on the ICC number.
    Checks the ICC1, ICC2, then ICC3 fields for a match. There should only be
    one match, so this function returns a list with one dict, with all fields.
    """
    try:
        response = CARRIER_TABLE.query(
            IndexName="ICC1DocketIndex",
            KeyConditionExpression=Key("icc1").eq(icc_number),
            FilterExpression=Attr("icc_docket_1_prefix").eq(icc_prefix),
        )
        if not response["Count"]:
            response = CARRIER_TABLE.query(
                IndexName="ICC2DocketIndex",
                KeyConditionExpression=Key("icc2").eq(icc_number),
                FilterExpression=Attr("icc_docket_2_prefix").eq(icc_prefix),
            )
        if not response["Count"]:
            response = CARRIER_TABLE.query(
                IndexName="ICC3DocketIndex",
                KeyConditionExpression=Key("icc3").eq(icc_number),
                FilterExpression=Attr("icc_docket_3_prefix").eq(icc_prefix),
            )

        if response["Count"] > 1:
            raise ValueError(
                f"Expected one match for ICC number {icc_number}, found {response['Count']}."
            )
    except Exception as e:
        logging.error(f"Error loading ICC number {icc_number}: {e}")
        return {}
    return {} if response["Count"] == 0 else response["Items"][0]


def process_icc_number(icc_number: str) -> typing.Tuple[str, int]:
    """
    This function takes an ICC number string, and ensures it matches the format
    MC/MX/FF followed by 1-8 digits OR just 1-8 digits.
    """
    # Remove all non-alphanumeric characters
    icc_number = re.sub(r"[^a-zA-Z0-9]", "", icc_number).upper()
    if not re.match(r"^(MC|MX|FF)?\d{1,8}$", icc_number):
        # The input must be MC/MX/FF followed by 1-8 digits OR just 1-8 digits.
        raise ValueError("Must provide a 8-digit ICC number, prefixed by MC, MX, or FF")
    if not icc_number.startswith(("MC", "MX", "FF")):
        return "", int(icc_number)
    return icc_number[:2], int(icc_number[2:])


@functools.lru_cache(maxsize=8)
def load_by_icc_number(
    icc_number: str,
) -> typing.Tuple[str, str, typing.Dict[str, typing.Any]]:
    """
    Loads carrier information from the CARRIER_TABLE based on the ICC number.
    If the ICC number is not prefixed, then it will try all three prefixes.
    Returns a dictionary with all fields from the table, as well as the final
    ICC number and prefix used, unless the ICC number was not found in which
    case it returns an empty dictionary.
    """
    if isinstance(icc_number, int):
        icc_number = str(icc_number)

    icc_prefix, icc_number = process_icc_number(icc_number)
    if icc_prefix == "":
        icc_prefix = "MC"
        response = load_by_icc_number_dynamo(icc_prefix, icc_number)
        if not response:
            icc_prefix = "MX"
            response = load_by_icc_number_dynamo(icc_prefix, icc_number)
        if not response:
            icc_prefix = "FF"
            response = load_by_icc_number_dynamo(icc_prefix, icc_number)
    else:
        response = load_by_icc_number_dynamo(icc_prefix, icc_number)
    return icc_prefix, icc_number, response


@functools.lru_cache(maxsize=8)
def load_by_dot_number(dot_number: int) -> typing.Dict[str, typing.Any]:
    """
    Loads carrier information from the CARRIER_TABLE based on the DOT number.
    Returns a dictionary with all fields from the table.
    """
    if isinstance(dot_number, str):
        try:
            dot_number = int(dot_number)
        except ValueError as e:
            logging.error(f"Invalid dot_number {dot_number}: {e}")
            raise TypeError("dot_number must be an integer") from e

    response = CARRIER_TABLE.get_item(Key={"dot_number": dot_number})
    return response.get("Item", {})


# TODO(P1): Add a function that takes in a carrier's DOT number, and returns the following:
# The carrier XXX got into 5 crashes since 202X. This is above the average of 3 crashes
# for carriers with 10-20 trucks.


def format_location_str(city: str, state: str) -> str:
    located_in = []
    if not pd.isna(city):
        try:
            located_in.append(titlecase.titlecase(city))
        except Exception as e:
            logging.error(f"Unable to titlecase {city}: {e}")
    if not pd.isna(state) and isinstance(state, str):
        located_in.append(state.upper())
    return ", ".join(located_in)


def format_basic_census_info(
    carrier_info: typing.Dict[str, typing.Any]
) -> typing.Dict[str, typing.Any]:
    carrier_name = carrier_info["name"]
    if (
        carrier_info["name_dba"]
        and carrier_info["name_dba"] not in CARRIER_DBA_BLACKLIST
    ):
        carrier_name = carrier_info["name_dba"]
    return {
        # This library turns MCMAHON DAIRY SERVICE to McMahon Dairy Service
        "company_name": titlecase.titlecase(carrier_name),
        "dot_number": str(carrier_info["dot_number"]),
    }


# For crashes: ingest crash by DOT number as list.
# Return # of crashes, and average # of crashes for carriers with same FLEETSIZE.
def format_census_info(
    carrier_info: typing.Dict[str, typing.Any]
) -> typing.Dict[str, typing.Any]:
    """Takes in raw carrier info from the CARRIER_TABLE and formats it into a
    clean dictionary with minimal fields for the LLM.

    If you update this, make sure to update langchain_tools.mock_get_carrier_info_tool as well.

    Scope 11/6/23: We are only using the following fields from the carrier info:
    1. NAME (some of them are 'NONE') NAME_DBA some are 'NO' or 'NONE' or 'OWNER', some are 'TRANSPORTATION'
    2. ADDDATE is in YYYYMMDD?
    3. # of months in business is datetime.datetime.now() - ADDDATE
    4. PHY_CITY, PHY_ST, PHY_ZIP
    5. Number of tractors/trailers/trucks owned, leased. (OWNTRUCK, OWNTRACT, OWNTRAIL, TRMTRUCK, TRMTRACT, TRMTRAIL, TRPTRUCK, TRPTRACT, TRPTRAIL)
    5a. Maybe just TOT_TRUCKS to start, owned leased as followup
    6. CDL_DRS (commercial drivers over TOT_DRS total drivers)
    7. RATING: S,C,U, or nan. S = Satisfactory, C = Conditional, U = Unsatisfactory
    8. CARSHIP: Called CARSHP in doc. C = Carrier, S = Shipper, B = Broker, R = Registrant (entity who registers vehicles but is not a carrier), F = Freight Forwarder, T = Cargo Tank, can have up to 4.
    """

    if not carrier_info or not carrier_info.get("name"):
        return {}

    formatted_info = format_basic_census_info(carrier_info)
    details = {}

    if not pd.isna(carrier_info["emailaddress"]):
        formatted_info["email"] = carrier_info["emailaddress"].lower()

    if not pd.isna(carrier_info["company_rep1"]):
        details["contact_name"] = titlecase.titlecase(carrier_info["company_rep1"])
    if not pd.isna(carrier_info["company_rep2"]):
        details["contact_name_alternate"] = titlecase.titlecase(
            carrier_info["company_rep2"]
        )
    if not pd.isna(carrier_info["tel_num"]):
        formatted_info["phone_number"] = str(carrier_info["tel_num"])
    elif not pd.isna(carrier_info["fax_num"]):
        details["fax_number"] = str(carrier_info["fax_num"])

    try:
        add_date = datetime.datetime.strptime(str(carrier_info["adddate"]), "%Y%m%d")
        details["in_business_for"] = langchain_utils.format_timedelta(
            datetime.datetime.now() - add_date, granularity="month"
        )
    except ValueError as e:
        logging.error(f"Invalid date format for adddate {carrier_info['adddate']}: {e}")

    # TODO(P0): Emit a location for the carrier's headquarters. City, state or lat/long.
    located_in = format_location_str(carrier_info["phy_city"], carrier_info["phy_st"])
    if located_in:
        formatted_info["located_in"] = located_in
    else:
        mailing_address = format_location_str(
            carrier_info["mai_city"], carrier_info["mai_st"]
        )
        if mailing_address:
            details["mailing_address"] = mailing_address

    cargo_transported = []
    for cargo_type in CARGO_TRANSPORTED_TYPES:
        if carrier_info.get(cargo_type, False):
            # Remove 'General Freight' as it is considered a default or non-specific cargo type
            if cargo_type == "genfreight":
                continue
            cargo_transported.append(CARGO_TRANSPORTED_TYPES[cargo_type])
    # If cargoother is 'X', then it is a custom cargo type under othercargo, so append it to the list.
    if (
        carrier_info["cargoothr"]
        and carrier_info["othercargo"]
        and isinstance(carrier_info["othercargo"], str)
    ):
        cargo_transported.append(carrier_info["othercargo"].title())

    # TODO(P1): Should we use a different conditional to check for NA?
    formatted_info["rating"] = "No Rating"
    if not pd.isna(carrier_info["tot_trucks"]):
        formatted_info["total_trucks"] = int(carrier_info["tot_trucks"])
    # if not pd.isna(carrier_info["cdl_drs"]):
    #     formatted_info["total_commercial_drivers"] = int(carrier_info["cdl_drs"])
    if not pd.isna(carrier_info["rating"]):
        formatted_info["rating"] = CARRIER_RATING_DICT[carrier_info["rating"]]
    if not pd.isna(carrier_info["carship"]):
        details["company_type"] = []
        for ctype in carrier_info["carship"]:
            details["company_type"].append(CARSHIP_DICT[ctype])
        if len(details["company_type"]) == 1:
            details["company_type"] = details["company_type"][0]
    if not pd.isna(carrier_info["mcs_150_date"]):
        details["mcs_form_update_date"] = datetime.datetime.strptime(
            str(carrier_info["mcs_150_date"]), "%Y%m%d"
        ).strftime("%Y-%m-%d")
    if cargo_transported:
        details["types_of_cargo_transported"] = cargo_transported

        # Equipment type
        equipment_types_transported = []
        for cargo in cargo_transported:
            cargo = cargo.lower().replace(" ", "")
            if cargo in CARGO_TYPE_TO_EQUIPMENT_TYPE:
                equipment_types_transported.append(CARGO_TYPE_TO_EQUIPMENT_TYPE[cargo])
            else:
                logging.warn(f"Missing equipment type for cargo {cargo}")

        if equipment_types_transported:
            formatted_info["equipment_types"] = equipment_types_transported

    formatted_info["details"] = details

    return formatted_info


def fetch_census_info(
    dot_number: int = None, icc_number: str = None
) -> typing.Dict[str, typing.Any]:
    """
    https://www.fmcsa.dot.gov/registration/mcmis-catalog-census-file-data-element-definitions
    Fetches and formats census information based on DOT or ICC number.
    Parameters:
    - dot_number: A 1-8 digit integer representing a carrier's DOT number.
    - icc_number: A 1-8 digit number prefixed by either one of ['MC', 'MX', 'FF']
    Returns:
    - A dictionary with either formatted census information or an error message.
    """
    if pd.isna(dot_number) and pd.isna(icc_number):
        raise ValueError(
            "Must provide either a valid dot_number (integer) or icc_number (string with optional 'MC', 'MX', 'FF' prefix)"
        )

    # If DOT number is specified, try to load by DOT number. If that fails, try to load by ICC number.
    if not pd.isna(dot_number):
        response = load_by_dot_number(dot_number)
        dot_number = str(dot_number)
        if response:  # record matching dot number found
            formatted_info = format_census_info(response)
            formatted_info["dot_number"] = dot_number
            return formatted_info
        # no record matching dot number found
        icc_prefix, icc_number, response = load_by_icc_number(dot_number)
        if response != {}:  # record matching icc number found
            formatted_info = format_basic_census_info(response)
            return {
                "error": f"I was unable to find {dot_number}, but I found icc {icc_prefix}{icc_number}, which corresponds with {formatted_info['company_name']}. Would you like to more information on this company?"
            }
    # If ICC number is specified, try to load by ICC number. If that fails, try to load by DOT number.
    else:
        # If the ICC number is not prefixed, then it will try all three prefixes.
        icc_prefix, icc_number, response = load_by_icc_number(icc_number)
        formatted_icc_number = f"{icc_prefix}{icc_number}"
        if response:  # record matching icc number found
            formatted_info = format_census_info(response)
            formatted_info["icc_number"] = formatted_icc_number
            return formatted_info
        # no record matching icc number found
        icc_prefix = "MC"
        try:
            response = load_by_dot_number(icc_number)
            if response != {}:  # record matching dot number found
                formatted_info = format_basic_census_info(response)
                return {
                    "error": f'I was unable to find {icc_prefix}{icc_number}, but I found dot {icc_number}, which corresponds with {formatted_info["company_name"]}. Would you like to more information on this company?'
                }
        except TypeError:
            pass

    # No matching record for either dot and icc number  found
    return {
        "error": f"Unable to find carrier information on DOT {dot_number} / ICC {icc_number}."
    }
