import inspect
import logging
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Set
from typing import Tuple

import haversine
import pandas as pd
from langchain.tools.base import ToolException

from common import google_maps_utils
from common import opensearch_utils
from machine_learning.freightgpt.carrier_tool import carrier_search_utils
from machine_learning.freightgpt.carrier_tool.constants import US_COUNTY_LAT_LNG
from machine_learning.freightgpt.constants import OPENSEARCH_INDEX_NAME

GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()
OS_QUERY_BUILDER = opensearch_utils.OSQueryBuilder(index_name=OPENSEARCH_INDEX_NAME)

DEFAULT_MAX_IN_BUSINESS_FOR_MONTHS = 12000
DEFAULT_MIN_IN_BUSINESS_FOR_MONTHS = 0
EXPANDED_MAX_FLEET_SIZE = 500000
DEFAULT_MAX_FLEET_SIZE = 300
DEFAULT_MIN_FLEET_SIZE = 10
DEFAULT_MAX_INSURANCE_AMOUNT = 100000
NUM_DAYS_IN_MONTH = 30
NEARBY_COUNTY_THRESHOLD_MILES = 50
# Default radius for near city search, in miles
DEFAULT_NEAR_CITY_RADIUS = 50
# Max number of inspections for open search
MAX_INSPECTIONS = 100000


def get_non_default_kwargs(func: Callable) -> Callable:
    """
    Returns all kwargs that were not their defaults for a given function

    Decorates carrier search to give the LLM an indication of what parameters / kwargs the function was invoked with.

    @get_non_default_kwargs
    def run_carrier_search(...):
        ...

    >>> run_carrier_search(near_city="LA")
    >>> (0, {"near_city": "LA})

    """

    def wrapper(*args, **kwargs):
        logging.info(f"Carrier search kwargs: {kwargs}")
        # Get the signature of the function
        sig = inspect.signature(func)
        # Get the bound arguments (with defaults if not provided)
        bound_args = sig.bind(*args, **kwargs)
        bound_args.apply_defaults()

        # Extract the default values
        default_kwargs = {
            k: v.default
            for k, v in sig.parameters.items()
            if v.default is not inspect.Parameter.empty
        }

        # Identify kwargs that are different from the defaults
        non_default_kwargs = {
            k: v
            for k, v in bound_args.arguments.items()
            if k in kwargs and default_kwargs.get(k) != v
        }

        if "tool_store" in non_default_kwargs:
            del non_default_kwargs["tool_store"]
        logging.info(f"Carrier search non-default kwargs: {non_default_kwargs}")

        # Return the function result and non-default kwargs
        (results, specific_search, expanded_search) = func(*args, **kwargs)
        logging.info(f"Carrier search number of results: {len(results)}")
        logging.info(f"Carrier search specific search: {specific_search}")
        logging.info(f"Carrier search expanded search: {expanded_search}")
        return (results, specific_search, expanded_search), non_default_kwargs

    return wrapper


####################################################################
#         Lane Matching
####################################################################


def get_county_fips_from_location(
    address: str,
) -> Tuple[Set[str], Tuple[float, float]]:
    """
    Get the FIPS code(s) for a given location string based on an address. FIPS codes are returned
    as a list of 5-digit strings. If multiple FIPS codes are found, all are returned.

    Args:
        address (str): The address to get the FIPS code for, anything Google Maps can resolve.

    Returns:
        Tuple[Set[str]], Tuple[float, float]]: A tuple where the first element is either
        a set of FIPS codes, and the second element is a tuple of latitude and longitude for the given location.
        Example: (["01001", "01003"], (33.5206824, -86.8024326))
    """
    try:
        county_name, lat_lng = GMAPS_UTILS.get_county_from_address(address)
    except ValueError as e:
        logging.error(f"Error occurred while getting county from address: {e}")
        return set(), (pd.NA, pd.NA)

    if pd.isna(county_name):
        logging.warn("County name could not be resolved.")
        return set(), lat_lng

    # Remove " County" from the county name
    county_name = county_name.replace(" County", "")

    df_matches = US_COUNTY_LAT_LNG[
        US_COUNTY_LAT_LNG["name"].str.lower() == county_name.lower()
    ]

    if df_matches.empty:
        logging.error("No FIPS code found for county: %s", county_name)
        return set(), lat_lng

    if len(df_matches) > 1:
        logging.warn(
            "Multiple FIPS codes found for county: %s. Returning all codes.",
            county_name,
        )
        logging.warn("Values: %s", df_matches.index)

    return set(df_matches.index.tolist()), lat_lng


def get_counties_in_radius(
    lat_lng: Tuple[float], radius: int = NEARBY_COUNTY_THRESHOLD_MILES
) -> Set[str]:
    """
    Finds all counties within a given radius of a target location and returns a dictionary of FIPS codes and distances.

    Args:
    - lat_lng (Tuple[float, float]): The latitude and longitude of the target location.
    - radius (int): The radius in miles around the target location.

    Returns:
    - Set[str]: A list of fips codes within the radius. E.x. ["01001", "01003", ...]
    """
    if pd.isna(lat_lng) or pd.isna(lat_lng[0]) or pd.isna(lat_lng[1]):
        logging.error("Latitude and longitude is not provided.")
        return set()
    # Calculate distance to target location and filter counties
    US_COUNTY_LAT_LNG["distance"] = US_COUNTY_LAT_LNG.apply(
        lambda row: haversine.haversine(
            lat_lng, (row["lat"], row["lng"]), unit=haversine.Unit.MILES
        ),
        axis=1,
    )

    # Filter for counties within the specified radius and retrieve the index as a list
    filtered_fips = US_COUNTY_LAT_LNG[
        US_COUNTY_LAT_LNG["distance"] <= radius
    ].index.tolist()

    return set(filtered_fips)


def format_search_hits(hits: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Formats a list of search hit dictionaries into a cleaner, more readable structure.

    Args:
        hits (List[Dict[str, Any]]): A list of dictionaries, each representing a search hit.
            Each hit contains an `_id` representing the DOT number and a `_source` dictionary
            with further details.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, where each dictionary has been cleaned
            and formatted to include keys for the DOT number, carrier name, email, phone,
            cargo transported, equipment types, number of trucks, months in business, and
            parsed location attributes (city, state, zipcode, country) along with geolocation
            coordinates if available.

    Example:
        >>> hits = [{"_id": "123", "_source": {"carrier_name_formatted": "Example Inc", ...}}]
        >>> formatted_hits = format_search_hits(hits)
        >>> print(formatted_hits)
        [{
            "dot_number": "123",
            "carrier_name": "Example Inc",
            "email": "<EMAIL>",
            ...
        }]
    """

    def split_delimited_field(field: str, delimiter: str = "&") -> List[str]:
        return [item.strip() for item in field.split(delimiter)]

    all_formatted_hits = []
    for hit in hits:
        source = hit["_source"]

        formatted_data = {
            "dot_number": hit["_id"],
            "carrier_name": source["carrier_name_formatted"],
        }

        # MC Number
        if not pd.isna(source["icc_number"]):
            formatted_data["mc_number"] = source["icc_number"].split(".")[0]

        # Contact information
        email, phone = None, None
        if not pd.isna(source["email"]):
            email = source["email"]
            formatted_data["email"] = email

        if not pd.isna(source["phone_number"]):
            phone = carrier_search_utils.format_and_verify_phone_number(
                source["phone_number"]
            )
            formatted_data["phone"] = phone

        # Equipment type / cargo
        if not pd.isna(source["cargo_transported"]):
            formatted_data["cargo_transported"] = split_delimited_field(
                source["cargo_transported"]
            )

        if not pd.isna(source["equipment_type_transported"]):
            formatted_data["equipment_types"] = split_delimited_field(
                source["equipment_type_transported"]
            )

        # Fleet size and in business for
        if not pd.isna(source["num_trucks"]):
            formatted_data["num_trucks"] = source["num_trucks"]

        if not pd.isna(source["in_business_for_days"]):
            formatted_data["in_business_for_years"] = source[
                "in_business_for_days"
            ] // (30 * 12)

        # Geolocation
        if not pd.isna(source["location"]):
            formatted_data["location"] = source["location"]

        # Inspections / Crashes
        if not pd.isna(source["num_inspections_last_two_years"]):
            formatted_data["num_inspections_last_two_years"] = source[
                "num_inspections_last_two_years"
            ]

        if not pd.isna(source["num_crashes_last_two_years"]):
            formatted_data["num_crashes_last_two_years"] = source[
                "num_crashes_last_two_years"
            ]

        # Min insurance rating
        if not pd.isna(source["min_insurance_coverage_amount"]):
            formatted_data["min_insurance_coverage_amount"] = source[
                "min_insurance_coverage_amount"
            ]

        # Safety rating
        if not pd.isna(source["safety_rating"]):
            formatted_data["safety_rating"] = source["safety_rating"]

        # Hazmat
        if not pd.isna(source["hazmat"]):
            formatted_data["hazmat"] = source["hazmat"]

        all_formatted_hits.append(formatted_data)

    return all_formatted_hits


####################################################################
#         Carrier Search
####################################################################


# TODO(P1): #2382 Factor out the search logic into a separate class and methods, maybe into utils file
# Returns result tupled with invoked parameters
# >>> (0, {near_city: "LA"})
@get_non_default_kwargs
def run_carrier_search(
    text_search: str = "",
    cargo_or_equipment_hauled: List[str] = [],
    in_business_months_min: int = None,
    in_business_months_max: int = None,
    fleet_size_min: int = None,
    fleet_size_max: int = None,
    insurance_coverage_min: int = None,
    inspections_min: int = None,
    carries_hazmat: bool = False,
    safety_ratings: List[str] = ["S", "N"],  # Pre filter saftey ratings - S and N only.
    origin_city: str = None,
    origin_state: str = None,
    destination_city: str = None,
    destination_state: str = None,
    near_origin_radius: int = DEFAULT_NEAR_CITY_RADIUS,
    carrier_email: str = None,
    carrier_phone: str = None,
    backhaul: bool = False,
    total_response_size: int = 100,
    tool_store={},
) -> Tuple[Tuple[List[Dict[str, Any]], bool], Dict[str, Any]]:
    """
    Conducts a search for carrier companies based on various filters and queries. Search Algorithm:

    1. Prefilters:
        - Adds two prefilters to the initial query - fleet size between 10 and 300, and safety rating as satisfactory.

    2. Initial search:
        - Appends each provided input as a query or a filter.
        - Fields that are are numerical or geographical add a filter, and text matches add a query.
        - Carrier name searches are done as an exact token match and wildcard match.
        - "minimum_should_match" is incremented for all queries so that all of them are strongly enforced.
        - Searches that are specific (looking for a specific carrier name or email) are flagged.

    3. Run search:
        - Run search with sort on not-NaN MC number, fleet size, inspections, and years in business, all descending.
        - If the search yields less than 10 hits, and is not a specific search (flagged earlier), the fleet size prefilter
          is dropped and we try again.
        - Return result count and emit results.

    4. (If no results) Advanced text search:
        - If we are running a text search, but have no results, we re-run the search with fuzzy matching.
        - Return result count and emit results.

    Design Doc:
    https://docs.google.com/document/d/1o9j_CrkT3kdm7PBTiiqSr0zq7vtk0dMfEvVt2p-yLFc/edit#heading=h.ir6nk6nb4aoo


    Args:
        text_search (str): Company name or text to search for. Default is an empty string.
        cargo_or_equipment_hauled (List[str]): Types of cargo or equipment the carrier hauls. Default is an empty list.
        in_business_months_min (int): Minimum number of months the carrier has been in business. Default is None.
        in_business_months_max (int): Maximum number of months the carrier has been in business. Default is None.
        fleet_size_min (int): Minimum fleet size. Default is 10.
        fleet_size_max (int): Maximum fleet size. Default is 300.
        insurance_coverage_min (int): Minimum insurance coverage amount. Default is None.
        inspections_min (int): Optional. Minimum number of inspections in the last two years. Default is None.
        carries_hazmat (bool): Whether the carrier hauls hazardous materials. Default is False.
        safety_ratings (List[str]): List of acceptable safety ratings. Default is ["S", "N"].
        origin_city (str): Optional. If user specifies 'near city' or asks about a lane origin, use the origin parameters. Default is None.
        origin_state (str): Optional. If user specifies 'near city' or asks about a lane origin, use the origin parameters. Must be a two-letter state code. Default is None.
        destination_city (str): Optional. If user asks about a lane destination, use the destination parameters. Default is None.
        destination_state (str): Optional. If user asks about a lane destination, use the destination parameters. Must be a two-letter state code. Default is None.
        near_origin_radius (int): Optional. Radius in miles, leave empty unless user specifies miles. Default is 50.
        carrier_email (str): Email of the carrier. Default is None.
        carrier_phone (str): Phone number of the carrier. Default is None.
        backhaul (bool): whether to run lane search as a backhaul. Default is None.
        total_response_size (int): Number of search results to return. Default is 100.
        tool_store (Dict[Any, Any]): Dictionary to store previous results to avoid duplicates. Default is None.

    Returns:
        Tuple:
            Tuple:
                List[Dict[str, Any]]: The carrier search results
                bool: True if the search was specific, False otherwise
            Dict[str, Any]: Modified kwargs from decorator get_non_default_kwargs
            # TODO(P1): #2089 stop using a convoluted decorator

    Emits:
        List of carrier_search_results:
        [{'cargo_transported': ['Garbage, Refuse, Trash', 'Construction'],
            'carrier_name': 'Renu Recycling Services',
            'dot_number': '419176',
            'email': '<EMAIL>',
            'equipment_types': ['Garbage', 'Flatbed'],
            'hazmat': 'N',
            'in_business_for_days': 34,
            'location': 'Issaquah, WA 98027, US',
            'mc_number': 'MC1032738',
            'num_crashes_last_two_years': 1.0,
            'num_inspections_last_two_years': 20.0,
            'num_trucks': 71,
            'phone': '(*************',
            'safety_rating': 'S'},
        ...]

    Example:
        >>> results = run_carrier_search(text_search="Logistics", fleet_size_min=5, fleet_size_max=50)
        (25, {"text_search": "Logistics", "fleet_size_min":5, "fleet_size_max": 50})
    """
    # TODO(P1): #2089 Refactor carrier search to use filters objects and util functions.

    # Clean / preprocess text search name.
    clean_carrier_name = carrier_search_utils.clean_company_name(text_search)
    clean_carrier_name_no_ws = carrier_search_utils.remove_whitespace(
        clean_carrier_name
    )

    # Minimum matches required for queries.
    # When running with multiple text queries in the "should" clause, this minimum_should_match determines how many queries / matches
    # should be strongly enforced.
    # Necessary for opensearch: https://opensearch.org/docs/latest/query-dsl/minimum-should-match/
    minimum_should_match = 0

    # If we are running a search for a specific entry. Ideally, 1 hit expected.
    # Carrier name, Email, Phone number.
    specific_search = False

    # On low result count, we run an expanded search without prefilters.
    expanded_search = False

    # Sort
    numerical_sort = [
        # NULL ICC
        ("null_icc_number", "asc"),
        # Fleet size
        ("num_trucks", "desc"),
        # Num inspections
        ("num_inspections_last_two_years", "desc"),
        # In business for
        ("in_business_for_days", "desc"),
    ]

    # Ensure near city radius is right type
    if near_origin_radius and isinstance(near_origin_radius, str):
        try:
            near_origin_radius = int(float(near_origin_radius))
        except Exception as e:
            logging.error(
                f"Could not convert near_origin_radius to int: {near_origin_radius} with exception {e}"
            )
            raise e

    # If backhaul and lane search: (origin city to destination city OR origin state to destination state), swap
    if backhaul and (
        (origin_city or origin_state) and (destination_city or destination_state)
    ):
        origin_city, origin_state, destination_city, destination_state = (
            destination_city,
            destination_state,
            origin_city,
            origin_state,
        )

    ####################################################################
    #         Event Emission
    ####################################################################

    def format_hits(hits: List[Dict[str, Any]]) -> int:
        """Formats and returns unique hits"""
        formatted_search_hits = format_search_hits(hits)

        # Emit event
        event_formatted_hits = []
        for hit in formatted_search_hits:
            # Check / update tool store to avoid repeats
            dot_number = hit.get("dot_number")
            if tool_store:
                if "cached_dots" not in tool_store:
                    tool_store["cached_dots"] = set()

                if dot_number in tool_store["cached_dots"]:
                    continue
                else:
                    tool_store["cached_dots"].add(dot_number)

            event_formatted_hits.append(hit)

        return event_formatted_hits

    ####################################################################
    #         Defaults and Pre Filters
    ####################################################################

    filter_fleet_size_min = DEFAULT_MIN_FLEET_SIZE
    filter_fleet_size_max = DEFAULT_MAX_FLEET_SIZE

    # Both fleet size min and fleet size max provided by user
    if fleet_size_min and fleet_size_max:
        filter_fleet_size_min = fleet_size_min
        filter_fleet_size_max = fleet_size_max

    # Only fleet size min provided by user, use that with the expanded fleet size max.
    # "At least 400 trucks" should not impose the default max.
    elif fleet_size_min:
        filter_fleet_size_min = fleet_size_min
        filter_fleet_size_max = EXPANDED_MAX_FLEET_SIZE

    # Only fleet size max provided by user, use that with the default min.
    elif fleet_size_max:
        filter_fleet_size_max = fleet_size_max

    # Num trucks filter
    OS_QUERY_BUILDER.add_filter(
        opensearch_utils.RangeFilter(
            column_name="num_trucks",
            from_value=filter_fleet_size_min,
            to_value=filter_fleet_size_max,
        )
    )

    # Safety Rating Filter
    if safety_ratings:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query=" ".join(safety_ratings),
                column_name="safety_rating",
            )
        )
        minimum_should_match += 1

    ####################################################################
    #         Initial Search Construction
    ####################################################################

    # Equipment type / Cargo Filters
    if cargo_or_equipment_hauled:
        equipment_types = []
        cargo_transported_types = []
        for transport_good_category in cargo_or_equipment_hauled:
            # TODO (P2): potentially make this return dictionary of lists with keys "equipment_type" and "cargo_type"
            (
                transport_good_name,
                transport_good_type,
            ) = carrier_search_utils.find_equipment_or_cargo_transported(
                transport_good_category
            )

            if transport_good_type == "equipment_type":
                equipment_types.append(transport_good_name)

            if transport_good_type == "cargo_type":
                cargo_transported_types.append(transport_good_name)

            if (
                pd.isna(transport_good_name)
                and pd.isna(transport_good_type)
                and transport_good_category
            ):
                cargo_transported_types.append(transport_good_category)

        if equipment_types:
            OS_QUERY_BUILDER.add_query(
                opensearch_utils.FuzzyQuery(
                    query=" ".join(equipment_types),
                    column_name="equipment_type_transported",
                    fuzziness="1",
                )
            )
            minimum_should_match += 1

        if cargo_transported_types:
            OS_QUERY_BUILDER.add_query(
                opensearch_utils.FuzzyQuery(
                    query=" ".join(cargo_transported_types),
                    column_name="cargo_transported",
                    fuzziness="1",
                )
            )
            minimum_should_match += 1

    # Location filters:
    # - If origin_city is provided, add a geopoint filter. If state is also provided, geocode with city,state.
    # - If destination city is provided, find inspection crash counties for this city. If state is also provided, geocode with city, state.
    # - If either origin or destination state is provided without a city, add a location text query.

    # Origin city / state filter
    if origin_city:
        origin_location = (
            f"{origin_city}, {origin_state}" if origin_state else origin_city
        )
        try:
            lat, lng = GMAPS_UTILS.get_uncached_lat_lng(origin_location)
            OS_QUERY_BUILDER.add_filter(
                opensearch_utils.GeoPointDistanceFilter(
                    column_name="located_in_geopoint",
                    lat=lat,
                    lng=lng,
                    distance_miles=near_origin_radius,
                )
            )
        except Exception as e:
            logging.error(f"Failed to geocode with {e}. not adding geopoint filter.")
            raise ToolException(
                f"Failed to geocode origin city and origin state: {origin_city}, {origin_state}"
            )

    elif origin_state:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query=origin_state,
                column_name="location",
            )
        )
        minimum_should_match += 1

    # Destination city / state filter
    if destination_city:
        destination_location = (
            f"{destination_city}, {destination_state}"
            if destination_state
            else destination_city
        )

        # Find all counties and neighboring counties
        destination_county_fips, lat_lng = get_county_fips_from_location(
            destination_location
        )
        neighboring_fips = get_counties_in_radius(lat_lng)

        # Combine the sets into a list of str fips
        combined_fips = [
            str(int(fip)) for fip in destination_county_fips.union(neighboring_fips)
        ]

        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query=" ".join(combined_fips),
                column_name="inspection_crash_counties",
            )
        )
        minimum_should_match += 1

    elif destination_state:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query=destination_state,
                column_name="destination_states",
            )
        )
        minimum_should_match += 1

    # Inspections Filter
    if inspections_min:
        OS_QUERY_BUILDER.add_filter(
            opensearch_utils.RangeFilter(
                column_name="num_inspections_last_two_years",
                from_value=inspections_min,
                to_value=MAX_INSPECTIONS,
            )
        )
    # Consider in business min and max values as None if they are out of bounds
    if in_business_months_max is not None and in_business_months_max >= 720:  # 60 years
        in_business_months_max = None

    if in_business_months_min is not None and in_business_months_min <= 0:
        in_business_months_min = None

    # In business for filter
    if in_business_months_min or in_business_months_max:
        OS_QUERY_BUILDER.add_filter(
            opensearch_utils.RangeFilter(
                column_name="in_business_for_days",
                from_value=(
                    in_business_months_min or DEFAULT_MIN_IN_BUSINESS_FOR_MONTHS
                )
                * NUM_DAYS_IN_MONTH,
                to_value=(in_business_months_max or DEFAULT_MAX_IN_BUSINESS_FOR_MONTHS)
                * NUM_DAYS_IN_MONTH,
            )
        )

    # Insurance Filter
    if insurance_coverage_min:
        OS_QUERY_BUILDER.add_filter(
            opensearch_utils.RangeFilter(
                column_name="min_insurance_coverage_amount",
                from_value=insurance_coverage_min,
                to_value=DEFAULT_MAX_INSURANCE_AMOUNT,
            )
        )

    # Hazmat Filter
    if carries_hazmat:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query="Y" if carries_hazmat else "N", column_name="hazmat"
            )
        )
        minimum_should_match += 1

    # Email Filter
    if carrier_email:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.FullPhraseQuery(
                query=carrier_email,
                column_name="email",
            )
        )
        minimum_should_match += 1
        specific_search = True

    # Phone Filter
    if carrier_phone:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.FullPhraseQuery(
                query=str(carrier_search_utils.deformat_phone_number(carrier_phone)),
                column_name="phone_number",
            )
        )
        minimum_should_match += 1
        specific_search = True

    # Basic search: exact and substring match on name without whitespace.
    if clean_carrier_name_no_ws:
        OS_QUERY_BUILDER.add_query(
            opensearch_utils.MatchQuery(
                query=clean_carrier_name,
                column_name="carrier_search_term_tokens",
            )
        )

        OS_QUERY_BUILDER.add_query(
            opensearch_utils.WildcardQuery(
                query=clean_carrier_name_no_ws,
                column_name="carrier_search_term_nows",
            )
        )

        minimum_should_match += 2
        specific_search = True

    # If we are running a specific search (name, email, phone), drop the pre-filters.
    if specific_search:
        logging.warn("Dropping pre-filters due to specific search invoked.")
        OS_QUERY_BUILDER.delete_filter(
            filter_type="range_filter", column_name="num_trucks"
        )
        OS_QUERY_BUILDER.delete_query(
            query_type="match_query", column_name="safety_rating"
        )
        minimum_should_match -= 1

    # Run search
    response = OS_QUERY_BUILDER.build_and_run(
        size=total_response_size,
        numerical_sort=numerical_sort,
        minimum_should_match=minimum_should_match,
        show_query=True,
    )
    if not response:
        OS_QUERY_BUILDER.clear()
        raise ValueError("Opensearch query failed")

    ####################################################################
    #         Drop Pre Filters on Fleet Size
    ####################################################################

    # If response length is less than 10, we are not running a specific search, and the user has provided no filters,
    # drop pre filters on fleet size only and try again.
    if (
        len(response["hits"]["hits"]) < 10
        and not specific_search
        and not (fleet_size_min or fleet_size_max)
    ):
        expanded_search = True

        OS_QUERY_BUILDER.delete_filter(
            filter_type="range_filter", column_name="num_trucks"
        )

        response = OS_QUERY_BUILDER.build_and_run(
            size=total_response_size,
            numerical_sort=numerical_sort,
            minimum_should_match=minimum_should_match,
            show_query=True,
        )
        if not response:
            OS_QUERY_BUILDER.clear()
            raise ValueError("Opensearch query failed")

    ####################################################################
    #         Result Check
    ####################################################################

    # If hits, return immediately.
    if response["hits"]["hits"]:
        OS_QUERY_BUILDER.clear()
        return format_hits(response["hits"]["hits"]), specific_search, expanded_search

    # If no text search was provided, but we get no hits from pure filters, no need to run an advanced text search.
    # Return no results.
    if not clean_carrier_name:
        OS_QUERY_BUILDER.clear()
        return [], specific_search, expanded_search

    ####################################################################
    #         Advanced Text Search
    ####################################################################

    # Run a broader advanced fuzzy / DSL search if a text query was provided.
    logging.warning(
        "Could not find results from basic text search. Invoking advanced search."
    )

    # Delete existing text match queries
    OS_QUERY_BUILDER.delete_query(
        query_type="match_query", column_name="carrier_search_term_tokens"
    )

    OS_QUERY_BUILDER.delete_query(
        query_type="wildcard_query", column_name="carrier_search_term_nows"
    )

    # Advanced search: A DSL query and a fuzzy match
    OS_QUERY_BUILDER.add_query(
        opensearch_utils.DSLQuery(
            query=clean_carrier_name, column_name="carrier_search_term_tokens"
        )
    )

    OS_QUERY_BUILDER.add_query(
        opensearch_utils.FuzzyQuery(
            query=clean_carrier_name,
            column_name="carrier_search_term_tokens",
            boost=2.5,
        )
    )

    response = OS_QUERY_BUILDER.build_and_run(
        size=total_response_size,
        numerical_sort=numerical_sort,
        minimum_should_match=minimum_should_match,
        show_query=True,
    )
    if not response:
        OS_QUERY_BUILDER.clear()
        raise ValueError("Opensearch query failed")

    # Clear queries and filters
    OS_QUERY_BUILDER.clear()
    return format_hits(response["hits"]["hits"]), specific_search, expanded_search


if __name__ == "__main__":
    inputs = {
        "cargo_or_equipment_hauled": [],
        "insurance_coverage_min": 411,
        "fleet_size_min": 10,
        "fleet_size_max": 300,
        "in_business_months_min": 0,
        "in_business_months_max": 720,
        "safety_ratings": ["S", "N"],
        "inspections_min": 0,
        "backhaul": False,
        "origin_city": "",
        "origin_state": "AZ",
        "destination_city": "Dallas",
        "destination_state": "TX",
        "near_origin_radius": 50,
    }
    response = run_carrier_search(**inputs)
    import pprint

    pprint.pprint(response[0][:1])
