import logging
import os

import pandas as pd

from common.constants import PROJECT_ROOT


US_COUNTY_LAT_LNG_PATHNAME = os.path.join(
    PROJECT_ROOT,
    "machine_learning",
    "freightgpt",
    "data",
    "us_county_lat_lng.csv",
)

# https://transition.fcc.gov/oet/info/maps/census/fips/fips.txt
# CSV from https://gist.github.com/russellsamora/12be4f9f574e92413ea3f92ce1bc58e6
# FMCSA Reference: https://ask.fmcsa.dot.gov/euf/assets/mcmiscatalog/d_countyCD.html

# File for US County lat lng sheet used by carrier tool.
try:
    US_COUNTY_LAT_LNG = pd.read_csv(
        US_COUNTY_LAT_LNG_PATHNAME,
        dtype={"fips_code": str},
        index_col="fips_code",
    )
except Exception as e:
    logging.error(f"Error occurred while reading us_county_lat_lng.csv: {e}")
    raise

# Another reference without Puerto Rico
# County Name to FIPS encoding
# Source of truth: https://transition.fcc.gov/oet/info/maps/census/fips/fips.txt
# https://github.com/kjhealy/fips-codes/blob/master/state_and_county_fips_master.csv
