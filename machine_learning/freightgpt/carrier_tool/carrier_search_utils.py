import logging
import re
from typing import Any
from typing import Callable
from typing import Tuple

import num2words
import pandas as pd
from fuzzywuzzy import fuzz
from fuzzywuzzy import process

from common.constants import CARGO_TYPE_DICT
from common.constants import EQUIPMENT_TYPE_DICT
from machine_learning.freightgpt.constants import OPENSEARCH_PREPROCESSING_STOP_WORDS


####################################################################
#         Equipment Type / Cargo Transported Alg
####################################################################
def fuzzy_search_scorer(
    choice: str, query: str, scorer: Callable[[str, str], int] = fuzz.ratio
) -> int:
    """
    Computes a fuzzy match score between a given choice and query string, applying a length penalty
    to reduce the likelihood of false matches on short strings.

    Args:
    - choice (str): The string to be compared against the query.
    - query (str): The string to compare with the choice.
    - scorer (Callable[[str, str], int], optional): A function that computes the similarity score
      between the choice and the query. Defaults to `fuzz.ratio` from the fuzzywuzzy library.
      https://stackoverflow.com/questions/62142264/python-fuzzywuzzy-ratio-how-does-it-work#:~:text=The%20FuzzyWuzzy%20ratio%20raw%20score,matches%20in%20the%20two%20strings.

    Returns:
    - int: The final fuzzy match score between 0-100, adjusted with a length penalty if the choice length is
      greater than 5 characters. A higher score is a stronger match.

    The function proceeds as follows:
    1. Computes the initial similarity score using the provided `scorer` function.
    2. Applies a length boost based on the length of the query to adjust the score, increasing scores for higher length strings.
    3. Returns the adjusted final score.

    Example:
    >>> fuzzy_search_scorer("hello", "helo")
    >>> hello helo
    >>> 80

    Note:
    - The length penalty is only applied if the query length exceeds 5 characters.
    - This function is useful in scenarios where preventing false positives in fuzzy matching is
      critical, especially when dealing with very short strings.
    """
    # Ratio score between 0-100
    initial_score = scorer(choice, query)

    # Create a length penalty to prevent false matches against one character keys
    length_boost = 0
    if len(query) > 5:
        length_boost = 1 - (5 / len(query))

    final_score = initial_score * (
        1 + length_boost
    )  # Increase score for longer strings

    # Higher score is stronger match
    return final_score


def find_equipment_or_cargo_transported(
    cargo_str: str, acceptable_match_threshold: int = 90
) -> Tuple[str, str]:
    """
    Finds the equipment or cargo type based on the input string using exact and fuzzy matching.

    Args:
        cargo_str (str): The input string to search for in the dictionaries.
        acceptable_match_threshold (int): The minimum score threshold for an acceptable fuzzy match. Default is 50.

    Returns:
        Tuple[str, str]: A tuple containing the matched equipment or cargo type and its category ('equipment_type' or 'cargo_type').
                         Returns (None, None) if no acceptable match is found.

    Example:
        >>> equipment_type, category = find_equipment_or_cargo_transported("dry van")
        ("Dry Van", "equipment_type")

    Matching Process:
        1. Checks for an exact match in `EQUIPMENT_TYPE_DICT` and `CARGO_TYPE_DICT`.
        2. If no exact match is found, performs a fuzzy search across combined keys of both dictionaries.
        3. Applies a length penalty to the fuzzy match score to improve accuracy.
        4. Returns the best match if it exceeds the acceptable match threshold.

    """

    if cargo_str.lower() in EQUIPMENT_TYPE_DICT:
        return EQUIPMENT_TYPE_DICT[cargo_str.lower()], "equipment_type"

    if cargo_str.lower() in CARGO_TYPE_DICT:
        return CARGO_TYPE_DICT[cargo_str.lower()], "cargo_type"

    # Combine the keys from both dictionaries
    combined_keys = list(
        set(EQUIPMENT_TYPE_DICT.keys()).union(set(CARGO_TYPE_DICT.keys()))
    )

    best_match = process.extractOne(
        cargo_str, combined_keys, scorer=fuzzy_search_scorer
    )
    if best_match:
        # Higher score is stronger match.
        matched_equip_cargo_key, score = best_match[0], best_match[1]

        # Unreliable match.
        if score <= acceptable_match_threshold:
            return None, None

    # No matches
    else:
        return None, None

    # Determine which dictionary the match comes from
    if matched_equip_cargo_key in EQUIPMENT_TYPE_DICT:
        return EQUIPMENT_TYPE_DICT[matched_equip_cargo_key], "equipment_type"

    if matched_equip_cargo_key in CARGO_TYPE_DICT:
        return CARGO_TYPE_DICT[matched_equip_cargo_key], "cargo_type"

    return None, None


####################################################################
#         Processing / Cleaning
####################################################################


# TODO(P1): #2381) Move to common.utils
def clean_zip(zipcode: Any, country: str) -> str | None:
    """Cleans, validates, and parses a zipcode."""

    # Check if zipcode is NA
    if pd.isna(zipcode) or not zipcode:
        return None

    # Zipcode type converstion
    zip_len = 6 if country == "CA" else 5
    if isinstance(zipcode, int):
        zip = str(zipcode).replace(" ", "")
    else:
        if not isinstance(zipcode, str):
            logging.warning(
                "Zipcode {} has type {}, which is not str or int".format(
                    zipcode, type(zipcode)
                )
            )
            return None
        zip = zipcode.replace(" ", "")

    # Hyphen check
    if "-" in zip:
        if country != "US":
            logging.warning(f"Zipcode {zip} has hyphen, but country is not US.")
            return None
        zip = zip.split("-")[0]

    # Consider plus4 for zip codes in the US.
    if country == "US" and len(zip) > 5:
        # Split on last 4 digits of string.
        zip = zip[:-4]

    # Zipcode length validation
    if len(zip) < zip_len:
        logging.warning(
            "Zip {} does not have correct zip length of {} for {}".format(
                zip, zip_len, country
            )
        )
        zip = "0" * (zip_len - len(zip)) + zip
    elif len(zip) > zip_len:
        logging.warning(
            "Zip {} does not have correct zip length of {} for {}".format(
                zip, zip_len, country
            )
        )
        return None

    return zip


def replace_numbers_with_words(input_string: str) -> str:
    """
    Function to replace a found number with its English words representation

    Example:
        >>> replace_numbers_with_words("i have 20 carrots")
        "I have twenty carrots"

    """

    def replace_with_words(match):
        number = int(match.group(0))
        return num2words.num2words(number)

    # Use regular expression to find all numbers and replace them with their word representation
    result_string = re.sub(r"\d+", replace_with_words, input_string)
    return result_string


def remove_stop_words(input_string: str) -> str:
    """Replace stop words in a string with underscores."""

    # Tokenize the string
    words = input_string.split(" ")

    # Filter out stop words
    filtered_words = [
        word if word.lower() not in OPENSEARCH_PREPROCESSING_STOP_WORDS else "_"
        for word in words
    ]

    # Join the words back into a string
    filtered_string = " ".join(filtered_words)

    return filtered_string


def remove_whitespace(input_str: str) -> str:
    """Removes whitespace from string"""
    return re.sub(r"\s+", "", input_str)


def clean_company_name(name: str) -> str:
    """
    Cleans a company name string by applying several normalization and cleaning steps.

    The function performs the following operations in order:
    1. Converts the entire name to lowercase.
    2. Removes stop words from the name.
    3. Replaces all special characters with underscores and standardizes whitespace to a single space.
    4. Converts numeric digits to their word representations.
    5. Removes common legal business entity suffixes (e.g., "LLC", "Inc", "Corp", "Ltd", "Co").

    Example:
    >>> clean_company_name("Example Industries,   @#$$%%$          Inc. 123            ")
    'example industries one two three'
    """

    # Lowercase
    name = name.lower()

    # Remove stop words
    name = remove_stop_words(name)

    # Remove special characters and standardize whitespace
    name = re.sub(r"[^a-zA-Z0-9 ]", "_", name)
    name = re.sub(r"\s+", " ", name)
    name = name.strip()

    # Standardize numbers
    name = replace_numbers_with_words(name)

    # Remove common legal suffixes
    suffixes = ["llc", "inc", "corp", "ltd", "co"]
    for suffix in suffixes:
        if name.endswith(" " + suffix):
            name = name.replace(" " + suffix, "")

        if name.endswith(suffix):
            name = name.replace(suffix, "")

    return name


def format_and_verify_phone_number(phone_number_str: str) -> str:
    """Format the phone number and verify it is the appropriate length"""

    # Normalize the input by removing all non-digit characters
    normalized_number = re.sub(r"\D", "", phone_number_str)

    # Initialize variables
    country_code = ""
    remaining_number = ""

    # Determine the country code and the remaining part of the phone number
    if normalized_number.startswith("1") and len(normalized_number) == 11:
        country_code = "+1"
        remaining_number = normalized_number[1:]
    elif normalized_number.startswith("52") and len(normalized_number) == 12:
        country_code = "+52"
        remaining_number = normalized_number[2:]
    elif len(normalized_number) == 10:
        # Assuming a domestic number without a country code
        remaining_number = normalized_number
    else:
        logging.warning(f"Invalid phone number {phone_number_str}")
        return None

    # Format the phone number with the country code, if present
    if country_code:
        formatted_number = f"{country_code} ({remaining_number[:3]}) {remaining_number[3:6]}-{remaining_number[6:]}"
    else:
        # Format for numbers without a country code
        formatted_number = (
            f"({remaining_number[:3]}) {remaining_number[3:6]}-{remaining_number[6:]}"
        )

    return formatted_number


def deformat_phone_number(phone_number: str) -> int:
    """Deformats a phone number into a base 10 digit number"""
    # Remove all non-numeric characters
    cleaned_number = re.sub(r"\D", "", phone_number)

    # Check if the cleaned number has more than 10 digits, strip country code
    if len(cleaned_number) > 10:
        cleaned_number = cleaned_number[-10:]

    # Ensure the number is exactly 10 digits
    if len(cleaned_number) == 10:
        return int(cleaned_number)
    else:
        raise ValueError(
            "The phone number could not be converted to a 10-digit number."
        )
