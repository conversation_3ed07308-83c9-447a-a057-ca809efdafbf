import unittest

from carrier_search_utils import clean_company_name
from carrier_search_utils import clean_zip
from carrier_search_utils import deformat_phone_number
from carrier_search_utils import find_equipment_or_cargo_transported
from carrier_search_utils import fuzzy_search_scorer
from carrier_search_utils import remove_stop_words
from carrier_search_utils import remove_whitespace
from carrier_search_utils import replace_numbers_with_words


class TestFuzzySearchScorer(unittest.TestCase):
    def test_fuzzy_search_scorer_basic(self):
        score = fuzzy_search_scorer("hello", "helo")
        self.assertGreater(score, 80)
        self.assertLess(score, 90)

    def test_fuzzy_search_scorer_same(self):
        self.assertEqual(fuzzy_search_scorer("hello", "hello"), 100)

    def test_fuzzy_search_scorer_no_match(self):
        self.assertEqual(fuzzy_search_scorer("hello", "xxxxx"), 0)

    def test_fuzzy_search_scorer_empty_strings(self):
        self.assertEqual(fuzzy_search_scorer("", ""), 100)


class TestFindEquipmentOrCargoTransported(unittest.TestCase):
    def test_exact_match_equipment_type(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("Dry Van"),
            ("Dry Van", "equipment_type"),
        )

    def test_fuzzy_match_equipment_type(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("van 53"), ("Dry Van", "equipment_type")
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("flat bed"),
            ("Flatbed", "equipment_type"),
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("refrigerated"),
            ("Reefer", "equipment_type"),
        )

    def test_exact_match_cargo_type(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("Grain, Feed, Hay"),
            ("Grain, Feed, Hay", "cargo_type"),
        )

    def test_fuzzy_match(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("feed and hay"),
            ("Grain, Feed, Hay", "cargo_type"),
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("coal"),
            ("Coal/Coke", "cargo_type"),
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("Farm Supps"),
            ("Farm Supplies", "cargo_type"),
        )

    def test_no_match(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("random_string"), (None, None)
        )

    def test_single_letter_equipment_type(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("r"),
            ("Reefer", "equipment_type"),
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("v"),
            ("Dry Van", "equipment_type"),
        )

    def test_with_punctuation(self):
        self.assertEqual(
            find_equipment_or_cargo_transported("refrigerated.;'-"),
            ("Reefer", "equipment_type"),
        )
        self.assertEqual(
            find_equipment_or_cargo_transported("flat-bed"),
            ("Flatbed", "equipment_type"),
        )


class TestCleanZip(unittest.TestCase):
    def test_clean_valid_zip_with_hyphen(self):
        self.assertEqual(clean_zip("12345-6789", "US"), "12345")

    def test_clean_valid_zip_without_hyphen(self):
        self.assertEqual(clean_zip("12345", "US"), "12345")

    def test_clean_empty_zip(self):
        self.assertIsNone(clean_zip("", "US"))

    def test_clean_zip_with_spaces(self):
        self.assertEqual(clean_zip(" 123 45 ", "US"), "12345")


class TestReplaceNumbersWithWords(unittest.TestCase):
    def test_replace_single_number(self):
        self.assertEqual(
            replace_numbers_with_words("I have 20 carrots"), "I have twenty carrots"
        )

    def test_replace_multiple_numbers(self):
        self.assertEqual(
            replace_numbers_with_words("He is 18 and she is 21"),
            "He is eighteen and she is twenty-one",
        )

    def test_no_numbers_to_replace(self):
        self.assertEqual(
            replace_numbers_with_words("No numbers here"), "No numbers here"
        )

    def test_replace_hundreds(self):
        self.assertEqual(
            replace_numbers_with_words("She scored 400 in the game"),
            "She scored four hundred in the game",
        )

    def test_replace_large_numbers(self):
        self.assertEqual(
            replace_numbers_with_words("The population is 3000 in 2020"),
            "The population is three thousand in two thousand and twenty",
        )


class TestRemoveStopWords(unittest.TestCase):
    def test_remove_stop_words_basic(self):
        self.assertEqual(remove_stop_words("the quick brown fox"), "_ quick brown fox")

    def test_no_stop_words_present(self):
        self.assertEqual(remove_stop_words("quick brown fox"), "quick brown fox")


class TestRemoveWhitespace(unittest.TestCase):
    def test_remove_whitespace_basic(self):
        self.assertEqual(remove_whitespace(" a b c "), "abc")

    def test_remove_whitespace_no_extra_spaces(self):
        self.assertEqual(remove_whitespace("abc"), "abc")


class TestCleanCompanyName(unittest.TestCase):
    def test_clean_company_name_with_symbols_and_numbers(self):
        self.assertEqual(
            clean_company_name(
                "Example Industries,   @#$$%%$          Inc. 123            "
            ),
            "example industries_ _______ inc_ one hundred and twenty-three",
        )


class TestDeformatPhoneNumber(unittest.TestCase):
    def test_deformat_phone_number_us(self):
        self.assertEqual(deformat_phone_number("+****************"), 1234567890)

    def test_deformat_phone_number_plain(self):
        self.assertEqual(deformat_phone_number("1234567890"), 1234567890)

    def test_deformat_phone_number_invalid(self):
        with self.assertRaises(ValueError):
            deformat_phone_number("invalid_number")


if __name__ == "__main__":
    unittest.main()
