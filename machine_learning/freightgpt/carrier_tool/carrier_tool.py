import collections
import datetime
import functools
import logging
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Set
from typing import Tuple
from typing import Union

import boto3
import pandas as pd
import titlecase

from common import init_main
from common.s3_cache_file import load_s3_json

PROJECT_ROOT = init_main.initialize()

from machine_learning.freightgpt.carrier_tool.constants import US_COUNTY_LAT_LNG
from machine_learning.freightgpt.constants import (
    IGNORE_CRASH_ROAD_CONDITIONS,
    IGNORE_CRASH_WEATHER_CONDITIONS,
    STATE_FIPS_DICT,
    CRASH_EVENT,
    CRASH_WEATHER_CONDITION,
    CRASH_ROAD_SURFACE_CONDITION,
    VisualizationType,
)

from machine_learning.freightgpt.constants import ActiveType
from machine_learning.freightgpt.carrier_tool import carrier_tool_census
from machine_learning.freightgpt.carrier_tool.borders import USCensusBorders

import time

start_time = time.perf_counter()

S3_CLIENT = boto3.client("s3")
INSPECTION_BUCKET = "fmcsa-inspection-data"
CRASH_BUCKET = "fmcsa-crash-data"
NUM_SAMPLE_CRASHES = 5
NUM_SAMPLE_INSPECTIONS = 10
MAX_COUNTY_COUNT = 100

us_county_borders = USCensusBorders()

print(
    f"┌───Time to load carrier tool resources: {time.perf_counter() - start_time} seconds"
)


def get_county_info(county_id: float, state_code: str) -> Dict[str, Union[str, float]]:
    """
    Returns name/lat/lng of a county given its county ID and state code.

    Args:
    - county_id (float): County FIPS id, such as 89, 100, 510, etc.
    - state_code (str): Two-letter state code such as 'AK', 'CA', 'NY', etc.

    Returns:
    (dict) Information corresponding to county FIPS id.
        - name (str): County name.
        - lat (float): Latitude.
        - lng (float): Longitude.
    """
    if pd.isna(county_id) or pd.isna(state_code):
        return None
    # Get the FIPS code of the state
    state_fips = STATE_FIPS_DICT[state_code]["fips"]  # Also has state name

    # Regardless of county_id type, convert to a 3 digit string with leading zeros (e.g. 089, 100, 510)
    fips_code = f"{state_fips}{float(county_id):03.0f}"
    # TODO(P1): GMaps to find lat/lng of county, update US_COUNTY_LAT_LNG as S3CacheFile
    if fips_code not in US_COUNTY_LAT_LNG.index:
        # Find the closest FIPS code by integer difference
        logging.debug(f"FIPS code {fips_code} not found.")
        fips_code = min(
            US_COUNTY_LAT_LNG.index, key=lambda x: abs(int(x) - int(fips_code))
        )
        logging.debug(f"Using closest FIPS code {fips_code} instead.")

    # Get the latitude and longitude of the county
    county_info = US_COUNTY_LAT_LNG.loc[fips_code]

    return county_info.to_dict()


def compute_borders(
    county_counts: Set[Tuple[str, str]], state_counts: Dict[str, int]
) -> Dict[str, Dict[str, Union[List[List[Tuple[float, float]]], int]]]:
    """
    Compute and return geographic borders for a given set of counties and states.

    Checks if the size of county_state_set is less than a predefined maximum. If so,
    it updates the border store with each county-state pair and then returns the borders for these counties.
    If the size exceeds the maximum, it updates the border store for each state in state_set with a tolerance
    and returns the borders for these states.

    Parameters:
    - county_state_set (set of tuple): Set of (county, state) pairs.
    - state_counts (dict): Dict of states and counts.
    """

    # Check if the number of county-state pairs is within the allowed limit
    if len(county_counts) < MAX_COUNTY_COUNT:
        for county_state_pair in county_counts:
            us_county_borders.update_border_store(
                county_state_pair,
                count=county_counts[county_state_pair],
                tolerance=0.01,
            )

        return us_county_borders.get_county_borders()

    # If the number of county-state pairs exceeds the limit, process states
    for state in state_counts:
        us_county_borders.update_border_store(
            (None, state), count=state_counts[state], tolerance=0.1
        )

    return us_county_borders.get_state_borders()


def format_inspection_info(
    raw_data: Dict[str, Any]
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Formats the inspection data into a usable format.
    raw_data looks like this:
    ```json
        "legend":"table_key.column_name"
        {
            "inspection.dot_number":[
                {
                    "inspection.inspection_id":{
                        "inspection.*",
                        "carrier.*",
                        "violations":[
                        "merged_violations.*"
                        ],
                        "studies":[
                        "study.*"
                        ],
                        "units":[
                        "inspection_unit.*"
                        ]
                    }
                }
            ]
        }
    ```
    Keys are found in the following link.
    https://www.fmcsa.dot.gov/registration/mcmis-catalog-inspection-file-record-layout

    Other keys of interest:
    # alcohol_control_sub, drug_intrdctn_search, drug_intrdctn_arrests?
    # viol_total? driver_viol_total? vehicle_viol_total? same for oos?

    Args:
        raw_data: The raw inspection data. Loaded JSON from S3.

    Returns:
        A dictionary of formatted inspection data.

    """
    # TODO(P1): Should be inspection_id, but Roop's ingestion lost that field
    primary_key = "report_state"
    formatted_info = {}
    if raw_data is None or not raw_data.get(primary_key):
        return {}, {}
    num_inspections = len(raw_data[primary_key])
    county_counts = collections.defaultdict(lambda: 0)
    state_counts = collections.defaultdict(lambda: 0)

    violation_descs = []
    tot_inspections = 0
    counters = {
        "driver_violations": 0,
        "driver_oos": 0,
        "vehicle_violations": 0,
        "vehicle_oos": 0,
        "hazmat_violations": 0,
        "hazmat_oos": 0,
    }

    for i in range(num_inspections - 1, -1, -1):
        insp_date_list = raw_data.get("insp_date", [])
        if i >= len(insp_date_list):
            continue
        inspection_date = datetime.datetime.strptime(
            str(raw_data["insp_date"][i]), "%Y%m%d"
        )
        # Only look at inspections in the last 2 years
        if inspection_date < datetime.datetime.now() - datetime.timedelta(days=365 * 2):
            continue

        tot_inspections += 1

        for counter_key in counters:
            counter_list = raw_data.get(f"{counter_key}_total", [])
            if i < len(counter_list):
                counters[counter_key] += int(counter_list[i])

        county_code_list = raw_data.get("county_code", [])
        county_code_state_list = raw_data.get("county_code_state", [])

        # Ensure indexing won't lead to an error
        if i < len(county_code_list) and i < len(county_code_state_list):
            county_name = get_county_info(
                raw_data["county_code"][i], raw_data["county_code_state"][i]
            )["name"]
            state = raw_data["county_code_state"][i]
            county_counts[(county_name, state)] += 1
            state_counts[state] += 1

        violations_list = raw_data.get("violations", [])
        if (
            i >= len(violations_list)
            or not violations_list[i]
            or len(violation_descs) >= NUM_SAMPLE_INSPECTIONS
        ):
            continue
        for j in range(len(violations_list[i]["insp_violation_id"])):
            if len(violation_descs) < NUM_SAMPLE_INSPECTIONS:
                supp_desc_list = violations_list[i].get("supp_desc", [])
                if j < len(supp_desc_list):
                    supp_desc = supp_desc_list[j]
                    if not pd.isna(supp_desc) and isinstance(supp_desc, str):
                        violation_descs.append(supp_desc.capitalize())

    if tot_inspections:
        formatted_info["inspections_in_last_two_years"] = tot_inspections
    for counter_key in counters:
        if counters[counter_key]:
            formatted_key = counter_key.replace("oos", "out_of_service_violations")
            formatted_info[formatted_key] = counters[counter_key]

    if violation_descs:
        formatted_info["violation_descriptions"] = violation_descs

    inspection_borders = compute_borders(county_counts, state_counts)
    event = (
        {
            "active_type": ActiveType.ACTIVE,
            "region": "inspection_county",
            "coordinates": inspection_borders,
        }
        if inspection_borders
        else None
    )

    return formatted_info, event


def format_crash_info(
    raw_data: Dict[str, Any]
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Formats the crash data into a usable format.

    https://www.fmcsa.dot.gov/registration/mcmis-catalog-crash-file-record-layout

    potentially useful keys:
    city, state, event_id (list), report_date
    ROAD_SURFACE_CONDITION_ID, WEATHER_CONDITION_ID, location, county_code

    Args:
        raw_data: The raw crash data. Loaded JSON from S3.

    Returns:
        A dictionary of formatted crash data.
    """

    primary_key = "crash_id"
    formatted_info = {}
    if raw_data is None or not raw_data.get(primary_key):
        return {}, {}
    num_crashes = len(raw_data[primary_key])
    county_counts = collections.defaultdict(lambda: 0)
    state_counts = collections.defaultdict(lambda: 0)

    total_crashes = 0
    sample_crashes = []

    for i in range(num_crashes - 1, -1, -1):
        crash_date_list = raw_data.get("report_date", [])
        if i >= len(crash_date_list):
            continue

        crash_date = datetime.datetime.strptime(
            str(raw_data["report_date"][i]), "%Y%m%d"
        )
        # Only look at crashes in the last 2 years
        if crash_date < datetime.datetime.now() - datetime.timedelta(days=365 * 2):
            continue

        total_crashes += 1

        county_code_list = raw_data.get("county_code", [])
        county_code_state_list = raw_data.get("state", [])

        if i < len(county_code_list) and i < len(county_code_state_list):
            # Compute county border
            county_name = get_county_info(
                raw_data["county_code"][i], raw_data["state"][i]
            )["name"]
            state = raw_data["state"][i]
            county_counts[(county_name, state)] += 1
            state_counts[state] += 1

        if len(sample_crashes) >= NUM_SAMPLE_CRASHES:
            break

        sample_crash = {
            "crash_date": crash_date.strftime("%Y-%m-%d"),
        }

        def get_str_from_col(col: str) -> str:
            """
            Retrieves and returns a string value from a specified column's list in `raw_data` at index `i`.
            """
            col_list = raw_data.get(col, [])
            if i < len(col_list) and col_list[i] and isinstance(col_list[i], str):
                return col_list[i]
            return ""

        if state_val := get_str_from_col("state"):
            sample_crash["state"] = state_val.upper()

        if city_val := get_str_from_col("city"):
            sample_crash["city"] = titlecase.titlecase(city_val)

        if location_val := get_str_from_col("location"):
            sample_crash["location"] = titlecase.titlecase(location_val)

        # If there are events, translate them to descriptions.
        event_id_list = raw_data.get("event_id", [])
        if i < len(event_id_list) and isinstance(event_id_list[i], list):
            crash_events = []
            for e_code in event_id_list[i]:
                if not (event := CRASH_EVENT.get(e_code, False)):
                    continue
                event_other_desc_list = raw_data.get("event_other_desc", [])
                if event == "Other" and i < len(event_other_desc_list):
                    event = raw_data["event_other_desc"][i]
                crash_events.append(event)
            if crash_events:
                sample_crash["events"] = crash_events

        weather_condition_id_list = raw_data.get("weather_condition_id", [])
        road_surface_condition_id_list = raw_data.get("road_surface_condition_id", [])

        if i < len(weather_condition_id_list):
            weather = CRASH_WEATHER_CONDITION.get(weather_condition_id_list[i], False)
            if weather and weather not in IGNORE_CRASH_WEATHER_CONDITIONS:
                sample_crash["weather_condition"] = weather

        if i < len(road_surface_condition_id_list):
            road_condition = CRASH_ROAD_SURFACE_CONDITION.get(
                road_surface_condition_id_list[i], False
            )
            if road_condition and road_condition not in IGNORE_CRASH_ROAD_CONDITIONS:
                sample_crash["road_condition"] = road_condition

        if sample_crash:
            sample_crashes.append(sample_crash)

    if total_crashes:
        formatted_info["crashes_in_last_two_years"] = total_crashes

    if sample_crashes:
        formatted_info["sample_crashes"] = sample_crashes

    crash_borders = compute_borders(county_counts, state_counts)
    event = (
        {
            "active_type": ActiveType.ACTIVE,
            "region": "crash_county",
            "coordinates": crash_borders,
        }
        if crash_borders
        else None
    )

    return formatted_info, event


@functools.lru_cache(maxsize=8)
def fetch_inspection_info(
    dot_number: int,
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Gets formatted inspection information for a given DOT number."""
    s3_filename = f"{dot_number}.json"
    raw_inspection_data = load_s3_json(INSPECTION_BUCKET, s3_filename, S3_CLIENT)
    logging.debug(f"Loaded inspection data for {dot_number}:\n{raw_inspection_data}")
    inspection_info, county_borders_event = format_inspection_info(raw_inspection_data)
    logging.info(f"Formatted data:\n{inspection_info}")
    return inspection_info, county_borders_event


@functools.lru_cache(maxsize=8)
def fetch_crash_info(
    dot_number: int,
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Gets formatted crash information for a given DOT number."""
    s3_filename = f"{dot_number}.json"
    raw_crash_data = load_s3_json(CRASH_BUCKET, s3_filename, S3_CLIENT)
    logging.debug(f"Loaded crash data for {dot_number}:\n{raw_crash_data}")
    crash_info, county_borders_event = format_crash_info(raw_crash_data)
    logging.info(f"Formatted data:\n{crash_info}")
    return crash_info, county_borders_event


def fetch_carrier_info(
    dot_number: int = None,
    icc_number: str = None,
    fetch_inspection: bool = False,
    fetch_crash: bool = False,
    emission_function: Callable = None,
) -> Dict[str, Dict[str, Any]]:
    """Gets information based on DOT or ICC number.

    We use LRU cache avoid hitting the database, for latency.

    I thought about implementing flags to prevent unnecessary gets on inspection and crash data,
    but that breaks the one-line LRU cache decorator. We can add it later if we need to for performance.

    Example:
    get_carrier_info_tool(dot_number=1234567)
    get_carrier_info_tool(icc_number="MC1234567")
    """
    # TODO(P1): Consider limiting the number of entries in the memoization dict.
    census_dict = carrier_tool_census.fetch_census_info(dot_number, icc_number)
    logging.info(f"Loaded census data for {dot_number}:\n{census_dict}")
    if not census_dict:
        logging.warning(f"Could not find census data for {dot_number}")
        return {}

    if "error" in census_dict:
        return census_dict

    inspection_dict, inspection_event = {}, {}
    if fetch_inspection:
        inspection_dict, inspection_event = fetch_inspection_info(
            census_dict["dot_number"]
        )
        if inspection_dict:
            if census_dict.get("dot_number"):
                inspection_dict["dot_number"] = census_dict["dot_number"]
            if census_dict.get("icc_number"):
                inspection_dict["icc_number"] = census_dict["icc_number"]
            if census_dict.get("company_name"):
                inspection_dict["company_name"] = census_dict["company_name"]
        else:
            logging.warning(f"Could not find inspection data for {dot_number}")

        us_county_borders.clear_county_borders()
        us_county_borders.clear_state_borders()

    crash_dict, crash_event = {}, {}
    if fetch_crash:
        crash_dict, crash_event = fetch_crash_info(census_dict["dot_number"])
        if crash_dict:
            if census_dict.get("dot_number"):
                crash_dict["dot_number"] = census_dict["dot_number"]
            if census_dict.get("icc_number"):
                crash_dict["icc_number"] = census_dict["icc_number"]
            if census_dict.get("company_name"):
                crash_dict["company_name"] = census_dict["company_name"]
        else:
            logging.warning(f"Could not find crash data for {dot_number}")

        us_county_borders.clear_county_borders()
        us_county_borders.clear_state_borders()

    # Emit only if one out of crash or inspection is provided. Do not emit for census calls alone.
    emit = fetch_inspection ^ fetch_crash
    if emit and emission_function and callable(emission_function) and inspection_event:
        emission_function(inspection_event, event_type=VisualizationType.POLYGON)

    if emit and emission_function and callable(emission_function) and crash_event:
        emission_function(crash_event, event_type=VisualizationType.POLYGON)

    return {
        "census_dict": census_dict,
        "inspection_dict": inspection_dict,
        "crash_dict": crash_dict,
    }


def main():
    import time

    carriers = [
        # {"dot_number": 2271487},
        # {"dot_number": 2271487},
        # {"dot_number": 940613},
        # {"dot_number": 3413794},
        # {"dot_number": 1219809},
        # {"icc_number": "1219809"},
        # {"icc_number": "MC1219809"},
        # {"icc_number": "15295"},
        # {"icc_number": "MC15295"},
        # {"icc_number": "FF15295"},
        # {"dot_number": "238579"},
        # {"icc_number": "238579"},
        # {"dot_number": 54283}, # very big
        {"dot_number": 3672541},
        {"dot_number": 123456},
        {"dot_number": 2271487},
        {"dot_number": 2271487},  # Repeated call
        {"dot_number": 2271487},
        {"icc_number": "MC7755"},
    ]

    for carrier in carriers:
        start_time = time.perf_counter()
        result = fetch_carrier_info(**carrier)
        elapsed_time = time.perf_counter() - start_time
        # print(result)
        if not (result):
            print(f"Error fetching info for {carrier}")
        print(f"Elapsed time: {elapsed_time} seconds")
        print(fetch_carrier_info.cache_info())

    print(fetch_carrier_info(dot_number=2271487))
    print(fetch_carrier_info(dot_number=3154135))
    print(fetch_carrier_info(icc_number=2271487))
    print(fetch_carrier_info(icc_number="MC106619"))
    print(fetch_carrier_info(dot_number="15295"))
    print(fetch_carrier_info(icc_number="15295"))
    print(fetch_carrier_info(icc_number="MC15295"))
    print(fetch_carrier_info(icc_number="FF15295"))
    print(fetch_carrier_info(icc_number="MX15295"))


if __name__ == "__main__":
    main()
