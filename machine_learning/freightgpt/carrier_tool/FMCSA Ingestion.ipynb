{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# FMCSA Data Ingestion\n", "We need to run this script every month. Need to figure out how to automate."]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Ingest FMCSA Census Data to DynamoDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common.credentials import Credentials\n", "\n", "CREDENTIALS = Credentials()\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"census_data\"\n", ")\n", "\n", "\n", "# Read CSV files\n", "one = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20240209_1of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "two = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20240209_2of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "three = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20240209_3of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "\n", "# Concatenate into a larger dataframe\n", "census_df = pd.concat([one, two, three], ignore_index=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Science Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import zipfile\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"census_data\"\n", ")\n", "\n", "zipfile_path = os.path.join(PREFIX, \"CENSUS_PUB_20240209_splitfile.zip\")\n", "zipfile_dir = zipfile_path.split(\".zip\")[0]\n", "\n", "# Unzip into the same directory\n", "with zipfile.ZipFile(zipfile_path, \"r\") as zip_ref:\n", "    zip_ref.extractall(zipfile_dir)\n", "\n", "census_df = pd.DataFrame()\n", "# Read CSV files from the unzipped directory\n", "for file in os.listdir(zipfile_dir):\n", "    if file.endswith(\".csv\"):\n", "        print(file)\n", "        df = pd.read_csv(\n", "            os.path.join(zipfile_dir, file),\n", "            delimiter=\"~\",\n", "            encoding=\"ISO-8859-1\",\n", "            low_memory=False,\n", "        )\n", "        census_df = pd.concat([census_df, df], ignore_index=True)\n", "\n", "census_df[\"ADDDATE\"] = pd.to_datetime(\n", "    census_df[\"ADDDATE\"], format=\"%Y%m%d\", errors=\"coerce\"\n", ")\n", "# census_df[\"ADDDATE\"] = census_df[\"ADDDATE\"].dt.date\n", "import matplotlib.pyplot as plt\n", "\n", "# Increase the size of the plot\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot the histogram\n", "census_df[census_df[\"ADDDATE\"] > pd.to_datetime(\"2024-02-01\")][\"ADDDATE\"].hist()\n", "\n", "# Add labels and title\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Histogram of ADDDATE\")\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Assuming census_df is already defined and loaded\n", "\n", "# Convert ADDDATE and MCS_150_DATE to datetime format at the start to avoid repeated conversions\n", "census_df[\"ADDDATE\"] = pd.to_datetime(\n", "    census_df[\"ADDDATE\"], format=\"%Y%m%d\", errors=\"coerce\"\n", ")\n", "census_df[\"MCS_150_DATE\"] = pd.to_datetime(\n", "    census_df[\"MCS_150_DATE\"], format=\"%Y%m%d\", errors=\"coerce\"\n", ")\n", "\n", "# Start filtering\n", "filtered_df = census_df[census_df[\"ACT_STAT\"] == \"A\"]\n", "print(\"Removed inactive carriers\", len(filtered_df))\n", "\n", "filtered_df = filtered_df[~filtered_df[\"RATING\"].isin([\"C\", \"U\"])]\n", "print(\"Removed C and U rated carriers\", len(filtered_df))\n", "\n", "# # Calculate 'in_business_for' as a datetime timedelta and filter\n", "current_date = pd.Timestamp.now()\n", "# filtered_df[\"in_business_for\"] = current_date - filtered_df[\"ADDDATE\"]\n", "# filtered_df = filtered_df[filtered_df[\"in_business_for\"] > pd.Timedel<PERSON>(days=30)]\n", "# print(\"Removed carriers in business for less than 11 months\", len(filtered_df))\n", "\n", "# Ensure MCS_150_DATE is not null and filter based on the date being less than ~2 years ago\n", "filtered_df = filtered_df[filtered_df[\"MCS_150_DATE\"].notnull()]\n", "filtered_df = filtered_df[\n", "    filtered_df[\"MCS_150_DATE\"] > (current_date - pd.Timedel<PERSON>(days=760))\n", "]\n", "print(\"Removed carriers with MCS150DATE less than 2 years\", len(filtered_df))\n", "\n", "# Ensure that the carrier has email\n", "filtered_df = filtered_df[filtered_df[\"EMAILADDRESS\"].notnull()]\n", "print(\"Removed carriers without email\", len(filtered_df))\n", "\n", "# Ensure that the carrier has a phone number (CELL_NUM or TEL_NUM)\n", "filtered_df = filtered_df[\n", "    filtered_df[\"CELL_NUM\"].notnull() | filtered_df[\"TEL_NUM\"].notnull()\n", "]\n", "print(\"Removed carriers without phone number\", len(filtered_df))\n", "\n", "# Only keep CARSHIP which has substrings \"C\", \"F\", and \"T\"\n", "filtered_df = filtered_df[\n", "    filtered_df[\"CARSHIP\"].str.contains(\"C|F|T\", case=False, na=False)\n", "]\n", "print(\"Removed only shipper, only broker\", len(filtered_df))\n", "\n", "# Need at least 1 tot_truck\n", "filtered_df = filtered_df[filtered_df[\"TOT_TRUCKS\"] > 0]\n", "print(\"Removed carriers with no trucks\", len(filtered_df))\n", "\n", "filtered_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_df.head(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter for carriers that own tractors\n", "carriers_owning_tractors = census_df[census_df[\"OWNTRACT\"] > 0]\n", "\n", "# Filter for carriers that lease tractors - term or trip\n", "carriers_leasing_tractors = carriers_owning_tractors[\n", "    (carriers_owning_tractors[\"TRMTRACT\"] > 0)\n", "    | (carriers_owning_tractors[\"TRPTRACT\"] > 0)\n", "]\n", "\n", "# If we want to see the details for these carriers\n", "carrier_details = carriers_leasing_tractors[\n", "    [\n", "        \"NAME\",\n", "        # \"ICC_DOCKETS\",\n", "        \"ADDDATE\",\n", "        \"PHY_CITY\",\n", "        \"PHY_ST\",\n", "        \"PHY_ZIP\",\n", "        \"OWNTRUCK\",\n", "        \"OWNTRACT\",\n", "        \"OWNTRAIL\",\n", "        \"TRMTRUCK\",\n", "        \"TRMTRACT\",\n", "        \"TRMTRAIL\",\n", "        \"TRPTRUCK\",\n", "        \"TRPTRACT\",\n", "        \"TRPTRAIL\",\n", "        \"TOT_PWR\",  # includes buses\n", "        \"TOT_TRUCKS\",\n", "    ]\n", "]\n", "\n", "carrier_details.head()  # Displaying the first few rows for brevity."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if there are rows that have non-NA values for all required fields\n", "required_fields = [\n", "    \"name\",\n", "    \"name_dba\",\n", "    \"adddate\",\n", "    \"phy_city\",\n", "    \"phy_st\",\n", "    \"tot_trucks\",\n", "    \"cdl_drs\",\n", "    \"rating\",\n", "    \"carship\",\n", "]\n", "\n", "valid_rows = census_df.dropna(subset=[s.upper() for s in required_fields])\n", "\n", "if not valid_rows.empty:\n", "    # If there are valid rows, retrieve the 'DOT_NUMBER' from the first such row\n", "    dot_number = valid_rows.iloc[0][\"DOT_NUMBER\"]\n", "    print(f\"Found DOT_NUMBER: {dot_number}\")\n", "else:\n", "    print(\"No rows with all required fields present were found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filter NAME_DBA > 100\n", "DBA_BLACKLIST = [\n", "    \"OWNER\",\n", "    \"NO\",\n", "    \"NONE\",\n", "    \"TRANSPORTATION\",\n", "    \"Y\",\n", "    \"CONSTRUCTION\",\n", "    \"UNISHIPPERS\",\n", "    \"TRUCKING\",\n", "    \"LANDSCAPING\",\n", "    \"FARM\",\n", "    \"FARMER\",\n", "    \"FARMING\",\n", "    \"TOWING\",\n", "    \"LLC\",\n", "    \"GENERAL CONTRACTOR\",\n", "    \"PRIVATE\",\n", "    \"CARRIER\",\n", "    \"NOT APPLICABLE\",\n", "    \"CONTRACTOR\",\n", "    \"TRANSPORT\",\n", "    \"PRESIDENT\",\n", "]\n", "\n", "pd.set_option(\"display.max_rows\", None)\n", "census_df[\"NAME_DBA\"].value_counts()[census_df[\"NAME_DBA\"].value_counts() > 50]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Write to DynamoDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "dynamodb = boto3.client(\"dynamodb\")\n", "\n", "# Get stats on the table\n", "response = dynamodb.describe_table(TableName=\"CarrierFMCSA\")\n", "\n", "read_capacity = response[\"Table\"][\"ProvisionedThroughput\"][\"ReadCapacityUnits\"]\n", "write_capacity = response[\"Table\"][\"ProvisionedThroughput\"][\"WriteCapacityUnits\"]\n", "\n", "print(f\"Read Capacity: {read_capacity}\")\n", "print(f\"Write Capacity: {write_capacity}\")\n", "print(f'Item Count: {response[\"Table\"][\"ItemCount\"]}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import logging\n", "from decimal import Decimal\n", "import typing\n", "\n", "import aioboto3\n", "import boto3\n", "from botocore.exceptions import ClientError\n", "import pandas as pd\n", "\n", "\n", "async_dynamodb_session = aioboto3.Session()\n", "sync_dynamodb = boto3.resource(\"dynamodb\", region_name=\"us-east-1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["census_df = old_census_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_to_dynamo_format(value: typing.Any) -> typing.Any:\n", "    \"Converts a value to the appropriate format for DynamoDB.\"\n", "    if isinstance(value, dict):\n", "        return {key: convert_to_dynamo_format(val) for key, val in value.items()}\n", "    if isinstance(value, list):\n", "        return [convert_to_dynamo_format(val) for val in value]\n", "    if pd.isna(value):\n", "        return None\n", "    if isinstance(value, (int, float, Decimal)):\n", "        return None if abs(value) == float(\"inf\") else Decimal(str(value))\n", "    return str(value)\n", "\n", "\n", "def batch_upload(\n", "    table_name: str,\n", "    items: typing.List[typing.Dict[str, typing.Any]],\n", "    gsi_keys: typing.List[str],\n", "    timestamp: bool = True,\n", ") -> None:\n", "    \"\"\"Synchronously upload a batch of items to a DynamoDB table.\n", "\n", "    Args:\n", "        table_name (str): The name of the DynamoDB table.\n", "        items (list): A list of dictionaries, where each dictionary is an item to upload.\n", "        timestamp (bool): whether to include a timestamp with the inserted item\n", "\n", "    \"\"\"\n", "    table = sync_dynamodb.Table(table_name)\n", "\n", "    with table.batch_writer() as batch:\n", "        for item in items:\n", "            try:\n", "                dynamo_item = convert_to_dynamo_format(item)\n", "                for gsi_key in gsi_keys:\n", "                    if gsi_key in dynamo_item and pd.isna(dynamo_item[gsi_key]):\n", "                        del dynamo_item[gsi_key]\n", "                if timestamp:\n", "                    dynamo_item[\"ingested_timestamp\"] = datetime.datetime.now(\n", "                        datetime.timezone.utc\n", "                    ).isoformat()\n", "                print(\"dynamo item\", dynamo_item)\n", "                _ = batch.put_item(Item=dynamo_item)\n", "            except Exception as e:\n", "                logging.error(f\"Error: {e} caused by item: {item}\")\n", "                raise\n", "\n", "\n", "batch_upload(\"CarrierFMCSA\", [dd], gsi_keys=[\"icc1\", \"icc2\", \"icc3\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dd = census_df.to_dict(\"records\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming 'chunks' is an iterable of DataFrame chunks\n", "from common import dynamodb_utils\n", "import boto3\n", "\n", "CENSUS_TABLE = dynamodb.Table(\"CarrierFMCSA\")\n", "try:\n", "    response = CENSUS_TABLE.update(\n", "        ProvisionedThroughput={\n", "            \"WriteCapacityUnits\": 100,\n", "            \"ReadCapacityUnits\": 1,\n", "        }\n", "    )\n", "except Exception as e:\n", "    print(e)\n", "dynamodb = boto3.resource(\"dynamodb\")\n", "from common import dynamodb_utils\n", "\n", "CENSUS_TABLE = dynamodb.Table(\"CarrierFMCSA\")\n", "WORKERS = 1\n", "dynamodb_utils.upload_df_in_chunks(census_df, \"CarrierFMCSA\", 2)\n", "\n", "response = CENSUS_TABLE.update(\n", "    ProvisionedThroughput={\n", "        \"WriteCapacityUnits\": 1,\n", "        \"ReadCapacityUnits\": 1,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get count of table\n", "# !aws dynamodb scan --table-name CarrierFMCSA --select \"COUNT\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Add secondary indices for ICC lookup\n", "You have to manually change the attributename"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# add sort key icc3. did 1 and 2 already\n", "import boto3\n", "\n", "dynamodb = boto3.client(\"dynamodb\")\n", "\n", "response = dynamodb.update_table(\n", "    TableName=\"CarrierFMCSA\",\n", "    AttributeDefinitions=[\n", "        {\"AttributeName\": \"icc3\", \"AttributeType\": \"N\"},\n", "    ],\n", "    GlobalSecondaryIndexUpdates=[\n", "        {\n", "            \"Create\": {\n", "                \"IndexName\": \"ICC3DocketIndex\",\n", "                \"KeySchema\": [\n", "                    {\"AttributeName\": \"icc3\", \"KeyType\": \"HASH\"},\n", "                ],\n", "                \"Projection\": {\"ProjectionType\": \"ALL\"},\n", "                \"ProvisionedThroughput\": {\n", "                    \"ReadCapacityUnits\": 1000,\n", "                    \"WriteCapacityUnits\": 1000,\n", "                },\n", "            }\n", "        }\n", "    ],\n", ")\n", "\n", "\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Decrease icc2 capacity after it finishes updating\n", "response = dynamodb.update_table(\n", "    TableName=\"CarrierFMCSA\",\n", "    GlobalSecondaryIndexUpdates=[\n", "        {\n", "            \"Update\": {  # Change 'Create' to 'Update' because the index already exists\n", "                \"IndexName\": \"ICC3DocketIndex\",\n", "                \"ProvisionedThroughput\": {\n", "                    \"ReadCapacityUnits\": 1,  # Increase read capacity to 1000\n", "                    \"WriteCapacityUnits\": 1,  # Increase write capacity to 1000\n", "                },\n", "            }\n", "        }\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Ingest FMCSA Crash Data to S3\n", "DynamoDB size limits are too small for this dataset. We need to use S3."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from tqdm.notebook import tqdm\n", "\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common.credentials import Credentials\n", "\n", "CREDENTIALS = Credentials()\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"crash_data\"\n", ")\n", "\n", "\n", "def load_and_combine_crash_data(start_year, end_year) -> pd.DataFrame:\n", "    \"\"\"\n", "    This function loads crash-related data files from the given start year to the end year,\n", "    sorts the CrashEvent data by SEQ_NO, aggregates it ensuring placeholders for missing descriptions,\n", "    and combines it with CrashCarrier and CrashMaster into a single DataFrame, ensuring CRASH_ID is unique per row.\n", "\n", "    :param start_year: The starting year of the data files.\n", "    :param end_year: The ending year of the data files.\n", "    :param prefix: The file path prefix to the data files.\n", "    :return: The combined DataFrame.\n", "    \"\"\"\n", "    encoding = \"ISO-8859-1\"  # Encoding to handle non-UTF-8 characters\n", "    combined_df = pd.DataFrame()  # Initialize an empty DataFrame to combine all data\n", "\n", "    # Loop through each year in the range and load the relevant files\n", "    for year in range(start_year, end_year + 1):\n", "        # Construct file paths based on the year\n", "        suffix = (\n", "            f\"0101{year}_1231{year}HDR.txt\"\n", "            if year != 2023\n", "            else \"01012023_10092023HDR.txt\"\n", "        )\n", "        carrier_file = os.path.join(PREFIX, f\"CrashCarrier_{suffix}\")\n", "        event_file = os.path.join(PREFIX, f\"CrashEvent_{suffix}\")\n", "        master_file = os.path.join(PREFIX, f\"CrashMaster_{suffix}\")\n", "\n", "        # Load data from files into DataFrames\n", "        crash_carrier_df = pd.read_csv(\n", "            carrier_file, sep=\"\\t\", encoding=encoding, low_memory=False\n", "        )\n", "        crash_event_df = pd.read_csv(\n", "            event_file, sep=\"\\t\", encoding=encoding, low_memory=False\n", "        )\n", "        crash_master_df = pd.read_csv(\n", "            master_file, sep=\"\\t\", encoding=encoding, low_memory=False\n", "        )\n", "\n", "        # Sort CrashEvent data by SEQ_NO\n", "        crash_event_df = crash_event_df.sort_values(by=[\"CRASH_ID\", \"SEQ_NO\"])\n", "\n", "        # Function to handle EVENT_OTHER_DESC aggregation with placeholders for missing values\n", "        def aggregate_descriptions(series):\n", "            descriptions = series.tolist()  # Convert the series to a list\n", "            if len(descriptions) == 0:\n", "                return []\n", "            # Fill in None for missing descriptions to keep the list aligned with EVENT_ID\n", "            return [desc if pd.notnull(desc) else None for desc in descriptions]\n", "\n", "        # Aggregate CrashEvent data by CRASH_ID\n", "        crash_event_agg = (\n", "            crash_event_df.groupby(\"CRASH_ID\")\n", "            .agg({\"EVENT_ID\": list, \"EVENT_OTHER_DESC\": aggregate_descriptions})\n", "            .reset_index()\n", "        )\n", "\n", "        # Merge the aggregated CrashEvent data with the CrashCarrier data\n", "        combined_year_df = pd.merge(\n", "            crash_carrier_df, crash_event_agg, on=\"CRASH_ID\", how=\"left\"\n", "        )\n", "\n", "        # Merge the combined data with the CrashMaster data\n", "        combined_year_df = pd.merge(\n", "            combined_year_df, crash_master_df, on=\"CRASH_ID\", how=\"left\"\n", "        )\n", "\n", "        # Concatenate the combined data for the year with the main DataFrame\n", "        combined_df = pd.concat([combined_df, combined_year_df], ignore_index=True)\n", "\n", "    return combined_df\n", "\n", "\n", "crashes_df = load_and_combine_crash_data(2019, 2023)\n", "# lower all cols\n", "crashes_df.columns = [x.lower() for x in crashes_df.columns]\n", "# 203578 have no docket or dot number\n", "# 203949 have no DOT number\n", "# 593096 have no docket number\n", "crashes_df = crashes_df.dropna(subset=[\"dot_number\"])\n", "crashes_df = crashes_df.groupby(\"dot_number\").agg(list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "\n", "formatted_data = []\n", "for index, row in tqdm(crashes_df.iterrows(), total=len(crashes_df)):\n", "    # Creating the nested 'crashes' dictionary\n", "    crashes_dict = {col: row[col] for col in crashes_df.columns if col != \"dot_number\"}\n", "\n", "    # Creating the main dictionary for each row\n", "    row_dict = {\"dot_number\": int(index), \"crashes\": crashes_dict}\n", "\n", "    formatted_data.append(row_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import boto3\n", "import concurrent\n", "from tqdm.notebook import tqdm\n", "import typing\n", "import numpy as np\n", "\n", "# Assuming you have already configured boto3 with your AWS credentials\n", "import boto3\n", "import botocore\n", "\n", "client_config = botocore.config.Config(max_pool_connections=100)\n", "s3_client = boto3.client(\"s3\", config=client_config)\n", "\n", "\n", "def upload_chunk_to_s3(\n", "    chunk: typing.List[typing.Dict], pbar, index_col, object_col, bucket_name\n", "):\n", "    \"\"\"Assumes that you're passing in a list of dicts with an index_col to use as the filename and an object_col to use as the body of the object.\n", "\n", "    For example, if you have a list of dicts like this:\n", "    [\n", "        {\n", "            \"dot_number\": 123,\n", "            \"crashes\": {\n", "                \"crash1\": \"data\",\n", "                \"crash2\": \"data\"\n", "            }\n", "    ]\n", "    It will upload the following files to S3:\n", "    123.json:\n", "    {\n", "        \"crash1\": \"data\",\n", "        \"crash2\": \"data\"\n", "    }\n", "    \"\"\"\n", "    for item in chunk:\n", "        filename = f\"{item[index_col]}.json\"\n", "        s3_client.put_object(\n", "            Body=json.dumps(item[object_col]), Bucket=bucket_name, Key=filename\n", "        )\n", "        pbar.update(1)\n", "\n", "\n", "def upload_data_in_parallel(\n", "    data_list, index_col, object_col, bucket_name, num_workers=256\n", "):\n", "    # Split data_list into chunks using n<PERSON><PERSON>'s array_split\n", "    chunks = np.array_split(data_list, num_workers)\n", "\n", "    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:\n", "        # Initialize tqdm progress bar\n", "        with tqdm(total=len(data_list)) as pbar:\n", "            # Submit each chunk to the executor along with the pbar\n", "            futures = [\n", "                executor.submit(\n", "                    upload_chunk_to_s3, chunk, pbar, index_col, object_col, bucket_name\n", "                )\n", "                for chunk in chunks\n", "            ]\n", "\n", "            # Wait for all tasks to complete and retrieve the results\n", "            for _ in concurrent.futures.as_completed(futures):\n", "                # No need to call future.result() as results are handled within each chunk\n", "                pass\n", "\n", "\n", "# Example usage\n", "upload_data_in_parallel(formatted_data, \"dot_number\", \"crashes\", \"fmcsa-crash-data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "s3 = boto3.resource(\"s3\")\n", "\n", "bucket = s3.Bucket(\"fmcsa-crash-data\")\n", "\n", "count = sum(1 for _ in bucket.objects.all())\n", "print(count)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Ingest FMCSA Inspection Data to DynamoDB\n", "\n", "1. Install required packages\n", "2. Load the [data](https://drive.google.com/drive/u/0/folders/1ARqUsIKIfM-taGHHkTpzVNwof7EJqVc3) in from data/inspection_data\n", "3. Aggregate violation description data by `insp_violation_id`, then group by list.\n", "4. Join grouped violation description with violation table on `insp_violation_id`\n", "5. Join all tables on `inspection_id` to create the final dataframe\n", "6. Aggregate by `dot_number` and group by list to create the final dictionary. It should look like this:\n", "\n", "```\n", "\"legend\":\"table_key.column_name\"\n", "{\n", "   \"inspection.dot_number\":[\n", "      {\n", "         \"inspection.inspection_id\":{\n", "            \"inspection.*\",\n", "            \"carrier.*\",\n", "            \"violations\":[\n", "               \"merged_violations.*\"\n", "            ],\n", "            \"studies\":[\n", "               \"study.*\"\n", "            ],\n", "            \"units\":[\n", "               \"inspection_unit.*\"\n", "            ]\n", "         }\n", "      }\n", "   ]\n", "}\n", "```\n", "7. Upload to S3 since DynamoDB has too small of a size limit.\n", "\n", "This code is very messy and difficult to work with / not very replicable.\n", "\n", "I first tried raw python, it was too slow. Then I tried dask, which worked up until the last step-ish.\n", "I tried pyspark, which was way faster than dask but consumed >750GB of memory since I tried to groupby and agg-list each df before the join. IDEAL SOLUTION: Doing that groupby agg-list at the end would probably work for a pyspark-only pipeline.\n", "\n", "My final solution used dask to do all but the final groupby agg-list, which I did with pyspark by loading in the dask dataframe as a spark dataframe. I lost inspection_id, but it works and this mass ingestion is a one-time thing anyways."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["!pip install  ../../.\n", "!pip install -r ../../common/requirements.txt\n", "!pip install -r requirements.txt\n", "!/opt/conda/bin/python -m pip install --upgrade \"dask[complete]\"\n", "!/opt/conda/bin/python -m pip install pyarrow==14.0.1\n", "!/opt/conda/bin/python -m pip install pyspark==3.5.0\n", "# restart kernel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dask.dataframe as dd"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Load the data in\n", "takes 25 min"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from tqdm import tqdm\n", "import boto3\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common.credentials import Credentials\n", "\n", "CREDENTIALS = Credentials()\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"inspection_data\"\n", ")\n", "\n", "\n", "import pandas as pd\n", "import os\n", "\n", "\n", "def read_inspection_data(file_path, encoding=\"ISO-8859-1\"):\n", "    \"\"\"\n", "    Utility function to read a CSV file into a DataFrame with given encoding, handling parsing errors.\n", "\n", "    :param file_path: Complete file path to the CSV file.\n", "    :param encoding: The encoding used to read the CSV file.\n", "    :return: DataFrame with the loaded data.\n", "    \"\"\"\n", "    # Attempt to read the CSV file\n", "    return pd.read_csv(\n", "        file_path,\n", "        sep=\"\\t\",\n", "        engine=\"python\",\n", "        encoding=encoding,\n", "        on_bad_lines=\"skip\",\n", "    )\n", "\n", "\n", "def load_and_combine_inspection_data(start_year, end_year, base_path=PREFIX) -> tuple:\n", "    # Dictionary to hold the dataframes\n", "    # https://www.fmcsa.dot.gov/registration/mcmis-catalog-inspection-file-data-element-definitions#SS\n", "\n", "    # Ignore Insp_part_section{suffix2}, maybe below links related?\n", "    # https://www.fmcsa.dot.gov/sites/fmcsa.dot.gov/files/docs/Violations_that_Can_Be_Found_on_an_Intermodal_Chassis_508CLN.pdf\n", "    # https://www.phind.com/search?cache=zz00rmzawck3m0sajfweouop\n", "\n", "    file_mappings = {\n", "        \"carrier\": \"Insp_Carrier_Pub_\",\n", "        \"inspection_unit\": \"Insp_Unit_Pub_\",\n", "        \"inspection_violation\": \"Insp_Viol_Pub_\",\n", "        \"study\": \"Insp_Study_Pub_\",\n", "        \"violation_description\": \"Insp_Supp_Violation\",\n", "        \"inspection\": \"Insp_Pub_\",\n", "    }\n", "\n", "    dfs = {k: pd.DataFrame() for k in file_mappings}\n", "\n", "    # Loop through each year and load the data into the DataFrames\n", "    for year in range(start_year, end_year + 1):\n", "        suffix1 = (\n", "            f\"0101{year}_1231{year}HDR.txt\"\n", "            if year != 2023\n", "            else \"01012023_10092023HDR.txt\"\n", "        )\n", "        suffix2 = f\"{year}HDR.txt\"\n", "\n", "        # Read and concatenate each DataFrame\n", "        for key, file_prefix in tqdm(file_mappings.items(), total=len(file_mappings)):\n", "            file_name = file_prefix + (\n", "                suffix1 if key != \"violation_description\" else suffix2\n", "            )\n", "            file_path = os.path.join(base_path, file_name)\n", "            current_df = read_inspection_data(file_path)\n", "            current_df.columns = [c.lower() for c in current_df.columns]\n", "            dfs[key] = pd.concat([dfs[key], current_df], ignore_index=True)\n", "\n", "    # Return the DataFrames in the order requested\n", "    return dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use the function to load and combine the data\n", "raw_inspection_dfs = load_and_combine_inspection_data(2019, 2023)\n", "inspection_dfs = raw_inspection_dfs.copy()\n", "# # lower all cols\n", "# crashes_df.columns = [x.lower() for x in crashes_df.columns]\n", "# # 203578 have no docket or dot number\n", "# # 203949 have no DOT number\n", "# # 593096 have no docket number\n", "# crashes_df = crashes_df.dropna(subset=[\"dot_number\"])\n", "import pickle\n", "\n", "pickle.dump(raw_inspection_dfs, open(\"data/raw_inspection_dfs.pkl\", \"wb\"))"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Pickle"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import pickle\n", "\n", "\n", "inspection_dfs = pickle.load(open(\"data/raw_inspection_dfs.pkl\", \"rb\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Write the DataFrames to CSV files, but only the first 1000 rows\n", "# for key, df in inspection_dfs.items():\n", "#     df.head(1000).to_csv(f\"data/{key}.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Format DFs into dot key aggregates"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# table name -> keys\n", "# inspection_df -> inspection_id, dot_number\n", "# carrier_df -> inspection_id\n", "# inspection_unit_df -> inspection_id\n", "# inspection_violation_df -> inspection_id\n", "# study inspection_id\n", "# supllement -> Insp_Violation_ID\n", "# join this fuckers"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["#### Too Slow"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "import time\n", "import typing\n", "import pandas as pd\n", "\n", "\n", "def aggregate_rows(df, group_by, agg_column_name, exclude_columns=None):\n", "    if exclude_columns is None:\n", "        exclude_columns = [group_by]\n", "\n", "    # Drop excluded columns\n", "    df = df.drop(columns=exclude_columns)\n", "\n", "    # Convert DataFrame to dictionary\n", "    df_dict = df.to_dict(\"records\")\n", "\n", "    # Aggregate the dictionaries\n", "    aggregated = (\n", "        df.groupby(group_by)\n", "        .agg({agg_column_name: lambda x: list(df_dict)})\n", "        .reset_index()\n", "    )\n", "\n", "    return aggregated\n", "\n", "\n", "def create_inspection_dataframe(\n", "    inspection_dfs: typing.Dict[str, pd.DataFrame], join_cols: typing.Dict[str, str]\n", "):\n", "    start_time = s = time.perf_counter()\n", "\n", "    # First, handle the special case of inspection_violation joining with violation_description\n", "    violation_combined_df = inspection_dfs[\"inspection_violation\"].merge(\n", "        inspection_dfs[\"violation_description\"],\n", "        left_on=\"insp_violation_id\",\n", "        right_on=\"insp_violation_id\",\n", "        how=\"left\",\n", "    )\n", "    print(\"violation_combined_df\", time.perf_counter() - s)\n", "    s = time.perf_counter()\n", "    # Add the combined dataframe back into inspection_dfs under a new key\n", "    inspection_dfs[\"violation_combined\"] = violation_combined_df\n", "    # Update join_cols to use the new combined dataframe\n", "    join_cols[\"violation_combined\"] = \"inspection_id\"\n", "\n", "    # Ensure inspection_violation and violation_description are not processed again\n", "    join_cols.pop(\"inspection_violation\", None)\n", "    join_cols.pop(\"violation_description\", None)\n", "\n", "    # Start with the base inspection dataframe\n", "    merged_df = inspection_dfs[\"inspection\"].copy()\n", "    print(\"copy\", time.perf_counter() - s)\n", "    s = time.perf_counter()\n", "    # Iterate over each dataframe in inspection_dfs and merge\n", "    for df_key, join_col in join_cols.items():\n", "        print(df_key)\n", "        if df_key != \"inspection\":  # Skip the base dataframe\n", "            df = inspection_dfs[df_key].copy()\n", "            # Aggregate non-unique keys into lists of dictionaries\n", "            agg_col_name = df_key + \"_data\"\n", "            aggregated_data = aggregate_rows(df, join_col, agg_col_name)\n", "            # Merge with the base dataframe\n", "            print(\"agg data\", time.perf_counter() - s)\n", "            s = time.perf_counter()\n", "            merged_df = merged_df.merge(\n", "                aggregated_data, on=join_col, how=\"left\"\n", "            ).rename(columns={\"aggregated_data\": agg_col_name})\n", "            print(\"merge df\", time.perf_counter() - s)\n", "            s = time.perf_counter()\n", "\n", "    # Group by DOT number and aggregate inspections into lists\n", "    grouped_df = (\n", "        merged_df.groupby(\"dot_number\")\n", "        .apply(lambda x: x.to_dict(orient=\"records\"))\n", "        .reset_index()\n", "        .rename(columns={0: \"inspections\"})\n", "    )\n", "\n", "    total_time = time.perf_counter()\n", "    print(\"Total time:\", total_time - start_time)\n", "\n", "    return grouped_df\n", "\n", "\n", "cutoff = 1000\n", "cutoff_dfs = {k: df[:cutoff].copy() for k, df in inspection_dfs.items()}\n", "\n", "\n", "join_cols = {\n", "    \"inspection\": \"inspection_id\",\n", "    \"carrier\": \"inspection_id\",\n", "    \"inspection_unit\": \"inspection_id\",\n", "    \"study\": \"inspection_id\",\n", "    # 'inspection_violation': 'inspection_id',  # Handled separately\n", "    # 'violation_description': 'insp_violation_id'  # Handled separately\n", "}\n", "\n", "# Creating the DataFrame\n", "inspection_dataframe = create_inspection_dataframe(cutoff_dfs, join_cols)\n", "\n", "inspection_dataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["def aggregate_rows(df_details: tuple) -> pd.DataFrame:\n", "    df, group_by, agg_column_name = df_details\n", "    start_time = time.perf_counter()\n", "    aggregated = (\n", "        df.groupby(group_by)\n", "        .apply(lambda x: x.to_dict(orient=\"records\"))\n", "        .reset_index(name=agg_column_name)\n", "    )\n", "    # Ensure the group_by column is in the aggregated data\n", "    aggregated[group_by] = aggregated[group_by].astype(str)\n", "    elapsed_time = time.perf_counter() - start_time\n", "    print(f\"Time to aggregate '{group_by}': {elapsed_time:.2f} seconds\")\n", "    return aggregated\n", "\n", "\n", "def convert_column_to_int(df, column_name):\n", "    if column_name in df.columns:\n", "        df[column_name] = df[column_name].astype(int)\n", "\n", "\n", "def create_inspection_dataframe(\n", "    inspection_dfs: typing.Dict[str, pd.DataFrame], join_cols: typing.Dict[str, str]\n", ") -> pd.DataFrame:\n", "    start_time = time.perf_counter()\n", "\n", "    # Create copies to avoid modifying the original dataframes\n", "    inspection_dfs = {k: v.copy() for k, v in inspection_dfs.items()}\n", "\n", "    # Convert 'inspection_id' and 'insp_violation_id' to string in all dataframes\n", "    for df in inspection_dfs.values():\n", "        convert_column_to_int(df, \"inspection_id\")\n", "        convert_column_to_int(df, \"insp_violation_id\")\n", "\n", "    # Handle the special case of inspection_violation joining with violation_description\n", "    merge_start_time = time.perf_counter()\n", "    violation_combined_df = inspection_dfs[\"inspection_violation\"].merge(\n", "        inspection_dfs[\"violation_description\"], on=\"insp_violation_id\", how=\"left\"\n", "    )\n", "    inspection_dfs[\"violation_combined\"] = violation_combined_df\n", "    join_cols[\"violation_combined\"] = \"inspection_id\"\n", "    join_cols.pop(\"inspection_violation\", None)\n", "    join_cols.pop(\"violation_description\", None)\n", "    merge_elapsed_time = time.perf_counter() - merge_start_time\n", "    print(f\"Time for special case merge: {merge_elapsed_time:.2f} seconds\")\n", "\n", "    # Parallelize the aggregation of rows\n", "    df_details_for_parallel = [\n", "        (inspection_dfs[df_key], join_cols[df_key], df_key + \"_data\")\n", "        for df_key in join_cols\n", "        if df_key != \"inspection\"\n", "    ]\n", "    aggregate_start_time = time.perf_counter()\n", "    with Pool(processes=4) as pool:\n", "        aggregated_data_list = pool.map(aggregate_rows, df_details_for_parallel)\n", "    aggregate_elapsed_time = time.perf_counter() - aggregate_start_time\n", "    print(f\"Total time for parallel aggregation: {aggregate_elapsed_time:.2f} seconds\")\n", "\n", "    # Merge aggregated data with the base dataframe\n", "    merge_start_time = time.perf_counter()\n", "    merged_df = inspection_dfs[\"inspection\"].copy()\n", "    convert_column_to_int(merged_df, \"inspection_id\")\n", "    for aggregated_data in aggregated_data_list:\n", "        convert_column_to_int(aggregated_data, \"inspection_id\")\n", "        merged_df = pd.merge(merged_df, aggregated_data, on=\"inspection_id\", how=\"left\")\n", "    merge_elapsed_time = time.perf_counter() - merge_start_time\n", "    print(f\"Time for merging aggregated data: {merge_elapsed_time:.2f} seconds\")\n", "\n", "    # Group by DOT number and aggregate inspections into lists\n", "    group_start_time = time.perf_counter()\n", "    grouped_df = (\n", "        merged_df.groupby(\"dot_number\")\n", "        .apply(lambda x: x.to_dict(orient=\"records\"))\n", "        .reset_index()\n", "        .rename(columns={0: \"inspections\"})\n", "    )\n", "    group_elapsed_time = time.perf_counter() - group_start_time\n", "    print(f\"Time for grouping by DOT number: {group_elapsed_time:.2f} seconds\")\n", "\n", "    total_time = time.perf_counter() - start_time\n", "    print(\"Total time:\", total_time)\n", "\n", "    return grouped_df\n", "\n", "\n", "cutoff = 10000\n", "cutoff_dfs = {k: df[:cutoff].copy() for k, df in inspection_dfs.items()}\n", "inspection_dataframe = create_inspection_dataframe(cutoff_dfs, join_cols)\n", "\n", "inspection_dataframe"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import dask.dataframe as dd\n", "from tqdm import tqdm\n", "\n", "cutoff = int(1e9)\n", "cutoff_dfs = {k: df[:cutoff].copy() for k, df in inspection_dfs.items()}\n", "\n", "# Define the indices for each DataFrame with lists\n", "column_map = {\n", "    \"inspection\": [\"inspection_id\", \"dot_number\"],\n", "    \"carrier\": [\"inspection_id\"],\n", "    \"inspection_unit\": [\"inspection_id\"],\n", "    \"inspection_violation\": [\"insp_violation_id\", \"inspection_id\"],\n", "    \"study\": [\"inspection_id\"],\n", "    \"violation_description\": [\"insp_violation_id\"],\n", "}\n", "\n", "dask_dfs = {}\n", "\n", "for key in tqdm(cutoff_dfs):\n", "    if key in column_map:\n", "        df = cutoff_dfs[key].copy()\n", "        # Convert all specified columns to int, handling NaN values\n", "        for column in column_map[key]:\n", "            df = df.dropna(subset=[column])\n", "            df[column] = df[column].astype(int)\n", "\n", "        # Set the first column in the list as the index\n", "        df = df.set_index(column_map[key][0])\n", "        df.index.name = column_map[key][0]\n", "\n", "        dask_dfs[key] = dd.from_pandas(df, npartitions=64)\n", "\n", "# Verifying the changes\n", "for k in dask_dfs:\n", "    print(k, dask_dfs[k].shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# s = time.perf_counter()\n", "# merged_violation_df = dd.merge(dask_dfs['inspection_violation'], dask_dfs['violation_description'], left_index=True, right_index=True).compute()\n", "# print('violation merge', time.perf_counter() - s)\n", "\n", "# s = time.perf_counter()\n", "# merged_violation_df = merged_violation_df.reset_index()\n", "# merged_violation_df = merged_violation_df.rename(columns={'index': 'insp_violation_id'})\n", "# merged_violation_df = merged_violation_df.set_index('inspection_id')\n", "# print('set index', time.perf_counter()-s)\n", "\n", "# merged_df = dd.merge(dask_dfs['inspection'], merged_violation_df, left_index=True, right_index=True, how='outer', suffixes=('inspection','violation'))\n", "# merged_df = dd.merge(merged_df, dask_dfs['study'], left_index=True, right_index=True, how='outer')\n", "# merged_df = dd.merge(merged_df, dask_dfs['carrier'], left_index=True, right_index=True, how='outer')\n", "# merged_df = dd.merge(merged_df, dask_dfs['inspection_unit'], left_index=True, right_index=True, how='outer').compute()\n", "\n", "# # merged_df = merged_df.groupby(['inspection_id']).agg(dedup_agg).compute()\n", "\n", "# merged_violation_df\n", "# print('aggregation', time.perf_counter() - s)\n", "\n", "# merged_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from tqdm import tqdm\n", "\n", "\n", "def save_df_in_chunks(df, path, filename_prefix, chunk_size=100000):\n", "    \"\"\"\n", "    Splits a DataFrame into chunks and saves each chunk as a pickle file with progress tracking.\n", "\n", "    :param df: DataFrame to be saved.\n", "    :param path: Directory path where pickle files will be saved.\n", "    :param filename_prefix: Prefix for the filename of each chunk.\n", "    :param chunk_size: Number of rows in each chunk.\n", "    \"\"\"\n", "    # Create directory for pickle files if it doesn't exist\n", "    os.makedirs(path, exist_ok=True)\n", "\n", "    # Calculate the number of chunks\n", "    num_chunks = len(range(0, df.shape[0], chunk_size))\n", "\n", "    # Split DataFrame into chunks and save each chunk\n", "    for i in tqdm(range(num_chunks), desc=\"Saving chunks\", total=num_chunks):\n", "        start = i * chunk_size\n", "        end = start + chunk_size\n", "        chunk = df[start:end]\n", "        chunk.to_pickle(os.path.join(path, f\"{filename_prefix}_chunk_{i}.pkl\"))\n", "\n", "\n", "def load_df_from_chunks(path, filename_prefix):\n", "    \"\"\"\n", "    Loads DataFrame chunks from pickle files, concatenates them into a single DataFrame, with progress tracking.\n", "\n", "    :param path: Directory path where pickle files are stored.\n", "    :param filename_prefix: Prefix for the filename of each chunk to be loaded.\n", "    :return: Concatenated DataFrame.\n", "    \"\"\"\n", "    # List all pickle files with the specified prefix in the directory\n", "    files = sorted(\n", "        [\n", "            f\n", "            for f in os.listdir(path)\n", "            if f.startswith(filename_prefix + \"_chunk_\") and f.endswith(\".pkl\")\n", "        ]\n", "    )\n", "\n", "    # Initialize an empty list to store DataFrame chunks\n", "    df_chunks = []\n", "\n", "    # Load each chunk with progress tracking\n", "    for f in tqdm(files, desc=\"Loading chunks\", total=len(files)):\n", "        chunk = pd.read_pickle(os.path.join(path, f))\n", "        df_chunks.append(chunk)\n", "\n", "    # Concatenate all chunks\n", "    return pd.concat(df_chunks, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import dask.dataframe as dd\n", "import time\n", "import pandas as pd\n", "\n", "\n", "def merge_violation_dataframes(inspection_violation_df, violation_description_df):\n", "    start_time = time.perf_counter()\n", "    merged_violation_df = dd.merge(\n", "        inspection_violation_df,\n", "        violation_description_df,\n", "        left_index=True,\n", "        right_index=True,\n", "    ).compute()\n", "    print(\"Violation merge:\", time.perf_counter() - start_time)\n", "\n", "    # Use the new function to prepare the DataFrame for Dask\n", "    merged_violation_df = change_df_index(\n", "        merged_violation_df, \"insp_violation_id\", \"inspection_id\"\n", "    )\n", "\n", "    return merged_violation_df\n", "\n", "\n", "def change_df_index(df, old_index, new_index, npartitions=64):\n", "    start_time = time.perf_counter()\n", "\n", "    df = df.reset_index().rename(columns={\"index\": old_index}).set_index(new_index)\n", "    dask_df = dd.from_pandas(df, npartitions=npartitions)\n", "\n", "    print(\"Prepare DataFrame for Dask:\", time.perf_counter() - start_time)\n", "    return dask_df\n", "\n", "\n", "def convert_df_to_dict(df, orient=\"index\"):\n", "    start_time = time.perf_counter()\n", "    df_grouped = df.groupby([\"inspection_id\"]).agg(list).compute()\n", "    print(\"Aggregate to list:\", time.perf_counter() - start_time)\n", "\n", "    start_time = time.perf_counter()\n", "    result_dict = df_grouped.to_dict(orient=orient)\n", "    print(\"Convert to dict:\", time.perf_counter() - start_time)\n", "\n", "    return result_dict\n", "\n", "\n", "def apply_mapping_process(base_df, mapping_df, key_name):\n", "    if isinstance(base_df, pd.DataFrame):\n", "        base_df = dd.from_pandas(base_df, npartitions=64)\n", "    data_dict = convert_df_to_dict(mapping_df)\n", "\n", "    start_time = time.perf_counter()\n", "\n", "    def map_data_to_inspection(df, mapping_dict):\n", "        df[key_name] = df.index.map(mapping_dict)\n", "        return df\n", "\n", "    final_df = base_df.map_partitions(map_data_to_inspection, data_dict).compute()\n", "    print(\"Map partitions:\", time.perf_counter() - start_time)\n", "\n", "    return final_df\n", "\n", "\n", "# Example usage\n", "start_time = time.perf_counter()\n", "# base_df = dd.merge(dask_dfs['inspection'], dask_dfs['carrier'], left_index=True, right_index=True, how='left')\n", "# merged_violation_df = merge_violation_dataframes(dask_dfs['inspection_violation'], dask_dfs['violation_description'])\n", "# print('violations')\n", "# base_df = apply_mapping_process(base_df, merged_violation_df, 'violations')\n", "# pickle.dump(base_df, open('pickles/base_violations_df.pkl', 'wb'))\n", "print(\"study\")\n", "base_df = pickle.load(open(\"pickles/base_violations_df.pkl\", \"rb\"))\n", "base_df = apply_mapping_process(base_df, dask_dfs[\"study\"], \"study\")\n", "# pickle.dump(base_df, open('pickles/base_study_df.pkl', 'wb'))\n", "print(\"units\")\n", "# base_df = pickle.load(open('pickles/base_study_df.pkl', 'rb'))\n", "base_df = apply_mapping_process(base_df, dask_dfs[\"inspection_unit\"], \"units\")\n", "total_time_seconds = time.perf_counter() - start_time\n", "hours = int(total_time_seconds // 3600)\n", "minutes = int((total_time_seconds % 3600) // 60)\n", "seconds = total_time_seconds % 60\n", "\n", "print(f\"Total time: {hours} hours, {minutes} minutes, {seconds:.2f} seconds\")\n", "\n", "# Saving with a specified filename prefix\n", "save_df_in_chunks(base_df, \"pickles\", \"base_df\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10k -> 17, 57\n", "# 100k -> 128,"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["# Loading with the specified filename prefix\n", "base_df = load_df_from_chunks(\"pickles\", \"base_df\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["base_df.to_csv(\"data/fully_joined_inspection_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["# i have a couple issues\n", "# 1. converted final df to dict 49.88031911000144. this is for 10k rows. for 20 mil rows it would take >27 hrs.\n", "# 2. final_dict[65532570] outputs\n", "# 'docket_number': '[123310]',\n", "#  'insp_interstate': \"['Y']\",\n", "#  'insp_carrier_state_id': '[<NA>]',\n", "#  'violations': '[<NA>]',\n", "#  'study': '[<NA>]',\n", "#  'units': '[\\'{\\\\\\'insp_unit_id\\\\\\': \\\\\\'[*********, *********]\\\\\\', \\\\\\'insp_unit_type_id\\\\\\': \\\\\\'[11, 9]\\\\\\', \\\\\\'insp_unit_number\\\\\\': \\\\\\'[1, 2]\\\\\\', \\\\\\'insp_unit_make\\\\\\': \"[\\\\\\'FRHT\\\\\\', \\\\\\'UTIL\\\\\\']\", \\\\\\'insp_unit_company\\\\\\': \"[\\\\\\'3534\\\\\\', \\\\\\'3613\\\\\\']\", \\\\\\'insp_unit_license\\\\\\': \"[\\\\\\'AK4574\\\\\\', \\\\\\'ZK7843\\\\\\']\", \\\\\\'insp_unit_license_state\\\\\\': \"[\\\\\\'ID\\\\\\', \\\\\\'ID\\\\\\']\", \\\\\\'insp_unit_vehicle_id_number\\\\\\': \"[\\\\\\'1FUJHHDR0KLKH4700\\\\\\', \\\\\\'1UYVS3535HU718802\\\\\\']\", \\\\\\'insp_unit_decal\\\\\\': \"[\\\\\\'N\\\\\\', \\\\\\'N\\\\\\']\", \\\\\\'insp_unit_decal_number\\\\\\': \\\\\\'[<NA>, <NA>]\\\\\\'}\\']'}\n", "\n", "\n", "# i want it to actually have a dictionary, not some string that is difficult to decode\n", "def change_df_index(df, old_index, new_index, npartitions=512):\n", "    start_time = time.perf_counter()\n", "\n", "    df = df.reset_index().rename(columns={\"index\": old_index}).set_index(new_index)\n", "    return df\n", "\n", "\n", "def convert_df_to_dict(df, orient=\"index\"):\n", "    start_time = time.perf_counter()\n", "\n", "    # Grouping and aggregating using Pandas\n", "    df_grouped = df.groupby(\"dot_number\").agg(list)\n", "    print(\"Aggregate to list:\", time.perf_counter() - start_time)\n", "\n", "    start_time = time.perf_counter()\n", "\n", "    # Converting to dictionary\n", "    result_dict = df_grouped.to_dict(orient=orient)\n", "    print(\"Convert to dict:\", time.perf_counter() - start_time)\n", "\n", "    return result_dict\n", "\n", "\n", "from dask.diagnostics import ProgressBar\n", "\n", "start_time = time.perf_counter()\n", "changed_base_df = change_df_index(base_df, \"inspection_id\", \"dot_number\")\n", "print(\"changed base df index\", time.perf_counter() - start_time)\n", "start_time = time.perf_counter()\n", "final_dict = convert_df_to_dict(changed_base_df)\n", "\n", "total_time_seconds = time.perf_counter() - start_time\n", "hours = int(total_time_seconds // 3600)\n", "minutes = int((total_time_seconds % 3600) // 60)\n", "seconds = total_time_seconds % 60\n", "\n", "print(f\"Total time: {hours} hours, {minutes} minutes, {seconds:.2f} seconds\")\n", "\n", "len(final_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import col, collect_list\n", "import time\n", "\n", "# Initialize Spark Session\n", "spark = SparkSession.builder.appName(\"DataFrameAggregation\").getOrCreate()\n", "\n", "# Assuming 'base_df' is your initial DataFrame\n", "\n", "\n", "def change_df_index_spark(df, old_index, new_index):\n", "    start_time = time.perf_counter()\n", "\n", "    # Renaming and setting index in PySpark\n", "    df = df.withColumnRenamed(old_index, new_index)\n", "\n", "    print(\"Prepare DataFrame for Spark:\", time.perf_counter() - start_time)\n", "    return df\n", "\n", "\n", "def convert_df_to_dict_spark(df, group_by_col):\n", "    start_time = time.perf_counter()\n", "\n", "    # Grouping and aggregating\n", "    df_grouped = df.groupBy(group_by_col).agg(collect_list(\"your_columns_here\"))\n", "\n", "    print(\"Aggregate to list:\", time.perf_counter() - start_time)\n", "\n", "    start_time = time.perf_counter()\n", "\n", "    # Converting to dictionary\n", "    result_dict = df_grouped.rdd.map(lambda row: (row[0], row[1])).collectAsMap()\n", "\n", "    print(\"Convert to dict:\", time.perf_counter() - start_time)\n", "\n", "    return result_dict\n", "\n", "\n", "# Example usage\n", "changed_base_df = change_df_index_spark(base_df, \"inspection_id\", \"dot_number\")\n", "final_dict = convert_df_to_dict_spark(changed_base_df, \"dot_number\")\n", "\n", "# Print elapsed time and dictionary length\n", "total_time_seconds = time.perf_counter() - start_time\n", "hours = int(total_time_seconds // 3600)\n", "minutes = int((total_time_seconds % 3600) // 60)\n", "seconds = total_time_seconds % 60\n", "print(f\"Total time: {hours} hours, {minutes} minutes, {seconds:.2f} seconds\")\n", "print(len(final_dict))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import pickle\n", "from tqdm import tqdm\n", "\n", "import os\n", "import pickle\n", "from tqdm import tqdm\n", "\n", "\n", "def save_dict_in_chunks(dict_data, path, filename_prefix, chunk_size=1000000):\n", "    \"\"\"\n", "    Saves a dictionary in chunks.\n", "\n", "    :param dict_data: Dictionary to be saved in chunks.\n", "    :param path: Directory path where pickle files will be saved.\n", "    :param filename_prefix: Prefix for the filename of each chunk.\n", "    :param chunk_size: Number of keys in each chunk.\n", "    \"\"\"\n", "    # Create directory for pickle files if it doesn't exist\n", "    os.makedirs(path, exist_ok=True)\n", "\n", "    # Get all keys and split into chunks\n", "    all_keys = list(dict_data.keys())\n", "    key_chunks = [\n", "        all_keys[i : i + chunk_size] for i in range(0, len(all_keys), chunk_size)\n", "    ]\n", "\n", "    # Save each chunk of the dictionary\n", "    for i, keys in enumerate(\n", "        tqdm(key_chunks, desc=\"Saving dict chunks\", total=len(key_chunks))\n", "    ):\n", "        dict_chunk = {k: dict_data[k] for k in keys if k in dict_data}\n", "        with open(os.path.join(path, f\"{filename_prefix}_chunk_{i}.pkl\"), \"wb\") as f:\n", "            pickle.dump(dict_chunk, f)\n", "\n", "\n", "def load_dict_from_chunks(path, filename_prefix):\n", "    \"\"\"\n", "    Loads dictionary chunks from pickle files and merges them into a single dictionary.\n", "\n", "    :param path: Directory path where pickle files are stored.\n", "    :param filename_prefix: Prefix for the filename of each chunk to be loaded.\n", "    :return: Merged dictionary.\n", "    \"\"\"\n", "    # List all pickle files with the specified prefix in the directory\n", "    files = sorted(\n", "        [\n", "            f\n", "            for f in os.listdir(path)\n", "            if f.startswith(filename_prefix + \"_chunk_\") and f.endswith(\".pkl\")\n", "        ]\n", "    )\n", "\n", "    # Initialize an empty dictionary to store merged data\n", "    final_dict = {}\n", "\n", "    # Load each chunk and merge into final_dict\n", "    for f in tqdm(files, desc=\"Loading dict chunks\", total=len(files)):\n", "        with open(os.path.join(path, f), \"rb\") as file:\n", "            dict_chunk = pickle.load(file)\n", "            final_dict.update(dict_chunk)\n", "\n", "    return final_dict\n", "\n", "\n", "# Saving the dictionary in chunks\n", "save_dict_in_chunks(final_dict, \"pickles\", \"final_dict\")\n", "\n", "# Loading the dictionary from chunks\n", "final_dict1 = load_dict_from_chunks(\"pickles\", \"final_dict\")\n", "len(final_dict1)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["##### Old"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# I want dask dfs all the way down... setting index ruins that.\n", "dask_dfs[\"inspection\"].reset_index().rename(\n", "    columns={\"index\": \"inspection_id\"}\n", ").set_index(dask_dfs.dot_number).compute()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import time\n", "\n", "\n", "s = time.perf_counter()\n", "\n", "\n", "grouped_df = dask_dfs[\"inspection_unit\"].groupby([\"inspection_id\"]).agg(list).compute()\n", "\n", "\n", "print(time.perf_counter() - s)\n", "\n", "\n", "grouped_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import dask.dataframe as dd\n", "\n", "s = time.perf_counter()\n", "# Convert to dictionary\n", "s = time.perf_counter()\n", "merged_violation_df = dd.merge(\n", "    dask_dfs[\"inspection_violation\"],\n", "    dask_dfs[\"violation_description\"],\n", "    left_index=True,\n", "    right_index=True,\n", ").compute()\n", "print(\"violation merge\", time.perf_counter() - s)\n", "\n", "s = time.perf_counter()\n", "merged_violation_df = merged_violation_df.reset_index()\n", "merged_violation_df = merged_violation_df.rename(columns={\"index\": \"insp_violation_id\"})\n", "merged_violation_df = merged_violation_df.set_index(\"inspection_id\")\n", "merged_violation_df = dd.from_pandas(merged_violation_df, npartitions=16)\n", "print(\"set index\", time.perf_counter() - s)\n", "merged_violation_df = merged_violation_df.groupby([\"inspection_id\"]).agg(list).compute()\n", "print(\"merged_violation_df and aggregate\", time.perf_counter() - s)\n", "s = time.perf_counter()\n", "violations_dict = merged_violation_df.to_dict(orient=\"index\")\n", "print(\"violations dict\", time.perf_counter() - s)\n", "s = time.perf_counter()\n", "\n", "\n", "# Define a function to map the violations_dict to each partition\n", "def map_violations_to_inspection(df, violations_dict):\n", "    df[\"violation\"] = df.index.map(violations_dict)\n", "    return df\n", "\n", "\n", "# Apply this function to each partition of dask_dfs['inspection']\n", "inspection_with_violations = dask_dfs[\"inspection\"].map_partitions(\n", "    map_violations_to_inspection, violations_dict\n", ")\n", "print(\"violations dict\", time.perf_counter() - s)\n", "s = time.perf_counter()\n", "\n", "# Compute the final DataFrame\n", "final_df = inspection_with_violations.compute()\n", "print(\"map partitions\", time.perf_counter() - s)\n", "s = time.perf_counter()\n", "\n", "final_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["inspection_dfs.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["dedup_agg = dd.Aggregation(\n", "    name=\"dedup\",\n", "    chunk=lambda s: s.apply(\n", "        lambda x: [i for i in set(x) if not pd.isna(i)]\n", "    ),  # no set, preserve order\n", "    agg=lambda s0: s0.obj.groupby(level=list(range(s0.obj.index.nlevels))).sum(),\n", "    finalize=lambda s1: s1.apply(lambda final: list(sorted(final))),  # set?\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import time\n", "\n", "\n", "s = time.perf_counter()\n", "\n", "\n", "grouped_df = dask_dfs[\"inspection_unit\"].groupby([\"inspection_id\"]).agg(list).compute()\n", "\n", "\n", "print(time.perf_counter() - s)\n", "\n", "\n", "grouped_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# dedup is 212. lsit is 398!?"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["cols = [\"inspection_id\"]  # 'insp_violation_id' is 1\n", "for k in inspection_dfs:\n", "    print(k.upper())\n", "    df = inspection_dfs[k]\n", "    for col in cols:\n", "        if col in df.columns:\n", "            print(col, max(df[col].value_counts()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SLOWER, but returns list of dicts instead of dict of lists.\n", "# TAKES IN DASK_DFS, RETURNS DF merged_violation_df in merged_violation_df and aggregate step\n", "def transform_group(group):\n", "    return group.apply(lambda x: x.to_dict(), axis=1).tolist()\n", "\n", "\n", "# Merge and group\n", "merged_violation_df = dd.merge(\n", "    dask_dfs[\"inspection_violation\"],\n", "    dask_dfs[\"violation_description\"],\n", "    left_index=True,\n", "    right_index=True,\n", ").compute()\n", "print(\"violation merge\", time.perf_counter() - s)\n", "\n", "s = time.perf_counter()\n", "merged_violation_df = merged_violation_df.reset_index()\n", "merged_violation_df = merged_violation_df.rename(columns={\"index\": \"insp_violation_id\"})\n", "merged_violation_df = merged_violation_df.set_index(\"inspection_id\")\n", "print(\"set index\", time.perf_counter() - s)\n", "merged_violation_df = dd.from_pandas(merged_violation_df, npartitions=16)\n", "\n", "grouped = merged_violation_df.groupby(\"inspection_id\").apply(transform_group).compute()\n", "\n", "grouped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dedup inspection_unit\n", "# 10k 2.9 sec\n", "# 100k 25 sec\n", "# 1 mil 240 sec\n", "# 10 mil 40 min\n", "# 500k: 25 min, below\n", "# Time taken with 1 partitions: 1032.5814406530008 seconds\n", "# Time taken with 2 partitions: 1052.7936718290002 seconds\n", "# Time taken with 4 partitions: 1056.8188923510024 seconds\n", "# Time taken with 8 partitions: 1067.1911752779997 seconds\n", "# Time taken with 16 partitions: 1076.8864076749996 seconds\n", "# Time taken with 32 partitions: 1078.7549318500023 seconds\n", "# Time taken with 64 partitions: 1087.4600847000002 seconds\n", "# Time taken with 128 partitions: 1102.100120097999 seconds\n", "# Time taken with 256 partitions: 1101.3128396950015 seconds"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# carrier/inspection_unit merge\n", "# 100k  1.82 sec\n", "# 1 mil 2.7 sec\n", "# 10 mil is 15 sec"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 100k, inspection unit grouping + carrier grouping -> merge 63.6 seconds (64 partitions),\n", "# 8 partitions -> 61.377 seconds\n", "# merge -> grouping 46.25 seconds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# table name -> keys\n", "# inspection_df -> inspection_id, dot_number\n", "# carrier_df -> inspection_id\n", "# inspection_unit_df -> inspection_id\n", "# inspection_violation_df -> inspection_id\n", "# study inspection_id\n", "# supllement -> Insp_Violation_ID\n", "# join this fuckers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["merged_df.loc[65526369].to_dict()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Older"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import dask.dataframe as dd\n", "import pandas as pd\n", "import typing\n", "from multiprocessing import Pool\n", "import time\n", "\n", "\n", "def aggregate_rows_dask(df, group_by, agg_column_name):\n", "    start_time = time.perf_counter()\n", "\n", "    def custom_agg(x):\n", "        return [x.drop(columns=[group_by]).to_dict(orient=\"records\")]\n", "\n", "    aggregated = (\n", "        df.groupby(group_by)\n", "        .apply(custom_agg, meta={agg_column_name: \"object\"})\n", "        .reset_index()\n", "    )\n", "    aggregated = aggregated.compute()  # Compute the result here\n", "\n", "    elapsed_time = time.perf_counter() - start_time\n", "    print(f\"Time to aggregate '{group_by}' with Das<PERSON>: {elapsed_time:.2f} seconds\")\n", "    return aggregated\n", "\n", "\n", "def create_inspection_dataframe(inspection_dfs, join_cols):\n", "    start_time = time.perf_counter()\n", "\n", "    # Convert DataFrames to Dask DataFrames\n", "    inspection_dfs = {\n", "        k: dd.from_pandas(v, npartitions=64) for k, v in inspection_dfs.items()\n", "    }\n", "\n", "    # Convert 'inspection_id' and 'insp_violation_id' to int\n", "    for df in inspection_dfs.values():\n", "        convert_column_to_int(df, \"inspection_id\")\n", "        convert_column_to_int(df, \"insp_violation_id\")\n", "\n", "    # Special case merge\n", "    merge_start_time = time.perf_counter()\n", "    inspection_dfs[\"violation_combined\"] = inspection_dfs[\"inspection_violation\"].merge(\n", "        inspection_dfs[\"violation_description\"], on=\"insp_violation_id\", how=\"left\"\n", "    )\n", "    inspection_dfs[\"violation_combined\"] = inspection_dfs[\n", "        \"violation_combined\"\n", "    ].compute()  # Compute the result here\n", "    merge_elapsed_time = time.perf_counter() - merge_start_time\n", "    print(f\"Time for special case merge: {merge_elapsed_time:.2f} seconds\")\n", "\n", "    # Parallelize the aggregation of rows using Dask\n", "    aggregate_start_time = time.perf_counter()\n", "    aggregated_data_list = [\n", "        aggregate_rows_dask(inspection_dfs[df_key], join_cols[df_key], df_key + \"_data\")\n", "        for df_key in join_cols\n", "        if df_key != \"inspection\"\n", "    ]\n", "    aggregate_elapsed_time = time.perf_counter() - aggregate_start_time\n", "    print(f\"Total time for parallel aggregation: {aggregate_elapsed_time:.2f} seconds\")\n", "\n", "    # Merge aggregated data with the base dataframe\n", "    merge_start_time = time.perf_counter()\n", "    merged_df = inspection_dfs[\"inspection\"].compute().copy()\n", "    for aggregated_data in aggregated_data_list:\n", "        merged_df = merged_df.merge(aggregated_data, on=\"inspection_id\", how=\"left\")\n", "    merge_elapsed_time = time.perf_counter() - merge_start_time\n", "    print(f\"Time for merging aggregated data: {merge_elapsed_time:.2f} seconds\")\n", "\n", "    # Group by DOT number and aggregate inspections into lists\n", "    group_start_time = time.perf_counter()\n", "    merged_df = dd.from_pandas(merged_df, npartitions=4)\n", "    merged_df = merged_df.set_index(\"dot_number\")\n", "    grouped_df = (\n", "        merged_df.groupby(\"dot_number\")\n", "        .apply(lambda x: x.to_dict(orient=\"records\"), meta=(\"x\", \"object\"))\n", "        .reset_index()\n", "    )\n", "    result_df = grouped_df.compute()  # Compute the result here\n", "    group_elapsed_time = time.perf_counter() - group_start_time\n", "    print(f\"Time for grouping by DOT number: {group_elapsed_time:.2f} seconds\")\n", "\n", "    total_time = time.perf_counter() - start_time\n", "    print(\"Total time:\", total_time)\n", "\n", "    return result_df\n", "\n", "\n", "cutoff = 100000\n", "cutoff_dfs = {k: df[:cutoff].copy() for k, df in inspection_dfs.items()}\n", "\n", "inspection_dataframe = create_inspection_dataframe(cutoff_dfs, join_cols)\n", "\n", "inspection_dataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# Assuming inspection_dataframe is your final merged DataFrame\n", "\n", "# Find the first entry with non-empty violation data\n", "example_with_violation = None\n", "for index, row in inspection_dataframe.iterrows():\n", "    # Check if violation data is present and not empty\n", "    if row.get(\"violation_combined_data\") and len(row[\"violation_combined_data\"]) > 0:\n", "        example_with_violation = row\n", "        break\n", "\n", "# Display the found example\n", "if example_with_violation is not None:\n", "    print(\"Example with violation data found:\")\n", "    print(example_with_violation)\n", "else:\n", "    print(\"No examples with violation data were found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["for k in inspection_dict:\n", "    if len(inspection_dict[k]) > 10:\n", "        print(inspection_dict[k])\n", "        break"]}], "metadata": {"availableInstances": [{"_defaultOrder": 0, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.t3.medium", "vcpuNum": 2}, {"_defaultOrder": 1, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.t3.large", "vcpuNum": 2}, {"_defaultOrder": 2, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.t3.xlarge", "vcpuNum": 4}, {"_defaultOrder": 3, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.t3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 4, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5.large", "vcpuNum": 2}, {"_defaultOrder": 5, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 6, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 7, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 8, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 9, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 10, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 11, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 12, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5d.large", "vcpuNum": 2}, {"_defaultOrder": 13, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5d.xlarge", "vcpuNum": 4}, {"_defaultOrder": 14, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5d.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 15, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5d.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 16, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5d.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 17, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5d.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 18, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5d.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 19, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 20, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": true, "memoryGiB": 0, "name": "ml.geospatial.interactive", "supportedImageNames": ["sagemaker-geospatial-v1-0"], "vcpuNum": 0}, {"_defaultOrder": 21, "_isFastLaunch": true, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.c5.large", "vcpuNum": 2}, {"_defaultOrder": 22, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.c5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 23, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.c5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 24, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.c5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 25, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 72, "name": "ml.c5.9xlarge", "vcpuNum": 36}, {"_defaultOrder": 26, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 96, "name": "ml.c5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 27, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 144, "name": "ml.c5.18xlarge", "vcpuNum": 72}, {"_defaultOrder": 28, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.c5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 29, "_isFastLaunch": true, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g4dn.xlarge", "vcpuNum": 4}, {"_defaultOrder": 30, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g4dn.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 31, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g4dn.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 32, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g4dn.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 33, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g4dn.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 34, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g4dn.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 35, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 61, "name": "ml.p3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 36, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 244, "name": "ml.p3.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 37, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 488, "name": "ml.p3.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 38, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.p3dn.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 39, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.r5.large", "vcpuNum": 2}, {"_defaultOrder": 40, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.r5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 41, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.r5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 42, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.r5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 43, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.r5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 44, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.r5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 45, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.r5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 46, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.r5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 47, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 48, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 49, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 50, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 51, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 52, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 53, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.g5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 54, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.g5.48xlarge", "vcpuNum": 192}, {"_defaultOrder": 55, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 56, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4de.24xlarge", "vcpuNum": 96}], "interpreter": {"hash": "e3bab1b93d258dbfa4dccbeb3f02abbdbb0f314bf46cf351dcc60ff2a957c79e"}, "kernelspec": {"display_name": "Python 3.11.7 ('vs-notebook')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}