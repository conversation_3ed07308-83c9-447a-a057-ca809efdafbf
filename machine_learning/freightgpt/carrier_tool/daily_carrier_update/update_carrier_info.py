import argparse
import logging
import os
import traceback

from numpy import nan

from common import dynamodb_utils
from common import init_main

# import requests

# from common import ecr_job_util

parser = argparse.ArgumentParser(
    description="Ingest a carrier data file from the data directory"
)

parser.add_argument(
    "--env",
    type=str,
    choices=["dev", "prod"],
    default="dev",
    help="Environment to run the script in",
)

parser.add_argument(
    "--file",
    type=str,
    default="Carrier_-_All_With_History_20240517.csv",
    help="Name of file to ingest",
)

args = parser.parse_args()

os.environ["ENV"] = args.env.upper()

IS_DEV = os.getenv("ENV", "DEV") == "DEV"
TABLE_NAME = "CarrierFMCSADev" if IS_DEV else "CarrierFMCSA"

FILE_NAME = args.file
PROJECT_ROOT = init_main.initialize()

import pandas as pd

FMCSA_HEADERS = [
    "docket_number",
    "dot_number",
    "mx_type",
    "rfc_number",
    "common_stat",
    "contract_stat",
    "broker_stat",
    "common_app_pend",
    "contract_app_pend",
    "broker_app_pend",
    "common_rev_pend",
    "contract_rev_pend",
    "broker_rev_pend",
    "property_chk",
    "passenger_chk",
    "hhg_chk",
    "private_auth_chk",
    "enterprise_chk",
    "min_cov_amount",
    "cargo_req",
    "bond_req",
    "bipd_file",
    "cargo_file",
    "bond_file",
    "undeliverable_mail",
    "dba_name",
    "legal_name",
    "bus_street_po",
    "bus_colonia",
    "bus_city",
    "bus_state_code",
    "bus_ctry_code",
    "bus_zip_code",
    "bus_telno",
    "bus_fax",
    "mail_street_po",
    "mail_colonia",
    "mail_city",
    "mail_state_code",
    "mail_ctry_code",
    "mail_zip_code",
    "mail_telno",
    "mail_fax",
]


def clean_df(df):
    # Modifies certain columns in place to prepare for insertion into DynamoDB
    logging.info(f"Cleaning dataframe: {df.head()}")
    # Remove trailing .0 from phone numbers
    df[["bus_telno", "mail_telno", "bus_fax", "mail_fax"]] = df[
        ["bus_telno", "mail_telno", "bus_fax", "mail_fax"]
    ].replace(r"\.0$", "", regex=True)

    # Remap values from Y/N to X/null
    columns_to_remap = [
        "common_app_pend",
        "contract_app_pend",
        "broker_app_pend",
        "common_rev_pend",
        "contract_rev_pend",
        "broker_rev_pend",
        "property_chk",
        "passenger_chk",
        "hhg_chk",
        "private_auth_chk",
        "enterprise_chk",
        "cargo_req",
        "bond_req",
        "cargo_file",
        "bond_file",
    ]
    for col in columns_to_remap:
        df[col] = df[col].map({"Y": "X", "N": None})

    # Map header names to columns that already exist in the dynamo table
    df.rename(
        columns={
            "property_chk": "genfreight",  # This value needs to be remapped from Y/N to X/null
            "passenger_chk": "passengers",  # This value needs to be remapped from Y/N to X/null
            "hhg_chk": "household",  # This value needs to be remapped from Y/N to X/null
            "undeliverable_mail": "undeliv_phy",  # This value needs to be remapped from Y/N to null/U
            "dba_name": "name_dba",
            "legal_name": "name",
            "bus_street_po": "phy_str",
            "bus_city": "phy_city",
            "bus_state_code": "phy_st",
            "bus_ctry_code": "phy_natn",
            "bus_zip_code": "phy_zip",
            "bus_telno": "tel_num",  # Can be duplicate in mail_telno
            "bus_fax": "fax_num",  # Can be duplicate in mail_fax
            "mail_street_po": "mai_str",
            "mail_city": "mai_city",
            "mail_state_code": "mai_st",
            "mail_ctry_code": "mai_natn",
            "mail_zip_code": "mai_zip",
            "mail_telno": "cell_num",  # Can be duplicate in bus_telno
            "mail_fax": "fax_num_2",  # Can be duplicate in bus_fax
        },
        inplace=True,
    )

    # Remap undeliverable_mail
    df["undeliv_phy"] = df["undeliv_phy"].map({"Y": None, "N": "U"})

    # Adding a temporary sorting helper column
    df["sort_helper"] = df["docket_number"].apply(lambda x: x.startswith("MC"))
    # Sorting the DataFrame based on the helper column, placing True (MC prefix) first
    df = df.sort_values(by="sort_helper", ascending=False).drop("sort_helper", axis=1)
    # Drop all rows in which dot_number is empty or 0
    df = df[~df["dot_number"].astype(str).str.strip().isin(["0"])]
    # Drop duplicate rows that don't have an "MC" prefix in docket_number
    df.drop_duplicates(subset=["dot_number"], keep="first", inplace=True)
    # Convert NaN values to None
    df = df.replace({nan: None})

    return df


def main():
    # JOB_NAME = "Daily Carrier Update"
    # (
    #     job_start_time,
    #     job_start_timestr_central,
    #     mail_api,
    #     log_filename,
    # ) = ecr_job_util.set_up(JOB_NAME, set_up_logging_flag=True)
    # mail_report_attachments = [log_filename]
    file_dir = os.path.join(
        PROJECT_ROOT,
        "machine_learning",
        "freightgpt",
        "carrier_tool",
        "daily_carrier_update",
        "data",
    )

    # html_url = "https://data.transportation.gov/Trucking-and-Motorcoaches/Carrier/6qg9-x4f8/about_data"
    # html_extractor = download_daily_diff

    # As the button on the page uses a relative url for the download, this absolute url should always download that day's data.
    # I will leave the rest of the code as a backup in case this changes, though if it does the code is not likely to continue working.
    # download_url = "https://data.transportation.gov/download/6eyk-hxee/text%2Fplain"

    try:
        # HEADERS = {
        #     "User-Agent": "Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
        # }

        # r = requests.get(download_url, headers=HEADERS)
        # now_str = pd.Timestamp.now().strftime("%Y%m%d_%H")
        # html_filename = os.path.join(file_dir, f"carrier_data_{now_str}.txt")
        # with open(html_filename, "w", encoding="utf-8") as f:
        #     f.write(r.text)

        html_filename = os.path.join(file_dir, FILE_NAME)

        daily_df = pd.read_csv(
            html_filename,
            dtype={
                "bus_telno": str,
                "mail_telno": str,
                "bus_fax": str,
                "mail_fax": str,
                "bus_colonia": str,
                "mail_colonia": str,
            },
            lineterminator="\n",
            names=FMCSA_HEADERS,
        )
        print("loaded data: " + str(daily_df.head()))
        print("before", daily_df["dot_number"].value_counts())
        daily_df = clean_df(daily_df)
        logging.info(f"Cleaned: {daily_df.head()}")
        print("after", daily_df["dot_number"].value_counts())
        # Now store in the correct dynamo table
        print(daily_df.head())
        logging.info(f"Writing entries to {TABLE_NAME}")
        dynamodb_resource = dynamodb_utils.sync_dynamodb

        CENSUS_TABLE = dynamodb_resource.Table(TABLE_NAME)
        try:
            CENSUS_TABLE.update(
                ProvisionedThroughput={
                    "WriteCapacityUnits": 100,
                    "ReadCapacityUnits": 1,
                }
            )
        except Exception as e:
            print(e)
            if "ResourceInUseException" in str(e):
                logging.warning("Table already in use, skipping update")
                pass
            elif "ValidationException" in str(e):
                logging.info(
                    "Provisioned throughput is already set to 100/1, skipping update"
                )
                pass
            else:
                raise

        dynamodb_utils.wait_for_table_active(TABLE_NAME)
        dynamodb_utils.thread_pool_batch_update(
            table_name=TABLE_NAME,
            primary_key="dot_number",
            items=daily_df.to_dict("records"),
            timestamp=False,
        )

        try:
            CENSUS_TABLE.update(
                ProvisionedThroughput={
                    "WriteCapacityUnits": 1,
                    "ReadCapacityUnits": 1,
                }
            )
        except Exception as e:
            print(e)
            if "ValidationException" in str(e):
                logging.info(
                    "Provisioned throughput is already set to 1/1, skipping update"
                )
                pass
            else:
                raise

        # ecr_job_util.success_email(
        #     mail_api=mail_api,
        #     dev=False,
        #     job_name=JOB_NAME,
        #     job_start_time=job_start_time   ,
        #     job_start_timestr_central=job_start_timestr_central,
        #     mail_header=f" Daily Carrier Diff Ingested ",
        #     mail_body="Ingested carrier info",
        # )
    except Exception as e:
        print(e)
        print(traceback.format_exc())
        # dynamodb_utils.wait_for_table_active(TABLE_NAME)
        # try:
        #     CENSUS_TABLE.update(
        #         ProvisionedThroughput={
        #             "WriteCapacityUnits": 1,
        #             "ReadCapacityUnits": 1,
        #         }
        #     )
        # except Exception as e:
        #     if "ValidationException" in str(e):
        #         logging.info("Provisioned throughput is already set to 1/1, skipping update")
        #         pass
        #     else:
        #         raise
        # ecr_job_util.failure_email(
        #     e=e,
        #     mail_api=mail_api,
        #     dev=False,
        #     job_name=JOB_NAME,
        #     job_start_time=job_start_time,
        #     job_start_timestr_central=job_start_timestr_central,
        #     mail_body="Failed daily carrier ingestion. Error:\n" + str(e),
        # )
        return


if __name__ == "__main__":
    main()


# Sample dev command: python ./machine_learning/freightgpt/carrier_tool/daily_carrier_update/update_carrier_info.py --env dev
# Sample prod command: python ./machine_learning/freightgpt/carrier_tool/daily_carrier_update/update_carrier_info.py --env prod
