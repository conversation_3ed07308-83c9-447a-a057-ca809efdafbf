{"cells": [{"cell_type": "markdown", "id": "d11a0393-0276-4aad-9167-6806a26ace9d", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## Run with 128 GB, 16-32 vCPUs\n", "errored with Py4JError: PythonUtils does not exist in the JVM\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "bc6e43a5-4241-4393-825a-17ba7b6c5004", "metadata": {}, "outputs": [], "source": ["# !rm -r /tmp #if you run into disk errors"]}, {"cell_type": "code", "execution_count": null, "id": "7b19b3d4-52c1-4273-b258-afc0f9a02d7a", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install  ../../.\n", "!pip install -r ../../common/requirements.txt\n", "!pip install -r requirements.txt\n", "!/opt/conda/bin/python -m pip install --upgrade \"dask[complete]\"\n", "!/opt/conda/bin/python -m pip install pyarrow==14.0.1\n", "!/opt/conda/bin/python -m pip install pyspark==3.5.0\n", "!apt update\n", "!apt install default-jdk -y\n", "!apt install htop -y\n", "!apt install less -y\n", "!apt install zip -y\n", "!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "a43e7921-b509-46e3-8ee4-b1150b8555e6", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark import SparkConf\n", "\n", "# Create a Spark configuration with desired settings\n", "conf = SparkConf()\n", "conf.set(\"spark.driver.memory\", \"150g\")\n", "conf.set(\"spark.executor.memory\", \"150g\")\n", "conf.set(\"spark.memory.offHeap.enabled\", \"true\")\n", "conf.set(\"spark.memory.offHeap.size\", \"70g\")\n", "conf.set(\"spark.sql.shuffle.partitions\", \"200\")\n", "conf.set(\"spark.shuffle.memoryFraction\", \"0.6\")\n", "conf.set(\"spark.storage.memoryFraction\", \"0.6\")\n", "conf.set(\"spark.shuffle.spill.compress\", \"true\")\n", "conf.set(\"spark.driver.maxResultSize\", \"100g\")  # Increased max result size\n", "conf.set(\"spark.sql.debug.maxToStringFields\", \"100\")  # Increase if needed\n", "\n", "\n", "# Create a Spark session with the above configuration\n", "spark = (\n", "    SparkSession.builder.appName(\"DataFrameAggregation\").config(conf=conf).getOrCreate()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47f9a71a-e5b0-4656-a08f-3d1536eed21d", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import col\n", "\n", "import os\n", "import pandas as pd\n", "from tqdm import tqdm\n", "import boto3\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common.credentials import Credentials\n", "\n", "CREDENTIALS = Credentials()\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"inspection_data\"\n", ")\n", "\n", "\n", "def read_inspection_data(file_path):\n", "    return spark.read.csv(file_path, sep=\"\\t\", header=True, inferSchema=True)\n", "\n", "\n", "file_mappings = {\n", "    \"carrier\": \"Insp_Carrier_Pub_\",\n", "    \"inspection_unit\": \"Insp_Unit_Pub_\",\n", "    \"inspection_violation\": \"Insp_Viol_Pub_\",\n", "    \"study\": \"Insp_Study_Pub_\",\n", "    \"violation_description\": \"Insp_Supp_Violation\",\n", "    \"inspection\": \"Insp_Pub_\",\n", "}\n", "\n", "dataframes = {key: None for key in file_mappings}\n", "\n", "\n", "def load_and_combine_inspection_data(start_year, end_year, base_path=PREFIX) -> tuple:\n", "    for year in range(start_year, end_year + 1):\n", "        suffix1 = (\n", "            f\"0101{year}_1231{year}HDR.txt\"\n", "            if year != 2023\n", "            else \"01012023_10092023HDR.txt\"\n", "        )\n", "        suffix2 = f\"{year}HDR.txt\"\n", "        for key, file_prefix in tqdm(file_mappings.items(), total=len(file_mappings)):\n", "            file_name = file_prefix + (\n", "                suffix1 if key != \"violation_description\" else suffix2\n", "            )\n", "            file_path = os.path.join(base_path, file_name)\n", "            current_df = read_inspection_data(file_path)\n", "            if dataframes[key] is None:\n", "                dataframes[key] = current_df\n", "            else:\n", "                dataframes[key] = dataframes[key].union(current_df)\n", "    return dataframes"]}, {"cell_type": "code", "execution_count": null, "id": "728d8e7f-853e-428f-ac1a-9383f6f7e9f7", "metadata": {}, "outputs": [], "source": ["# Rename columns to lowercase\n", "dataframes = load_and_combine_inspection_data(2019, 2023)\n", "for key in dataframes:\n", "    dataframes[key] = dataframes[key].select(\n", "        [col(c).alias(c.lower()) for c in dataframes[key].columns]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "fbf6c60a-1106-45fe-a4a5-303f1a34ad1b", "metadata": {"tags": []}, "outputs": [], "source": ["import time\n", "from pyspark.sql import functions as F\n", "from pyspark.sql.types import StringType, ArrayType, MapType\n", "from pyspark.sql.functions import udf\n", "from tqdm.notebook import tqdm\n", "\n", "\n", "def format_print_seconds(operation_name: str, total_time_seconds: float) -> None:\n", "    hours = int(total_time_seconds // 3600)\n", "    minutes = int((total_time_seconds % 3600) // 60)\n", "    seconds = total_time_seconds % 60\n", "\n", "    time_str = f\"{operation_name} - Total time: \"\n", "    if hours > 0:\n", "        time_str += f\"{hours} hours, \"\n", "    if minutes > 0 or hours > 0:  # Include minutes if there are hours\n", "        time_str += f\"{minutes} minutes, \"\n", "    time_str += f\"{seconds:.2f} seconds\"\n", "\n", "    print(time_str)\n", "\n", "\n", "# CODERABBIT COMMENT:\n", "# The function aggregate_to_dict is using a placeholder for NA values and then replacing them after aggregation. This could be inefficient for large datasets. Consider using built-in Spark functions for handling nulls if possible.\n", "# Function for Aggregating and Converting to Dictionary\n", "def aggregate_to_dict(df, group_column):\n", "    # Placeholder for NA values\n", "    placeholder = \"NA_PLACEHOLDER\"\n", "\n", "    # Replace NA values with placeholder\n", "    df_filled = df.fillna(placeholder)\n", "\n", "    # Get all column names except excluded columns\n", "    all_columns = [col for col in df_filled.columns if col != group_column]\n", "\n", "    # Dynamically aggregate all other columns into lists\n", "    aggregated_cols = [F.collect_list(F.col(col)).alias(col) for col in all_columns]\n", "    aggregated_df = df_filled.groupBy(group_column).agg(*aggregated_cols)\n", "\n", "    # Replace placeholder with NA in the aggregated lists\n", "    replace_placeholder_udf = udf(\n", "        lambda lst: [None if x == placeholder else x for x in lst],\n", "        ArrayType(StringType()),\n", "    )\n", "    for col in all_columns:\n", "        aggregated_df = aggregated_df.withColumn(\n", "            col, replace_placeholder_udf(F.col(col))\n", "        )\n", "\n", "    start_time_count = time.perf_counter()\n", "    num_rows = aggregated_df.count()\n", "    format_print_seconds(\"Count Rows\", time.perf_counter() - start_time_count)\n", "\n", "    start_time_collect = time.perf_counter()\n", "    collected_rows = aggregated_df.collect()\n", "    format_print_seconds(\"Collect Rows\", time.perf_counter() - start_time_collect)\n", "\n", "    start_time_iterate = time.perf_counter()\n", "    data_dict = {\n", "        row[group_column]: {col: row[col] for col in all_columns}\n", "        for row in tqdm(collected_rows, total=num_rows)\n", "    }\n", "    format_print_seconds(\"Create Dictionary\", time.perf_counter() - start_time_iterate)\n", "\n", "    return data_dict\n", "\n", "\n", "# Function for Adding Aggregated Data to DataFrame\n", "def add_aggregated_data_to_df(main_df, data_dict, join_column, new_column_name):\n", "    # Define and register UDF\n", "    get_data_udf = udf(\n", "        lambda x: data_dict.get(x, None),\n", "        MapType(StringType(), MapType(StringType(), ArrayType(StringType()))),\n", "    )\n", "\n", "    # Add new column to main DataFrame and measure time\n", "    start_time_add_column = time.perf_counter()\n", "    main_df = main_df.withColumn(new_column_name, get_data_udf(<PERSON>.col(join_column)))\n", "    format_print_seconds(\n", "        f\"Add '{new_column_name}' column\", time.perf_counter() - start_time_add_column\n", "    )\n", "\n", "    return main_df\n", "\n", "\n", "# NOTE: I could probably get a full pyspark pipline to work, but as soon as we operate with final_df, then RAM usage explodes (800gb+). Maybe compute, save to disk along the way.\n", "# Runtime is about 22 minutes below, on 32-64 vCPUs.\n", "# start_time = time.perf_counter()\n", "# merged_violation_df = dataframes[\"inspection_violation\"].join(dataframes[\"violation_description\"], \"insp_violation_id\", \"left\")\n", "# merged_inspection_df = dataframes[\"inspection\"]\n", "# format_print_seconds('violation merge', time.perf_counter() - start_time)\n", "\n", "# start_time_agg = time.perf_counter()\n", "# violations_dict = aggregate_to_dict(merged_violation_df, \"inspection_id\")\n", "# format_print_seconds('violation agg', time.perf_counter() - start_time_agg)\n", "\n", "# merged_inspection_df = add_aggregated_data_to_df(dataframes[\"inspection\"], violations_dict, \"inspection_id\", \"violations\")\n", "\n", "# start_time_units = time.perf_counter()\n", "# units_dict = aggregate_to_dict(dataframes[\"inspection_unit\"], \"inspection_id\")\n", "# format_print_seconds('units agg', time.perf_counter() - start_time_units)\n", "\n", "# merged_inspection_df = add_aggregated_data_to_df(merged_inspection_df, units_dict, \"inspection_id\", \"units\")\n", "\n", "# start_time_studies = time.perf_counter()\n", "# studies_dict = aggregate_to_dict(dataframes[\"study\"], \"inspection_id\")\n", "# format_print_seconds('studies agg', time.perf_counter() - start_time_studies)\n", "\n", "# merged_inspection_df = add_aggregated_data_to_df(merged_inspection_df, studies_dict, \"inspection_id\", \"studies\")\n", "\n", "# start_time_carrier_merge = time.perf_counter()\n", "# final_df = merged_inspection_df.join(dataframes[\"carrier\"], \"inspection_id\", \"left\")\n", "# format_print_seconds('carrier merge', time.perf_counter() - start_time_carrier_merge)\n", "\n", "# # print('agg by dot')\n", "# # dot_number_dict = aggregate_to_dict(final_df, \"dot_number\")\n", "\n", "# total_time_seconds = time.perf_counter() - start_time\n", "# format_print_seconds(\"Total\", total_time_seconds)\n", "\n", "# total_time_seconds = time.perf_counter() - start_time"]}, {"cell_type": "markdown", "id": "3508ca5d-e4fc-4917-97a4-f44d037a004d", "metadata": {}, "source": ["## Gave up on pyspark due to OOM\n", "what if we use dask output and group by on that?\n", "\n", "Also pys<PERSON> would probably work, but I need to aggregate_to_dict only at the end, and join ASAP not one by one. If I ever need to rewrite this, do that."]}, {"cell_type": "markdown", "id": "32ff29f9-3ff9-4334-a2e4-0a858f93fbea", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Load file from disk from dask pipeline, now group by dot number and agg list"]}, {"cell_type": "code", "execution_count": null, "id": "250659a3-3136-4cd5-9e8f-2def688ba6b3", "metadata": {"tags": []}, "outputs": [], "source": ["base_df = spark.read.csv(\n", "    \"data/fully_joined_inspection_data.csv\",\n", "    header=True,\n", "    inferSchema=True,\n", "    quote='\"',\n", "    escape='\"',\n", ")\n", "base_df = base_df.drop(\"_c0\")"]}, {"cell_type": "code", "execution_count": null, "id": "d37ce1e6-18b3-4585-8576-98e4a1da2a93", "metadata": {"tags": []}, "outputs": [], "source": ["# A udf here would be most efficient, but i'm too lazy to specify the struct type explicitly and ArrayType requires me to specify\n", "# int vs str which I can't do. so. fuck me.\n", "# str_to_dict_udf = F.udf(str_to_dict, MapType(StringType(), ArrayType()))\n", "# keys_to_replace = ['units', 'study', 'violations']\n", "# for key in keys_to_replace:\n", "#     base_df = base_df.withColumn(key, str_to_dict_udf(base_df[key]))"]}, {"cell_type": "code", "execution_count": null, "id": "a0da2350-eb3b-4c84-a97f-ceec1d2c1ef8", "metadata": {"tags": []}, "outputs": [], "source": ["import time\n", "from pyspark.sql import functions as F\n", "from pyspark.sql.types import StringType, ArrayType, MapType\n", "from pyspark.sql.functions import udf\n", "from tqdm.notebook import tqdm\n", "\n", "placeholder = \"NA_PLACEHOLDER\"\n", "df = base_df\n", "group_column = \"dot_number\"\n", "# Replace NA values with placeholder\n", "df_filled = df.fillna(placeholder)\n", "\n", "# Get all column names except excluded columns\n", "all_columns = [col for col in df_filled.columns if col != group_column]\n", "\n", "# Dynamically aggregate all other columns into lists\n", "aggregated_cols = [F.collect_list(F.col(col)).alias(col) for col in all_columns]\n", "aggregated_df = df_filled.groupBy(group_column).agg(*aggregated_cols)\n", "\n", "# Replace placeholder with NA in the aggregated lists\n", "replace_placeholder_udf = udf(\n", "    lambda lst: [None if x == placeholder else x for x in lst], ArrayType(StringType())\n", ")\n", "for col in all_columns:\n", "    aggregated_df = aggregated_df.withColumn(col, replace_placeholder_udf(F.col(col)))\n", "\n", "start_time_count = time.perf_counter()\n", "num_rows = aggregated_df.count()\n", "format_print_seconds(\"Count Rows\", time.perf_counter() - start_time_count)"]}, {"cell_type": "code", "execution_count": null, "id": "2c472e05-445e-4f39-b2bc-9acd77ddc64d", "metadata": {}, "outputs": [], "source": ["start_time_collect = time.perf_counter()\n", "collected_rows = aggregated_df.collect()\n", "format_print_seconds(\"Collect Rows\", time.perf_counter() - start_time_collect)"]}, {"cell_type": "code", "execution_count": null, "id": "cd447c4b-e029-486e-be3a-cf091b3447b0", "metadata": {"tags": []}, "outputs": [], "source": ["start_time_iterate = time.perf_counter()\n", "data_list = [\n", "    {\n", "        \"inspections\": {col: row[col] for col in all_columns},\n", "        group_column: int(row[group_column]),\n", "    }\n", "    for row in tqdm(collected_rows, total=num_rows)\n", "]\n", "format_print_seconds(\"Create Dictionary\", time.perf_counter() - start_time_iterate)"]}, {"cell_type": "code", "execution_count": null, "id": "703e4397-6ea6-4229-aa62-e79c673e148f", "metadata": {"tags": []}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "import ast\n", "\n", "# Keys to replace strings with dictionaries\n", "keys_to_replace = [\"units\", \"study\", \"violations\"]\n", "\n", "\n", "def str_to_dict(input_string):\n", "    if pd.isna(input_string):\n", "        return None\n", "    if not isinstance(input_string, str):\n", "        raise TypeError(f\"Expected string, not {type(input_string)}\")\n", "\n", "    # Replace \"<NA>\" with None\n", "    input_string = input_string.replace(\"<NA>\", \"None\").replace(\"nan\", \"None\")\n", "\n", "    # Directly process strings that represent lists or dictionaries\n", "    if (\n", "        input_string.startswith(\"[\")\n", "        and input_string.endswith(\"]\")\n", "        or input_string.startswith(\"{\")\n", "        and input_string.endswith(\"}\")\n", "    ):\n", "        try:\n", "            eval_obj = ast.literal_eval(input_string)\n", "            if isinstance(eval_obj, list):\n", "                return [\n", "                    item if not isinstance(item, str) else ast.literal_eval(item)\n", "                    for item in eval_obj\n", "                ]\n", "            elif isinstance(eval_obj, dict):\n", "                return {\n", "                    key: value\n", "                    if not isinstance(value, str)\n", "                    else ast.literal_eval(value)\n", "                    for key, value in eval_obj.items()\n", "                }\n", "        except Exception as e:\n", "            print(f\"Error in ast.literal_eval: {e}\")\n", "            raise e\n", "\n", "    return input_string\n", "\n", "\n", "# str_to_dict_udf = F.udf(str_to_dict, MapType(StringType(), ArrayType()))\n", "# Assuming data_list is your list of data\n", "for idx, item in tqdm(enumerate(data_list), total=len(data_list)):\n", "    for key in keys_to_replace:\n", "        for i, val in enumerate(item[\"inspections\"][key]):\n", "            item[\"inspections\"][key][i] = str_to_dict(val)"]}, {"cell_type": "markdown", "id": "e8a624c0-fc4d-4889-b54e-9e408ad05d3f", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Great that worked! Now we just need to upload.\n", "\n", "Turns out uploading to dynamo doesn't work because of file limit size. https://stackoverflow.com/questions/66000284/dynamodb-item-size-issue\n", "S3 time. is json the most efficient?"]}, {"cell_type": "code", "execution_count": null, "id": "243c02dc-a021-4338-b171-9842f3afff4a", "metadata": {"tags": []}, "outputs": [], "source": ["import sys\n", "import math\n", "\n", "\n", "def format_size(size_bytes):\n", "    \"\"\"\n", "    Convert a size in bytes to a human-readable format.\n", "    \"\"\"\n", "    if size_bytes == 0:\n", "        return \"0B\"\n", "    size_names = (\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\")\n", "    i = int(math.floor(math.log(size_bytes, 1024)))\n", "    p = math.pow(1024, i)\n", "    s = round(size_bytes / p, 2)\n", "    return f\"{s} {size_names[i]}\"\n", "\n", "\n", "def calculate_sizes(data_list):\n", "    \"\"\"\n", "    Calculate the total size of the list and the size of the largest dictionary.\n", "    \"\"\"\n", "    total_size = sys.getsizeof(data_list)\n", "    max_dict_size = max(sys.getsizeof(d) for d in data_list)\n", "\n", "    return format_size(total_size), format_size(max_dict_size)\n", "\n", "\n", "# Example usage:\n", "total_size, max_dict_size = calculate_sizes(data_list)\n", "print(\"Total size of list:\", total_size)\n", "print(\"Size of largest dictionary:\", max_dict_size)"]}, {"cell_type": "code", "execution_count": null, "id": "d6c79c44-c857-4815-9e88-c6ec8727743b", "metadata": {}, "outputs": [], "source": ["def save_df_in_chunks(df, path, filename_prefix, chunk_size=10000):\n", "    \"\"\"\n", "    Splits a DataFrame into chunks and saves each chunk as a pickle file with progress tracking.\n", "\n", "    :param df: DataFrame to be saved.\n", "    :param path: Directory path where pickle files will be saved.\n", "    :param filename_prefix: Prefix for the filename of each chunk.\n", "    :param chunk_size: Number of rows in each chunk.\n", "    \"\"\"\n", "    # Create directory for pickle files if it doesn't exist\n", "    os.makedirs(path, exist_ok=True)\n", "\n", "    # Calculate the number of chunks\n", "    num_chunks = len(range(0, df.shape[0], chunk_size))\n", "\n", "    # Split DataFrame into chunks and save each chunk\n", "    for i in tqdm(range(num_chunks), desc=\"Saving chunks\", total=num_chunks):\n", "        start = i * chunk_size\n", "        end = start + chunk_size\n", "        chunk = df[start:end]\n", "        chunk.to_pickle(os.path.join(path, f\"{filename_prefix}_chunk_{i}.pkl\"))\n", "\n", "\n", "save_df_in_chunks(uploadable_df, \"pickles\", \"uploadable_df\")"]}, {"cell_type": "code", "execution_count": null, "id": "a1c76250-8840-46a7-8698-6797fa9e401f", "metadata": {"tags": []}, "outputs": [], "source": ["def load_df_from_chunks(path, filename_prefix):\n", "    \"\"\"\n", "    Loads DataFrame chunks from pickle files, concatenates them into a single DataFrame, with progress tracking.\n", "\n", "    :param path: Directory path where pickle files are stored.\n", "    :param filename_prefix: Prefix for the filename of each chunk to be loaded.\n", "    :return: Concatenated DataFrame.\n", "    \"\"\"\n", "    # List all pickle files with the specified prefix in the directory\n", "    files = sorted(\n", "        [\n", "            f\n", "            for f in os.listdir(path)\n", "            if f.startswith(filename_prefix + \"_chunk_\") and f.endswith(\".pkl\")\n", "        ]\n", "    )\n", "\n", "    # Initialize an empty list to store DataFrame chunks\n", "    df_chunks = []\n", "\n", "    # Load each chunk with progress tracking\n", "    for f in tqdm(files, desc=\"Loading chunks\", total=len(files)):\n", "        chunk = pd.read_pickle(os.path.join(path, f))\n", "        df_chunks.append(chunk)\n", "\n", "    # Concatenate all chunks\n", "    return pd.concat(df_chunks, ignore_index=True)\n", "\n", "\n", "# Loading the dictionary from chunks\n", "uploadable_df1 = load_df_from_chunks(\"pickles\", \"uploadable_df\")"]}, {"cell_type": "code", "execution_count": null, "id": "03dec3a0-f66b-4540-a6b4-8cd864c3abe3", "metadata": {"tags": []}, "outputs": [], "source": ["!pip install aioboto3"]}, {"cell_type": "code", "execution_count": null, "id": "db60da20-0f2a-45d8-9c99-debcf5d32707", "metadata": {"tags": []}, "outputs": [], "source": ["import boto3\n", "from tqdm.notebook import tqdm\n", "\n", "# keep track of unsucessful uploads since they're too big use a secondary system for them: s3?\n", "dynamodb = boto3.resource(\"dynamodb\")\n", "CRASHES_TABLE = dynamodb.Table(\"CarrierInspectionFMCSA\")\n", "try:\n", "    response = CRASHES_TABLE.update(\n", "        ProvisionedThroughput={\n", "            \"WriteCapacityUnits\": 100,\n", "            \"ReadCapacityUnits\": 1,\n", "        }\n", "    )\n", "except Exception as e:\n", "    print(e)\n", "from common import dynamodb_utils\n", "\n", "\n", "chunk = dynamodb_utils.upload_df_in_chunks(\n", "    uploadable_df, \"CarrierInspectionFMCSA\", chunk_size=1000, use_tqdm=True\n", ")\n", "\n", "response = CRASHES_TABLE.update(\n", "    ProvisionedThroughput={\n", "        \"WriteCapacityUnits\": 1,\n", "        \"ReadCapacityUnits\": 1,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bff13c57-8f44-49cb-b9b9-1c1a82951ca9", "metadata": {"tags": []}, "outputs": [], "source": ["len(uploadable_df)"]}, {"cell_type": "code", "execution_count": null, "id": "869dc391-29d1-49ab-a6bb-ecb1b5d118c4", "metadata": {"tags": []}, "outputs": [], "source": ["from common import dynamodb_utils\n", "\n", "\n", "dynamodb_utils.multithreaded_batch_upload(\"CarrierInspectionFMCSA\", data_list, 32)"]}, {"cell_type": "markdown", "id": "5c2da318-5117-4968-9ccb-b7e05a14ab2d", "metadata": {}, "source": ["### Upload data list to s3"]}, {"cell_type": "code", "execution_count": null, "id": "7b038432-11e9-4cb0-9232-abab80bc3553", "metadata": {}, "outputs": [], "source": ["import os\n", "from tqdm.notebook import tqdm\n", "import pickle\n", "\n", "\n", "def save_list_in_chunks(list_data, path, filename_prefix, chunk_size=10000):\n", "    # Create directory for pickle files if it doesn't exist\n", "    os.makedirs(path, exist_ok=True)\n", "\n", "    # Get all elements and split into chunks\n", "    list_chunks = [\n", "        list_data[i : i + chunk_size] for i in range(0, len(list_data), chunk_size)\n", "    ]\n", "\n", "    # Save each chunk of the list\n", "    for i, chunk in enumerate(\n", "        tqdm(list_chunks, desc=\"Saving list chunks\", total=len(list_chunks))\n", "    ):\n", "        with open(os.path.join(path, f\"{filename_prefix}_chunk_{i}.pkl\"), \"wb\") as f:\n", "            pickle.dump(chunk, f)\n", "\n", "\n", "def load_list_from_chunks(path, filename_prefix):\n", "    # List all pickle files with the specified prefix in the directory\n", "    files = sorted(\n", "        [\n", "            f\n", "            for f in os.listdir(path)\n", "            if f.startswith(filename_prefix + \"_chunk_\") and f.endswith(\".pkl\")\n", "        ]\n", "    )\n", "\n", "    # Initialize an empty list to store merged data\n", "    final_list = []\n", "\n", "    # Load each chunk and merge into final_list\n", "    for f in tqdm(files, desc=\"Loading list chunks\", total=len(files)):\n", "        with open(os.path.join(path, f), \"rb\") as file:\n", "            chunk = pickle.load(file)\n", "            final_list.extend(chunk)\n", "\n", "    return final_list\n", "\n", "\n", "# save_list_in_chunks(data_list, 'pickles', 'data_list')\n", "data_list = load_list_from_chunks(\"pickles\", \"data_list\")"]}, {"cell_type": "code", "execution_count": null, "id": "a852d0a9-f99f-4850-a79a-ea45e14e396e", "metadata": {"tags": []}, "outputs": [], "source": ["s3_bucket = \"fmcsa-inspection-data\"\n", "s3_resource = boto3.resource(\"s3\")"]}, {"cell_type": "code", "execution_count": null, "id": "875d5158-9172-49f6-9a1f-f4eba1066540", "metadata": {"tags": []}, "outputs": [], "source": ["import typing\n", "\n", "\n", "def upload_chunk_to_s3(chunk: typing.List[typing.Dict], pbar):\n", "    for item in chunk:\n", "        filename = f\"{item['dot_number']}.json\"\n", "\n", "        s3.put_object(\n", "            Body=json.dumps(item[\"inspections\"]),\n", "            Bucket=\"fmcsa-inspection-data\",\n", "            Key=filename,\n", "        )\n", "\n", "        pbar.update(1)"]}, {"cell_type": "code", "execution_count": null, "id": "eb9ce4a3-b4b2-4b50-93dc-a8cb47b06709", "metadata": {"tags": []}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "\n", "# Initialize variables to track the largest size and corresponding dot number\n", "largest_size = 0\n", "largest_dot_number = None\n", "\n", "# Iterate through data_list with a progress bar\n", "for item in tqdm(data_list, desc=\"Processing\"):\n", "    # current_size = total_size(item['inspections'], verbose=False)\n", "    # current_size = getsizeof(json.dumps(item['inspections']))\n", "    # if current_size > largest_size:\n", "    #     largest_size = current_size\n", "    #     largest_dot_number = item['dot_number']\n", "    if item[\"dot_number\"] != 54283:\n", "        continue\n", "        filename = f\"{item['dot_number']}.json\"\n", "        s3.put_object(\n", "            Body=json.dumps(item[\"inspections\"]),\n", "            Bucket=\"fmcsa-inspection-data\",\n", "            Key=filename,\n", "        )\n", "\n", "# print(\"DOT Number with largest total size:\", largest_dot_number)"]}, {"cell_type": "code", "execution_count": null, "id": "3d91bdc1-1f42-44b4-bfb6-52446c66b214", "metadata": {"tags": []}, "outputs": [], "source": ["import math\n", "\n", "\n", "def format_size(size_bytes):\n", "    \"\"\"\n", "\n", "    Convert a size in bytes to a human-readable format.\n", "    \"\"\"\n", "\n", "    if size_bytes == 0:\n", "        return \"0B\"\n", "\n", "    size_names = (\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\")\n", "\n", "    i = int(math.floor(math.log(size_bytes, 1024)))\n", "\n", "    p = math.pow(1024, i)\n", "\n", "    s = round(size_bytes / p, 2)\n", "\n", "    return f\"{s} {size_names[i]}\"\n", "\n", "\n", "format_size(largest_size)"]}, {"cell_type": "code", "execution_count": null, "id": "6257e608-b37e-44c0-a3d2-bf96f84377bc", "metadata": {"tags": []}, "outputs": [], "source": ["import json\n", "import boto3\n", "import concurrent\n", "from tqdm.notebook import tqdm\n", "import typing\n", "import numpy as np\n", "\n", "# Assuming you have already configured boto3 with your AWS credentials\n", "s3 = boto3.client(\"s3\")\n", "\n", "\n", "def upload_chunk_to_s3(chunk: typing.List[typing.Dict], pbar):\n", "    for item in chunk:\n", "        filename = f\"{item['dot_number']}.json\"\n", "        s3.put_object(\n", "            Body=json.dumps(item[\"inspections\"]),\n", "            Bucket=\"fmcsa-inspection-data\",\n", "            Key=filename,\n", "        )\n", "        pbar.update(1)\n", "\n", "\n", "def upload_data_in_parallel(data_list, num_workers=256):\n", "    # Split data_list into chunks using n<PERSON><PERSON>'s array_split\n", "    chunks = np.array_split(data_list, num_workers)\n", "\n", "    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:\n", "        # Initialize tqdm progress bar\n", "        with tqdm(total=len(data_list)) as pbar:\n", "            # Submit each chunk to the executor along with the pbar\n", "            futures = [\n", "                executor.submit(upload_chunk_to_s3, chunk, pbar) for chunk in chunks\n", "            ]\n", "\n", "            # Wait for all tasks to complete and retrieve the results\n", "            for future in concurrent.futures.as_completed(futures):\n", "                # No need to call future.result() as results are handled within each chunk\n", "                pass\n", "\n", "\n", "# Example usage\n", "upload_data_in_parallel(data_list)"]}, {"cell_type": "code", "execution_count": null, "id": "fc584bd6-23b1-4b70-835a-0151aebf6dd9", "metadata": {}, "outputs": [], "source": ["# full data, time to 1/1000th completion (982) 32vCPUs\n", "# 32 workers, 74 seconds\n", "# 64 workers,\n", "# 128 worker, 44 seconds\n", "# 256 worker,\n", "# 512 worker, 44 seconds"]}, {"cell_type": "code", "execution_count": null, "id": "b980fb94-1fbc-4737-a847-f6a3edf22b76", "metadata": {}, "outputs": [], "source": ["# 12 seconds 4\n", "# 06 seconds 8\n", "# 14 seconds 16\n", "# 24 seconds 32\n", "# 50 seconds 64\n", "# 120 seconds 128\n", "# 261 seconds 256"]}, {"cell_type": "code", "execution_count": null, "id": "6db62790-fb1b-485f-9254-986a2a991530", "metadata": {"tags": []}, "outputs": [], "source": ["import boto3\n", "import json\n", "import time\n", "\n", "s3 = boto3.client(\"s3\")\n", "\n", "\n", "def load_from_s3(dot_number):\n", "    response = s3.get_object(Bucket=\"fmcsa-inspection-data\", Key=f\"{dot_number}.json\")\n", "    data = json.loads(response[\"Body\"].read())  # slow, but still only 1 second\n", "    return data\n", "\n", "\n", "s = time.perf_counter()\n", "d = load_from_s3(54283)\n", "time.perf_counter() - s"]}, {"cell_type": "code", "execution_count": null, "id": "ab4ac70e-e48a-4f2d-a52c-2fe946839ccd", "metadata": {"tags": []}, "outputs": [], "source": ["s = time.perf_counter()\n", "d = load_from_s3(2726073)\n", "time.perf_counter() - s\n", "\n", "# try cpickle or some other shit"]}, {"cell_type": "code", "execution_count": null, "id": "fcf5d19e-4dab-4ec0-842c-a706db14d837", "metadata": {"tags": []}, "outputs": [], "source": ["import json\n", "import boto3\n", "\n", "s3 = boto3.client(\"s3\")\n", "\n", "# def upload_to_s3(bucket_name, dot_number, data):\n", "#     filename = f\"{dot_number}.json\"\n", "#     s3.put_object(Body=json.dumps(data), Bucket=bucket_name, Key=filename)\n", "\n", "\n", "def load_data(dot_number):\n", "    filename = f\"{dot_number}.json\"\n", "    file_path = os.path.join(\n", "        \"path_to_data_directory\", filename\n", "    )  # Replace \"path_to_data_directory\" with the actual path to your data directory\n", "\n", "    with open(file_path, \"r\") as file:\n", "        data = json.load(file)\n", "\n", "    return data\n", "\n", "\n", "bucket_name = \"fmcsa-inspection-data\"\n", "\n", "for item in tqdm(data_list):\n", "    dot_number = item[\"dot_number\"]\n", "    inspections_data = item[\"inspections\"]\n", "    upload_to_s3(bucket_name, dot_number, inspections_data)"]}, {"cell_type": "markdown", "id": "542e2b7d-b8ae-403c-8f67-e7d01466da14", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Just violations"]}, {"cell_type": "code", "execution_count": null, "id": "6c45de12-019f-468d-9173-12325b8c1d14", "metadata": {"tags": []}, "outputs": [], "source": ["# Join and merge operations\n", "merged_violation_df = dataframes[\"inspection_violation\"].join(\n", "    dataframes[\"violation_description\"], \"insp_violation_id\", \"left\"\n", ")\n", "merged_inspection_df = dataframes[\"inspection\"].join(\n", "    dataframes[\"carrier\"], \"inspection_id\", \"left\"\n", ")\n", "merged_violation_df.count(), merged_inspection_df.count()"]}, {"cell_type": "code", "execution_count": null, "id": "5ea35823-7f43-4404-be6b-3d18d5d28de2", "metadata": {"tags": []}, "outputs": [], "source": ["# # Show the DataFrame with the selected columns\n", "merged_inspection_df.select(\n", "    [\n", "        \"inspection_id\",\n", "        \"dot_number\",\n", "        \"report_state\",\n", "        \"report_number\",\n", "        \"insp_date\",\n", "        \"region\",\n", "        \"safety_inspector_key\",\n", "        \"viol_total\",\n", "        \"oos_total\",\n", "        \"driver_viol_total\",\n", "        \"vehicle_viol_total\",\n", "        \"hazmat_viol_total\",\n", "    ]\n", ").show(truncate=20)"]}, {"cell_type": "code", "execution_count": null, "id": "28f01ca8-1648-433c-9025-2f82c88f5d62", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "from pyspark.sql.types import (\n", "    StructType,\n", "    <PERSON>ruct<PERSON><PERSON>,\n", "    IntegerType,\n", "    StringType,\n", "    ArrayType,\n", ")\n", "\n", "# STEP 1, GROUP BY AGG LIST\n", "# Placeholder for NA values\n", "placeholder = \"NA_PLACEHOLDER\"  # Choose an appropriate placeholder\n", "\n", "# Replace NA values with placeholder\n", "merged_violation_df_filled = merged_violation_df.fillna(placeholder)\n", "\n", "# Get all column names except 'inspection_id'\n", "all_columns_except_id = [\n", "    col for col in merged_violation_df_filled.columns if col != \"inspection_id\"\n", "]\n", "\n", "# Dynamically aggregate all other columns into lists\n", "aggregated_cols = [\n", "    F.collect_list(F.col(col)).alias(col) for col in all_columns_except_id\n", "]\n", "violations_grouped = merged_violation_df_filled.groupBy(\"inspection_id\").agg(\n", "    *aggregated_cols\n", ")\n", "\n", "\n", "# Optional: Define a UDF to replace placeholder with NA in lists\n", "def replace_placeholder_with_na(lst):\n", "    return [None if x == placeholder else x for x in lst]\n", "\n", "\n", "replace_placeholder_udf = F.udf(\n", "    replace_placeholder_with_na, ArrayType(StringType())\n", ")  # Adjust the type if needed\n", "\n", "# Replace placeholder with NA in the aggregated lists\n", "violations_grouped = violations_grouped.select(\n", "    \"inspection_id\",\n", "    *[replace_placeholder_udf(F.col(col)).alias(col) for col in all_columns_except_id]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b50fbc58-ce1b-4ff0-9a23-7428804af11d", "metadata": {"tags": []}, "outputs": [], "source": ["import time\n", "from tqdm import tqdm\n", "\n", "\n", "# Count the number of rows and measure time\n", "start_time_count = time.perf_counter()\n", "num_rows = violations_grouped.count()\n", "print(f\"Time taken to count rows: {time.perf_counter() - start_time_count} seconds\")\n", "\n", "# Collect the rows and measure time. this takes 7 minutes.\n", "start_time_collect = time.perf_counter()\n", "collected_rows = violations_grouped.collect()\n", "print(f\"Time taken to collect rows: {time.perf_counter() - start_time_collect} seconds\")\n", "\n", "# Iterate through the rows with tqdm and measure time\n", "start_time_iterate = time.perf_counter()\n", "violations_rows = [row for row in tqdm(collected_rows, total=num_rows)]\n", "print(\n", "    f\"Time taken to iterate through rows with tqdm: {time.perf_counter() - start_time_iterate} seconds\"\n", ")\n", "\n", "violations_dict = {\n", "    row[\"inspection_id\"]: {col: row[col] for col in all_columns_except_id}\n", "    for row in tqdm(violations_rows)\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "84beebcf-ca5c-42f5-8882-5a1d970b1461", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql.functions import udf\n", "from pyspark.sql.types import MapType, StringType, ArrayType\n", "\n", "import time\n", "\n", "# CODERABBIT COMMENT: The UDF get_violations is used to add a 'violations' column to a dataframe. Ensure that the UDF is efficiently implemented and consider using native Spark functions if possible for better performance.\n", "# 1. Define the UDF and measure time\n", "start_time_udf_def = time.perf_counter()\n", "\n", "\n", "def get_violations(inspection_id):\n", "    return violations_dict.get(inspection_id, None)\n", "\n", "\n", "return_type = MapType(StringType(), ArrayType(StringType()))\n", "get_violations_udf = udf(get_violations, return_type)\n", "print(f\"Time taken to define UDF: {time.perf_counter() - start_time_udf_def} seconds\")\n", "\n", "# 2. Add the 'violations' column and measure time\n", "start_time_add_column = time.perf_counter()\n", "merged_inspection_df = merged_inspection_df.withColumn(\n", "    \"violations\", get_violations_udf(F.col(\"inspection_id\"))\n", ")\n", "print(\n", "    f\"Time taken to add 'violations' column: {time.perf_counter() - start_time_add_column} seconds\"\n", ")\n", "\n", "# 3. (Optional) Show some results and measure time\n", "start_time_show = time.perf_counter()\n", "merged_inspection_df.select(\"inspection_id\", \"violations\").show(truncate=False)\n", "print(f\"Time taken to show results: {time.perf_counter() - start_time_show} seconds\")"]}, {"cell_type": "markdown", "id": "f58a024f-c5fe-436c-8337-6f6a36c974ab", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Older Code"]}, {"cell_type": "code", "execution_count": null, "id": "0523e12b-c866-48b9-bdb4-df951e3c2850", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "\n", "def are_all_values_unique(df, col_name):\n", "    \"\"\"\n", "\n", "    Check if all values in a specified column of a DataFrame are unique.\n", "\n", "\n", "    :param df: The PySpark DataFrame to check.\n", "\n", "    :param col_name: The name of the column to check for uniqueness.\n", "\n", "    :return: True if all values are unique, False otherwise.\n", "    \"\"\"\n", "\n", "    # Group by the specified column and count occurrences\n", "\n", "    value_counts = df.groupBy(col_name).count()\n", "\n", "    # Get the maximum count\n", "\n", "    max_count = value_counts.agg(F.max(\"count\")).collect()[0][0]\n", "\n", "    # Return True if the maximum count is 1, indicating all values are unique\n", "\n", "    return max_count == 1\n", "\n", "\n", "# Example usage\n", "\n", "unique_check = are_all_values_unique(merged_df, \"inspection_id\")\n", "\n", "print(unique_check)"]}, {"cell_type": "code", "execution_count": null, "id": "f8d284e5-63d7-4d48-8b5c-1b1f3e14571c", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "from pyspark.sql.types import MapType, StringType\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.types import (\n", "    StructType,\n", "    <PERSON>ruct<PERSON><PERSON>,\n", "    IntegerType,\n", "    StringType,\n", "    ArrayType,\n", ")\n", "import json\n", "\n", "# Define the schema for the JSON data\n", "schema = StructType(\n", "    [\n", "        StructField(\"insp_violation_id\", IntegerType()),\n", "        StructField(\"seq_no\", IntegerType()),\n", "        StructField(\"part_no\", StringType()),\n", "        StructField(\"part_no_section\", StringType()),\n", "        StructField(\"insp_viol_unit\", StringType()),\n", "        StructField(\"insp_unit_id\", IntegerType()),\n", "        StructField(\"insp_violation_category_id\", IntegerType()),\n", "        StructField(\"out_of_service_indicator\", StringType()),\n", "        StructField(\"defect_verification_id\", IntegerType()),\n", "        StructField(\"citation_number\", StringType()),\n", "        StructField(\"part_section_id\", IntegerType()),\n", "        StructField(\"supp_desc\", StringType()),\n", "    ]\n", ")\n", "\n", "\n", "# Define a UDF to convert an array of JSON strings to an array of structs\n", "def json_to_struct(json_list):\n", "    try:\n", "        return [json.loads(j) for j in json_list]\n", "    except Exception as e:\n", "        print(f\"Error processing JSON: {e}\")\n", "        return []\n", "\n", "\n", "json_to_struct_udf = F.udf(json_to_struct, schema)\n", "\n", "\n", "# Join merged_df with merged_violation_df on inspection_id\n", "combined_df = merged_df.join(merged_violation_df, \"inspection_id\", \"left\")\n", "combined_df.select(\"violations_json\").show(truncate=False, n=5)\n", "\n", "# Aggregate the violations into a JSON string\n", "combined_df = combined_df.groupBy(\"inspection_id\").agg(\n", "    F.collect_list(\n", "        <PERSON>.to_json(\n", "            F.struct(\n", "                <PERSON>.col(\"insp_violation_id\"),\n", "                <PERSON><PERSON>col(\"seq_no\"),\n", "                <PERSON><PERSON>col(\"part_no\"),\n", "                <PERSON>.col(\"part_no_section\"),\n", "                <PERSON>.col(\"insp_viol_unit\"),\n", "                <PERSON>.col(\"insp_unit_id\"),\n", "                <PERSON>.col(\"insp_violation_category_id\"),\n", "                <PERSON>.col(\"out_of_service_indicator\"),\n", "                <PERSON>.col(\"defect_verification_id\"),\n", "                <PERSON><PERSON>col(\"citation_number\"),\n", "                <PERSON>.col(\"part_section_id\"),\n", "                <PERSON>.col(\"supp_desc\"),\n", "            )\n", "        )\n", "    ).alias(\"violations_json\")\n", ")\n", "\n", "# Apply the UDF to convert JSON strings to structs\n", "combined_df = combined_df.withColumn(\n", "    \"violations\", json_to_struct_udf(col(\"violations_json\"))\n", ")\n", "\n", "# Show the result\n", "combined_df.select([\"inspection_id\", \"violations\"]).show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "id": "f287f465-6556-463f-b168-72ed405c1362", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "# Assuming merged_violation_df has columns that you want to include in the dictionary\n", "# Create a struct (dictionary) of columns from merged_violation_df\n", "violations_struct = F.struct(\n", "    [F.col(col) for col in merged_violation_df.columns if col != \"inspection_id\"]\n", ")\n", "\n", "# Group by inspection_id and aggregate the structs into a list\n", "violations_aggregated = merged_violation_df.groupBy(\"inspection_id\").agg(\n", "    F.collect_list(violations_struct).alias(\"violations\")\n", ")\n", "\n", "# Join with merged_df on inspection_id\n", "final_df = merged_df.join(violations_aggregated, on=\"inspection_id\", how=\"left\")\n", "\n", "\n", "final_df.withColumn(\"first_violation\", col(\"violations\")[0]).select(\n", "    [\"first_violation\"]\n", ").show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c56a604e-3f09-4eb6-b18a-dfa5aad722df", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql.functions import collect_list, struct\n", "\n", "merged_violation_df = dataframes[\"inspection_violation\"].join(\n", "    dataframes[\"violation_description\"], \"insp_violation_id\", \"left\"\n", ")\n", "# Assuming 'inspection_id' is the common column\n", "merged_df = dataframes[\"inspection\"].join(\n", "    dataframes[\"carrier\"], \"inspection_id\", \"left\"\n", ")\n", "# counts match exactly. about 14 mil\n", "\n", "merged_df"]}, {"cell_type": "code", "execution_count": null, "id": "1c1bd76e-1a16-4e76-99a6-4c4fbc9e9808", "metadata": {"tags": []}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "print(df.count())\n", "# Grouping column\n", "grouping_column = \"dot_number\"\n", "\n", "# Get all column names except the grouping column\n", "other_columns = [col for col in df.columns if col != grouping_column]\n", "\n", "# Create the aggregation dictionary with string expressions\n", "aggregations = {col: \"collect_list\" for col in other_columns}\n", "\n", "# Perform the groupBy and agg operations\n", "grouped_df = df.groupBy(grouping_column).agg(aggregations)\n", "\n", "# Check the result\n", "grouped_df.count()"]}, {"cell_type": "code", "execution_count": null, "id": "89c1e4f1-70db-4c40-a53e-f788b75f6bfd", "metadata": {"tags": []}, "outputs": [], "source": ["final_df = grouped_df.to<PERSON>andas()\n", "len(final_df)"]}, {"cell_type": "code", "execution_count": null, "id": "01933a22-80ef-4124-a02e-3301c6c3c78f", "metadata": {"tags": []}, "outputs": [], "source": ["final_df.columns = [\n", "    col.replace(\"collect_list(\", \"\").replace(\")\", \"\") for col in final_df.columns\n", "]\n", "\n", "final_df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "78139f82-e30c-485e-8978-fc1023bf17ca", "metadata": {"tags": []}, "outputs": [], "source": ["# Get the first row of the DataFrame\n", "first_row = grouped_df.first()\n", "\n", "# Convert the Row object to a dictionary\n", "first_row_dict = first_row.asDict()\n", "\n", "# Print the dictionary\n", "len(first_row_dict[\"collect_list(mcmis_add_date)\"]) == df[\n", "    df[\"dot_number\"] == 1829\n", "].count()"]}], "metadata": {"availableInstances": [{"_defaultOrder": 0, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.t3.medium", "vcpuNum": 2}, {"_defaultOrder": 1, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.t3.large", "vcpuNum": 2}, {"_defaultOrder": 2, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.t3.xlarge", "vcpuNum": 4}, {"_defaultOrder": 3, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.t3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 4, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5.large", "vcpuNum": 2}, {"_defaultOrder": 5, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 6, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 7, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 8, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 9, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 10, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 11, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 12, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5d.large", "vcpuNum": 2}, {"_defaultOrder": 13, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5d.xlarge", "vcpuNum": 4}, {"_defaultOrder": 14, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5d.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 15, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5d.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 16, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5d.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 17, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5d.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 18, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5d.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 19, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 20, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": true, "memoryGiB": 0, "name": "ml.geospatial.interactive", "supportedImageNames": ["sagemaker-geospatial-v1-0"], "vcpuNum": 0}, {"_defaultOrder": 21, "_isFastLaunch": true, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.c5.large", "vcpuNum": 2}, {"_defaultOrder": 22, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.c5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 23, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.c5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 24, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.c5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 25, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 72, "name": "ml.c5.9xlarge", "vcpuNum": 36}, {"_defaultOrder": 26, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 96, "name": "ml.c5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 27, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 144, "name": "ml.c5.18xlarge", "vcpuNum": 72}, {"_defaultOrder": 28, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.c5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 29, "_isFastLaunch": true, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g4dn.xlarge", "vcpuNum": 4}, {"_defaultOrder": 30, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g4dn.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 31, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g4dn.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 32, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g4dn.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 33, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g4dn.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 34, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g4dn.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 35, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 61, "name": "ml.p3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 36, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 244, "name": "ml.p3.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 37, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 488, "name": "ml.p3.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 38, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.p3dn.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 39, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.r5.large", "vcpuNum": 2}, {"_defaultOrder": 40, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.r5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 41, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.r5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 42, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.r5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 43, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.r5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 44, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.r5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 45, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.r5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 46, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.r5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 47, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 48, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 49, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 50, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 51, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 52, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 53, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.g5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 54, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.g5.48xlarge", "vcpuNum": 192}, {"_defaultOrder": 55, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 56, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4de.24xlarge", "vcpuNum": 96}], "instance_type": "ml.r5.4xlarge", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}