import collections
import copy
import logging
import os
from typing import Dict
from typing import List
from typing import Tuple
from typing import Union

import boto3
import geopandas as gpd
import shapely

from common.constants import PROJECT_ROOT
from machine_learning.freightgpt.constants import STATE_FIPS_DICT

# Census border files
US_COUNTY_CENSUS_SHAPEFILE_PATH = os.path.join(
    PROJECT_ROOT, "machine_learning", "freightgpt", "data", "tl_2023_us_county.zip"
)
US_COUNTY_CENSUS_BUCKET_NAME = "us-census-county"
US_STATE_CENSUS_SHAPEFILE_PATH = os.path.join(
    PROJECT_ROOT, "machine_learning", "freightgpt", "data", "tl_2023_us_state.zip"
)
US_STATE_CENSUS_BUCKET_NAME = "us-census-state"


class USCensusBorders:
    """Computes and stores borders from US Census in format [(lat, lng)...]"""

    def __init__(self) -> None:
        # Used to store multiple county borders to avoid repeated computation.
        # Stored as "County,State" -> {border: [(lat, lng)...], count: 0}
        self._county_store = collections.defaultdict(
            lambda: {"coordinates": [], "count": 0}
        )
        self._state_store = collections.defaultdict(
            lambda: {"coordinates": [], "count": 0}
        )

        # Load county and state data
        self._county_gdf = self._load_census_file(
            US_COUNTY_CENSUS_BUCKET_NAME,
            US_COUNTY_CENSUS_SHAPEFILE_PATH,
            "tl_2023_us_county.zip",
        )
        self._state_gdf = self._load_census_file(
            US_STATE_CENSUS_BUCKET_NAME,
            US_STATE_CENSUS_SHAPEFILE_PATH,
            "tl_2023_us_state.zip",
        )

    def _load_census_file(self, bucket_name, filepath, s3_filename) -> gpd.GeoDataFrame:
        """
        Loads the census county data from the US Census Shapefile
        https://www2.census.gov/geo/tiger/TIGER2023/COUNTY/
        """
        # TODO(P0): #1677 Use S3Cache File for us border shapefile
        try:
            if not os.path.exists(filepath):
                s3_resource = boto3.resource("s3")
                s3_resource.meta.client.download_file(
                    bucket_name,
                    s3_filename,
                    filepath,
                )

            # Shapefile containing geopandas dataframe of borders in United States from US Census, 2023.
            return gpd.read_file(filepath)
        except Exception as e:
            logging.error(f"Error reading or processing county shapefile: {e}")
            raise

    def get_county_borders(
        self,
    ) -> Dict[str, Dict[str, Union[List[List[Tuple[float, float]]], int]]]:
        """Getter for all computed county borders and counts."""
        return copy.deepcopy(dict(self._county_store))

    def get_state_borders(
        self,
    ) -> Dict[str, Dict[str, Union[List[List[Tuple[float, float]]], int]]]:
        """Getter for all computed state borders and counts."""
        return copy.deepcopy(dict(self._state_store))

    def clear_county_borders(self) -> None:
        """Clear all county borders from memory."""
        self._county_store.clear()

    def clear_state_borders(self) -> None:
        """Clear all state borders from memory."""
        self._state_store.clear()

    def compute_borders(
        self, county: str, state: str, tolerance: float = 0.001
    ) -> List[List[Tuple[float, float]]]:
        """Get the border coordinates of a specific county within a state.

        Args:
            county (str): The name of the county.
            state (str): The abbreviation of the state.
            tolerance (float, optional): The tolerance value used to remove excess lat / lng points.

        Returns:
            list: A list of border coordinates of tuples (lat, lng) representing the county's boundary.

        Example:
            get_county_border("Los Angeles", "CA", 0.001) -> [(42.8, -89.6), ...]
        """
        if county:
            gdf_row = self._county_gdf[
                (self._county_gdf["NAME"] == county)
                & (self._county_gdf["STATEFP"] == STATE_FIPS_DICT[state]["fips"])
            ]
        else:
            gdf_row = self._state_gdf[
                self._state_gdf["STATEFP"] == STATE_FIPS_DICT[state]["fips"]
            ]

        if gdf_row.empty:
            logging.warning(f"Could not find border information for {state}")
            return []

        # Get boundary and simplify with tolerance
        geometry = gdf_row.iloc[0].geometry
        simplified_geometry = geometry.simplify(tolerance, preserve_topology=True)

        # Check if the geometry is a MultiPolygon. Large counties are represented as multipolygons.
        # A multipolygon is just a list of polygons, and we want the convex hull / boundary of all polygons.
        if isinstance(simplified_geometry, shapely.geometry.MultiPolygon):
            border_coordinates = []
            for polygon in simplified_geometry.geoms:
                # Access the exterior of each Polygon and add coordinates to the list
                coords = list(
                    zip(polygon.exterior.coords.xy[1], polygon.exterior.coords.xy[0])
                )
                border_coordinates.append(coords)
        else:
            # If it's a Polygon, directly access the exterior
            border_coordinates = [
                list(
                    zip(
                        simplified_geometry.exterior.coords.xy[1],
                        simplified_geometry.exterior.coords.xy[0],
                    )
                )
            ]

        return border_coordinates

    def update_border_store(
        self,
        county_state_pair: Tuple[str, str],
        count: int = 1,
        tolerance: float = 0.01,
    ) -> None:
        """
        Computes the border of a specified county-state pair and updates a dictionary with
        the border data and occurrence count of each county.

        Args:
        - county_state_pair (Tuple[str, str]): A tuple containing the county name and state abbreviation.
                                               County can be None.

        Returns:
        - None: The function updates the 'counties' dictionary in place and does not return a value.
        """
        if county_state_pair[0] is None:
            border_key = county_state_pair[1]
            border_store = self._state_store
        else:
            border_key = f"{county_state_pair[0]},{county_state_pair[1]}"
            border_store = self._county_store

        if border_key not in border_store:
            border_coordinates = self.compute_borders(
                *county_state_pair, tolerance=tolerance
            )  # compute border
            border_store[border_key]["coordinates"] = border_coordinates

        border_store[border_key]["count"] += count
