{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory notebook for carrier preprocessing and ingestion into opensearch.\n", "## Please use script census_search_ingestion.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common.credentials import Credentials\n", "\n", "CREDENTIALS = Credentials()\n", "\n", "\n", "PREFIX = os.path.join(\n", "    PROJECT_ROOT, \"machine_learning\", \"freightgpt\", \"data\", \"census_data\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read CSV files\n", "one = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20231009_1of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "two = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20231009_2of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "three = pd.read_csv(\n", "    os.path.join(PREFIX, \"CENSUS_PUB_20231009_3of3.csv\"),\n", "    delimiter=\"~\",\n", "    encoding=\"ISO-8859-1\",\n", ")\n", "\n", "# Concatenate into a larger dataframe\n", "census_df = pd.concat([one, two, three], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["census_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from machine_learning.freightgpt.constants import CARGO_TRANSPORTED_TYPES\n", "\n", "columns_to_process = [\n", "    \"dot_number\",\n", "    \"name\",\n", "    \"name_dba\",\n", "    \"phy_natn\",\n", "    \"phy_city\",\n", "    \"phy_st\",\n", "    \"phy_zip\",\n", "    \"mai_natn\",\n", "    \"mai_city\",\n", "    \"mai_st\",\n", "    \"mai_zip\",\n", "    \"tot_trucks\",\n", "] + list(CARGO_TRANSPORTED_TYPES.keys())\n", "carrier_search_df = census_df[[col.upper() for col in columns_to_process]]\n", "# carrier_search_limited = carrier_search_df.head(1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carrier_search_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Override cache file for ease of update\n", "\n", "from common.constants import PROJECT_ROOT\n", "from common import google_maps_utils\n", "\n", "gmaps_utils = google_maps_utils.GoogleMapsUtils()\n", "gmaps_utils.override_cache_file_df(\n", "    \"zip_lat_lng\",\n", "    f\"{PROJECT_ROOT}/machine_learning/freightgpt/carrier_tool/data/lat_lng_cache.csv\",\n", "    [\"city\", \"state\", \"zip\", \"country\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Precompute Lat / Lng for all valid City, St, Zip, Natn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_zip(zipcode, country: str):\n", "    if pd.isna(zipcode) or not zipcode:\n", "        return None\n", "\n", "    zip_len = 6 if country == \"CA\" else 5\n", "    if type(zipcode) == int:\n", "        zip = str(zipcode).replace(\" \", \"\")\n", "    else:\n", "        if type(zipcode) != str:\n", "            print(\n", "                \"Zipcode {} has type {}, which is not str or int\".format(\n", "                    zipcode, type(zipcode)\n", "                )\n", "            )\n", "            return None\n", "        zip = zipcode.replace(\" \", \"\")\n", "\n", "    if \"-\" in zip:\n", "        if country != \"US\":\n", "            print(f\"Zipcode {zip} has hyphen, but country is not US.\")\n", "            return None\n", "        zip = zip.split(\"-\")[0]\n", "\n", "    # Consider plus4 for zip codes in the US.\n", "    if country == \"US\" and len(zip) > 5:\n", "        # <PERSON> on last 4 digits of string.\n", "        zip = zip[:-4]\n", "\n", "    if len(zip) < zip_len:\n", "        print(\n", "            \"Zip {} does not have correct zip length of {} for {}\".format(\n", "                zip, zip_len, country\n", "            )\n", "        )\n", "        zip = \"0\" * (zip_len - len(zip)) + zip\n", "    elif len(zip) > zip_len:\n", "        print(\n", "            \"Zip {} does not have correct zip length of {} for {}\".format(\n", "                zip, zip_len, country\n", "            )\n", "        )\n", "        return None\n", "\n", "    return zip"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import titlecase\n", "from tqdm import tqdm\n", "\n", "tqdm.pandas()\n", "\n", "\n", "def is_valid_addy(city, state, country):\n", "    return not (\n", "        (str(country).upper() not in [\"US\", \"MX\", \"CA\"])\n", "        or (pd.isna(city) or pd.isna(state) or pd.isna(country))\n", "    )\n", "\n", "\n", "# Apply the custom function directly if it's more complex\n", "def get_location(row):\n", "    row_dict = row.to_dict()\n", "    city, state, zipcode, country = (\n", "        row_dict[\"PHY_CITY\"],\n", "        row_dict[\"PHY_ST\"],\n", "        row_dict[\"PHY_ZIP\"],\n", "        row_dict[\"PHY_NATN\"],\n", "    )\n", "\n", "    if not is_valid_addy(city, state, country):\n", "        city, state, zipcode, country = (\n", "            row_dict[\"MAI_CITY\"],\n", "            row_dict[\"MAI_ST\"],\n", "            row_dict[\"MAI_ZIP\"],\n", "            row_dict[\"MAI_NATN\"],\n", "        )\n", "\n", "    if not is_valid_addy(city, state, country):\n", "        return None\n", "\n", "    return (city, state, clean_zip(zipcode, country), country)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Apply the function and create a series of tuples for valid addresses\n", "# address_tuples = carrier_search_df.progress_apply(lambda row: get_location(row), axis=1)\n", "\n", "# # Remove None values (invalid addresses)\n", "# valid_address_tuples = address_tuples.dropna()\n", "# valid_address_tuples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# unique_addresses_df = valid_address_tuples.drop_duplicates()\n", "# unique_addresses_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Geocode all valid address tuples\n", "def geocode_cache(row):\n", "    city, state, zipcode, country = row\n", "\n", "    coordinates = None\n", "    try:\n", "        coordinates = gmaps_utils.get_lat_lng(\n", "            city=titlecase.titlecase(city),\n", "            state=state,\n", "            country=country,\n", "            zipcode=zipcode if not pd.isna(zipcode) else \"\",\n", "            append=True,\n", "        )\n", "    except:\n", "        print(city, state, zipcode, country)\n", "\n", "    return coordinates\n", "\n", "\n", "# unique_addresses_df.progress_apply(lambda row: geocode_cache(row))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create search index parquet file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["carrier_search_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "from num2words import num2words\n", "\n", "from machine_learning.freightgpt.constants import OPENSEARCH_PREPROCESSING_STOP_WORDS\n", "\n", "def basic_clean(input_str):\n", "    \"\"\"Turns a string into lowercase and normalizes whitespace\"\"\"\n", "    input_str = input_str.lower()\n", "\n", "    # Remove special characters and standardize whitespace\n", "    # name = re.sub(r'[^a-zA-Z0-9]', ' ', name)\n", "    input_str = re.sub(r\"\\s+\", \" \", input_str)\n", "    input_str = input_str.strip()\n", "\n", "    return input_str\n", "\n", "\n", "def replace_numbers_with_words(input_string):\n", "    \"\"\"Function to replace a found number with its English words representation\"\"\"\n", "\n", "    def replace_with_words(match):\n", "        number = int(match.group(0))\n", "        return num2words(number)\n", "\n", "    # Use regular expression to find all numbers and replace them with their word representation\n", "    result_string = re.sub(r\"\\d+\", replace_with_words, input_string)\n", "    return result_string\n", "\n", "\n", "def remove_stop_words(input_string):\n", "    \"\"\"Removes stop words from input.\"\"\"\n", "\n", "    # Tokenize the string\n", "    words = input_string.split(\" \")\n", "\n", "    # Filter out the stop words\n", "    filtered_words = [word if word.lower() not in OPENSEARCH_PREPROCESSING_STOP_WORDS else \"_\" for word in words]\n", "\n", "    # Join the words back into a string\n", "    filtered_string = \" \".join(filtered_words)\n", "\n", "    return filtered_string\n", "\n", "\n", "def clean_company_name(name):\n", "    \"\"\"Cleans company name\"\"\"\n", "\n", "    # Lowercase\n", "    name = name.lower()\n", "\n", "    # Remove stop words\n", "    name = remove_stop_words(name)\n", "\n", "    # Remove special characters and standardize whitespace\n", "    name = re.sub(r\"[^a-zA-Z0-9 ]\", \"_\", name)\n", "    name = re.sub(r\"\\s+\", \" \", name)\n", "    name = name.strip()\n", "\n", "    # Standardize numbers\n", "    name = replace_numbers_with_words(name)\n", "\n", "    # Remove common legal suffixes\n", "    suffixes = [\"llc\", \"inc\", \"corp\", \"ltd\", \"co\"]\n", "    for suffix in suffixes:\n", "        if name.endswith(\" \" + suffix):\n", "            name = name.replace(\" \" + suffix, \"\")\n", "\n", "        if name.endswith(suffix):\n", "            name = name.replace(suffix, \"\")\n", "\n", "    return name\n", "\n", "\n", "def remove_whitespace(input_str):\n", "    \"\"\"Removes whitespace from string\"\"\"\n", "    return re.sub(r\"\\s+\", \"\", input_str)\n", "\n", "\n", "clean_company_name(\"M & C LOGISTICS LLC\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remove_whitespace(\"m _ c logistics\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from machine_learning.freightgpt.constants import CARRIER_DBA_BLACKLIST"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INDEX_COLUMNS = [\n", "    \"dot_number_text\",\n", "    \"carrier_name_tokens\",\n", "    \"carrier_search_term_tokens\",\n", "    \"carrier_search_term_nows_text\",\n", "    \"num_trucks_int\",\n", "    \"located_in_geopoint\",\n", "    \"location_text\",\n", "    \"cargo_transported_tokens\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Assuming clean_company_name, remove_whitespace, get_location, geocode_cache, and other necessary functions are defined\n", "\n", "\n", "def process_row(row):\n", "    try:\n", "        new_row = {}\n", "\n", "        new_row[\"dot_number_text\"] = row[\"DOT_NUMBER\"]\n", "\n", "        # Carrier search terms\n", "        carrier_name = row[\"NAME\"]\n", "        if (\n", "            not pd.isna(row[\"NAME_DBA\"])\n", "            and row[\"NAME_DBA\"] not in CARRIER_DBA_BLACKLIST\n", "        ):\n", "            carrier_name = row[\"NAME_DBA\"]\n", "\n", "        if pd.isna(carrier_name):\n", "            clean_carrier_name, clean_carrier_name_nows = pd.NA, pd.NA\n", "        else:\n", "            clean_carrier_name = clean_company_name(carrier_name)\n", "            clean_carrier_name_nows = remove_whitespace(clean_carrier_name)\n", "\n", "        new_row[\"carrier_name_tokens\"] = carrier_name\n", "        new_row[\"carrier_search_term_tokens\"] = clean_carrier_name\n", "        new_row[\"carrier_search_term_nows_text\"] = clean_carrier_name_nows\n", "\n", "        # Num trucks\n", "        new_row[\"num_trucks_int\"] = row[\"TOT_TRUCKS\"]\n", "\n", "        # Geographic info\n", "        new_row[\"located_in_geopoint\"] = pd.NA\n", "        new_row[\"location_text\"] = pd.NA\n", "        location = get_location(row)\n", "        if location:\n", "            new_row[\"location_text\"] = \",\".join(\n", "                [str(phy_part) if phy_part else \"\" for phy_part in list(location)]\n", "            )\n", "            coordinates = geocode_cache(location)\n", "            if (\n", "                coordinates is not None\n", "                and not pd.isna(coordinates.any())\n", "                and len(coordinates) > 0\n", "            ):\n", "                coordinates = coordinates[0]\n", "\n", "                lat, lng = coordinates\n", "                new_row[\"located_in_geopoint\"] = f\"{lat},{lng}\"\n", "\n", "        # Cargo type\n", "        cargo_transported = [\n", "            CARGO_TRANSPORTED_TYPES[cargo_type].lower()\n", "            for cargo_type in CARGO_TRANSPORTED_TYPES\n", "            if not pd.isna(row[cargo_type.upper()])\n", "        ]\n", "        new_row[\"cargo_transported_tokens\"] = (\n", "            \" \".join(cargo_transported) if cargo_transported else pd.NA\n", "        )\n", "\n", "        return pd.Series(new_row)\n", "\n", "    except Exception as e:\n", "        print(f\"Failed with {e}\")\n", "        print(row, location, coordinates)\n", "\n", "    return pd.Series()\n", "\n", "\n", "# Apply the function to each row\n", "processed_df = carrier_search_df.progress_apply(process_row, axis=1)\n", "processed_df = processed_df.reindex(columns=INDEX_COLUMNS)\n", "\n", "processed_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Output to parquet file\n", "PARQUET_FILE_PATH = (\n", "    f\"{PROJECT_ROOT}/machine_learning/freightgpt/carrier_tool/data/search_df.parquet\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df.to_parquet(PARQUET_FILE_PATH, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df.count()"]}], "metadata": {"interpreter": {"hash": "e3bab1b93d258dbfa4dccbeb3f02abbdbb0f314bf46cf351dcc60ff2a957c79e"}, "kernelspec": {"display_name": "Python 3.11.7 ('vs-notebook')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}