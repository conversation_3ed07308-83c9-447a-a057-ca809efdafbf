{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from machine_learning.freightgpt.carrier_tool import census_search_ingestion\n", "# census_df = census_search_ingestion.extract_df_from_zip(\n", "#     zip_filename=\"CENSUS_PUB_20240209_splitfile.zip\"\n", "# )\n", "# census_df = census_search_ingestion.filter_carriers(census_df)\n", "# # census_df = census_df.head(10000)\n", "# # Export DOTs to file\n", "# dot_column_data = census_df['dot_number']\n", "# dot_column_data = dot_column_data\n", "# dot_column_data.to_csv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "dot_column_data = pd.read_csv(\"dot_numbers.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dot_column_data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dot_column_data.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "from machine_learning.freightgpt.constants import (\n", "    STATE_FIPS_DICT,\n", ")\n", "\n", "\n", "def process_raw_inspection_data(raw_data):\n", "    # TODO(P1): Should be inspection_id, but <PERSON><PERSON>'s ingestion lost that field\n", "    primary_key = \"report_state\"\n", "    formatted_info = {}\n", "    if raw_data is None or not raw_data.get(primary_key):\n", "        return {}\n", "    num_inspections = len(raw_data[primary_key])\n", "    counties = set()\n", "    tot_inspections = 0\n", "\n", "    for i in range(num_inspections - 1, -1, -1):\n", "        insp_date_list = raw_data.get(\"insp_date\", [])\n", "        if i >= len(insp_date_list):\n", "            continue\n", "        inspection_date = datetime.datetime.strptime(\n", "            str(raw_data[\"insp_date\"][i]), \"%Y%m%d\"\n", "        )\n", "        # Only look at inspections in the last 2 years\n", "        if inspection_date < datetime.datetime.now() - datetime.timedelta(days=365 * 2):\n", "            continue\n", "\n", "        tot_inspections += 1\n", "\n", "        county_code_list = raw_data.get(\"county_code\", [])\n", "        county_code_state_list = raw_data.get(\"county_code_state\", [])\n", "        # Ensure indexing won't lead to an error\n", "        if i < len(county_code_list) and i < len(county_code_state_list):\n", "            # Get the FIPS code of the state\n", "            state_fips = STATE_FIPS_DICT[raw_data[\"county_code_state\"][i]][\n", "                \"fips\"\n", "            ]  # Also has state name\n", "\n", "            # Regardless of county_id type, convert to a 3 digit string with leading zeros (e.g. 089, 100, 510)\n", "            fips_code = int(f\"{state_fips}{float(raw_data['county_code'][i]):03.0f}\")\n", "            counties.add(fips_code)\n", "\n", "    if tot_inspections:\n", "        formatted_info[\"inspections_in_last_two_years\"] = tot_inspections\n", "\n", "    if counties:\n", "        formatted_info[\"inspection_counties\"] = list(counties)\n", "\n", "    return formatted_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_raw_crash_data(raw_data):\n", "    primary_key = \"crash_id\"\n", "    formatted_info = {}\n", "    if raw_data is None or not raw_data.get(primary_key):\n", "        return {}\n", "    num_crashes = len(raw_data[primary_key])\n", "\n", "    total_crashes = 0\n", "    counties = set()\n", "\n", "    for i in range(num_crashes - 1, -1, -1):\n", "        crash_date_list = raw_data.get(\"report_date\", [])\n", "        if i >= len(crash_date_list):\n", "            continue\n", "\n", "        crash_date = datetime.datetime.strptime(\n", "            str(raw_data[\"report_date\"][i]), \"%Y%m%d\"\n", "        )\n", "        # Only look at crashes in the last 2 years\n", "        if crash_date < datetime.datetime.now() - datetime.timedelta(days=365 * 2):\n", "            continue\n", "\n", "        total_crashes += 1\n", "\n", "        county_code_list = raw_data.get(\"county_code\", [])\n", "        county_code_state_list = raw_data.get(\"state\", [])\n", "\n", "        if i < len(county_code_list) and i < len(county_code_state_list):\n", "            # Get the FIPS code of the state\n", "            state_fips = STATE_FIPS_DICT[raw_data[\"state\"][i]][\n", "                \"fips\"\n", "            ]  # Also has state name\n", "\n", "            # Regardless of county_id type, convert to a 3 digit string with leading zeros (e.g. 089, 100, 510)\n", "            fips_code = int(f\"{state_fips}{float(raw_data['county_code'][i]):03.0f}\")\n", "            counties.add(fips_code)\n", "\n", "    if total_crashes:\n", "        formatted_info[\"crashes_in_last_two_years\"] = total_crashes\n", "\n", "    if counties:\n", "        formatted_info[\"crash_counties\"] = list(counties)\n", "\n", "    return formatted_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import s3_cache_file\n", "import boto3\n", "import json\n", "\n", "\n", "def get_crash_inspection_for_dot_partition(partition: str):\n", "\n", "    S3_CLIENT = boto3.client(\"s3\")\n", "    CRASH_BUCKET = \"fmcsa-crash-data\"\n", "    INSPECTION_BUCKET = \"fmcsa-inspection-data\"\n", "\n", "    for row in partition:\n", "        inspection_info, crash_info = {}, {}\n", "\n", "        try:\n", "            inspection_data = s3_cache_file.load_s3_json(\n", "                bucket=INSPECTION_BUCKET,\n", "                filename=f\"{row.dot_number}.json\",\n", "                client=S3_CLIENT,\n", "            )\n", "            inspection_info = process_raw_inspection_data(inspection_data)\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed inspection processing on dot_number {row.dot_number} with exception {e}\"\n", "            )\n", "\n", "        try:\n", "            crash_data = s3_cache_file.load_s3_json(\n", "                bucket=CRASH_BUCKET, filename=f\"{row.dot_number}.json\", client=S3_CLIENT\n", "            )\n", "            crash_info = process_raw_crash_data(crash_data)\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed inspection processing on dot_number {row.dot_number} with exception {e}\"\n", "            )\n", "\n", "        yield (\n", "            row.dot_number,\n", "            inspection_info.get(\"inspections_in_last_two_years\"),\n", "            json.dumps(inspection_info.get(\"inspection_counties\", [])),\n", "            crash_info.get(\"crashes_in_last_two_years\"),\n", "            json.dumps(crash_info.get(\"crash_counties\", [])),\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pyspark.sql import SparkSession\n", "\n", "# Create a Spark session\n", "SPARK = (\n", "    SparkSession.builder.appName(\"ProcessInspectionCrash\").master(\"local\").getOrCreate()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_dot_df = SPARK.createDataFrame(\n", "    dot_column_data\n", ")  # add .to_frame() if running from scratch\n", "spark_dot_df = spark_dot_df.repartition(\n", "    72\n", ")  # 18GB * 1024 / 256 -> 72 partitions each of 256 MB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_dot_df.show(), spark_dot_df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["columns = [\n", "    \"dot_number\",\n", "    \"inspections_in_last_two_years\",\n", "    \"inspection_counties\",\n", "    \"crashes_in_last_two_years\",\n", "    \"crash_counties\",\n", "]\n", "processed_df = spark_dot_df.rdd.mapPartitions(\n", "    get_crash_inspection_for_dot_partition\n", ").toDF(columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df.write.option(\"header\", \"true\").option(\"sep\", \",\").mode(\"overwrite\").csv(\n", "    \"crash_inspection_output\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df = SPARK.read.csv(\"crash_inspection_output\", header=True, inferSchema=True)\n", "combined_df.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df.coalesce(1).write.csv(\"combined\", mode=\"overwrite\", header=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import shutil\n", "\n", "shutil.rmtree(\"crash_inspection_output\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "# Specify the directory containing the CSV file\n", "directory_path = \"combined\"\n", "\n", "# List all files in the directory\n", "files = os.listdir(directory_path)\n", "\n", "# Filter for CSV files\n", "csv_files = [file for file in files if file.endswith(\".csv\")]\n", "\n", "# Check if there is exactly one CSV file and read it\n", "if len(csv_files) == 1:\n", "    file_path = os.path.join(directory_path, csv_files[0])\n", "    df = pd.read_csv(file_path)\n", "    print(df.head())  # Display the first few rows to verify it's loaded correctly\n", "else:\n", "    print(\n", "        \"Error: Expected one CSV file, found {}: {}\".format(len(csv_files), csv_files)\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df = SPARK.read.csv(\"combined\", header=True, inferSchema=True)\n", "pd_dataframe = combined_df.coalesce(1).toPandas()\n", "pd_dataframe.head()"]}], "metadata": {"interpreter": {"hash": "e3bab1b93d258dbfa4dccbeb3f02abbdbb0f314bf46cf351dcc60ff2a957c79e"}, "kernelspec": {"display_name": "Python 3.11.7 ('vs-notebook')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}