import argparse
import json
import os
import re
import time
import zipfile
from typing import List
from typing import Tuple

import opensearchpy
import pandas as pd
import titlecase
from pyspark.sql import SparkSession
from tqdm import tqdm

from common import dynamodb_utils
from common.constants import PROJECT_ROOT

SPARK = SparkSession.builder.appName("Ingest Parquet Files").getOrCreate()

tqdm.pandas()

from machine_learning.freightgpt.constants import (
    CARRIER_DBA_BLACKLIST,
    CARGO_TRANSPORTED_TYPES,
    OPENSEARCH_INDEX_COLUMN_MAPPINGS,
    OPENSEARCH_INDEX_NAME,
    CARGO_TYPE_TO_EQUIPMENT_TYPE,
    FMCSA_HEADERS,
    STATE_FIPS_DICT,
)

from machine_learning.freightgpt.carrier_tool import (
    carrier_search,
    carrier_search_utils,
)

from common import opensearch_utils
from common import google_maps_utils

GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()

PARQUET_FILE_PATH = os.path.join(
    PROJECT_ROOT,
    "machine_learning",
    "freightgpt",
    "carrier_tool",
    "data",
    "search_df.parquet",
)
OPENSEARCH_INDEX_COLUMN = "dot_number"
INSURANCE_FILE_PATH = os.path.join(
    PROJECT_ROOT,
    "machine_learning",
    "freightgpt",
    "carrier_tool",
    "data",
    "Carrier_-_All_With_History_20240517.csv",
)


def extract_df_from_zip(zip_filename: str) -> pd.DataFrame:
    """Exctracts census df from census pub splitfile zip."""
    PREFIX = os.path.join(
        PROJECT_ROOT, "machine_learning", "freightgpt", "data", "census_data"
    )

    zipfile_path = os.path.join(PREFIX, zip_filename)
    zipfile_dir = zipfile_path.split(".zip")[0]

    # Unzip into the same directory
    with zipfile.ZipFile(zipfile_path, "r") as zip_ref:
        zip_ref.extractall(zipfile_dir)

    census_df = pd.DataFrame()
    # Read CSV files from the unzipped directory
    for file in os.listdir(zipfile_dir):
        if file.endswith(".csv"):
            df = pd.read_csv(
                os.path.join(zipfile_dir, file),
                delimiter="~",
                encoding="ISO-8859-1",
                low_memory=False,
            )
            census_df = pd.concat([census_df, df], ignore_index=True)
            print("Read", file)

    census_df.columns = census_df.columns.str.lower()

    return census_df


def read_spark_partition_output(output_dir: str, index: list = []) -> pd.DataFrame:
    """Reads a spark partitioned output directory into a single pandas dataframe."""
    combined_df = SPARK.read.csv(output_dir, header=True, inferSchema=True)
    pd_df = combined_df.coalesce(1).toPandas()

    if index:
        pd_df = pd_df.set_index(index)

    return pd_df


def pull_insurance_info(index: list = []) -> pd.DataFrame:
    """Pulls insurance amounts by DOT number from data.transportation.gov"""
    # Specify the columns you want to extract
    selected_columns = ["dot_number", "min_cov_amount"]

    # Read the data without specifying dtype to identify problematic values
    insurance_df = pd.read_csv(
        INSURANCE_FILE_PATH,
        usecols=selected_columns,
        lineterminator="\n",
        names=FMCSA_HEADERS,
    )

    # Check for non-numeric values and handle them (e.g., remove or correct)
    insurance_df["dot_number"] = pd.to_numeric(
        insurance_df["dot_number"], errors="coerce"
    )
    insurance_df["min_cov_amount"] = pd.to_numeric(
        insurance_df["min_cov_amount"], errors="coerce"
    )

    # Drop rows with NaN values resulting from conversion errors
    insurance_df = insurance_df.dropna(subset=["dot_number", "min_cov_amount"])

    # Convert columns to integer dtype
    insurance_df["dot_number"] = insurance_df["dot_number"].astype(int)
    insurance_df["min_cov_amount"] = insurance_df["min_cov_amount"].astype(int)

    # Set the index if needed
    if index:
        insurance_df = insurance_df.set_index(index)

    return insurance_df


####################################################################
#         Dynamo ingestion
####################################################################


def upload_to_dynamodb(census_df: pd.DataFrame, dynamodb_table_name: str) -> None:
    dynamodb_resource = dynamodb_utils.sync_dynamodb
    dynamodb_client = dynamodb_utils.sync_dynamodb_client
    # Assuming 'chunks' is an iterable of DataFrame chunks
    CENSUS_TABLE = dynamodb_resource.Table(dynamodb_table_name)
    try:
        response = CENSUS_TABLE.update(
            ProvisionedThroughput={
                "WriteCapacityUnits": 100,
                "ReadCapacityUnits": 1,
            }
        )
    except Exception as e:
        print(e)

    GSI_NAMES = ["ICC1DocketIndex", "ICC2DocketIndex", "ICC3DocketIndex"]
    for gsi_name in GSI_NAMES:
        try:
            dynamodb_utils.update_table_gsi_provisioned_throughput(
                table_name=dynamodb_table_name,
                gsi_name=gsi_name,
                read_capacity_units=1,
                write_capacity_units=100,
            )
        except Exception as e:
            print(e)

    dynamodb_utils.upload_df_in_chunks(
        census_df,
        dynamodb_table_name,
        chunk_size=1000,
        gsi_keys=["icc1", "icc2", "icc3"],
        use_tqdm=True,
    )

    # Downscale the table after ingestion

    for gsi_name in GSI_NAMES:
        try:
            response = dynamodb_client.update_table(
                TableName=dynamodb_table_name,
                GlobalSecondaryIndexUpdates=[
                    {
                        "Update": {
                            "IndexName": gsi_name,
                            "ProvisionedThroughput": {
                                "ReadCapacityUnits": 1,
                                "WriteCapacityUnits": 1,  # Decrease write capacity to 1
                            },
                        }
                    }
                ],
            )
        except Exception as e:
            print(e)

    try:
        response = CENSUS_TABLE.update(
            ProvisionedThroughput={
                "WriteCapacityUnits": 1,
                "ReadCapacityUnits": 1,
            }
        )
    except Exception as e:
        print(e)

    print(response)


def ingest_census_to_dynamo() -> None:
    DYNAMODB_TABLE = "CarrierFMCSA"

    # describe_dynamo_table(dynamodb, table_name=DYNAMODB_TABLE)
    census_df = extract_df_from_zip(zip_filename="CENSUS_PUB_20240209_splitfile.zip")
    upload_to_dynamodb(census_df, DYNAMODB_TABLE)


####################################################################
#         Opensearch ingestion
####################################################################


def filter_carriers(census_df: pd.DataFrame) -> pd.DataFrame:
    """Filters out invalid carriers based on the following:

    - Active carriers
    - Carriers with conditional or unsatisfactory ratings
    - Carriers in business for at least 2 years
    - Carriers with email
    - Carriers with phone number
    - Carriers with at least 1 truck
    - Carriers of type Carrier, Freight Forwarder, or Cargo Tank
    """
    # Assuming census_df is already defined and loaded

    # Convert ADDDATE and MCS_150_DATE to datetime format at the start to avoid repeated conversions
    census_df["adddate"] = pd.to_datetime(
        census_df["adddate"], format="%Y%m%d", errors="coerce"
    )
    census_df["mcs_150_date"] = pd.to_datetime(
        census_df["mcs_150_date"], format="%Y%m%d", errors="coerce"
    )

    # Start filtering
    # Keep only active carriers
    filtered_df = census_df[census_df["act_stat"] == "A"]
    print("Removed inactive carriers", len(filtered_df))

    # # Remove carriers with conditional or unsatisfactory ratings
    # filtered_df = filtered_df[~filtered_df["rating"].isin(["C", "U"])]
    # print("Removed C and U rated carriers", len(filtered_df))

    # # Calculate 'in_business_for' as a datetime timedelta and filter
    current_date = pd.Timestamp.now()
    filtered_df["in_business_for_days"] = (
        current_date - filtered_df["adddate"]
    ).dt.days

    # Ensure MCS_150_DATE is not null and filter based on the date being less than ~2 years ago
    filtered_df = filtered_df[filtered_df["mcs_150_date"].notnull()]
    filtered_df = filtered_df[
        filtered_df["mcs_150_date"] > (current_date - pd.Timedelta(days=760))
    ]
    print("Removed carriers with MCS150DATE less than 2 years", len(filtered_df))

    # Ensure that the carrier has email
    filtered_df = filtered_df[filtered_df["emailaddress"].notnull()]
    print("Removed carriers without email", len(filtered_df))

    # Ensure that the carrier has a phone number (CELL_NUM or TEL_NUM)
    filtered_df = filtered_df[
        filtered_df["cell_num"].notnull() | filtered_df["tel_num"].notnull()
    ]
    print("Removed carriers without phone number", len(filtered_df))

    # Only keep CARSHIP which has substrings "C", "F", and "T"
    # C: Carrier, F: Freight Forwarder, T: Cargo Tank
    # This means we're ignoring S: Shipper, B: Broker, R: Registrant.
    filtered_df = filtered_df[
        filtered_df["carship"].str.contains("C|F|T", case=False, na=False)
    ]
    print("Removed only shipper, only broker", len(filtered_df))

    # Need at least 1 truck, so we don't have carriers with only limo/bus/etc.
    filtered_df = filtered_df[filtered_df["tot_trucks"] > 0]
    print("Removed carriers with no trucks", len(filtered_df))

    # ICC number must be prefixed by MC and must be not NaN
    # filtered_df = filtered_df[
    #     ((filtered_df["icc_docket_1_prefix"] == "MC") & (filtered_df["icc1"].notna()))
    #     | ((filtered_df["icc_docket_2_prefix"] == "MC") & (filtered_df["icc2"].notna()))
    #     | ((filtered_df["icc_docket_3_prefix"] == "MC") & (filtered_df["icc3"].notna()))
    # ]
    # print("Removed carriers with no MC number", len(filtered_df))

    return filtered_df


def get_location(row: pd.Series) -> Tuple[str, str, str, str] | None:
    """
    Validates if a row in a census DataFrame has a valid location and extracts the information.
    A location is considered valid if the country is 'US', 'MX', or 'CA', and the city,
    state, and country fields are not missing. The zipcode is cleaned before being returned.

    Parameters:
    - row (pd.Series): A pandas Series object representing a row from a DataFrame, which must contain the
      following columns: ['phy_city', 'phy_st', 'phy_zip', 'phy_natn', 'mai_city', 'mai_st', 'mai_zip', 'mai_natn'].

    Returns:
    - Tuple[str, str, str, str] | None: Returns a tuple containing the valid city, state, cleaned zipcode,
      and country as strings if a valid location is found. Returns None if neither the physical nor mailing
      address provides a valid location.
    """
    row_dict = row.to_dict()

    def is_valid_address(city, state, country):
        return not (
            (not pd.isna(country) and str(country).upper() not in ["US", "MX", "CA"])
            or (pd.isna(city) or pd.isna(state) or pd.isna(country))
        )

    # Initial location
    city, state, zipcode, country = (
        row_dict["phy_city"],
        row_dict["phy_st"],
        row_dict["phy_zip"],
        row_dict["phy_natn"],
    )

    # If invalid, try the mail address.
    if not is_valid_address(city, state, country):
        city, state, zipcode, country = (
            row_dict["mai_city"],
            row_dict["mai_st"],
            row_dict["mai_zip"],
            row_dict["mai_natn"],
        )

    if not is_valid_address(city, state, country):
        return None

    return (city, state, carrier_search_utils.clean_zip(zipcode, country), country)


def clean_other_cargo(other_cargo: str) -> List[str]:
    if pd.isna(other_cargo) or other_cargo.strip().upper() == "OTHER":
        return []

    # Normalize the string by replacing "&" with "and", and converting to title case
    other_cargo = other_cargo.replace(" AND ", "&").title()

    # Split the string based on commas, "and", and possibly other delimiters if needed
    split_pattern = r",|&|/"
    raw_transported_types = re.split(split_pattern, other_cargo)

    # Strip whitespace from each item and filter out any empty strings
    clean_types = [item.strip() for item in raw_transported_types if item.strip()]

    return clean_types


def ingest_carrier_row(row: pd.Series) -> pd.Series:
    """
    Handles the cleaning and formatting of a census df row for search, contact details, geographic location,
    and cargo/equipment types based on the provided row of data.

    The function performs several key operations:
    - Chooses and cleans the carrier name, prioritizing the "doing business as" (DBA) name
      if available and not blacklisted.
    - Formats contact information, lowering email addresses and converting phone numbers
      to strings, excluding any missing values.
    - Extracts and formats geographic information, including city, state, zipcode, and country,
      and attempts to geocode these into latitude and longitude coordinates.
    - Determines the types of cargo and equipment transported, based on predefined mappings
      and the specific cargo types listed in the row.
    - Returns a cleaned series where each key's presence indicates a non-missing value in
      the input, streamlining downstream data handling.

    Returns:
        pd.Series: A pandas Series containing a cleaned and formatted subset of the
            input data, including the DOT number, carrier name, search terms, contact
            information, geographic details, and cargo/equipment types.
    """
    try:
        clean_row_dict = {}

        clean_row_dict["dot_number"] = str(row["dot_number"])

        # ICC number
        clean_row_dict["icc_number"] = pd.NA

        # Loop over prefixes
        for i, prefix in enumerate(
            ["icc_docket_1_prefix", "icc_docket_2_prefix", "icc_docket_3_prefix"]
        ):
            # Find first instance where prefix is MC and ICC Number is not NaN (indexed by icc1, icc2, etc.)
            if row[prefix] == "MC" and not pd.isna(row["icc" + str(i + 1)]):
                clean_row_dict["icc_number"] = f"{row[prefix]}{row['icc' + str(i+1)]}"
                break
        clean_row_dict["null_icc_number"] = int(pd.isna(clean_row_dict["icc_number"]))

        # Carrier search terms
        carrier_name = row["name"]
        if (
            not pd.isna(row["name_dba"])
            and row["name_dba"] not in CARRIER_DBA_BLACKLIST
        ):
            carrier_name = row["name_dba"]

        if pd.isna(carrier_name):  # Carrier name is required
            return pd.Series()
        else:
            clean_carrier_name = carrier_search.clean_company_name(carrier_name)
            clean_carrier_name_nows = carrier_search.remove_whitespace(
                clean_carrier_name
            )

        clean_row_dict["carrier_name_formatted"] = titlecase.titlecase(carrier_name)
        clean_row_dict["carrier_search_term_tokens"] = clean_carrier_name
        clean_row_dict["carrier_search_term_nows"] = clean_carrier_name_nows

        # Num trucks
        clean_row_dict["num_trucks"] = row["tot_trucks"]

        # In business for
        clean_row_dict["in_business_for_days"] = row["in_business_for_days"]

        # Email address
        if not pd.isna(row["emailaddress"]):
            clean_row_dict["email"] = row["emailaddress"].lower()

        # Phone number
        clean_row_dict["phone_number"] = (
            # Phone numbers are saved as floats with .0 - parse out.
            str(int(row["tel_num"]))
            if not pd.isna(row["tel_num"])
            else pd.NA
        )
        if pd.isna(clean_row_dict["phone_number"]):
            clean_row_dict["phone_number"] = (
                # Phone numbers are saved as floats with .0 - parse out.
                str(int(row["cell_num"]))
                if not pd.isna(row["cell_num"])
                else pd.NA
            )

        # Geographic info
        clean_row_dict["located_in_geopoint"] = pd.NA
        clean_row_dict["location"] = pd.NA
        location = get_location(row)
        if location:
            city, state, zipcode, country = location
            zipcode = zipcode if not pd.isna(zipcode) else ""

            # Formatted, delimited location
            clean_row_dict[
                "location"
            ] = f"{titlecase.titlecase(city)}, {state.upper()} {zipcode}"
            coordinates = None
            try:
                coordinates = GMAPS_UTILS.get_lat_lng(
                    city=titlecase.titlecase(city),
                    state=state,
                    country=country,
                    zipcode=zipcode,
                )
            except Exception as e:
                print("Failed to geocode", e, city, state, zipcode, country)

            if (
                coordinates is not None
                and not pd.isna(coordinates.any())
                and len(coordinates) > 0
            ):
                lat, lng = coordinates
                # Opensearch accepts geopoints in format lng, lat.
                # https://opensearch.org/docs/2.3/opensearch/supported-field-types/geo-point/
                clean_row_dict["located_in_geopoint"] = [lng, lat]

        # Cargo type
        cargo_transported = []
        equipment_types_transported = []
        for cargo_type in CARGO_TRANSPORTED_TYPES:
            if pd.isna(row[cargo_type]) or cargo_type == "genfreight":
                continue

            cargo_transported.append(CARGO_TRANSPORTED_TYPES[cargo_type])

            if cargo_type in CARGO_TYPE_TO_EQUIPMENT_TYPE:
                equipment_types_transported.append(
                    CARGO_TYPE_TO_EQUIPMENT_TYPE[cargo_type]
                )

        cargo_transported.extend(clean_other_cargo(row["othercargo"]))
        clean_row_dict["cargo_transported"] = (
            " & ".join([ct for ct in cargo_transported]) if cargo_transported else pd.NA
        )

        clean_row_dict["equipment_type_transported"] = (
            " & ".join(list({et for et in equipment_types_transported}) or ["Dry Van"])
            if equipment_types_transported
            else pd.NA
        )

        # Inspections and crashes
        clean_row_dict["num_inspections_last_two_years"] = row[
            "inspections_in_last_two_years"
        ]
        clean_row_dict["num_crashes_last_two_years"] = row["crashes_in_last_two_years"]
        inspection_counties = [str(i) for i in json.loads(row["inspection_counties"])]
        crash_counties = [str(i) for i in json.loads(row["crash_counties"])]
        inspection_crash_counties = set(inspection_counties + crash_counties)
        clean_row_dict["inspection_crash_counties"] = " ".join(
            inspection_crash_counties
        )

        # Destination states
        destination_states = set()
        for county_fips in inspection_crash_counties:
            for state, state_fips_data in STATE_FIPS_DICT.items():
                if state_fips_data["fips"] == county_fips.zfill(5)[:2]:
                    destination_states.add(state)
        clean_row_dict["destination_states"] = " ".join(destination_states)

        # Hazmat
        clean_row_dict["hazmat"] = row["hm_ind"]

        # Safety Rating
        clean_row_dict["safety_rating"] = (
            "N" if pd.isna(row["rating"]) else row["rating"]
        )

        # Insurance
        clean_row_dict["min_insurance_coverage_amount"] = row["min_cov_amount"]

        return pd.Series(clean_row_dict)

    except Exception as e:
        print(f"ingest_carrier_row failed with {e}")

    return pd.Series()


def ingest_census_to_opensearch(
    opensearch_preprocess: bool = False, recreate_index: bool = False
) -> None:
    """
    Ingests processed census data into an OpenSearch index.

    Overview of Steps:
        1. If `opensearch_preprocess` is True, preprocess the data:
           - Extract and filter data from a zip file.
           - Select and process specific columns from the data.
           - Save the processed data to a parquet file.
        2. Validate the existence of the parquet file containing processed data.
        3. If `recreate_index` is True, delete and recreate the specified OpenSearch index.
        4. Ingest the data from the parquet file into the OpenSearch index.

    Args:
        opensearch_preprocess (bool): If True, the census data will be preprocessed before
            ingestion.

        recreate_index (bool): If True, the OpenSearch index will be deleted and recreated
            before ingestion.

    Raises:
        FileNotFoundError: If the parquet file expected to contain preprocessed data does
            not exist and `opensearch_preprocess` is False.
    """
    # Re-run preprocessing and over-write parquet file.
    if opensearch_preprocess:
        print("Running preprocessing.")

        # Extract census DF and filter / remove unnecessary columns.
        census_df = extract_df_from_zip(
            zip_filename="CENSUS_PUB_20240209_splitfile.zip"
        )
        census_df = filter_carriers(census_df)
        # census_df = census_df.head(10000)

        columns_to_process = [
            "dot_number",
            "name",
            "name_dba",
            "emailaddress",
            "tel_num",
            "cell_num",
            "phy_natn",
            "phy_city",
            "phy_st",
            "phy_zip",
            "mai_natn",
            "mai_city",
            "mai_st",
            "mai_zip",
            "tot_trucks",
            "in_business_for_days",
            "icc_docket_1_prefix",
            "icc_docket_2_prefix",
            "icc_docket_3_prefix",
            "icc1",
            "icc2",
            "icc3",
            "hm_ind",
            "rating",
            "othercargo",
        ] + list(CARGO_TRANSPORTED_TYPES.keys())
        census_df = census_df[columns_to_process]

        print("Loading insurance information")
        insurance_df = pull_insurance_info(index=[OPENSEARCH_INDEX_COLUMN])

        print("Loading crash/inspection data")
        crash_inspection_df = read_spark_partition_output(
            output_dir="machine_learning/freightgpt/carrier_tool/combined",
            index=[OPENSEARCH_INDEX_COLUMN],
        )

        print("Merging inspection, crash, and insurance dfs with census")
        census_df = census_df.merge(
            insurance_df,
            left_on=OPENSEARCH_INDEX_COLUMN,
            right_index=True,
            how="left",
        )

        census_df = census_df.merge(
            crash_inspection_df,
            left_on=OPENSEARCH_INDEX_COLUMN,
            right_index=True,
            how="left",
        )

        # Process dataframe and reindex to have column names match mapping.
        processed_df = census_df.progress_apply(ingest_carrier_row, axis=1)

        # Ensure that new dataframe has columns that exactly match the opensearch index.
        processed_df = processed_df.reindex(
            columns=list(
                OPENSEARCH_INDEX_COLUMN_MAPPINGS["mappings"]["properties"].keys()
            )
            + [OPENSEARCH_INDEX_COLUMN]
        )

        # Save to parquet file as an intermediary.
        print(f"Processed entries {processed_df.size}. Saving to parquet file.")
        processed_df.to_parquet(PARQUET_FILE_PATH, engine="pyarrow")

    # Validate file exists
    if not os.path.exists(PARQUET_FILE_PATH):
        raise FileNotFoundError("Error: no parquet file found for opensearch index.")

    # Recreate index if required
    if recreate_index:
        print(f"Re-creating index {OPENSEARCH_INDEX_NAME}")
        try:
            opensearch_utils.delete_index(OPENSEARCH_INDEX_NAME)
        except opensearchpy.exceptions.NotFoundError:
            print("Index does not exist. Proceeding with index creation.")

        opensearch_utils.create_index(
            OPENSEARCH_INDEX_NAME, index_mappings=OPENSEARCH_INDEX_COLUMN_MAPPINGS
        )

        # OS takes a couple of seconds to register the new index.
        # TODO (P2): add more deterministic way to detect if index is created.
        time.sleep(45)

    # Ingest
    opensearch_utils.ingest_parquet_file(
        spark_session=SPARK,
        parquet_file_path=PARQUET_FILE_PATH,
        index_name=OPENSEARCH_INDEX_NAME,
        id_column=OPENSEARCH_INDEX_COLUMN,
        show=True,
        batch_size=500,
    )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dynamo",
        action="store_true",
        default=False,
        help="Only run ingestion of census data into dynamo.",
    )
    parser.add_argument(
        "--opensearch",
        action="store_true",
        default=False,
        help="Only run ingestion of census data into opensearch index.",
    )
    parser.add_argument(
        "--opensearch_preprocess",
        action="store_true",
        default=False,
        help="Re-preprocess the census into the opensearch parquet file and overwrite.",
    )
    parser.add_argument(
        "--recreate_index",
        action="store_true",
        default=False,
        help="Completely recreate the opensearch index.",
    )
    args = parser.parse_args()

    # If both opensearch and dynamo are provided (or neither), run both ingestions.
    if not (args.opensearch ^ args.dynamo):
        ingest_census_to_dynamo()
        ingest_census_to_opensearch(
            opensearch_preprocess=args.opensearch_preprocess,
            recreate_index=args.recreate_index,
        )

    if args.dynamo:
        ingest_census_to_dynamo()

    if args.opensearch:
        ingest_census_to_opensearch(
            opensearch_preprocess=args.opensearch_preprocess,
            recreate_index=args.recreate_index,
        )


if __name__ == "__main__":
    # For opensearch
    # python machinelearning/freightgpt/carrier_tool/census_search_ingestion.py --opensearch --opensearch_preprocess --recreate_index
    # Approx run time is ~3-4 hours
    #
    #
    # For dynamo
    # python machinelearning/freightgpt/carrier_tool/census_search_ingestion.py --dynamo
    # Approx run time is ~6 (?) hours

    main()
