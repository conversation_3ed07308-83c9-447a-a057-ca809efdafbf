from common import init_main

PROJECT_ROOT = init_main.initialize()
import unittest

import pandas as pd
from machine_learning.price_recommendation.price_predictor import (
    PricePredictorInput,
    AugmentedPricePredictorInput,
)


class TestPricePredictorInput(unittest.TestCase):
    def test_required_fields(self):
        with self.assertRaises(TypeError):
            PricePredictorInput()

    def test_data_cleaning(self):
        input_data = {
            "origin_city": " new york ",
            "origin_state": "ny",
            "destination_city": " los angeles ",
            "destination_state": "ca",
        }
        ppi = PricePredictorInput(**input_data)
        self.assertEqual(ppi.origin_city, "New York")
        self.assertEqual(ppi.origin_state, "NY")
        self.assertEqual(ppi.destination_city, "Los Angeles")
        self.assertEqual(ppi.destination_state, "CA")

    def test_time_parsing(self):
        input_data = {
            "origin_city": "New York",
            "origin_state": "NY",
            "destination_city": "Los Angeles",
            "destination_state": "CA",
            "origin_open_time": "2023-07-11 08:00:00",
        }
        ppi = PricePredictorInput(**input_data)
        self.assertEqual(ppi.origin_open_time, pd.Timestamp("2023-07-11 08:00:00"))

    def test_compare(self):
        input_data = {
            "origin_city": "New York",
            "origin_state": "NY",
            "destination_city": "Los Angeles",
            "destination_state": "CA",
            "origin_open_time": "2023-07-11 08:00:00",
        }
        ppi1 = PricePredictorInput(**input_data)
        input_data_2 = {
            "origin_city": "New York",
            "origin_state": "NY",
            "destination_city": "Los Angeles",
            "destination_state": "CA",
        }
        ppi2 = PricePredictorInput(**input_data_2)
        self.assertFalse(ppi1.compare(ppi2))
        ppi2.origin_open_time = pd.Timestamp("2023-07-11 08:00:00")
        self.assertTrue(ppi1.compare(ppi2))


class TestAugmentedPricePredictorInput(unittest.TestCase):
    def test_augmented_price_predictor_input(self):
        input_data = {
            "origin_city": "New York",
            "origin_state": "NY",
            "destination_city": "Los Angeles",
            "destination_state": "CA",
            "origin_lat": 40.7128,
            "origin_lng": -74.0060,
            "destination_lat": 34.0522,
            "destination_lng": -118.2437,
        }
        aug = AugmentedPricePredictorInput(**input_data)
        self.assertEqual(aug.origin_lat, 40.7128)
        self.assertEqual(aug.origin_lng, -74.0060)
        self.assertEqual(aug.destination_lat, 34.0522)
        self.assertEqual(aug.destination_lng, -118.2437)


if __name__ == "__main__":
    unittest.main()
