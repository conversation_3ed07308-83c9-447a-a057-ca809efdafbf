import tensorflow as tf


class R2Score(tf.keras.metrics.Metric):
    def __init__(self, name="r2_score", **kwargs):
        super().__init__(name=name, **kwargs)
        self.residual_sum_of_squares = self.add_weight(name="rss", initializer="zeros")
        self.total_sum_of_squares = self.add_weight(name="tss", initializer="zeros")

    def update_state(self, y_true, y_pred, sample_weight=None):
        y_true = tf.cast(y_true, tf.float32)
        y_pred = tf.cast(y_pred, tf.float32)
        residual_sum_of_squares = tf.reduce_sum(tf.square(y_true - y_pred))
        total_sum_of_squares = tf.reduce_sum(tf.square(y_true - tf.reduce_mean(y_true)))
        self.residual_sum_of_squares.assign_add(residual_sum_of_squares)
        self.total_sum_of_squares.assign_add(total_sum_of_squares)

    def result(self):
        return 1 - (self.residual_sum_of_squares / self.total_sum_of_squares)
