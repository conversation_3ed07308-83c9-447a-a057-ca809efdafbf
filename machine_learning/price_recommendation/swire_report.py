import os

import numpy as np

from common import init_main

PROJECT_ROOT = init_main.initialize()
import pandas as pd
import matplotlib.pyplot as plt
from common import query_utils
from statsmodels.stats.multicomp import pairwise_tukeyhsd

QUERY_STR = """
SELECT
    b.name AS broker_name,
    TIMESTAMPDIFF(SECOND, s.originCloseTime, sm.shipmentIngestionTime) / 3600.0 AS difference_hours
FROM
    mvp_db_dev.brokers b
JOIN
    mvp_db_dev.shipments s ON b.id = s.brokerId
JOIN
    mvp_db_dev.shipmentmetadata sm ON s.id = sm.shipmentId
ORDER BY
    b.name,
    difference_hours;
"""


def clean_df(df: pd.DataFrame) -> pd.DataFrame:
    # Remove demo data broker names.
    excluded_brokers = ["CargoCare", "Reliant Freight Services"]
    new_df = df[~df.broker_name.isin(excluded_brokers)]
    new_df = new_df.dropna()
    new_df = new_df[new_df["difference_hours"] > 0 & new_df["difference_hours"] < 1000]
    return new_df


def print_brokers_ordered_by_delay(df: pd.DataFrame) -> None:
    mean_times = df.groupby("broker_name")["difference_hours"].mean()
    sorted_companies = mean_times.sort_values()
    print(sorted_companies)


def plot_broker_histograms(df: pd.DataFrame, color: str) -> None:
    brokers = df["broker_name"].unique()

    bins = np.linspace(0, 1000, 50)

    for broker in brokers:
        broker_df = df[df["broker_name"] == broker]
        plt.figure()
        plt.hist(
            broker_df["difference_hours"],
            bins=bins,
            color=color,
            density=True,
        )
        plt.title(f"Broker: {broker}")
        plt.xlabel("Delay")
        plt.ylabel("Occurrences")
        plt.show()


def tukey_test(df: pd.DataFrame) -> None:
    """
    https://en.wikipedia.org/wiki/Tukey%27s_range_test
    """
    tukey = pairwise_tukeyhsd(
        endog=df["difference_hours"],
        groups=df["broker_name"],
        alpha=0.05,
    )
    print(tukey)


def main():
    records_df = query_utils.get_data_in_batches(
        "mvp_db_dev", 1000, QUERY_STR, use_tqdm=True
    )

    records_df.to_csv(
        os.path.join(
            PROJECT_ROOT, "machine_learning/price_recommendation/records_df.csv"
        ),
        index=False,
    )

    records_df = pd.read_csv("records_df.csv")
    records_df_dropna = clean_df(records_df)

    broker_counts = records_df_dropna["broker_name"].value_counts()
    print(f"Broker counts: \n{broker_counts}")

    summary_stats = records_df_dropna.groupby("broker_name")["difference_hours"].agg(
        ["min", "max", "mean", "median"]
    )
    print(summary_stats)

    print_brokers_ordered_by_delay(records_df_dropna)

    plot_broker_histograms(records_df_dropna, "royalblue")
    tukey_test(records_df_dropna)


if __name__ == "__main__":
    main()
