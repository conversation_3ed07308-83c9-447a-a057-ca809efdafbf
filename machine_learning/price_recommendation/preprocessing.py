import logging
import re

import holidays
import numpy as np
import pandas as pd

from common.constants import EQUIPMENT_TYPE_DICT
from machine_learning.price_recommendation.constants import CANADA_PROVINCES
from machine_learning.price_recommendation.constants import MEXICO_STATES
from machine_learning.price_recommendation.constants import STATE_NAME_TO_CODE
from machine_learning.price_recommendation.constants import US_STATES

# Assuming US holidays
us_holidays = holidays.US()


def camel_to_snake(s: str) -> str:
    return re.sub(r"(?<!^)(?=[A-Z])", "_", s).lower()


def encode_cyclic_features(df, max_values, numeric_columns):
    """
    Encodes cyclic features in a pandas DataFrame using sine and cosine transformations.

    CAUTION: This modifies the numeric columns list, replacing `col` with `sin_col` and `cos_col`.

    Args:
        df (pandas.DataFrame): The DataFrame to transform.
        max_values (dict): A dictionary mapping column suffixes to their maximum values.

    Returns:
        pandas.DataFrame: The transformed DataFrame with sine and cosine features added for the relevant columns.
    """
    assert False
    # TODO(P0): This function is not unit testable. We need to fix this.
    # Apply the transformation to all relevant columns
    for col in df.columns:
        # Why 2:?
        suffix = "_".join(col.split("_")[2:])
        for max_col, max_value in max_values.items():
            if max_col == suffix:
                df[col + "_sin"] = df[col].apply(
                    lambda x: np.sin(2 * np.pi * x / max_value)
                )
                df[col + "_cos"] = df[col].apply(
                    lambda x: np.cos(2 * np.pi * x / max_value)
                )
                numeric_columns.remove(col)
                numeric_columns.append(col + "_sin")
                numeric_columns.append(col + "_cos")
                df.drop(columns=[col], inplace=True)


def separate_time_fields(col: str, df: pd.DataFrame):
    # Remove "time" suffix
    assert "time" == col[-4:]
    ml_col = col[:-4]
    df[ml_col + "hour"] = df[col].dt.hour
    df[ml_col + "day"] = df[col].dt.day
    df[ml_col + "month"] = df[col].dt.month
    df[ml_col + "day_of_year"] = df[col].dt.dayofyear
    df[[f"{ml_col}day_of_week", f"{ml_col}week", f"{ml_col}year"]] = df[
        col
    ].dt.isocalendar()[["day", "week", "year"]]

    # us holidays does not gracefully handle NAs.
    mask = df[col].notnull()
    df.loc[mask, ml_col + "is_workday"] = df.loc[mask, col].dt.dayofweek.apply(
        lambda x: int(x < 5)
    )
    df.loc[mask, ml_col + "is_holiday"] = df.loc[mask, col].dt.date.apply(
        lambda x: int(x in us_holidays)
    )
    df.drop(columns=[col], inplace=True)


def standardize_state_code(state: str) -> str:
    # TODO(P1): Add items to STATE_NAME_TO_CODE.
    if pd.isna(state):
        return pd.NA
    upper_code = state.upper()
    if upper_code not in US_STATES | CANADA_PROVINCES | MEXICO_STATES:
        return pd.NA
    if upper_code in STATE_NAME_TO_CODE:
        return STATE_NAME_TO_CODE[upper_code]
    return upper_code


def derive_country_from_state(state: str) -> str:
    if pd.isna(state):
        return pd.NA
    if state in US_STATES:
        return "US"
    if state in CANADA_PROVINCES:
        return "CA"
    if state in MEXICO_STATES:
        return "MX"
    logging.warning(f"State {state} not found in STATE_NAME_TO_CODE.")
    return pd.NA


def build_timestamp(date_str: str, time_str: str = "") -> pd.Timestamp:
    # Remove any non-date, non-time elements from the string (if present)
    date_str = re.sub(r"[^\d/]", "", date_str)
    time_str = re.sub(r"[^\d:]", "", time_str)

    if not date_str:
        return pd.NaT
    else:
        formatted_time = f" {time_str}" if time_str else ""
        return pd.Timestamp(f"{date_str}{formatted_time}")


def map_equipment_type(equipment_type: str) -> str:
    if pd.isna(equipment_type) or not equipment_type:
        return pd.NA
    elif equipment_type.strip().lower() in EQUIPMENT_TYPE_DICT:
        return EQUIPMENT_TYPE_DICT[equipment_type.strip().lower()]
    else:
        print(f"Equipment type {equipment_type} not found in dictionary")
        return pd.NA
