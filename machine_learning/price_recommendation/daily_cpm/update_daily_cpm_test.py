import os

from common import init_main

PROJECT_ROOT = init_main.initialize()
import unittest
import pandas as pd
from machine_learning.price_recommendation.daily_cpm import update_daily_cpm


class TestNTIGraphExtraction(unittest.TestCase):
    def test_extract_nti_graph(self):
        # Load csv from file
        html_filename = os.path.join(
            PROJECT_ROOT,
            "machine_learning",
            "price_recommendation",
            "daily_cpm",
            "data",
            "freightwaves_20231213_195607.html",
        )
        with open(html_filename, encoding="utf-8") as f:
            html_content = f.read()
        freightwaves_df = update_daily_cpm.extract_nti_graph(html_content)
        df = update_daily_cpm.clean_df(freightwaves_df)
        reference_df = pd.read_csv(
            os.path.join(
                PROJECT_ROOT,
                "machine_learning",
                "price_recommendation",
                "daily_cpm",
                "data",
                "reference_daily_cpm.csv",
            ),
            index_col="date",
        )
        pd.testing.assert_frame_equal(df, reference_df, "The dataframes do not match.")


if __name__ == "__main__":
    unittest.main()
