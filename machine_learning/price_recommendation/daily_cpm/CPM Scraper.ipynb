{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install wayback"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get freightwaves data from 6 month graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "import json\n", "import pandas as pd\n", "from wayback import Mode\n", "\n", "from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "from machine_learning.price_recommendation.daily_cpm.update_daily_cpm import (\n", "    extract_nti_graph,\n", "    clean_df,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "HEADERS = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148\"\n", "}\n", "\n", "r = requests.get(\"http://freightwaves.com\", headers=HEADERS)\n", "freightwaves_df = extract_nti_graph(r.text)\n", "freightwaves_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Use wayback machine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from wayback import WaybackClient\n", "\n", "client = WaybackClient()\n", "results = client.search(\"freightwaves.com\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["records = list(results)\n", "records[0].timestamp, records[-1].timestamp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pull from the last record.\n", "# Usage example with your existing code\n", "from wayback import Mode\n", "import datetime\n", "\n", "response = client.get_memento(records[-1], mode=Mode.original)\n", "content = response.content.decode()\n", "\n", "# Use the function to extract NTI.USA data\n", "freightwaves_archive_df = extract_nti_graph(content)\n", "\n", "# Display the DataFrame if data extraction was successful\n", "if freightwaves_archive_df is not None:\n", "    print(freightwaves_archive_df)\n", "else:\n", "    print(\"No data extracted.\")\n", "\n", "# This must cover to 2023-09-19 since thats where we can use the next link from.\n", "# index 5432 is known to be good.\n", "assert freightwaves_archive_df.Date.min() < pd.Timestamp(\"2023-09-19\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pull from NTI website\n", "https://sonar.freightwaves.com/national-truckload-index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import typing\n", "\n", "\n", "def extract_nti_var(html_content: str) -> typing.Optional[pd.DataFrame]:\n", "    \"\"\"\n", "    Extracts NTI data from the given HTML content and returns it as a Pandas DataFrame.\n", "\n", "    Args:\n", "    html_content (str): The HTML content as a string.\n", "\n", "    Returns:\n", "    Optional[pd.DataFrame]: A DataFrame containing the extracted data, or None if not found.\n", "    \"\"\"\n", "    soup = BeautifulSoup(html_content, \"html.parser\")\n", "\n", "    # Find the script tag containing the NTI data\n", "    script_tag = soup.find(\n", "        \"script\", string=lambda text: text and \"var nti  = [{\" in text\n", "    )\n", "\n", "    if script_tag:\n", "        # Extract and parse the data from the script tag\n", "        data_str = script_tag.string\n", "        # Assuming the data is in JSON format after 'var nti  = '\n", "        start = data_str.find(\"var nti  = [\") + len(\"var nti  = \")\n", "        end = data_str.find(\"];\", start) + 1\n", "        json_data_str = data_str[start:end]\n", "\n", "        try:\n", "            parsed_data = json.loads(json_data_str)\n", "            # Convert the parsed data to a DataFrame\n", "            df = pd.DataFrame(parsed_data)\n", "            df[\"Date\"] = pd.to_datetime(df[\"Date\"])\n", "            df[\"Close\"] = pd.to_numeric(df[\"Close\"])\n", "            return df\n", "        except json.JSONDecodeError:\n", "            print(\"Error parsing JSON data from the script tag.\")\n", "            return None\n", "    else:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "HEADERS = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148\"\n", "}\n", "\n", "r = requests.get(\n", "    \"https://sonar.freightwaves.com/national-truckload-index\", headers=HEADERS\n", ")\n", "freightwaves_df = extract_nti_var(r.text)\n", "freightwaves_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://sonar.freightwaves.com/national-truckload-index\"\n", "client = WaybackClient()\n", "results = client.search(url)\n", "nti_records = list(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[(i, r.timestamp.strftime(\"%x\")) for i, r in enumerate(nti_records)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nti_df1 = extract_nti_var(\n", "    client.get_memento(nti_records[14], mode=Mode.original).content.decode()\n", ")\n", "nti_df2 = extract_nti_var(\n", "    client.get_memento(nti_records[8], mode=Mode.original).content.decode()\n", ")\n", "combined_nti_df = pd.concat(\n", "    [freightwaves_df, freightwaves_archive_df, nti_df1, nti_df2], ignore_index=True\n", ")\n", "\n", "combined_nti_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# We only get as far back as 2022-05-05\n", "combined_nti_df[\"Date\"] = pd.to_datetime(combined_nti_df[\"Date\"])\n", "print(combined_nti_df[combined_nti_df[\"Date\"] == combined_nti_df[\"Date\"].min()])\n", "print(combined_nti_df[combined_nti_df[\"Date\"] == combined_nti_df[\"Date\"].max()])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check that there are no missing dates\n", "combined_nti_df[\"Date\"].diff().value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "combined_nti_df = combined_nti_df.sort_values(\"Date\")\n", "plt.plot(combined_nti_df[\"Date\"], combined_nti_df[\"Close\"], marker=\"o\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import s3_cache_file\n", "\n", "daily_cpm_s3 = s3_cache_file.S3CacheFile(\"truce-ml-data\", \"data/daily_cpm.csv\", \"date\")\n", "# daily_cpm_s3.df = clean_df(combined_nti_df)\n", "daily_cpm_s3.df = pd.read_csv(\"data/daily_cpm.csv\", index_col=\"date\")\n", "daily_cpm_s3.save_and_upload()\n", "\n", "# from common import dynamodb_utils\n", "\n", "\n", "# dynamo_df = combined_nti_df.copy()[[\"Date\", \"Close\"]]\n", "\n", "\n", "# dynamo_df[\"Date\"] = dynamo_df[\"Date\"].dt.strftime(\"%Y-%m-%d\")\n", "\n", "\n", "# dynamo_df = dynamo_df.rename(columns={\"Date\": \"date\", \"Close\": \"cpm\"})\n", "\n", "\n", "# dynamo_df.drop_duplicates(subset=[\"date\"], inplace=True)\n", "# dynamo_df\n", "\n", "\n", "# dynamodb_utils.upload_df_in_chunks(dynamo_df, \"DailyCPM\", 25, True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now let's do analysis, compare it to our CPM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare Value to Close when neither are null. print the number of rows where they are different\n", "combined_nti_df[\"Date\"] = pd.to_datetime(combined_nti_df[\"Date\"])\n", "combined_nti_df[\"Value\"] = pd.to_numeric(combined_nti_df[\"Value\"])\n", "combined_nti_df[\"Close\"] = pd.to_numeric(combined_nti_df[\"Close\"])\n", "\n", "df2 = combined_nti_df[\n", "    combined_nti_df[\"Value\"].notna() | combined_nti_df[\"Close\"].notna()\n", "]\n", "# Set 0.0 to be NA\n", "df2[\"Value\"] = df2[\"Value\"].replace(0.0, pd.NA)\n", "df2[\"Close\"] = df2[\"Close\"].replace(0.0, pd.NA)\n", "(df2[\"Value\"] - df2[\"Close\"]).value_counts()\n", "\n", "\n", "def cpm_from_value_close(row):\n", "    if pd.isna(row[\"Value\"]):\n", "        return row[\"Close\"]\n", "    if pd.isna(row[\"Close\"]):\n", "        return row[\"Value\"]\n", "    return row[\"Close\"]\n", "\n", "\n", "df2[\"cpm\"] = df2.apply(cpm_from_value_close, axis=1)\n", "df2.dropna(subset=[\"cpm\"], inplace=True)\n", "df2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "df2 = df2.sort_values(by=\"Date\")\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(df2[\"Date\"], df2[\"cpm\"], marker=\"o\")\n", "plt.title(\"CPM over Time\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"CPM\")\n", "plt.grid(True)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# Make numpy values easier to read.\n", "np.set_printoptions(precision=3, suppress=True)\n", "\n", "from common.s3_cache_file import S3\n", "\n", "s3_ml_data = S3(\"truce-ml-data\")\n", "# ml_df_filename = \"data/pricing_ml_df_v0.csv\"\n", "# ml_df_filename = \"data/pricing_ml_df_2023-05-10_18-58-13.csv\"\n", "\n", "\n", "def load_ml_data(filename: str, index_col=None) -> pd.DataFrame:\n", "    s3_ml_data.pull(filename, merge=False)\n", "    return pd.read_csv(filename, index_col=index_col)\n", "\n", "\n", "# Sheer training + RDS full data\n", "sheer_rds_train_df = load_ml_data(\"data/rds_sheer_weighted_pricing_train.csv\")\n", "\n", "# Sheer training data\n", "sheer_train_df = load_ml_data(\"data/sheer_pricing_train.csv\")\n", "\n", "# Sheer validation data\n", "sheer_val_df = load_ml_data(\"data/sheer_pricing_val_test.csv\")\n", "\n", "# RDS full data\n", "rds_pricing_full_df = load_ml_data(\"data/rds_pricing_full.csv\", index_col=\"id\")\n", "rds_pricing_train_df = load_ml_data(\"data/rds_pricing_train.csv\")\n", "rds_pricing_val_df = load_ml_data(\"data/rds_pricing_val.csv\")\n", "rds_pricing_test_df = load_ml_data(\"data/rds_pricing_test.csv\")\n", "\n", "mystery_data_full_df = load_ml_data(\"data/mystery_full.csv\")\n", "\n", "# ML Scrape Data\n", "ml_scrape_df = load_ml_data(\"data/ml_scrape_full.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add data source column\n", "rds_pricing_full_df[\"data_source\"] = \"rds_pricing_full\"\n", "mystery_data_full_df[\"data_source\"] = \"mystery_data_full\"\n", "ml_scrape_df[\"data_source\"] = \"ml_scrape_\" + ml_scrape_df[\"board\"]\n", "ml_scrape_df[\"data_source\"] = ml_scrape_df[\"data_source\"].apply(lambda x: x.lower())\n", "\n", "combined_df = pd.concat([rds_pricing_full_df, mystery_data_full_df, ml_scrape_df])\n", "\n", "combined_df[\"cpm\"] = combined_df[\"OUTPUT_cogs_total\"] / combined_df[\"distance_miles\"]\n", "combined_df[\"cpm\"] = combined_df[\"cpm\"].replace(np.inf, np.nan)\n", "combined_df[combined_df[\"cpm\"] < 10][\"cpm\"].hist(bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df[\"origin_close_time\"] = pd.to_datetime(combined_df[\"origin_close_time\"])\n", "# Drop rows with null cpm\n", "combined_df.dropna(subset=[\"cpm\"], inplace=True)\n", "# Make cpm numeric, making any non-numeric values null\n", "combined_df[\"cpm\"] = pd.to_numeric(combined_df[\"cpm\"], errors=\"coerce\")\n", "\n", "df2[\"data_source\"] = \"freightwaves\"\n", "combined_df = pd.concat(\n", "    [combined_df, df2.rename(columns={\"Date\": \"origin_close_time\"})], ignore_index=True\n", ")\n", "\n", "# Aggregating by every 6 days\n", "median_cpm = (\n", "    combined_df.groupby(\n", "        [pd.<PERSON><PERSON>(key=\"origin_close_time\", freq=\"7D\"), \"data_source\"]\n", "    )[\"cpm\"]\n", "    .median()\n", "    .reset_index()\n", ")\n", "\n", "# Make sure the 'origin_close_time' column is still in the correct format\n", "median_cpm[\"origin_close_time\"] = pd.to_datetime(\n", "    median_cpm[\"origin_close_time\"]\n", ").dt.date\n", "# Only keep cpms between 1 and 5\n", "median_cpm = median_cpm[(median_cpm[\"cpm\"] >= 1) & (median_cpm[\"cpm\"] <= 5)]\n", "\n", "\n", "plt.figure(figsize=(12, 6))\n", "for source in median_cpm[\"data_source\"].unique():\n", "    subset = median_cpm[median_cpm[\"data_source\"] == source]\n", "    plt.scatter(\n", "        subset[\"origin_close_time\"], subset[\"cpm\"], label=source, s=10\n", "    )  # s is the size of dots\n", "\n", "\n", "plt.title(\"Median CPM by Day, Colored by Data Source\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Median CPM\")\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Processing combined_df\n", "combined_df[\"origin_close_time\"] = pd.to_datetime(combined_df[\"origin_close_time\"])\n", "combined_df.dropna(subset=[\"cpm\"], inplace=True)\n", "combined_df[\"cpm\"] = pd.to_numeric(combined_df[\"cpm\"], errors=\"coerce\")\n", "\n", "# Processing df2\n", "df2[\"data_source\"] = \"freightwaves\"\n", "df2.rename(columns={\"Date\": \"origin_close_time\"}, inplace=True)\n", "df2[\"origin_close_time\"] = pd.to_datetime(df2[\"origin_close_time\"])\n", "\n", "# Concatenating dataframes\n", "combined_df = pd.concat([combined_df, df2], ignore_index=True)\n", "\n", "# Aggregating by every 6 days\n", "median_cpm = (\n", "    combined_df.groupby(\n", "        [pd.<PERSON><PERSON>(key=\"origin_close_time\", freq=\"1D\"), \"data_source\"]\n", "    )[\"cpm\"]\n", "    .median()\n", "    .reset_index()\n", ")\n", "median_cpm[\"origin_close_time\"] = pd.to_datetime(\n", "    median_cpm[\"origin_close_time\"]\n", ").dt.date\n", "\n", "# Filtering cpm between 1 and 5\n", "median_cpm = median_cpm[(median_cpm[\"cpm\"] >= 1) & (median_cpm[\"cpm\"] <= 5)]\n", "\n", "# Calculating the total average of everything except 'freightwaves'\n", "average_cpm = combined_df[combined_df[\"data_source\"] != \"freightwaves\"][\"cpm\"].mean()\n", "\n", "# Plotting\n", "plt.figure(figsize=(12, 6))\n", "for source in median_cpm[\"data_source\"].unique():\n", "    subset = median_cpm[median_cpm[\"data_source\"] == source]\n", "    plt.scatter(\n", "        subset[\"origin_close_time\"], subset[\"cpm\"], label=source, s=10\n", "    )  # s is the size of dots\n", "\n", "# Adding a line for the total average of non-freightwaves data sources\n", "plt.axhline(\n", "    y=average_cpm,\n", "    color=\"r\",\n", "    linestyle=\"-\",\n", "    label=f\"Average CPM (Excl. Freightwaves) = {average_cpm:.2f}\",\n", ")\n", "\n", "plt.title(\"Median CPM by Day, Colored by Data Source\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Median CPM\")\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filtering out 'freightwaves' from combined_df for the average calculation\n", "combined_df_without_freightwaves = combined_df[\n", "    combined_df[\"data_source\"] != \"freightwaves\"\n", "]\n", "\n", "# Calculating the 6-day average\n", "avg_cpm = (\n", "    combined_df_without_freightwaves.groupby(\n", "        pd.Grouper(key=\"origin_close_time\", freq=\"6D\")\n", "    )[\"cpm\"]\n", "    .median()\n", "    .reset_index()\n", ")\n", "\n", "# filtering cpm between 1 and 5\n", "avg_cpm = avg_cpm[(avg_cpm[\"cpm\"] >= 1) & (avg_cpm[\"cpm\"] <= 5)]\n", "\n", "# Plotting\n", "plt.figure(figsize=(12, 6))\n", "for source in median_cpm[\"data_source\"].unique():\n", "    subset = median_cpm[median_cpm[\"data_source\"] == source]\n", "    plt.scatter(\n", "        subset[\"origin_close_time\"], subset[\"cpm\"], label=source, s=10\n", "    )  # s is the size of dots\n", "\n", "# Adding the average line\n", "plt.plot(\n", "    avg_cpm[\"origin_close_time\"],\n", "    avg_cpm[\"cpm\"],\n", "    label=\"6-Day Average (Excluding Freightwaves)\",\n", "    linewidth=2,\n", "    color=\"black\",\n", "    alpha=0.5,\n", ")\n", "\n", "plt.title(\"Median CPM by Day, Colored by Data Source\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Median CPM\")\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run scraper in reverse chronological order (no longer needed)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Other no-longer needed extraction methods for older versions of website"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Given the specific pattern provided, I'll attempt to extract the market price for \"NTI.USA\" again.\n", "\n", "\n", "def extract_nti_ticker(html_content):\n", "    \"\"\"\n", "    Extracts the market price for \"NTI.USA\" from the given HTML content in the new format.\n", "\n", "    Args:\n", "    html_content (str): The HTML content as a string.\n", "\n", "    Returns:\n", "    str: The extracted market price, or None if not found.\n", "    \"\"\"\n", "    soup = BeautifulSoup(html_content, \"html.parser\")\n", "\n", "    # Finding the div with the specified class for NTI.USA market price\n", "    nti_usa_label_div = soup.find(\"div\", {\"class\": \"fw-ticker-label\"}, string=\"NTI.USA\")\n", "\n", "    if nti_usa_label_div:\n", "        nti_usa_price_div = nti_usa_label_div.find_next_sibling(\n", "            \"div\", {\"class\": \"fw-ticker-market-price\"}\n", "        )\n", "        if nti_usa_price_div:\n", "            return float(nti_usa_price_div.text.strip())\n", "    return None\n", "\n", "\n", "# # Extracting the market price using the new format\n", "# market_price = extract_nti_ticker(content)\n", "# market_price\n", "\n", "# Parsing the HTML content to extract the \"Value\" and \"Close\" for \"NTI.USA\" from the provided structure\n", "\n", "\n", "def extract_nti_table(html_content):\n", "    \"\"\"\n", "    Extracts the Value and Close prices for \"NTI.USA\" from the given HTML content.\n", "\n", "    Args:\n", "    html_content (str): The HTML content as a string.\n", "\n", "    Returns:\n", "    tuple: A tuple containing the Value and Close prices, or (None, None) if not found.\n", "    \"\"\"\n", "    soup = BeautifulSoup(html_content, \"html.parser\")\n", "\n", "    # Finding the <tr> tag with the specified class and data-ticker-name for NTI.USA\n", "    nti_usa_tr = soup.find(\"tr\", {\"class\": \"tickerItem\", \"data-ticker-name\": \"NTI.USA\"})\n", "\n", "    # Extracting the prices if the tag is found\n", "    if nti_usa_tr:\n", "        price_cells = nti_usa_tr.find_all(\"td\")\n", "        value = price_cells[2].text.strip() if len(price_cells) > 2 else None\n", "        close = price_cells[3].text.strip() if len(price_cells) > 3 else None\n", "        return float(value[1:]), float(close[1:])\n", "    else:\n", "        return None, None\n", "\n", "\n", "# # Extracting the values using the function\n", "# value, close = extract_nti_table(content)\n", "# value, close\n", "# 20220528055925.html for sure. maybe since 2022-06-07\n", "# https://chat.openai.com/c/30c40661-2ddf-4ccc-9ea7-9eaf03fc96b3\n", "# https://www.phind.com/search?cache=mkxk3m6r85p90m1v1hf2g8kz\n", "# This has 6 lanes it seems, maybe avg them to get national avg?\n", "\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "\n", "\n", "def merge_dataframes(df_main, df_new):\n", "    \"\"\"\n", "    Merge two dataframes on Date, Value, and Close columns.\n", "    \"\"\"\n", "    return (\n", "        pd.concat([df_main, df_new])\n", "        .drop_duplicates(subset=[\"Date\"])\n", "        .reset_index(drop=True)\n", "    )\n", "\n", "\n", "def get_earliest_date(df):\n", "    \"\"\"\n", "    Returns the earliest date in the given DataFrame.\n", "    \"\"\"\n", "    return pd.to_datetime(df[\"Date\"]).min()\n", "\n", "\n", "def fetch_and_process_data(record):\n", "    \"\"\"\n", "    Fetches HTML content for a given record, extracts NTI.USA data, and returns a DataFrame.\n", "    \"\"\"\n", "    try:\n", "        response = client.get_memento(record, mode=Mode.original)\n", "    except Exception as e:\n", "        print(f\"Error fetching data: {e}\")\n", "        return None\n", "    content = response.content.decode()\n", "\n", "    # First attempt to extract data using extract_nti_graph\n", "    df_new = extract_nti_graph(content)\n", "\n", "    # # If extract_nti_graph fails to find data, use extract_nti_table\n", "    # if df_new is None or df_new.empty:\n", "    #     value, close = extract_nti_table(content)\n", "    #     if value is not None and close is not None:\n", "    #         # Create a DataFrame row from the extracted values\n", "    #         record_date = record.timestamp.strftime(\"%Y-%m-%d\")\n", "    #         df_new = pd.DataFrame(\n", "    #             {\"Date\": [record_date], \"Value\": [value], \"Close\": [close]}\n", "    #         )\n", "    #         print(\"Found data using extract_nti_table.\")\n", "\n", "    # if df_new is None or df_new.empty:\n", "    #     value = extract_nti_ticker(content)\n", "    #     if value is not None:\n", "    #         # Create a DataFrame row from the extracted values\n", "    #         record_date = record.timestamp.strftime(\"%Y-%m-%d\")\n", "    #         df_new = pd.DataFrame(\n", "    #             {\"Date\": [record_date], \"Value\": [value], \"Close\": [None]}\n", "    #         )\n", "    #         print(\"Found data using extract_nti_ticker.\")\n", "\n", "    return df_new\n", "\n", "\n", "# Assuming reversed_records is sorted from newest to oldest\n", "earliest_date_needed = get_earliest_date(nti_usa_df)\n", "\n", "for i, record in enumerate(reversed(records)):\n", "    record_timestamp = record.timestamp.replace(\n", "        tzinfo=None\n", "    )  # Assuming UTC for simplicity\n", "\n", "    # Stop if the record's timestamp is earlier than the earliest date needed\n", "    if record_timestamp.date() >= earliest_date_needed.date():\n", "        continue\n", "\n", "    print(i, record_timestamp.strftime(\"%Y-%m-%d\"))\n", "\n", "    # Fetch and process data for the current record\n", "    df_new = fetch_and_process_data(record)\n", "    if df_new is None:\n", "        print(i, record_timestamp.strftime(\"%Y-%m-%d\"))\n", "        continue\n", "\n", "    # If new data is extracted successfully, merge it with the existing DataFrame\n", "    if df_new is not None and not df_new.empty:\n", "        nti_usa_df = merge_dataframes(nti_usa_df, df_new)\n", "\n", "        # Update the earliest date needed\n", "        earliest_date_needed = get_earliest_date(nti_usa_df)\n", "\n", "\n", "# Final DataFrame\n", "print(nti_usa_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nti_usa_df[\"Date\"] = pd.to_datetime(nti_usa_df[\"Date\"])\n", "nti_usa_df[\"Value\"] = pd.to_numeric(nti_usa_df[\"Value\"])\n", "nti_usa_df[\"Close\"] = pd.to_numeric(nti_usa_df[\"Close\"])\n", "\n", "nti_usa_df[nti_usa_df[\"Date\"] == nti_usa_df[\"Date\"].min()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nti_usa_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# record = list(reversed(records))[3253]\n", "# record.timestamp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = client.get_memento(record, mode=Mode.original)\n", "content = response.content.decode()\n", "print(record.timestamp.strftime(\"%Y%m%d%H%M%S\"))\n", "\n", "bs = BeautifulSoup(content, \"html.parser\")\n", "bs"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}