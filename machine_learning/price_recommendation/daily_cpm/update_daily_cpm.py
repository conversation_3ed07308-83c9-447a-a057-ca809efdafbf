import logging
import os
import re
from typing import Optional

import requests

from common import ecr_job_util
from common import init_main
from common import s3_cache_file

PROJECT_ROOT = init_main.initialize()

from bs4 import BeautifulSoup
import json
import pandas as pd


def extract_nti_graph(html_content: str) -> Optional[pd.DataFrame]:
    """
    Parses the given HTML content to find and extract the "NTI.USA" data.

    Args:
    html_content (str): The HTML content as a string, from https://www.freightwaves.com/.

    Returns a DataFrame if successful, otherwise raise error.
    """
    soup = BeautifulSoup(html_content, "html.parser")

    # Find the script tag containing "NTI.USA"
    script_tag = soup.find("script", string=lambda x: x and "NTI.USA" in x)

    if script_tag:
        # Extract the script text
        script_text = script_tag.string

        # Find the start of the JSON-like structure
        pattern = r'\{"NTI\.USA":\[\{("Date"|Date)'
        match = re.search(pattern, script_text)
        start_index = match.start() if match else -1

        # Find the end of the JSON-like structure
        end_index = script_text.find("]}", start_index) + 2

        if start_index != -1 and end_index != -1:
            # Extract the JSON-like string
            json_like_str = script_text[start_index:end_index]

            # Convert to valid JSON by adding double quotes around keys and fixing the float values
            formatted_json_str = re.sub(
                r"(?<={|,)\s*(\w+)(?=:)", r'"\1"', json_like_str
            )
            formatted_json_str = re.sub(r"(?<=:)\.", "0.", formatted_json_str)

            try:
                # Parse the formatted JSON string
                json_data = json.loads(formatted_json_str)
                # Convert the JSON data to a DataFrame
                df = pd.DataFrame(json_data["NTI.USA"])
                df["Date"] = pd.to_datetime(df["Date"])
                df["Value"] = pd.to_numeric(df["Value"])
                df["Close"] = pd.to_numeric(df["Close"])
                return df
            except json.JSONDecodeError as e:
                raise json.JSONDecodeError(f"JSON parsing error: {e}")
        else:
            raise ValueError("Couldn't locate the JSON data in the script tag.")
    else:
        raise ValueError("'NTI.USA' not found in any script tag.")


def extract_nti_var(html_content: str) -> Optional[pd.DataFrame]:
    """
    Extracts NTI data from the given HTML content and returns it as a Pandas DataFrame.

    Args:
    html_content (str): The HTML content as a string. From https://sonar.freightwaves.com/national-truckload-index.

    Returns:
    Optional[pd.DataFrame]: A DataFrame containing the extracted data, or None if not found.
    """
    soup = BeautifulSoup(html_content, "html.parser")

    # Find the script tag containing the NTI data
    script_tag = soup.find(
        "script", string=lambda text: text and "var nti  = [{" in text
    )

    if script_tag:
        # Extract and parse the data from the script tag
        data_str = script_tag.string
        # Assuming the data is in JSON format after 'var nti  = '
        # TODO(P2): Use regex
        start = data_str.find("var nti  = [") + len("var nti  = ")
        end = data_str.find("];", start) + 1
        json_data_str = data_str[start:end]

        try:
            parsed_data = json.loads(json_data_str)
            # Convert the parsed data to a DataFrame
            df = pd.DataFrame(parsed_data)
            df["Date"] = pd.to_datetime(df["Date"])
            df["Close"] = pd.to_numeric(df["Close"])
            return df
        except json.JSONDecodeError:
            logging.error("Error parsing JSON data from the script tag.")
            return None
    else:
        return None


def clean_df(input_df: pd.DataFrame) -> pd.DataFrame:
    """
    Cleans the given DataFrame by removing rows with missing values.

    - Removes duplicate rows.
    - Converts the "Date" column to a string in the format "YYYY-MM-DD".
    - Renames the "Date" and "Close" columns to "date" and "cpm" respectively.

    args:
        input_df: The DataFrame to clean, generated by extract_nti_graph from the scraped html.

    returns:
        A dataframe with the following columns:
            date: The date of the CPM value in the format "YYYY-MM-DD".
            cpm: The CPM value for the given date.
    """
    df = input_df[["Date", "Close"]]
    if df["Close"].isna().sum() > 0:
        logging.error("Error: Some CPM values are missing.")
        # df = df.dropna()
        raise ValueError("Error: Some CPM values are missing.")
    df["Date"] = df["Date"].dt.strftime("%Y-%m-%d")
    df = df.rename(columns={"Date": "date", "Close": "cpm"})
    # Assume there are no duplicate dates
    if df["date"].duplicated().any():
        logging.warning("Duplicate dates found.")
        df.drop_duplicates(subset=["date"], inplace=True)
        # raise ValueError("Duplicate dates found.")
    return df.set_index("date")


def main():
    # TODO(P1): #1767 Use cloudwatch_log_utils.py to log the start of the job.
    JOB_NAME = "Daily CPM"
    (
        job_start_time,
        job_start_timestr_central,
        mail_api,
        log_filename,
    ) = ecr_job_util.set_up(JOB_NAME, set_up_logging_flag=True)
    mail_report_attachments = [log_filename]
    file_dir = os.path.join(
        PROJECT_ROOT, "machine_learning", "price_recommendation", "daily_cpm", "data"
    )

    # This used to work until 2/21/2024
    # html_url = "https://www.freightwaves.com/"
    # html_extractor = extract_nti_graph

    # This works as of 2/23/2024, but there seems to be a 5-day delay in the data
    html_url = "https://sonar.freightwaves.com/national-truckload-index"
    html_extractor = extract_nti_var

    try:
        HEADERS = {
            "User-Agent": "Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
        }

        r = requests.get(html_url, headers=HEADERS)
        # Save the HTML content to a file for debugging
        now_str = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        html_filename = os.path.join(file_dir, f"freightwaves_{now_str}.html")
        with open(html_filename, "w", encoding="utf-8") as f:
            f.write(r.text)
        # The HTML file triggers gmail virus protection, killing both the success and failure emails
        # mail_report_attachments.append(html_filename)
        freightwaves_df = html_extractor(r.text)

        cpm_df = clean_df(freightwaves_df)

        daily_cpm_s3 = s3_cache_file.S3CacheFile(
            "truce-ml-data", "data/daily_cpm.csv", "date"
        )
        daily_cpm_s3.df = cpm_df
        daily_cpm_s3.save_and_upload()

        last_cpm = cpm_df["cpm"].iloc[-1]
        mail_report_attachments.append(daily_cpm_s3.filename)

        ecr_job_util.success_email(
            mail_api=mail_api,
            dev=False,
            job_name=JOB_NAME,
            job_start_time=job_start_time,
            job_start_timestr_central=job_start_timestr_central,
            mail_header=f" CPM: ${last_cpm:.2f}",
            mail_body="Uploaded df len: " + str(len(cpm_df)),
            mail_report_attachments=mail_report_attachments,
        )
    except Exception as e:
        ecr_job_util.failure_email(
            e=e,
            mail_api=mail_api,
            dev=False,
            job_name=JOB_NAME,
            job_start_time=job_start_time,
            job_start_timestr_central=job_start_timestr_central,
            mail_body="Failed to upload CPM to S3. Error:\n" + str(e),
            mail_report_attachments=mail_report_attachments,
        )


if __name__ == "__main__":
    main()
