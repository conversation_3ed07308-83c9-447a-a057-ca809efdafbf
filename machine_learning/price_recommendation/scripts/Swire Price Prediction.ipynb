{"cells": [{"cell_type": "code", "execution_count": null, "id": "beb2b2df-c1a6-43d4-abf1-b6d3e170059b", "metadata": {}, "outputs": [], "source": ["# Ensure common and other packages are installed.\n", "%pip install ../../.\n", "%pip install boto3 googlemaps seaborn pandas fredapi geopandas matplotlib descartes holidays openpyxl tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "17fb16e8-2b49-4d7b-9310-b81972736b91", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import time\n", "from common import query_utils\n", "from tqdm import tqdm\n", "import numpy as np\n", "\n", "records_df = pd.DataFrame()\n", "\n", "QUERY_STR = \"\"\"SELECT\n", "    * FROM shippers;\"\"\"\n", "\n", "records = query_utils.query(QUERY_STR, \"mvp_db_dev\")\n", "shippers_df = query_utils.append_records_to_df(records, records_df)\n", "shippers_df"]}, {"cell_type": "code", "execution_count": null, "id": "2e0aa231-aa05-4c57-801c-638351711b88", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"data/rds_raw.csv\")\n", "# swire dev id\n", "df = df[df[\"shipperId\"] == \"015650d2-8d60-46c6-908e-f3e484601b0c\"]\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "9916ce16-9ca9-4ae5-bed0-ef31d0a214e1", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "import matplotlib.cm as cm\n", "from matplotlib.dates import YearLocator, DateFormatter\n", "\n", "\n", "def calculate_cpm(df):\n", "    \"\"\"\n", "    Calculates the cost per mile (cpm).\n", "    \"\"\"\n", "    df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "    df[\"cpm\"] = (df[\"cogsTotal\"] - df[\"cogsAccessorial\"]) / df[\"distanceMiles\"]\n", "    return df\n", "\n", "\n", "def calculate_margin(df):\n", "    \"\"\"\n", "    Calculates the margin.\n", "    \"\"\"\n", "    df[\"revenueAccessorial\"].fillna(0.0, inplace=True)\n", "    df[\"revenue\"] = df[\"revenueTotal\"] - df[\"revenueAccessorial\"]\n", "    df[\"margin\"] = df[\"revenue\"] - (df[\"cogsTotal\"] - df[\"cogsAccessorial\"])\n", "    return df\n", "\n", "\n", "def filter_outliers(df, col_name):\n", "    \"\"\"\n", "    Filters outliers based on the IQR method.\n", "    \"\"\"\n", "    Q1 = df[col_name].quantile(0.25)\n", "    Q3 = df[col_name].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    df = df[~((df[col_name] < (Q1 - 1.5 * IQR)) | (df[col_name] > (Q3 + 1.5 * IQR)))]\n", "    return df\n", "\n", "\n", "def plot_cpm(df, lower_bound, upper_bound):\n", "    \"\"\"\n", "    Creates the cpm plot with lanes color coded.\n", "    \"\"\"\n", "    # Calculate average cpm per lane and separate lanes into in bounds and out of bounds\n", "    average_cpm = df.groupby(\"laneId\")[\"cpm\"].mean()\n", "    in_bounds_lanes = average_cpm[\n", "        (average_cpm >= lower_bound) & (average_cpm <= upper_bound)\n", "    ].sort_values(ascending=False)\n", "    out_bounds_lanes = average_cpm[\n", "        ~average_cpm.index.isin(in_bounds_lanes.index)\n", "    ].sort_values(ascending=False)\n", "\n", "    # Create a colormap for in-bounds and out-bounds lanes\n", "    colors_in = cm.Blues(np.linspace(0, 1, len(in_bounds_lanes)))\n", "    colors_out = cm.Reds(np.linspace(0, 1, len(out_bounds_lanes)))\n", "    colormap_in = dict(zip(in_bounds_lanes.index, colors_in))\n", "    colormap_out = dict(zip(out_bounds_lanes.index, colors_out))\n", "    colormap = {**colormap_in, **colormap_out}\n", "\n", "    # Get details for the top 10 lanes\n", "    top_lanes = df[df[\"laneId\"].isin(in_bounds_lanes.index[:10])]\n", "    lane_details = top_lanes.groupby(\"laneId\").first()[\n", "        [\"originCity\", \"originState\", \"destinationCity\", \"destinationState\"]\n", "    ]\n", "\n", "    # Create cpm plot\n", "    plt.figure(figsize=(10, 6))\n", "    patches = []\n", "\n", "    for lane in average_cpm.index:\n", "        lane_data = df[df[\"laneId\"] == lane]\n", "        plt.scatter(\n", "            lane_data[\"originCloseTime\"], lane_data[\"cpm\"], color=colormap[lane], s=1\n", "        )\n", "\n", "        # If the lane is in the top 10, add it to the legend\n", "        if lane in lane_details.index:\n", "            origin = f\"{lane_details.loc[lane, 'originCity']}, {lane_details.loc[lane, 'originState']}\"\n", "            destination = f\"{lane_details.loc[lane, 'destinationCity']}, {lane_details.loc[lane, 'destinationState']}\"\n", "            patches.append(\n", "                mpatches.Patch(color=colormap[lane], label=f\"{origin} → {destination}\")\n", "            )\n", "\n", "    # Add horizontal bounding lines\n", "    plt.axhline(y=lower_bound, color=\"r\", linestyle=\"--\")\n", "    plt.axhline(y=upper_bound, color=\"g\", linestyle=\"--\")\n", "\n", "    # Add labels\n", "    plt.xlabel(\"Origin Close Time\")\n", "    plt.ylabel(\"Cost per Mile ($/mi)\")\n", "    plt.title(\"Cost per Mile over Time\")\n", "\n", "    # Add the legend outside the plot\n", "    plt.legend(handles=patches, loc=\"upper left\", bbox_to_anchor=(1, 1))\n", "    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter(\"%b '%y\"))\n", "    plt.show()\n", "\n", "\n", "def plot_margin(df):\n", "    \"\"\"\n", "    Creates the margin plot with lanes color coded.\n", "    \"\"\"\n", "    # Calculate average margin per lane\n", "    average_margin = df.groupby(\"laneId\")[\"margin\"].mean()\n", "    colors = cm.BrBG(np.linspace(0, 1, len(average_margin)))\n", "    colormap = dict(zip(average_margin.index, colors))\n", "\n", "    # Create margin plot\n", "    plt.figure(figsize=(10, 6))\n", "    for lane in average_margin.index:\n", "        lane_data = df[df[\"laneId\"] == lane]\n", "        plt.scatter(\n", "            lane_data[\"originCloseTime\"], lane_data[\"margin\"], color=colormap[lane], s=1\n", "        )\n", "\n", "    # Add labels\n", "    plt.xlabel(\"Origin Close Time\")\n", "    plt.ylabel(\"Margin ($)\")\n", "    plt.title(\"Margin over Time\")\n", "    plt.legend(handles=patches, loc=\"upper left\", bbox_to_anchor=(1, 1))\n", "    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter(\"%b '%y\"))\n", "    plt.show()\n", "\n", "\n", "# Load your data (replace 'your_data.csv' with your actual data file)\n", "df = pd.read_csv(\"data/rds_raw.csv\")\n", "# Convert 'originCloseTime' column to datetime format\n", "df[\"originCloseTime\"] = pd.to_datetime(df[\"originCloseTime\"])\n", "# swire dev id\n", "df = df[df[\"shipperId\"] == \"015650d2-8d60-46c6-908e-f3e484601b0c\"]\n", "\n", "# Filter data for distance more than 300 miles\n", "df = df[df[\"distanceMiles\"] > 300]\n", "\n", "# Calculate cpm\n", "df = calculate_cpm(df)\n", "\n", "# Calculate margin\n", "df = calculate_margin(df)\n", "\n", "# Filter outliers in cpm\n", "df = filter_outliers(df, \"cpm\")\n", "\n", "# Set lower and upper bounds for cpm\n", "lower_bound = 2.15\n", "upper_bound = 2.4\n", "\n", "# Create cpm plot\n", "plot_cpm(df, lower_bound, upper_bound)\n", "# Create margin plot\n", "plot_margin(df)"]}, {"cell_type": "code", "execution_count": null, "id": "930d329c-547a-4520-9f63-62ed1f128537", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "import matplotlib.cm as cm\n", "from matplotlib.dates import YearLocator, DateFormatter\n", "\n", "def calculate_cpm(df):\n", "    '''\n", "    Calculates the cost per mile (cpm).\n", "    '''\n", "    df['cogsAccessorial'].fillna(0.0, inplace=True)\n", "    df['cpm'] = (df['cogsTotal'] - df['cogsAccessorial']) / df['distanceMiles']\n", "    return df\n", "\n", "def calculate_margin(df):\n", "    '''\n", "    Calculates the margin.\n", "    '''\n", "    df['revenueAccessorial'].fillna(0.0, inplace=True)\n", "    df['revenue'] = df['revenueTotal'] - df['revenueAccessorial']\n", "    df['margin'] = df['revenue'] - (df['cogsTotal'] - df['cogsAccessorial'])\n", "    return df\n", "\n", "def filter_outliers(df, col_name):\n", "    '''\n", "    Filters outliers based on the IQR method.\n", "    '''\n", "    Q1 = df[col_name].quantile(0.25)\n", "    Q3 = df[col_name].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    df = df[~((df[col_name] < (Q1 - 1.5 * IQR)) |(df[col_name] > (Q3 + 1.5 * IQR)))]\n", "    return df\n", "\n", "def plot_cpm(df, lower_bound, upper_bound):\n", "    '''\n", "    Creates the cpm plot with lanes color coded.\n", "    '''\n", "    # Calculate average cpm per lane and separate lanes into in bounds and out of bounds\n", "    average_cpm = df.groupby('laneId')['cpm'].mean()\n", "    in_bounds_lanes = average_cpm[(average_cpm >= lower_bound) & (average_cpm <= upper_bound)].sort_values(ascending=False)\n", "    out_bounds_lanes = average_cpm[~average_cpm.index.isin(in_bounds_lanes.index)].sort_values(ascending=False)\n", "\n", "    # Create a colormap for in-bounds and out-bounds lanes\n", "    colors_in = cm.Blues(np.linspace(0, 1, len(in_bounds_lanes)))\n", "    colors_out = cm.Reds(np.linspace(0, 1, len(out_bounds_lanes)))\n", "    colormap_in = dict(zip(in_bounds_lanes.index, colors_in))\n", "    colormap_out = dict(zip(out_bounds_lanes.index, colors_out))\n", "    colormap = {**colormap_in, **colormap_out}\n", "\n", "    # Get details for the top 10 lanes\n", "    top_lanes = df[df['laneId'].isin(in_bounds_lanes.index[:10])]\n", "    lane_details = top_lanes.groupby('laneId').first()[['originCity', 'originState', 'destinationCity', 'destinationState']]\n", "\n", "    # Create cpm plot\n", "    plt.figure(figsize=(10,6))\n", "    patches = []\n", "\n", "    for lane in average_cpm.index:\n", "        lane_data = df[df['laneId'] == lane]\n", "        plt.scatter(lane_data['originCloseTime'], lane_data['cpm'], color=colormap[lane], s=1)\n", "\n", "        # If the lane is in the top 10, add it to the legend\n", "        if lane in lane_details.index:\n", "            origin = f\"{lane_details.loc[lane, 'originCity']}, {lane_details.loc[lane, 'originState']}\"\n", "            destination = f\"{lane_details.loc[lane, 'destinationCity']}, {lane_details.loc[lane, 'destinationState']}\"\n", "            patches.append(mpatches.Patch(color=colormap[lane], label=f'{origin} → {destination}'))\n", "\n", "    # Add horizontal bounding lines\n", "    plt.axhline(y=lower_bound, color='r', linestyle='--')\n", "    plt.axhline(y=upper_bound, color='g', linestyle='--')\n", "\n", "    # Add labels\n", "    plt.xlabel('originCloseTime')\n", "    plt.ylabel('cpm')\n", "    plt.title('cpm over Time')\n", "\n", "    # Add the legend outside the plot\n", "    plt.legend(handles=patches, loc='upper left', bbox_to_anchor=(1,1))\n", "\n", "    plt.show()\n", "\n", "def plot_margin(df):\n", "    '''\n", "    Creates the margin plot with lanes color coded.\n", "    '''\n", "    # Calculate average margin per lane\n", "    average_margin = df.groupby('laneId')['margin'].mean()\n", "    colors = cm.BrBG(np.linspace(0, 1, len(average_margin)))\n", "    colormap = dict(zip(average_margin.index, colors))\n", "\n", "    # Create margin plot\n", "    plt.figure(figsize=(10,6))\n", "    for lane in average_margin.index:\n", "        lane_data = df[df['laneId'] == lane]\n", "        plt.scatter(lane_data['originCloseTime'], lane_data['margin'], color=colormap[lane], s=1)\n", "\n", "    # Add labels\n", "    plt.xlabel('originCloseTime')\n", "    plt.ylabel('Margin')\n", "    plt.title('Margin over Time')\n", "\n", "    plt.show()\n", "\n", "# Load your data (replace 'your_data.csv' with your actual data file)\n", "df = pd.read_csv('data/rds_raw.csv')\n", "# Convert 'originCloseTime' column to datetime format\n", "df['originCloseTime'] = pd.to_datetime(df['originCloseTime'])\n", "# swire dev id\n", "df = df[df['shipperId'] == '015650d2-8d60-46c6-908e-f3e484601b0c']\n", "\n", "# Filter data for distance more than 300 miles\n", "df = df[df['distanceMiles'] > 300]\n", "\n", "# Calculate cpm\n", "df = calculate_cpm(df)\n", "\n", "# Calculate margin\n", "df = calculate_margin(df)\n", "\n", "# Filter outliers in cpm\n", "# df = filter_outliers(df, 'cpm')\n", "\n", "# Set lower and upper bounds for cpm\n", "lower_bound = 2.15\n", "upper_bound = 2.4\n", "\n", "# Create cpm plot\n", "plot_cpm(df, lower_bound, upper_bound)\n", "# Create margin plot\n", "plot_margin(df)\n", "\n", "How can I modify this above code to\n", "- Move the lane-coloring to a function\n", "- change the x-axis labels to be \"May '23\" instead of \"2023-05\""]}, {"cell_type": "code", "execution_count": null, "id": "e5b419d8-ee48-4486-af2f-dddeedf776ac", "metadata": {"tags": []}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "cpm_df = df[df[\"distanceMiles\"] > 300]\n", "cpm_df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "cpm_df[\"cpm\"] = (cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"]) / cpm_df[\n", "    \"distanceMiles\"\n", "]\n", "cpm_df[\"revenueAccessorial\"].fillna(0.0, inplace=True)\n", "cpm_df[\"revenue\"] = cpm_df[\"revenueTotal\"] - cpm_df[\"revenueAccessorial\"]\n", "cpm_df[\"margin\"] = (\n", "    cpm_df[\"revenue\"] - (cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"])\n", ") / cpm_df[\"revenue\"]\n", "\n", "# Create plot\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(cpm_df[\"originCloseTime\"], cpm_df[\"cpm\"], label=\"cpm\", s=1)\n", "\n", "# Add horizontal bounding lines\n", "plt.axhline(y=2.15, color=\"r\", linestyle=\"--\", label=\"Lower bound\")\n", "plt.axhline(y=2.4, color=\"g\", linestyle=\"--\", label=\"Upper bound\")\n", "\n", "# Add labels and legend\n", "plt.xlabel(\"originCloseTime\")\n", "plt.ylabel(\"cpm\")\n", "plt.title(\"cpm over Time\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ce40cfb4-ccd8-4126-ab4f-378c1cc8b4ae", "metadata": {}, "outputs": [], "source": ["# Prepare the data\n", "import matplotlib.patches as mpatches\n", "\n", "cpm_df = df[df[\"distanceMiles\"] > 300]\n", "cpm_df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "cpm_df[\"cpm\"] = (cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"]) / cpm_df[\n", "    \"distanceMiles\"\n", "]\n", "\n", "# Calculate IQR for cpm\n", "Q1_cpm = cpm_df[\"cpm\"].quantile(0.25)\n", "Q3_cpm = cpm_df[\"cpm\"].quantile(0.75)\n", "IQR_cpm = Q3_cpm - Q1_cpm\n", "\n", "# Filter out the outliers from cpm\n", "cpm_df = cpm_df[\n", "    ~(\n", "        (cpm_df[\"cpm\"] < (Q1_cpm - 1.5 * IQR_cpm))\n", "        | (cpm_df[\"cpm\"] > (Q3_cpm + 1.5 * IQR_cpm))\n", "    )\n", "]\n", "\n", "# Set bounds\n", "lower_bound = 2.15\n", "upper_bound = 2.4\n", "\n", "# Calculate average cpm per lane and separate lanes into in bounds and out of bounds\n", "average_cpm = cpm_df.groupby(\"laneId\")[\"cpm\"].mean()\n", "in_bounds_lanes = average_cpm[\n", "    (average_cpm >= lower_bound) & (average_cpm <= upper_bound)\n", "].sort_values(ascending=False)\n", "out_bounds_lanes = average_cpm[\n", "    ~average_cpm.index.isin(in_bounds_lanes.index)\n", "].sort_values(ascending=False)\n", "\n", "# Create a colormap for in-bounds and out-bounds lanes\n", "colors_in = cm.Blues(np.linspace(0, 1, len(in_bounds_lanes)))\n", "colors_out = cm.Reds(np.linspace(0, 1, len(out_bounds_lanes)))\n", "colormap_in = dict(zip(in_bounds_lanes.index, colors_in))\n", "colormap_out = dict(zip(out_bounds_lanes.index, colors_out))\n", "colormap = {**colormap_in, **colormap_out}\n", "\n", "# Get details for the top 10 lanes\n", "top_lanes = cpm_df[cpm_df[\"laneId\"].isin(in_bounds_lanes.index[:10])]\n", "lane_details = top_lanes.groupby(\"laneId\").first()[\n", "    [\"originCity\", \"originState\", \"destinationCity\", \"destinationState\"]\n", "]\n", "\n", "# Create cpm plot\n", "plt.figure(figsize=(10, 6))\n", "patches = []\n", "\n", "for lane in average_cpm.index:\n", "    lane_data = cpm_df[cpm_df[\"laneId\"] == lane]\n", "    plt.scatter(\n", "        lane_data[\"originCloseTime\"], lane_data[\"cpm\"], color=colormap[lane], s=1\n", "    )\n", "\n", "    # If the lane is in the top 10, add it to the legend\n", "    if lane in lane_details.index:\n", "        origin = f\"{lane_details.loc[lane, 'originCity']}, {lane_details.loc[lane, 'originState']}\"\n", "        destination = f\"{lane_details.loc[lane, 'destinationCity']}, {lane_details.loc[lane, 'destinationState']}\"\n", "        patches.append(\n", "            mpatches.Patch(color=colormap[lane], label=f\"{origin} → {destination}\")\n", "        )\n", "\n", "# Add horizontal bounding lines\n", "plt.axhline(y=lower_bound, color=\"r\", linestyle=\"--\")\n", "plt.axhline(y=upper_bound, color=\"g\", linestyle=\"--\")\n", "\n", "# Add labels\n", "plt.xlabel(\"originCloseTime\")\n", "plt.ylabel(\"cpm\")\n", "plt.title(\"cpm over Time\")\n", "\n", "# Add the legend outside the plot\n", "plt.legend(handles=patches, loc=\"upper left\", bbox_to_anchor=(1, 1))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4dc94eeb-7354-41a2-89f1-d090bb9a4a56", "metadata": {}, "outputs": [], "source": ["import matplotlib.cm as cm\n", "\n", "# Prepare the data\n", "cpm_df = df[df[\"distanceMiles\"] > 300]\n", "cpm_df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "cpm_df[\"cpm\"] = (cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"]) / cpm_df[\n", "    \"distanceMiles\"\n", "]\n", "cpm_df[\"revenueAccessorial\"].fillna(0.0, inplace=True)\n", "cpm_df[\"revenue\"] = cpm_df[\"revenueTotal\"] - cpm_df[\"revenueAccessorial\"]\n", "cpm_df[\"margin\"] = cpm_df[\"revenue\"] - (cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"])\n", "\n", "# Calculate margin in dollars\n", "cpm_df[\"margin_dollars\"] = cpm_df[\"revenue\"] - (\n", "    cpm_df[\"cogsTotal\"] - cpm_df[\"cogsAccessorial\"]\n", ")\n", "\n", "# Calculate average margin per lane\n", "average_margins = (\n", "    cpm_df.groupby(\"laneId\")[\"margin_dollars\"].mean().sort_values(ascending=False)\n", ")\n", "\n", "# Create a colormap for laneId\n", "colors = cm.BrBG(np.linspace(0, 1, len(average_margins)))\n", "colormap = dict(zip(average_margins.index, colors))\n", "\n", "# Get details for the top 10 lanes\n", "top_lanes = cpm_df[cpm_df[\"laneId\"].isin(average_margins.index[:10])]\n", "lane_details = top_lanes.groupby(\"laneId\").first()[\n", "    [\"originCity\", \"originState\", \"destinationCity\", \"destinationState\"]\n", "]\n", "\n", "# Create margin plot\n", "plt.figure(figsize=(10, 6))\n", "patches = []\n", "\n", "for lane in average_margins.index:\n", "    lane_data = cpm_df[cpm_df[\"laneId\"] == lane]\n", "    plt.scatter(\n", "        lane_data[\"originCloseTime\"],\n", "        lane_data[\"margin_dollars\"],\n", "        color=colormap[lane],\n", "        s=1,\n", "    )\n", "\n", "    # If the lane is in the top 10, add it to the legend\n", "    if lane in lane_details.index:\n", "        origin = f\"{lane_details.loc[lane, 'originCity']}, {lane_details.loc[lane, 'originState']}\"\n", "        destination = f\"{lane_details.loc[lane, 'destinationCity']}, {lane_details.loc[lane, 'destinationState']}\"\n", "        patches.append(\n", "            mpatches.Patch(color=colormap[lane], label=f\"{origin} → {destination}\")\n", "        )\n", "\n", "# Add labels\n", "plt.xlabel(\"originCloseTime\")\n", "plt.ylabel(\"Margin ($)\")\n", "plt.title(\"Margin over Time\")\n", "\n", "# Add the legend outside the plot\n", "plt.legend(handles=patches, loc=\"upper left\", bbox_to_anchor=(1, 1))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ac29299c-6d64-4ca1-8323-c5cad58d1dd2", "metadata": {}, "outputs": [], "source": ["# First, convert 'originCloseTime' to datetime if it's not already\n", "df[\"originCloseTime\"] = pd.to_datetime(df[\"originCloseTime\"])\n", "\n", "# Define your time period - Q4'22\n", "start_date = \"2022-7-01\"\n", "end_date = \"2023-7-01\"\n", "\n", "# Filter df for this time period\n", "q4_df = df[(df[\"originCloseTime\"] >= start_date) & (df[\"originCloseTime\"] <= end_date)]\n", "\n", "# Then, group by 'laneId' and count the number of shipments\n", "shipment_counts = q4_df.groupby(\"laneId\")[\"cogsTotal\"].count()\n", "\n", "# Filter out lanes with fewer than 100 shipments\n", "filtered_df = q4_df[q4_df[\"laneId\"].isin(shipment_counts[shipment_counts >= 100].index)]\n", "\n", "# Calculate variance of 'cogsTotal' for each lane\n", "variances = filtered_df.groupby(\"laneId\")[\"cogsTotal\"].var()\n", "\n", "# Get top 10 lanes by variance\n", "highest_variances = variances.nlargest(10)\n", "\n", "# Print origin and destination information for these lanes\n", "for lane_id in highest_variances.index:\n", "    lane_info = df[df[\"laneId\"] == lane_id].iloc[0]\n", "    # print(f\"Lane ID: {lane_id}\")\n", "    # print(f\"Origin: {lane_info['originCity']}, {lane_info['originState']}\")\n", "    # print(f\"Destination: {lane_info['destinationCity']}, {lane_info['destinationState']}\")\n", "    # print(f\"Number of shipments: {shipment_counts[lane_id]}\")\n", "    # print(f\"Variance: {highest_variances[lane_id]}\")\n", "    # print(\"-------------------\")"]}, {"cell_type": "code", "execution_count": null, "id": "d5abb76a-5aff-4c7e-adae-a8e9cc369031", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Ensure 'originCloseTime' is treated as a datetime object\n", "filtered_df[\"originCloseTime\"] = pd.to_datetime(filtered_df[\"originCloseTime\"])\n", "\n", "# Calculate the variances and get the top 10 'laneId's\n", "variances = filtered_df.groupby(\"laneId\")[\"cogsTotal\"].apply(lambda x: x.dropna().var())\n", "highest_variances = variances.nlargest(10)\n", "\n", "# Initialize a DataFrame to store the results\n", "results = pd.DataFrame()\n", "\n", "for lane_id in highest_variances.index:\n", "    # Filter the original DataFrame to get the rows with the current 'laneId'\n", "    lane_df = filtered_df[filtered_df[\"laneId\"] == lane_id]\n", "\n", "    # Get the 'originCity', 'originState', 'destinationCity', and 'destinationState'\n", "    origin_city = lane_df[\"originCity\"].iloc[0]\n", "    origin_state = lane_df[\"originState\"].iloc[0]\n", "    destination_city = lane_df[\"destinationCity\"].iloc[0]\n", "    destination_state = lane_df[\"destinationState\"].iloc[0]\n", "\n", "    # Count the number of shipments\n", "    shipment_count = lane_df.shape[0]\n", "\n", "    # Append the information to the results DataFrame\n", "    results = results.append(\n", "        {\n", "            \"laneId\": lane_id,\n", "            \"originCity\": origin_city,\n", "            \"originState\": origin_state,\n", "            \"destinationCity\": destination_city,\n", "            \"destinationState\": destination_state,\n", "            \"shipmentCount\": shipment_count,\n", "            \"variance\": highest_variances[lane_id],\n", "        },\n", "        ignore_index=True,\n", "    )\n", "\n", "# Print the results\n", "print(results)\n", "\n", "# Plot 'cogsTotal' for the top 3 lanes\n", "import matplotlib.dates as mdates\n", "\n", "import seaborn as sns\n", "from pandas.plotting import register_matplotlib_converters\n", "from scipy.ndimage.filters import gaussian_filter1d\n", "\n", "register_matplotlib_converters()\n", "\n", "# Plot 'cogsTotal' for the top 3 lanes\n", "for i, lane_id in enumerate(highest_variances.index[:5]):\n", "    lane_df = filtered_df[filtered_df[\"laneId\"] == lane_id]\n", "    origin = lane_df[\"originCity\"].iloc[0] + \", \" + lane_df[\"originState\"].iloc[0]\n", "    destination = (\n", "        lane_df[\"destinationCity\"].iloc[0] + \", \" + lane_df[\"destinationState\"].iloc[0]\n", "    )\n", "\n", "    # Plot cogsTotal scatter plot\n", "    fig, ax = plt.subplots(figsize=(15, 5))\n", "    ax.scatter(lane_df[\"originCloseTime\"], lane_df[\"cogsTotal\"], label=\"cogsTotal\")\n", "\n", "    # Create a second y-axis\n", "    ax2 = ax.twinx()\n", "\n", "    # Plot smoothed standard deviation line per week\n", "    lane_df.set_index(\"originCloseTime\", inplace=True)\n", "    resampled = lane_df[\"cogsTotal\"].resample(\"W\").std()\n", "\n", "    # Handle NaNs with forward fill\n", "    resampled.fillna(method=\"ffill\", inplace=True)\n", "\n", "    ysmoothed = gaussian_filter1d(resampled, sigma=2)\n", "    ax2.plot(resampled.index, ysmoothed, color=\"red\", label=\"standard deviation\")\n", "\n", "    ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%b '%y\"))  # Changed line\n", "    plt.xticks(rotation=45)\n", "    ax.set_xlabel(\"originCloseTime\")\n", "    ax.set_ylabel(\"cogsTotal\", color=\"blue\")\n", "    ax2.set_ylabel(\"Standard Deviation\")\n", "    plt.title(f\"{origin} → {destination}\")\n", "    ax.legend(loc=\"upper left\")\n", "    ax2.legend(loc=\"upper right\")\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "88b18ef1-0c07-494d-bb0d-6ae70929c41d", "metadata": {}, "outputs": [], "source": ["import matplotlib.patheffects as pe\n", "\n", "plot_df = df.copy()\n", "\n", "LINE_COLOR = \"red\"\n", "OUTLINE_COLOR = \"red\"\n", "\n", "fig, ax = plt.subplots(figsize=(15, 5))\n", "# Convert laneId to categorical codes\n", "lane_codes = plot_df[\"laneId\"].astype(\"category\").cat.codes\n", "ax.scatter(\n", "    plot_df[\"originCloseTime\"],\n", "    plot_df[\"cogsTotal\"],\n", "    c=\"k\",\n", "    label=\"cogsTotal\",\n", "    s=5,\n", "    alpha=0.1,\n", ")\n", "# Create a second y-axis\n", "ax2 = ax.twinx()\n", "\n", "# Calculate the coefficient of variation\n", "plot_df.set_index(\"originCloseTime\", inplace=True)\n", "resampled_mean = plot_df[\"cogsTotal\"].resample(\"W\").mean()\n", "resampled_std = plot_df[\"cogsTotal\"].resample(\"W\").std()\n", "coefficient_of_variation = (resampled_std / resampled_mean) * 100\n", "\n", "ysmoothed_cv = gaussian_filter1d(coefficient_of_variation, sigma=2)\n", "\n", "line = ax2.plot(\n", "    resampled_std.index,\n", "    ysmoothed_cv,\n", "    color=LINE_COLOR,\n", "    linewidth=3,\n", "    label=\"Coefficient of Variation\",\n", ")\n", "line[0].set_path_effects(\n", "    [pe.Stroke(linewidth=3, foreground=OUTLINE_COLOR), pe.Normal()]\n", ")\n", "\n", "ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%y-%m-%d\"))\n", "plt.xticks(rotation=45)\n", "ax.set_xlabel(\"Origin Close Time\")\n", "ax.set_ylabel(\"Truck Cost\", color=\"black\")\n", "ax2.set_ylabel(\"Coefficient of Variation\")\n", "plt.title(f\"Volatility of Truck Costs\")\n", "ax.legend(loc=\"upper left\")\n", "ax2.legend(loc=\"upper right\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "51a3d8f8-9706-4c19-915d-38a4bcac7d43", "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "\n", "# Set the size of the plot\n", "plt.figure(figsize=(10, 5))\n", "\n", "# Create the histogram with a density curve\n", "sns.histplot(data=last_week_data, kde=True, bins=20, color=\"black\")\n", "\n", "# Set the labels and title\n", "plt.xlabel(\"Truck Cost\")\n", "plt.ylabel(\"Density\")\n", "plt.title(\"Last Week's Truck Cost Histogram with <PERSON><PERSON> Plot\")\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}