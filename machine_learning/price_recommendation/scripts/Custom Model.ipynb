{"cells": [{"cell_type": "markdown", "id": "5d9ff4d2-6174-41fa-a7e1-f0add4071f3b", "metadata": {}, "source": ["# Imports and Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "9ad0129b-6261-41bb-b65d-c68f2e4c3444", "metadata": {"tags": []}, "outputs": [], "source": ["# https://www.tensor|flow.org/install/source#gpu See this table for supported CUDA and cuDNN versions for different TensorFlow versions.\n", "%pip install boto3 pandas matplotlib tensorflow==2.11 sklearn scikit-learn google-cloud-aiplatform ../../../."]}, {"cell_type": "code", "execution_count": null, "id": "55717d4b-03a1-40f8-b889-bb14315238e4", "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()"]}, {"cell_type": "markdown", "id": "d977aea2-8b01-477b-985a-f54df406f148", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["# Load and Process Data"]}, {"cell_type": "code", "execution_count": null, "id": "29c66c43-b844-4226-a8a8-9e649ff79959", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "\n", "# Make numpy values easier to read.\n", "np.set_printoptions(precision=3, suppress=True)\n", "\n", "from common.s3_cache_file import S3\n", "\n", "s3_ml_data = S3(\"truce-ml-data\")\n", "# ml_df_filename = \"data/pricing_ml_df_v0.csv\"\n", "# ml_df_filename = \"data/pricing_ml_df_2023-05-10_18-58-13.csv\"\n", "\n", "\n", "def load_ml_data(filename: str, index_col=None) -> pd.DataFrame:\n", "    s3_ml_data.pull(filename, merge=False)\n", "    return pd.read_csv(filename, index_col=index_col)\n", "\n", "\n", "# Sheer training + RDS full data\n", "sheer_rds_train_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"rds_sheer_weighted_pricing_train.csv\"))\n", "\n", "# Sheer training data\n", "sheer_train_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"sheer_pricing_train.csv\"))\n", "\n", "# Sheer validation data\n", "sheer_val_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"sheer_pricing_val_test.csv\"))\n", "\n", "# RDS full data\n", "rds_pricing_full_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"rds_pricing_full.csv\"), index_col=\"id\")\n", "rds_pricing_train_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"rds_pricing_train.csv\"))\n", "rds_pricing_val_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"rds_pricing_val.csv\"))\n", "rds_pricing_test_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"rds_pricing_test.csv\"))\n", "\n", "# Mystery data\n", "mystery_data_full_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"mystery_full.csv\"))\n", "\n", "# ML Scrape Data\n", "ml_scrape_df = load_ml_data(os.path.join(PROJECT_ROOT, \"machine_learning\", \"price_recommendation\", \"data\", \"ml_scrape_full.csv\"))"]}, {"cell_type": "code", "execution_count": null, "id": "5430c0c1-1a2c-4e9b-ac17-a1c98343a95a", "metadata": {}, "outputs": [], "source": ["# Add data source column\n", "rds_pricing_full_df[\"data_source\"] = \"rds_pricing_full\"\n", "mystery_data_full_df[\"data_source\"] = \"mystery_data_full\"\n", "ml_scrape_df[\"data_source\"] = \"ml_scrape_\" + ml_scrape_df[\"board\"]\n", "ml_scrape_df[\"data_source\"] = ml_scrape_df[\"data_source\"].apply(lambda x: x.lower())\n", "\n", "combined_df = pd.concat([rds_pricing_full_df, mystery_data_full_df, ml_scrape_df])\n", "combined_df[\"data_source\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "0b768007-de14-4093-a1c3-d4d766f5eabf", "metadata": {}, "outputs": [], "source": ["# Clean, add noise, and convert to datetime\n", "time_fields = [\n", "    \"origin_open_time\",\n", "    \"origin_close_time\",\n", "    \"destination_open_time\",\n", "    \"destination_close_time\",\n", "]\n", "for time_field in time_fields:\n", "    combined_df[time_field] = pd.to_datetime(combined_df[time_field])\n", "\n", "\n", "# Randomize +/- $3 to OUTPUT_cogs_total\n", "combined_df[\"OUTPUT_cogs_total\"] = combined_df[\"OUTPUT_cogs_total\"].apply(\n", "    lambda x: x + np.random.uniform(-3, 3)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ad552c5a", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import seaborn as sns\n", "\n", "\n", "def plot_avg_price_by_month(\n", "    df,\n", "    category_col,\n", "    date_col=\"origin_close_time\",\n", "    value_col,\n", "    min_samples_per_month=100,\n", "    period=\"W\",\n", "):\n", "    \"\"\"\n", "    Plots the average of OUTPUT_cogs_total by month, colored by the specified category,\n", "    including only months with at least min_samples_per_month samples.\n", "\n", "    Parameters:\n", "    - df (DataFrame): The dataframe containing the data.\n", "    - category_col (str): The column name for categorization (e.g., 'source' or 'split').\n", "    - date_col (str): The column name for the date (default 'origin_close_time').\n", "    - value_col (str): The column name for the value to average (default 'OUTPUT_cogs_total').\n", "    - min_samples_per_month (int): Minimum number of samples required per month.\n", "    \"\"\"\n", "    # Create a copy of the dataframe to avoid modifying the original\n", "    df_copy = df.copy()\n", "\n", "    # Ensuring date_col is a datetime\n", "    df_copy[date_col] = pd.to_datetime(df_copy[date_col])\n", "\n", "    # Creating a month-year column for aggregation\n", "    df_copy[\"month_year\"] = df_copy[date_col].dt.to_period(period).dt.to_timestamp()\n", "\n", "    # Grouping by month and filtering out months with fewer than min_samples_per_month\n", "    filtered_df = df_copy.groupby(\"month_year\").filter(\n", "        lambda x: len(x) >= min_samples_per_month\n", "    )\n", "\n", "    # Grouping by month and the category, and calculating the mean\n", "    grouped = (\n", "        filtered_df.groupby([\"month_year\", category_col])[value_col]\n", "        .mean()\n", "        .reset_index()\n", "    )\n", "\n", "    # Plotting\n", "    plt.figure(figsize=(12, 6))\n", "    sns.scatterplot(data=grouped, x=\"month_year\", y=value_col, hue=category_col)\n", "\n", "    plt.xticks(rotation=45)\n", "    plt.xlabel(\"Month-Year\")\n", "    plt.ylabel(f\"Average {value_col}\")\n", "    plt.title(f\"Average {value_col} by Month, Colored by {category_col}\")\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "\n", "# Example usage:\n", "plot_avg_price_by_month(combined_df, \"data_source\", value_col=\"OUTPUT_cogs_total\")\n", "plot_avg_price_by_month(combined_df, \"data_source\", value_col=\"OUTPUT_revenue_total\")"]}, {"cell_type": "code", "execution_count": null, "id": "ec84b8ea-acca-4996-ad66-7a34e6fc08b8", "metadata": {"tags": []}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import pandas as pd\n", "import datetime\n", "\n", "\n", "def create_stacked_histogram(df, stack_column, start_date, end_date):\n", "    \"\"\"\n", "    Creates a stacked histogram based on the specified stack_column in the dataframe.\n", "\n", "    Parameters:\n", "    - df (DataFrame): The dataframe containing the data.\n", "    - stack_column (str): The column name to use for stacking in the histogram.\n", "    - start_date (str): The start date for the x-axis range.\n", "    - end_date (str): The end date for the x-axis range.\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "    # Creating a list of data for each category in the stack_column\n", "    df_copy = df.copy()\n", "    categories = df_copy[stack_column].unique()\n", "    data_list = [\n", "        df_copy[df_copy[stack_column] == category][\"origin_close_time\"]\n", "        for category in categories\n", "    ]\n", "\n", "    # Creating the stacked histogram with opacity\n", "    ax.hist(\n", "        data_list,\n", "        bins=pd.date_range(start=start_date, end=end_date, freq=\"M\"),\n", "        label=categories,\n", "        stacked=True,\n", "    )\n", "\n", "    # Set x-axis major ticks to monthly interval, on the 1st day of the month\n", "    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))\n", "    ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%b %Y\"))\n", "\n", "    # Set x-axis range\n", "    xmin = pd.to_datetime(start_date).to_pydatetime()\n", "    xmax = pd.to_datetime(end_date).to_pydatetime()\n", "    ax.set_xlim([xmin, xmax])\n", "\n", "    plt.xticks(rotation=90)\n", "    plt.xlabel(\"origin_close_time\")\n", "    plt.ylabel(\"Count\")\n", "    plt.title(f\"Stacked Histogram of origin_close_time by {stack_column}\")\n", "    plt.legend(title=stack_column)\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "\n", "# Example usage:\n", "end_date = datetime.datetime.now() + datetime.timedelta(days=60)\n", "\n", "create_stacked_histogram(\n", "    combined_df, \"data_source\", \"2018-08-01\", end_date.strftime(\"%Y-%m-%d\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "359a8957-1fc7-42b0-8e5a-4dda07463b00", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# combined_df_with_scrape = combined_df.copy()\n", "# mystery_rds_df = combined_df[\n", "#     ~combined_df[\"data_source\"].str.contains(\"ml_scrape\")].copy()\n", "\n", "# training_filename = 'data/vertex_ai_experiment_mystery_rds.csv'\n", "# combined_df = mystery_rds_df\n", "\n", "# Ensure time fields are datetime\n", "for time_field in time_fields:\n", "    # combined_df[time_field] = pd.to_datetime(combined_df[time_field])\n", "    assert pd.api.types.is_datetime64_any_dtype(combined_df[time_field])\n", "\n", "# Assuming combined_df exists and has a 'data_source' column\n", "\n", "# First, let's ensure the DataFrame is sorted by 'origin_close_time'\n", "combined_df = combined_df.sort_values(\"origin_close_time\")\n", "\n", "# Define the split dates\n", "val_start_1 = pd.to_datetime(\"2019-08-01\")\n", "val_end_1 = pd.to_datetime(\"2019-12-01\")\n", "\n", "\n", "# Test should be the last month, and val should be the second to last month\n", "todays_month = pd.to_datetime(\"today\").to_period(\"M\").to_timestamp()\n", "\n", "val_start_2 = todays_month - pd.DateOffset(months=2)\n", "val_end_2 = todays_month - pd.DateOffset(months=1) - pd.DateOffset(days=1)\n", "\n", "test_start = todays_month - pd.DateOffset(months=1)\n", "# val_start_2 = pd.to_datetime(\"2023-08-01\")\n", "# val_end_2 = pd.to_datetime(\"2023-10-31\")\n", "# test_start = pd.to_datetime(\"2023-11-01\")\n", "assert test_start > val_end_2\n", "\n", "# # Define data_sources for the test set\n", "# test_data_sources = ['ml_scrape_arrive', 'ml_scrape_emerge', 'ml_scrape_j.b. hunt']\n", "test_data_sources = [\"rds_pricing_full\"]\n", "\n", "# Create masks for the splits\n", "train_mask = (\n", "    (combined_df[\"origin_close_time\"] < val_start_1)\n", "    | (\n", "        (combined_df[\"origin_close_time\"] > val_end_1)\n", "        & (combined_df[\"origin_close_time\"] < val_start_2)\n", "    )\n", "    | (\n", "        (combined_df[\"origin_close_time\"] > val_end_2)\n", "        & (combined_df[\"origin_close_time\"] <= test_start)\n", "    )\n", ") | ~combined_df[\"data_source\"].isin(\n", "    test_data_sources\n", ")  # Exclude test_data_sources\n", "\n", "val_mask = (\n", "    (\n", "        (combined_df[\"origin_close_time\"] >= val_start_1)\n", "        & (combined_df[\"origin_close_time\"] <= val_end_1)\n", "    )\n", "    | (\n", "        (combined_df[\"origin_close_time\"] >= val_start_2)\n", "        & (combined_df[\"origin_close_time\"] <= val_end_2)\n", "    )\n", ") & combined_df[\"data_source\"].isin(\n", "    test_data_sources\n", ")  # Exclude test_data_sources\n", "\n", "test_mask = (combined_df[\"origin_close_time\"] > test_start) & combined_df[\n", "    \"data_source\"\n", "].isin(test_data_sources)\n", "\n", "# Assert the masks are mutually exclusive and exhaustive\n", "# assert not train_mask[test_mask].any()\n", "assert not train_mask[val_mask].any()\n", "assert not train_mask[test_mask].any()\n", "assert not val_mask[test_mask].any()\n", "\n", "# Apply masks to create the splits\n", "train_df = combined_df.loc[train_mask]\n", "val_df = combined_df.loc[val_mask]\n", "test_df = combined_df.loc[test_mask]\n", "\n", "print(f\"Training samples: {len(train_df)}\")\n", "print(f\"Validation samples: {len(val_df)}\")\n", "print(f\"Test samples: {len(test_df)}\")\n", "# print(f\"Total samples: {len(train_df) + len(val_df) + len(test_df)}\")\n", "# print(f\"Combined samples: {len(combined_df)}\")\n", "assert len(train_df) + len(val_df) + len(test_df) == len(combined_df)\n", "\n", "# Standardize shipment rank"]}, {"cell_type": "code", "execution_count": null, "id": "a32a5607", "metadata": {}, "outputs": [], "source": ["train_df[\"data_source\"].value_counts()\n", "# No point in training with scrape as of 12/14 since there isn't enough scrape data in the training set\n", "# data_source\n", "# rds_pricing_full     145060\n", "# mystery_data_full     42457\n", "# ml_scrape_arrive        267\n", "# ml_scrape_emerge        240"]}, {"cell_type": "code", "execution_count": null, "id": "394fabf6-6bdd-4298-96de-7ee1d3f6ec5d", "metadata": {}, "outputs": [], "source": ["# Add a new column 'split' to each DataFrame\n", "train_df = train_df.assign(split=\"TRAIN\")\n", "val_df = val_df.assign(split=\"VALIDATE\")\n", "test_df = test_df.assign(split=\"TEST\")\n", "\n", "# Concatenate all the DataFrames into a single DataFrame\n", "combined_df = pd.concat([train_df, val_df, test_df])\n", "\n", "# Write the entire DataFrame to a CSV file\n", "training_filename = os.path.join(\n", "    PROJECT_ROOT, \n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\", \n", "    \"vertex_ai_rds_mystery_scrape_\" + pd.to_datetime(\"today\").strftime(\"%Y_%m_%d\") + \".csv\"\n", ")\n", "combined_df.to_csv(training_filename, index=False)\n", "\n", "# Only use the full data for training, not the scrape data\n", "training_filename = os.path.join(\n", "    PROJECT_ROOT, \n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\", \n", "    \"vertex_ai_rds_mystery_\" + pd.to_datetime(\"today\").strftime(\"%Y_%m_%d\") + \".csv\"\n", ")\n", "combined_df[combined_df[\"data_source\"].str.contains(\"full\")].to_csv(\n", "    training_filename, index=False\n", ")\n", "\n", "# Two months into the future\n", "end_date = datetime.datetime.now() + datetime.timedelta(days=60)\n", "# Plots of vertex_ai_rds_mystery_cpm.csv, which is used presently for training\n", "plot_df = combined_df[combined_df[\"data_source\"].str.contains(\"full\")]\n", "plot_avg_price_by_month(plot_df, \"split\", value_col=\"OUTPUT_cogs_total\", period=\"W\",)\n", "create_stacked_histogram(plot_df, \"split\", \"2018-08-01\", end_date.strftime(\"%Y-%m-%d\"))"]}, {"cell_type": "code", "execution_count": null, "id": "441a17a6", "metadata": {}, "outputs": [], "source": ["plot_avg_price_by_month(plot_df, \"split\", value_col=\"OUTPUT_revenue_total\", period=\"W\")\n", "create_stacked_histogram(plot_df, \"split\", \"2018-08-01\", end_date.strftime(\"%Y-%m-%d\"))"]}, {"cell_type": "markdown", "id": "af76718b-4e99-49be-8364-04e647090e17", "metadata": {}, "source": ["# Model"]}, {"cell_type": "code", "execution_count": null, "id": "f49e7b47-5574-4bad-98a6-2be154008571", "metadata": {"tags": []}, "outputs": [], "source": ["# Drop NAs to get sampling weights vector\n", "pruned_df = sheer_rds_train_df.dropna()\n", "sampling_weights = pruned_df[\"sampling_weight\"]\n", "\n", "from constants import NUMERIC_INPUT_COLS, STR_INPUT_COLS, OUTPUT_COLS\n", "\n", "# pruned_df = ml_df[STR_INPUT_COLS + NUMERIC_INPUT_COLS + OUTPUT_COLS].dropna()\n", "# pruned_df\n", "from constants import TIME_CYCLIC_MAX_VALUES\n", "from preprocessing import encode_cyclic_features\n", "\n", "# Use the same set of columns for all DFs for sheer experiment\n", "COLS_SUBSET = [\n", "    \"distance_miles\",\n", "    \"origin_city\",\n", "    \"origin_state\",\n", "    \"origin_country\",\n", "    \"origin_zip\",\n", "    \"destination_city\",\n", "    \"destination_state\",\n", "    \"destination_country\",\n", "    \"destination_zip\",\n", "    \"origin_close_hour\",\n", "    \"origin_close_day\",\n", "    \"origin_close_month\",\n", "    \"origin_close_day_of_year\",\n", "    \"origin_close_day_of_week\",\n", "    \"origin_close_week\",\n", "    \"origin_close_year\",\n", "    \"origin_close_is_workday\",\n", "    \"origin_close_is_holiday\",\n", "    \"destination_close_hour\",\n", "    \"destination_close_day\",\n", "    \"destination_close_month\",\n", "    \"destination_close_day_of_year\",\n", "    \"destination_close_day_of_week\",\n", "    \"destination_close_week\",\n", "    \"destination_close_year\",\n", "    \"destination_close_is_workday\",\n", "    \"destination_close_is_holiday\",\n", "    \"origin_lat\",\n", "    \"origin_lng\",\n", "    \"destination_lat\",\n", "    \"destination_lng\",\n", "    \"origin_diesel_price\",\n", "    \"destination_diesel_price\",\n", "    \"OUTPUT_cogs_total\",\n", "]\n", "\n", "\n", "def preprocess(df: pd.DataFrame) -> pd.DataFrame:\n", "    pruned_df = df[COLS_SUBSET].dropna()\n", "    numeric_input_cols = NUMERIC_INPUT_COLS.copy()\n", "    encode_cyclic_features(pruned_df, TIME_CYCLIC_MAX_VALUES, numeric_input_cols)\n", "    return pruned_df, numeric_input_cols\n", "\n", "\n", "# sheer_rds_train_df, numeric_input_cols = preprocess(sheer_rds_train_df)\n", "# sheer_val_df, numeric_input_cols = preprocess(sheer_val_df)\n", "# sheer_train_df, numeric_input_cols = preprocess(sheer_train_df)\n", "# rds_pricing_full_df, numeric_input_cols = preprocess(rds_pricing_full_df)\n", "# rds_pricing_train_df, numeric_input_cols = preprocess(rds_pricing_train_df)\n", "# rds_pricing_val_df, numeric_input_cols = preprocess(rds_pricing_val_df)\n", "# rds_pricing_test_df, numeric_input_cols = preprocess(rds_pricing_test_df)\n", "train_df, numeric_input_cols = preprocess(train_df)\n", "val_df, numeric_input_cols = preprocess(val_df)\n", "test_df, numeric_input_cols = preprocess(test_df)"]}, {"cell_type": "code", "execution_count": null, "id": "0d325c9a-b8f4-40e3-883b-0d7<PERSON>ebbe9", "metadata": {}, "outputs": [], "source": ["from constants import OUTPUT_COLS\n", "\n", "\n", "def xy_split(dataset, y_cols=\"OUTPUT_cogs_total\"):\n", "    features = dataset[numeric_input_cols].copy()\n", "    labels = dataset[OUTPUT_COLS]\n", "    return np.array(features), np.array(labels)\n", "\n", "\n", "X_train, Y_train = xy_split(train_df)\n", "# X_finetune, Y_finetune = xy_split(rds_pricing_val_df)\n", "X_val, Y_val = xy_split(val_df)\n", "X_test, Y_test = xy_split(test_df)"]}, {"cell_type": "code", "execution_count": null, "id": "0bd46113-9e22-4b46-860a-ea06be20f75d", "metadata": {"tags": []}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "\n", "\n", "def evaluate_regression(Y_true, Y_pred):\n", "    \"\"\"\n", "    Computes several regression metrics given true and predicted target values.\n", "\n", "    Args:\n", "    - Y_true: a numpy array containing the true target values\n", "    - Y_pred: a numpy array containing the predicted target values\n", "\n", "    Returns:\n", "    - A dictionary containing the following metrics:\n", "      - mse: mean squared error\n", "      - mae: mean absolute error\n", "      - r2: R^2 coefficient of determination\n", "    \"\"\"\n", "    metrics = {}\n", "\n", "    # compute mean squared error\n", "    mse = mean_squared_error(Y_true, Y_pred)\n", "    metrics[\"mse\"] = mse\n", "\n", "    # compute mean absolute error\n", "    mae = mean_absolute_error(Y_true, Y_pred)\n", "    metrics[\"mae\"] = mae\n", "\n", "    # compute R^2 coefficient of determination\n", "    r2 = r2_score(Y_true, Y_pred)\n", "    metrics[\"r2\"] = r2\n", "\n", "    return metrics"]}, {"cell_type": "code", "execution_count": null, "id": "b9e2ee1f-2f85-499d-881e-3001aea4c5c7", "metadata": {"tags": []}, "outputs": [], "source": ["# https://www.tensorflow.org/tutorials/load_data/csv#using_tfdata\n", "# Create the model with dropout layers\n", "import tensorflow as tf\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint\n", "from tensorflow.keras.callbacks import ReduceLROnPlateau\n", "from tensorflow.keras import regularizers\n", "from metrics import R2Score\n", "\n", "from tensorflow.keras.callbacks import TensorBoard\n", "import os\n", "import datetime\n", "\n", "# Set up TensorBoard callback\n", "log_dir = \"logs/fit/\" + datetime.datetime.now().strftime(\"%Y%m%d-%H%M%S\")\n", "tensorboard_callback = TensorBoard(log_dir=log_dir, histogram_freq=1)\n", "\n", "\n", "reduce_lr = ReduceLROnPlateau(monitor=\"val_loss\", factor=0.2, patience=5, min_lr=1e-6)\n", "\n", "#     def update_state(self, y_true, y_pred, sample_weight=None):\n", "#         y_true = tf.cast(y_true, tf.float32)\n", "#         y_pred = tf.cast(y_pred, tf.float32)\n", "#         residual_sum_of_squares = tf.reduce_sum(tf.square(y_true - y_pred))\n", "#         total_sum_of_squares = tf.reduce_sum(tf.square(y_true - tf.reduce_mean(y_true)))\n", "#         self.residual_sum_of_squares.assign_add(residual_sum_of_squares)\n", "#         self.total_sum_of_squares.assign_add(total_sum_of_squares)\n", "\n", "#     def result(self):\n", "#         return 1 - (self.residual_sum_of_squares / self.total_sum_of_squares)\n", "\n", "\n", "def create_improved_model():\n", "    model = tf.keras.Sequential(\n", "        [\n", "            layers.Normalization(),\n", "            layers.Dense(\n", "                64, activation=\"relu\", kernel_regularizer=\"l1\"\n", "            ),  # , kernel_regularizer=regularizers.l1_l2(l1=1e-5, l2=1e-4)),\n", "            layers.Dense(32, activation=\"relu\", kernel_regularizer=\"l1\"),\n", "            layers.Dense(16, activation=\"relu\", kernel_regularizer=\"l1\"),\n", "            layers.<PERSON><PERSON>(1),\n", "        ]\n", "    )\n", "    return model\n", "\n", "\n", "improved_model = create_improved_model()\n", "\n", "improved_model.compile(\n", "    loss=tf.keras.losses.<PERSON><PERSON>(delta=500.0),\n", "    optimizer=tf.keras.optimizers.<PERSON>(),\n", "    metrics=[\n", "        tf.keras.metrics.MeanAbsoluteError(),\n", "        R2Score(),\n", "        tf.keras.metrics.MeanSquaredError(),\n", "    ],\n", ")\n", "\n", "# early_stopping = EarlyStopping(monitor='val_loss', patience=10, mode=\"min\", restore_best_weights=True)\n", "checkpoint = ModelCheckpoint(\"best_model.h5\", monitor=\"val_loss\", save_best_only=True)\n", "\n", "history = improved_model.fit(\n", "    X_train,\n", "    Y_train,\n", "    epochs=1000,\n", "    validation_data=(X_val, Y_val),\n", "    callbacks=[checkpoint, tensorboard_callback],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a0e2de7e-210d-43bb-98b1-740e3f99dc88", "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from metrics import R2Score\n", "\n", "improved_model = tf.keras.models.load_model(\n", "    \"best_model.h5\", custom_objects={\"R2Score\": R2Score}\n", ")\n", "\n", "X_train, Y_train = xy_split(train_df)\n", "# X_finetune, Y_finetune = xy_split(rds_pricing_val_df)\n", "X_val, Y_val = xy_split(val_df)\n", "X_test, Y_test = xy_split(test_df)\n", "\n", "Y_pred = improved_model.predict(X_test)\n", "if Y_test is not None:\n", "    Y_test = np.reshape(\n", "        Y_test, Y_pred.shape\n", "    )  # Make sure Y_test has the same shape as Y_pred\n", "    test_metrics = evaluate_regression(Y_test, Y_pred)\n", "    print(\"Test Metrics:\", test_metrics)"]}, {"cell_type": "code", "execution_count": null, "id": "4ccc5278-2efd-414d-b1db-d0e0cd93312c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Assuming rds_pricing_test_df is your test dataframe\n", "test_df[\"preds\"] = Y_pred\n", "\n", "# Calculate absolute error\n", "test_df[\"abs_error\"] = np.abs(test_df[\"OUTPUT_cogs_total\"] - test_df[\"preds\"])\n", "# Calculate percentage error\n", "test_df[\"pct_error\"] = (test_df[\"abs_error\"] / test_df[\"OUTPUT_cogs_total\"]) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "7f81dc38-7cdb-438f-ac28-0bc91bf49461", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(test_df[\"abs_error\"], bins=50, edgecolor=\"k\")\n", "plt.xlabel(\"Absolute Error\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Histogram of Absolute Errors\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bf9f4da0-d949-4cd6-907c-275293a938ad", "metadata": {"tags": []}, "outputs": [], "source": ["# Filter dataframe\n", "filtered_df = test_df[test_df[\"OUTPUT_cogs_total\"] < 4000].copy()\n", "\n", "# Create bucket\n", "filtered_df[\"cogs_total_bucket\"] = filtered_df[\"OUTPUT_cogs_total\"] // 200 * 200\n", "\n", "# Group by bucket and calculate mean absolute error\n", "grouped = filtered_df.groupby(\"cogs_total_bucket\")[\"abs_error\"].mean().reset_index()\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(grouped[\"cogs_total_bucket\"], grouped[\"abs_error\"], width=200)\n", "for i in range(len(grouped)):\n", "    plt.text(\n", "        x=grouped[\"cogs_total_bucket\"][i],\n", "        y=grouped[\"abs_error\"][i],\n", "        s=int(grouped[\"abs_error\"][i]),\n", "        horizontalalignment=\"center\",\n", "        verticalalignment=\"bottom\",\n", "    )\n", "plt.xlabel(\"OUTPUT_cogs_total Bucket\")\n", "plt.ylabel(\"Mean Absolute Error\")\n", "plt.title(\"Histogram of Mean Absolute Error by OUTPUT_cogs_total Bucket\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f4e7d6ad-ed19-4329-a591-d0d988f44a4c", "metadata": {}, "outputs": [], "source": ["# See if error on reefer is higher"]}, {"cell_type": "code", "execution_count": null, "id": "e405aac9-4ccb-47db-a244-b77766a2b638", "metadata": {}, "outputs": [], "source": ["# Filter dataframe\n", "filtered_df = test_df[test_df[\"OUTPUT_cogs_total\"] < 4000].copy()\n", "\n", "# Create bucket\n", "filtered_df[\"cogs_total_bucket\"] = filtered_df[\"OUTPUT_cogs_total\"] // 200 * 200\n", "\n", "# Group by bucket and calculate mean percentage error\n", "grouped = filtered_df.groupby(\"cogs_total_bucket\")[\"pct_error\"].mean().reset_index()\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(grouped[\"cogs_total_bucket\"], grouped[\"pct_error\"], width=200)\n", "for i in range(len(grouped)):\n", "    plt.text(\n", "        x=grouped[\"cogs_total_bucket\"][i],\n", "        y=grouped[\"pct_error\"][i],\n", "        s=int(grouped[\"pct_error\"][i]),\n", "        horizontalalignment=\"center\",\n", "        verticalalignment=\"bottom\",\n", "    )\n", "plt.xlabel(\"OUTPUT_cogs_total Bucket\")\n", "plt.ylabel(\"Mean Percentage Error (%)\")\n", "plt.title(\"Histogram of Mean Percentage Error by OUTPUT_cogs_total Bucket\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5f8c5f93", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_model_loss(fit):\n", "    plt.plot(fit.history[\"loss\"])\n", "    plt.plot(fit.history[\"val_loss\"])\n", "    plt.title(\"Loss Across Epochs\")\n", "    plt.xlabel(\"Epochs\")\n", "    plt.ylabel(\"Loss\")\n", "    plt.legend([\"Train\", \"Test\"], loc=\"upper left\")\n", "    plt.show()\n", "\n", "\n", "def plot_model_mae(fit):\n", "    plt.plot(fit.history[\"mean_absolute_error\"])\n", "    plt.plot(fit.history[\"val_mean_absolute_error\"])\n", "    plt.title(\"MAE Across Epochs\")\n", "    plt.xlabel(\"Epochs\")\n", "    plt.ylabel(\"MAE\")\n", "    plt.legend([\"Train\", \"Test\"], loc=\"upper left\")\n", "    plt.show()\n", "\n", "\n", "def plot_model_r2(fit):\n", "    plt.plot(fit.history[\"r2_score\"])\n", "    plt.plot(fit.history[\"val_r2_score\"])\n", "    plt.title(\"R2 Across Epochs\")\n", "    plt.xlabel(\"Epochs\")\n", "    plt.ylabel(\"R2\")\n", "    plt.legend([\"Train\", \"Test\"], loc=\"upper left\")\n", "    plt.show()\n", "\n", "\n", "def plot_pred_actual(true, pred):\n", "    plt.figure(figsize=(7, 7))\n", "    plt.scatter(true, pred, c=\"crimson\")\n", "    p1 = max(max(pred), max(true))\n", "    p2 = min(min(pred), min(true))\n", "    plt.plot([p1, p2], [p1, p2])\n", "    plt.xlabel(\"True Values\", fontsize=15)\n", "    plt.ylabel(\"Predicted Values\", fontsize=15)\n", "    plt.axis(\"equal\")\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5a8c214f", "metadata": {}, "outputs": [], "source": ["plot_model_loss(history)\n", "plot_model_mae(history)\n", "plot_model_r2(history)\n", "plot_pred_actual(Y_test, Y_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "09d47d09-ef9e-45e7-9380-ba6b1c22a3cc", "metadata": {"tags": []}, "outputs": [], "source": ["dense_layer = improved_model.layers[1]\n", "weights, biases = dense_layer.get_weights()\n", "\n", "# Calculate the absolute sum of weights for each input feature\n", "abs_sum_of_weights = np.sum(np.abs(weights), axis=1)\n", "\n", "# Sort the features based on their absolute sum of weights\n", "sorted_feature_indices = np.argsort(abs_sum_of_weights)[::-1]\n", "\n", "# Print the sorted feature indices and their corresponding weights\n", "for idx in sorted_feature_indices:\n", "    print(f\"{train_df.columns[idx]}: {abs_sum_of_weights[idx]}\")"]}, {"cell_type": "markdown", "id": "ddcf50b3-7d48-402b-91cd-0b402777105f", "metadata": {"tags": []}, "source": ["## SageMaker AutoML Model Debugging"]}, {"cell_type": "code", "execution_count": null, "id": "a47cada0-1198-4c5a-bd8d-82a793dbb3ee", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "sagemaker = boto3.client(\"sagemaker\")\n", "\n", "response = sagemaker.list_models(\n", "    SortBy=\"CreationTime\", SortOrder=\"Descending\", MaxResults=100\n", ")\n", "\n", "for model in response[\"Models\"]:\n", "    print(\n", "        f\"Model Name: {model['ModelName']}, Model ARN: {model['ModelArn']}, Creation Time: {model['CreationTime']}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "6a494099-7b40-4aa9-b512-ab53c91da4e3", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "sagemaker = boto3.client(\"sagemaker\", region_name=\"us-east-1\")\n", "\n", "response = sagemaker.describe_model(\n", "    ModelName=\"sheer-lat-lng-1OXfD8GeUonrhChNWi-079-e532738a\"\n", ")\n", "\n", "print(\"Model ARN:\", response[\"ModelArn\"])"]}, {"cell_type": "code", "execution_count": null, "id": "a90dfbbf-cb52-4ca5-9b5d-95aa579c3fcf", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "sagemaker = boto3.client(\"sagemaker\", region_name=\"us-east-1\")\n", "\n", "response = sagemaker.list_candidates_for_auto_ml_job(\n", "    AutoMLJobName=\"rds-sheer-trained-6-10-18-44\",\n", "    SortBy=\"CreationTime\",\n", "    SortOrder=\"Descending\",\n", ")\n", "\n", "for candidate in response[\"Candidates\"]:\n", "    print(\"Candidate name:\", candidate[\"CandidateName\"])\n", "    print(\"Candidate status:\", candidate[\"CandidateStatus\"])\n", "    print(\"Failure reason:\", candidate.get(\"FailureReason\", \"N/A\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}