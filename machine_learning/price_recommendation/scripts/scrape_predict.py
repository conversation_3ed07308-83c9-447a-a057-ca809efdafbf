import ast
import json
import os

import pandas as pd

from common import init_main
from common.google_maps_utils import GoogleMapsUtils

PROJECT_ROOT = init_main.initialize()

from machine_learning.price_recommendation.price_predictor import (
    VertexAutoMLPricePredictor,
)
from machine_learning.price_recommendation import price_predictor


if __name__ == "__main__":
    PRICER_ENDPOINT = "2320776576142147584"
    GMAPS_UTILS = GoogleMapsUtils()
    PRICER = VertexAutoMLPricePredictor(PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS)
    file_path = os.path.join(
        PROJECT_ROOT, "machine_learning", "freightgpt", "FreightGPT User Feedback.xlsx"
    )
    sheet_name = "User Feedback-Bad Price"
    df = pd.read_excel(file_path, sheet_name=sheet_name)
    print(df)

    data_columns = ["Feedback Time", "Price Predictor input"]
    extracted_data = df[data_columns].copy()

    def parse_input(input_str):
        try:
            # Load the outer JSON structure
            outer_dict = json.loads(input_str)
            # Extract and parse the inner string as a dictionary
            inner_dict = ast.literal_eval(outer_dict["input"])
            return (
                inner_dict["origin_city"],
                inner_dict["origin_state"],
                inner_dict["destination_city"],
                inner_dict["destination_state"],
                inner_dict.get("equipment_type", ""),
            )
        except (ValueError, SyntaxError, KeyError):
            return "", "", "", "", ""

    extracted_data[
        [
            "Origin City",
            "Origin State",
            "Destination City",
            "Destination State",
            "Equipment Type",
        ]
    ] = extracted_data["Price Predictor input"].apply(
        lambda x: pd.Series(parse_input(x))
    )

    extracted_data = extracted_data.drop(columns=["Price Predictor input"])

    def predict_prices(row):
        input_data = price_predictor.PricePredictorInput(
            origin_city=row["Origin City"],
            origin_state=row["Origin State"],
            destination_city=row["Destination City"],
            destination_state=row["Destination State"],
            origin_close_time=row["Feedback Time"],
            equipment_type=row["Equipment Type"],
            data_source="ml_scrape_arrive",
        )
        return PRICER.predict(input_data)["value"]

    extracted_data["prices_predicted"] = extracted_data.apply(predict_prices, axis=1)

    print(extracted_data.head())

    output_file_path = os.path.join(
        PROJECT_ROOT,
        "machine_learning",
        "freightgpt",
        "FreightGPT User Feedback with Predictions.xlsx",
    )
    extracted_data.to_excel(output_file_path, index=False)
