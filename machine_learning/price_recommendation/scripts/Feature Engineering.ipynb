{"cells": [{"cell_type": "markdown", "id": "f06fccbc-9ec6-431f-adb3-80afba9d447a", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["# Imports and Installs"]}, {"cell_type": "code", "execution_count": null, "id": "7c526c22", "metadata": {}, "outputs": [], "source": ["# Ensure common and other packages are installed.\n", "%pip install ../../../. boto3 googlemaps seaborn pandas fredapi geopandas matplotlib descartes holidays openpyxl tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "a4c7a4d0", "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()"]}, {"cell_type": "markdown", "id": "c04d341c-873b-4ac9-81fa-8c983a89b5af", "metadata": {"tags": []}, "source": ["# Load Data from RDS into DF"]}, {"cell_type": "code", "execution_count": null, "id": "01f802c8-408c-407e-bf95-61a653a69d2f", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from common import query_utils\n", "\n", "QUERY_STR = \"\"\"\n", "    SELECT\n", "        s.*,\n", "        oc.city AS originCity,\n", "        oc.country AS originCountry,\n", "        oc.state AS originState,\n", "        dc.city AS destinationCity,\n", "        dc.country AS destinationCountry,\n", "        dc.state AS destinationState,\n", "        oz.zip as originZip,\n", "        dz.zip as destinationZip\n", "    FROM\n", "        (\n", "            SELECT\n", "                s.*,\n", "                l.originCityId,\n", "                l.destinationCityId,\n", "                l.equipmentType,\n", "                l.<PERSON>\n", "            FROM\n", "                mvp_db_dev.shipments AS s\n", "                JOIN mvp_db_dev.lanes AS l ON s.laneid=l.id\n", "            WHERE\n", "                s.shipmentClass = 'canonical'\n", "        ) AS s\n", "        JOIN mvp_db_dev.cities AS oc ON s.originCityId = oc.id\n", "        JOIN mvp_db_dev.cities AS dc ON s.destinationCityId = dc.id\n", "        JOIN mvp_db_dev.zips AS oz ON s.originZipId = oz.id\n", "        JOIN mvp_db_dev.zips AS dz ON s.destinationZipId = dz.id\n", "\"\"\"\n", "\n", "# Use the utility function to get data in batches\n", "records_df = query_utils.get_data_in_batches(\n", "    \"mvp_db_dev\", 1000, QUERY_STR, use_tqdm=True\n", ")\n", "\n", "# Save the DataFrame to a CSV file\n", "rds_raw_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"rds_raw.csv\",\n", ")\n", "\n", "records_df.to_csv(rds_raw_file_path, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6b7fbc72-3b85-45d2-b6b1-77de117b9724", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from common import query_utils\n", "\n", "QUERY_STR = \"\"\" SELECT\n", "            s.id,\n", "            s.originCloseTime,\n", "            s.cogs<PERSON>otal,\n", "            s.cogsAccessorial,\n", "            s.distanceMiles,\n", "            sm.shipmentIngestionTime\n", "            FROM\n", "            mvp_db_dev.shipments AS s\n", "            JOIN mvp_db_dev.shipmentmetadata AS sm ON s.id=sm.shipmentId;\"\"\"\n", "\n", "# Use the utility function to get data in batches\n", "metadata_df = query_utils.get_data_in_batches(\n", "    \"mvp_db_dev\", 1000, QUERY_STR, use_tqdm=True\n", ")\n", "\n", "# Save the DataFrame to a CSV file\n", "metadata_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"rds_metadata_raw.csv\",\n", ")\n", "\n", "metadata_df.to_csv(metadata_file_path, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8bb171ce", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from common import query_utils\n", "from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "# PRetty print the dictionary\n", "import json\n", "\n", "\n", "def custom_encoder(obj):\n", "    if isinstance(obj, object):\n", "        return {\"type\": obj.__class__.__name__}\n", "    else:\n", "        return json.JSONEncoder.default(obj)\n", "\n", "\n", "QUERY_STR = \"\"\" SELECT\n", "            *\n", "            FROM\n", "            mvp_db_dev.ml_data_dump;\"\"\"\n", "\n", "# Use the utility function to get data in batches\n", "ml_data_dump_df = query_utils.get_data_in_batches(\n", "    \"mvp_db_dev\", 1000, QUERY_STR, use_tqdm=True\n", ")\n", "\n", "# Save the DataFrame to a CSV file\n", "import os\n", "\n", "ml_data_dump_raw_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"ml_data_dump_raw.csv\",\n", ")\n", "\n", "ml_data_dump_df.to_csv(ml_data_dump_raw_file_path, index=False)"]}, {"cell_type": "markdown", "id": "4b04ecc6-f765-4d5e-beb7-d61632bb360f", "metadata": {"tags": []}, "source": ["# Preprocessing Code"]}, {"cell_type": "markdown", "id": "b7e60eab-84f3-4065-bbef-ea0e466e1212", "metadata": {"tags": []}, "source": ["### Pipelines"]}, {"cell_type": "code", "execution_count": null, "id": "923287a7-bb1c-48e4-9f6b-54d33727dee5", "metadata": {}, "outputs": [], "source": ["from common import s3_cache_file\n", "import os\n", "import pandas as pd\n", "\n", "s3_ml_data = s3_cache_file.S3(\"truce-ml-data\")\n", "\n", "\n", "def save_df_to_s3(df, filename, ignore_cols=[], idx_cols=[], index=True):\n", "    # datetime.datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "    filename = os.path.join(\"data\", filename)\n", "    save_df = df.copy()\n", "    if idx_cols:\n", "        save_df = save_df.set_index(idx_cols)\n", "    save_df.drop(columns=ignore_cols).to_csv(filename, index=index)\n", "    s3_ml_data.upload(filename)\n", "\n", "\n", "CURRENT_YEAR = pd.Timestamp.now().year\n", "\n", "\n", "def filter_future_years(df, datetime_col):\n", "    # Create a mask for rows where datetime_col is NA\n", "    mask_na = df[datetime_col].isna()\n", "    # Combine the mask with the condition for the year\n", "    mask = mask_na | (df[datetime_col].dt.year <= CURRENT_YEAR)\n", "    return df[mask]\n", "\n", "daily_cpm_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"daily_cpm.csv\",\n", ")\n", "\n", "daily_cpm = s3_cache_file.S3CacheFile(\"truce-ml-data\", daily_cpm_file_path, \"date\")\n", "daily_cpm.pull()\n", "\n", "\n", "def get_daily_cpm(date_str):\n", "    if date_str in daily_cpm.df.index:\n", "        return daily_cpm.df.loc[date_str][\"cpm\"]"]}, {"cell_type": "code", "execution_count": null, "id": "085c622d-e5fa-4534-93d0-729abd03ff17", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from common.google_maps_utils import GoogleMapsUtils\n", "import time\n", "from machine_learning.price_recommendation.preprocessing import (\n", "    separate_time_fields,\n", "    camel_to_snake,\n", ")\n", "from tqdm.notebook import tqdm\n", "\n", "tqdm.pandas()\n", "\n", "# Do time separate fields in evaluation.ipynb in encode functino (preprocess)\n", "\n", "\n", "class DataPipeline:\n", "    def __init__(self, name: str):\n", "        self.name = name\n", "        self.df = pd.DataFrame()\n", "\n", "    def map_columns(self):\n", "        pass\n", "\n", "    def ingest(self, df: pd.DataFrame):\n", "        self.map_columns(df)\n", "        # save raw\n", "\n", "    def assign_dtypes(self):\n", "        pass\n", "\n", "    def clean(self):\n", "        pass\n", "        # clean float, str, dt\n", "        # canoncalize categorical str cols\n", "\n", "    def filter_rows(self):\n", "        # Remove all 0s, nas, anything else that may be useless (US/CA, etc)\n", "        pass\n", "\n", "    def fill_missing_cols(self):\n", "        pass\n", "        # fill the missing columns (country from state)\n", "\n", "    def preprocess(self):\n", "        # load raw\n", "        self.assign_dtypes()\n", "        # assign dtypes, cleaning if necessary\n", "        self.clean()\n", "        self.fill_missing_cols()\n", "        self.filter_rows()\n", "        # Origin Close Time can be date or datetime\n", "        # Print columns before assertion\n", "        print(f\"Columns in dataframe: {self.df.columns}\")\n", "        min_cols = {\n", "            \"origin_close_time\",\n", "            \"origin_city\",\n", "            \"origin_state\",\n", "            \"origin_country\",\n", "            \"destination_city\",\n", "            \"destination_state\",\n", "            \"destination_country\",\n", "            \"OUTPUT_cogs_total\",\n", "        }\n", "        for col in min_cols:\n", "            assert col in self.df.columns, f\"Missing required column: {col}\"\n", "\n", "    def enrich(self):\n", "        pass"]}, {"cell_type": "markdown", "id": "d26c281d-e07e-4ca1-be11-6b2751bc6c1f", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### RDS Pipeline"]}, {"cell_type": "code", "execution_count": null, "id": "3df20ce5-7663-45e7-845e-628591e4c290", "metadata": {"tags": []}, "outputs": [], "source": ["from machine_learning.price_recommendation.derived_features import DieselPrice\n", "\n", "\n", "class RDSPipeline(DataPipeline):\n", "    def __init__(self, name: str):\n", "        super().__init__(\"RDSPipeline\")\n", "\n", "        self.gmaps_utils = GoogleMapsUtils()\n", "\n", "        self.diesel_price = DieselPrice()\n", "\n", "    def update_cache(self):\n", "        start_time = time.perf_counter()\n", "\n", "        self.gmaps_utils.save_and_upload_cache_files()\n", "\n", "        self.diesel_price.save_and_upload()\n", "\n", "        print(f\"Time to update cache: {time.perf_counter() - start_time} seconds\")\n", "\n", "    def map_columns(self, df: pd.DataFrame):\n", "        ml_df_fields = [\n", "            \"stopCount\",\n", "            \"distanceMiles\",\n", "            \"shipmentRank\",\n", "            \"weight\",\n", "            \"preBook\",\n", "            \"clt\",\n", "            \"originCity\",\n", "            \"originState\",\n", "            \"originCountry\",\n", "            \"originZip\",\n", "            \"destinationCity\",\n", "            \"destinationState\",\n", "            \"destinationCountry\",\n", "            \"destinationZip\",\n", "            \"originOpenTime\",\n", "            \"originCloseTime\",\n", "            \"destinationOpenTime\",\n", "            \"destinationCloseTime\",\n", "            \"cogsTotal\",\n", "            \"cogsAccessorial\",\n", "            \"revenueTotal\",\n", "            \"revenueAccessorial\",\n", "            \"equipmentType\",\n", "        ]\n", "\n", "        for field in ml_df_fields:\n", "            self.df[camel_to_snake(field)] = df.pop(field)\n", "\n", "    def assign_dtypes(self):\n", "        self.datetime_cols = [\n", "            \"origin_open_time\",\n", "            \"origin_close_time\",\n", "            \"destination_open_time\",\n", "            \"destination_close_time\",\n", "        ]\n", "\n", "        for col in self.datetime_cols:\n", "            self.df[col] = pd.to_datetime(self.df[col], errors=\"coerce\")\n", "\n", "    def clean(self):\n", "        # clean cogs\n", "        self.df[\"cogs_accessorial\"].fillna(0.0, inplace=True)\n", "\n", "        self.df[\"OUTPUT_cogs_total\"] = (\n", "            self.df[\"cogs_total\"] - self.df[\"cogs_accessorial\"]\n", "        )\n", "\n", "        self.df.pop(\"cogs_total\")\n", "\n", "        self.df.pop(\"cogs_accessorial\")\n", "\n", "        # clean revenue\n", "        self.df[\"revenue_accessorial\"].fillna(0.0, inplace=True)\n", "\n", "        self.df[\"OUTPUT_revenue_total\"] = (\n", "            self.df[\"revenue_total\"] - self.df[\"revenue_accessorial\"]\n", "        )\n", "\n", "        self.df.pop(\"revenue_total\")\n", "\n", "        self.df.pop(\"revenue_accessorial\")\n", "\n", "    def filter_rows(self):\n", "        self.df = self.df[\n", "            (self.df[\"origin_country\"] == \"US\")\n", "            & (self.df[\"destination_country\"] == \"US\")\n", "        ]\n", "\n", "        self.df = self.df[self.df[\"OUTPUT_cogs_total\"] > 0]\n", "        self.df = self.df[self.df[\"OUTPUT_revenue_total\"] > 0]\n", "\n", "        for datetime_col in self.datetime_cols:\n", "            self.df = filter_future_years(self.df, datetime_col)\n", "\n", "    def enrich(self):\n", "        start_time = time.perf_counter()\n", "\n", "        self.df[[\"origin_lat\", \"origin_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"origin_city\"],\n", "                row[\"origin_state\"],\n", "                row[\"origin_country\"],\n", "                row[\"origin_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "\n", "        self.df[[\"destination_lat\", \"destination_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"destination_city\"],\n", "                row[\"destination_state\"],\n", "                row[\"destination_country\"],\n", "                row[\"destination_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        print(\n", "            f\"Time to enrich with lat/lng: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        self.df[\"origin_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"origin_close_time\"], state=row[\"origin_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "\n", "        self.df[\"destination_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"destination_close_time\"], state=row[\"destination_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        print(\n", "            f\"Time to enrich with diesel price: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        self.df[\"cpm\"] = (\n", "            self.df[\"origin_close_time\"].dt.strftime(\"%Y-%m-%d\").apply(get_daily_cpm)\n", "        )\n", "        print(\n", "            f\"Time to enrich with cpm: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        # separate_time_fields('origin_open_time', self.df)\n", "        # separate_time_fields('origin_close_time', self.df)\n", "        # separate_time_fields('destination_open_time', self.df)\n", "        # separate_time_fields('destination_close_time', self.df)\n", "        # Network CPM at time (jason)\n", "        # pd.read_excel('data/uscities.xlsx').columns\n", "        # dist from city with 100k pop\n", "        # Weather (stretch)\n", "        # Truck driver labor cost\n", "        self.update_cache()\n", "\n", "\n", "start_time = time.perf_counter()\n", "\n", "rds_pipeline = RDSPipeline(\"RDSPipeline\")\n", "\n", "rds_df = pd.read_csv(rds_raw_file_path, index_col=\"id\")\n", "\n", "rds_pipeline.ingest(rds_df)\n", "\n", "print(f\"Time to ingest data: {time.perf_counter() - start_time:.2f} seconds\")\n", "\n", "start_time = time.perf_counter()\n", "rds_pipeline.preprocess()\n", "\n", "print(f\"Time to preprocess data: {time.perf_counter() - start_time:.2f} seconds\")\n", "\n", "start_time = time.perf_counter()\n", "\n", "rds_pipeline.enrich()\n", "\n", "print(f\"Time to enrich data: {time.perf_counter() - start_time:.2f} seconds\")\n", "\n", "rds_pipeline.df"]}, {"cell_type": "code", "execution_count": null, "id": "dfcc8936-c19b-4733-b225-231e6f2fa7b9", "metadata": {}, "outputs": [], "source": ["save_df_to_s3(rds_pipeline.df, \"rds_pricing_full.csv\")"]}, {"cell_type": "markdown", "id": "e332f886-7aa2-4fdc-840d-5df85f0515da", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Mystery Pipeline\n", "\n", "TODO(P0): We have a bug where the timestamps should be in UTC but they're in local time. Fix."]}, {"cell_type": "code", "execution_count": null, "id": "a6786012-6084-43b2-9340-1aba8724a6b1", "metadata": {"tags": []}, "outputs": [], "source": ["from machine_learning.price_recommendation.preprocessing import (\n", "    derive_country_from_state,\n", ")\n", "import numpy as np\n", "\n", "\n", "class MysteryPipeline(DataPipeline):\n", "    def __init__(self):\n", "        super().__init__(\"MysteryPipeline\")\n", "        self.gmaps_utils = GoogleMapsUtils()\n", "        self.diesel_price = DieselPrice()\n", "        self.elapsed_time_sum = 0\n", "        self.last_cache_update = time.perf_counter()\n", "        self.datetime_cols = [\n", "            \"origin_open_time\",\n", "            \"origin_close_time\",\n", "            \"destination_open_time\",\n", "            \"destination_close_time\",\n", "        ]\n", "\n", "    def update_cache(self):\n", "        self.gmaps_utils.save_and_upload_cache_files()\n", "        self.diesel_price.save_and_upload()\n", "\n", "    def map_columns(self, df: pd.DataFrame):\n", "        column_map = {\n", "            \"ShipmentTruckType\": \"equipment_type\",\n", "            \"OriginCity\": \"origin_city\",\n", "            \"OriginStateProvince\": \"origin_state\",\n", "            \"OriginPostalCode\": \"origin_zip\",\n", "            \"DestinationCity\": \"destination_city\",\n", "            \"DestinationStateProvince\": \"destination_state\",\n", "            \"DestinationPostalCode\": \"destination_zip\",\n", "            \"COGS Total\": \"OUTPUT_cogs_total\",\n", "            \"Revenue Total\": \"OUTPUT_revenue_total\",\n", "            \"EarliestPickup\": \"origin_open_time\",\n", "            \"LatestPickup\": \"origin_close_time\",\n", "            \"EarliestDelivery\": \"destination_open_time\",\n", "            \"LatestDelivery\": \"destination_close_time\",\n", "            # Index(['ShipmentMode', 'ShipmentTruckType', 'OriginCity',\n", "            #        'OriginStateProvince', 'OriginPostalCode', 'OriginTimeZone',\n", "            #        'EarliestPickup', 'LatestPickup', 'DestinationCity',\n", "            #        'DestinationStateProvince', 'DestinationPostalCode',\n", "            #        'DestinationTimeZone', 'EarliestDelivery', 'LatestDelivery',\n", "            #        'CarrierName', 'CarrierDOTNumber', 'CarrierMCNumber', 'COGS Total',\n", "            #        'Revenue Total', 'DateTimeCreatedUTC', 'OffHoldTimeStampUTC',\n", "            #        'ShipmentDateCovered', 'LatestPickupUTC'],\n", "            #       dtype='object')\n", "        }\n", "\n", "        df_renamed = df.rename(columns=column_map)\n", "        self.df = df_renamed[column_map.values()]\n", "\n", "    def assign_dtypes(self):\n", "        for col in self.datetime_cols:\n", "            self.df[col] = pd.to_datetime(self.df[col], format=\"mixed\", errors=\"raise\")\n", "\n", "    def clean(self):\n", "        self.df[\"origin_city\"] = self.df[\"origin_city\"].str.title()\n", "        self.df[\"destination_city\"] = self.df[\"destination_city\"].str.title()\n", "        # Ensure zips are correct\n", "\n", "    def fill_missing_cols(self):\n", "        self.df[\"origin_country\"] = self.df[\"origin_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "        self.df[\"destination_country\"] = self.df[\"destination_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "\n", "    def filter_rows(self):\n", "        self.df = self.df[\n", "            (self.df[\"origin_country\"] == \"US\")\n", "            & (self.df[\"destination_country\"] == \"US\")\n", "        ]\n", "        self.df = self.df[self.df[\"OUTPUT_cogs_total\"] > 0]\n", "        self.df = self.df[self.df[\"OUTPUT_revenue_total\"] > 0]\n", "        for datetime_col in self.datetime_cols:\n", "            self.df = filter_future_years(self.df, datetime_col)\n", "\n", "    def get_lat_lng(self, row, origin_or_dest):\n", "        try:\n", "            lat_lng = self.gmaps_utils.get_lat_lng(\n", "                row[f\"{origin_or_dest}_city\"],\n", "                row[f\"{origin_or_dest}_state\"],\n", "                row[f\"{origin_or_dest}_country\"],\n", "                row[f\"{origin_or_dest}_zip\"],\n", "            )\n", "            if (\n", "                time.perf_counter() - self.last_cache_update >= 2000\n", "            ):  # Update cache every 10 seconds\n", "                cache_update_start = time.perf_counter()\n", "                self.gmaps_utils.save_and_upload_cache_files()\n", "                cache_update_end = time.perf_counter()\n", "                cache_update_time = cache_update_end - cache_update_start\n", "                print(f\"Cache updated. It took {cache_update_time:.2f} seconds.\")\n", "                self.last_cache_update = time.perf_counter()\n", "            return lat_lng\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to get lat/lng for {origin_or_dest} city: {row[f'{origin_or_dest}_city']}, {origin_or_dest} state: {row[f'{origin_or_dest}_state']}\"\n", "            )\n", "            print(f\"Error: {str(e)}\")\n", "            return np.nan, np.nan\n", "\n", "    def get_dist_mi(self, row) -> float:\n", "        try:\n", "            origin_str = (\n", "                f\"{row['origin_city']}, {row['origin_state']} {row['origin_zip']}\"\n", "            )\n", "            destination_str = \"{}, {} {}\".format(\n", "                row[\"destination_city\"],\n", "                row[\"destination_state\"],\n", "                row[\"destination_zip\"],\n", "            )\n", "            distance_miles = self.gmaps_utils.get_distance_miles(\n", "                origin_str, destination_str\n", "            )\n", "\n", "            # Check if 10 seconds have passed since the last cache update\n", "            if time.perf_counter() - self.last_cache_update >= 100:\n", "                # If so, save the cache\n", "                self.gmaps_utils.save_and_upload_cache_files()\n", "                # And update the time of the last cache update\n", "                self.last_cache_update = time.perf_counter()\n", "\n", "            return distance_miles\n", "        except Exception as e:\n", "            print(\n", "                f\"Failed to get distance miles for origin {origin_str} and destination {destination_str}\"\n", "            )\n", "            print(f\"Error: {str(e)}\")\n", "            return np.nan\n", "\n", "    def enrich(self):\n", "        s = time.perf_counter()\n", "        self.df[[\"origin_lat\", \"origin_lng\"]] = self.df.apply(\n", "            lambda row: self.get_lat_lng(row, \"origin\"), axis=1, result_type=\"expand\"\n", "        )\n", "        print(round(time.perf_counter() - s, 2), \"origin lat/lng\")\n", "        self.df[[\"destination_lat\", \"destination_lng\"]] = self.df.apply(\n", "            lambda row: self.get_lat_lng(row, \"destination\"),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        print(round(time.perf_counter() - s, 2), \"destination lat/lng\")\n", "\n", "        self.df[\"distance_miles\"] = self.df.apply(\n", "            lambda row: self.get_dist_mi(row), axis=1\n", "        )\n", "        print(round(time.perf_counter() - s, 2), \"distance miles\")\n", "\n", "        self.df[\"origin_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"origin_close_time\"], state=row[\"origin_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        print(round(time.perf_counter() - s, 2), \"origin diesel\")\n", "        self.update_cache()\n", "        self.df[\"destination_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"destination_close_time\"], state=row[\"destination_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        print(round(time.perf_counter() - s, 2), \"destination diesel\")\n", "\n", "        self.df[\"cpm\"] = (\n", "            self.df[\"origin_close_time\"].dt.strftime(\"%Y-%m-%d\").apply(get_daily_cpm)\n", "        )\n", "        print(\n", "            f\"Time to enrich with cpm: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        # separate_time_fields('origin_close_time', self.df)\n", "        # separate_time_fields('destination_close_time', self.df)\n", "\n", "        self.df[\"lane_id\"] = (\n", "            self.df[\"origin_city\"]\n", "            + \"_\"\n", "            + self.df[\"origin_state\"]\n", "            + \"_\"\n", "            + self.df[\"destination_city\"]\n", "            + \"_\"\n", "            + self.df[\"destination_state\"]\n", "        )\n", "\n", "        self.update_cache()\n", "\n", "mystery_data_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"mystery_data.csv\",\n", ")\n", "\n", "start_time = time.perf_counter()\n", "mystery_pipeline = MysteryPipeline()\n", "mystery_df = pd.read_csv(mystery_data_file_path)\n", "mystery_pipeline.ingest(mystery_df)\n", "print(f\"Time to ingest data: {time.perf_counter() - start_time:.2f} seconds\")\n", "start_time = time.perf_counter()\n", "mystery_pipeline.preprocess()\n", "print(f\"Time to preprocess data: {time.perf_counter() - start_time:.2f} seconds\")\n", "start_time = time.perf_counter()\n", "mystery_pipeline.enrich()\n", "print(f\"Time to enrich data: {time.perf_counter() - start_time:.2f} seconds\")\n", "mystery_pipeline.df"]}, {"cell_type": "code", "execution_count": null, "id": "8b81d538-4149-46cd-8e6d-0f596080e82b", "metadata": {}, "outputs": [], "source": ["save_df_to_s3(mystery_pipeline.df, \"mystery_full.csv\", index=False)"]}, {"cell_type": "markdown", "id": "c7df7793", "metadata": {}, "source": ["### ML Scrape Pipeline"]}, {"cell_type": "code", "execution_count": null, "id": "2b3f20d6", "metadata": {}, "outputs": [], "source": ["from machine_learning.price_recommendation import preprocessing\n", "from machine_learning.price_recommendation.preprocessing import build_timestamp\n", "from machine_learning.price_recommendation.preprocessing import (\n", "    derive_country_from_state,\n", ")\n", "from machine_learning.price_recommendation.derived_features import DieselPrice\n", "\n", "\n", "class MLScrapePipeline(DataPipeline):\n", "    def __init__(self, name: str):\n", "        super().__init__(\"MLScrapePipeline\")\n", "        self.gmaps_utils = GoogleMapsUtils()\n", "        self.diesel_price = DieselPrice()\n", "\n", "    def update_cache(self):\n", "        start_time = time.perf_counter()\n", "        self.gmaps_utils.save_and_upload_cache_files()\n", "        self.diesel_price.save_and_upload()\n", "        print(f\"Time to update cache: {time.perf_counter() - start_time} seconds\")\n", "\n", "    def map_columns(self, df: pd.DataFrame):\n", "        self.df = df\n", "        camelCols = [\n", "            \"board\",\n", "            \"cogsOfferHistory\",\n", "            \"commodity\",\n", "            \"destinationCity\",\n", "            \"destinationState\",\n", "            \"destinationZip\",\n", "            \"distanceMiles\",\n", "            \"equipmentType\",\n", "            \"lengthFt\",\n", "            \"originCity\",\n", "            \"originState\",\n", "            \"originZip\",\n", "            \"stopCount\",\n", "            \"weight\",\n", "        ]\n", "\n", "        ml_df_fields = []\n", "        for field in camelCols:\n", "            new_column_name = camel_to_snake(field)\n", "            self.df[new_column_name] = self.df.pop(field)\n", "            ml_df_fields.append(new_column_name)\n", "\n", "        column_map = {\n", "            \"cogsBookItNow\": \"OUTPUT_cogs_total\",\n", "            \"originCloseTime\": \"origin_close_timeonly\",\n", "            \"destinationCloseTime\": \"destination_close_timeonly\",\n", "            \"originOpenTime\": \"origin_open_timeonly\",\n", "            \"destinationOpenTime\": \"destination_open_timeonly\",\n", "            \"originPickupDate\": \"origin_date\",\n", "            \"destinationDropoffDate\": \"destination_date\",\n", "        }\n", "        self.df = self.df.rename(columns=column_map)\n", "        ml_df_fields.extend(column_map.values())\n", "        self.df = self.df[ml_df_fields]\n", "\n", "    def assign_dtypes(self):\n", "        # Replace NAs with empty strings\n", "        date_time_cols = [\n", "            \"origin_date\",\n", "            \"destination_date\",\n", "            \"origin_open_timeonly\",\n", "            \"origin_close_timeonly\",\n", "            \"destination_open_timeonly\",\n", "            \"destination_close_timeonly\",\n", "        ]\n", "        self.df[date_time_cols] = self.df[date_time_cols].fillna(\"\")\n", "\n", "        # Combine origin_date and origin_open_timeonly into origin_open_time\n", "        self.df[\"origin_open_time\"] = self.df.apply(\n", "            lambda row: build_timestamp(\n", "                row[\"origin_date\"], row[\"origin_open_timeonly\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.df[\"origin_close_time\"] = self.df.apply(\n", "            lambda row: build_timestamp(\n", "                row[\"origin_date\"], row[\"origin_close_timeonly\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.df[\"destination_open_time\"] = self.df.apply(\n", "            lambda row: build_timestamp(\n", "                row[\"destination_date\"], row[\"destination_open_timeonly\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.df[\"destination_close_time\"] = self.df.apply(\n", "            lambda row: build_timestamp(\n", "                row[\"destination_date\"], row[\"destination_close_timeonly\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.datetime_cols = [\n", "            \"origin_open_time\",\n", "            \"origin_close_time\",\n", "            \"destination_open_time\",\n", "            \"destination_close_time\",\n", "        ]\n", "        # Drop the columns we no longer need\n", "        self.df.drop(columns=date_time_cols, inplace=True)\n", "\n", "    def clean(self):\n", "\n", "        self.df[\"equipment_type\"] = self.df[\"equipment_type\"].apply(\n", "            preprocessing.map_equipment_type\n", "        )\n", "        # TODO(P2): This is US-only zip code\n", "        self.df[\"origin_zip\"] = self.df[\"origin_zip\"].apply(\n", "            lambda x: pd.NA if pd.isna(x) else str(x)[:5].zfill(5)\n", "        )\n", "        self.df[\"destination_zip\"] = self.df[\"destination_zip\"].apply(\n", "            lambda x: pd.NA if pd.isna(x) else str(x)[:5].zfill(5)\n", "        )\n", "        # Commodity is pretty dirty but seems to carry meaning, such as (0589 102 24rextj)\n", "        # representing a battery, so we'll leave it as is for now.\n", "\n", "    def fill_missing_cols(self):\n", "        self.df[\"origin_country\"] = self.df[\"origin_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "        self.df[\"destination_country\"] = self.df[\"destination_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "\n", "    def filter_rows(self):\n", "        print(\"Filtering rows...\")\n", "        print(f\"{'Before removing NA:':<85} {self.df.shape}\")\n", "        # Only drop rows where BOTH 'cogsBookItNow' and 'cogsOfferHistory' are NA\n", "        mask = self.df[\"OUTPUT_cogs_total\"].isna()\n", "        # If cogs_offer_history is an empty list, consider it NA\n", "        mask &= self.df[\"cogs_offer_history\"].isna() | self.df[\n", "            \"cogs_offer_history\"\n", "        ].apply(lambda x: x == \"[]\")\n", "        self.df = self.df[~mask]\n", "        print(\n", "            f\"{'After removing NA in both cogs_book_it_now and cogs_offer_history:':<85} {self.df.shape}\"\n", "        )\n", "\n", "        # Remove rows where origin or destination is NA\n", "        mask = (\n", "            self.df[\"origin_city\"].isna()\n", "            | self.df[\"origin_state\"].isna()\n", "            | self.df[\"destination_city\"].isna()\n", "            | self.df[\"destination_state\"].isna()\n", "        )\n", "        self.df = self.df[~mask]\n", "        print(f\"{'After removing NA in origin or destination:':<85} {self.df.shape}\")\n", "\n", "        # Remove rows where 'origin_close_time' are NA\n", "        mask = self.df[\"origin_close_time\"].isna()\n", "        self.df = self.df[~mask]\n", "        print(f\"{'After removing NA in origin_close_time:':<85} {self.df.shape}\")\n", "\n", "        # Assert that origin_pickup_date and destination_dropoff_date are not NA\n", "        assert self.df[\"origin_close_time\"].isna().sum() == 0\n", "\n", "        # Remove non-US rows, rows with 0 cogs, dates after current year\n", "        # Consider using this in a function since anove pipelines use it\n", "        self.df = self.df[\n", "            (self.df[\"origin_country\"] == \"US\")\n", "            & (self.df[\"destination_country\"] == \"US\")\n", "        ]\n", "        self.df = self.df[self.df[\"OUTPUT_cogs_total\"] > 0]\n", "        for datetime_col in self.datetime_cols:\n", "            self.df = filter_future_years(self.df, datetime_col)\n", "\n", "    def enrich(self):\n", "        start_time = time.perf_counter()\n", "\n", "        # Function to handle missing zipcodes with try-except\n", "        def handle_missing_zip(row, column_prefix):\n", "            city, state, country, zip_code = (\n", "                row[f\"{column_prefix}_city\"],\n", "                row[f\"{column_prefix}_state\"],\n", "                row[f\"{column_prefix}_country\"],\n", "                row[f\"{column_prefix}_zip\"],\n", "            )\n", "            if not pd.isna(zip_code):\n", "                return zip_code\n", "            try:\n", "                zip_code = self.gmaps_utils.get_zip_from_city_state(\n", "                    city, state, country\n", "                )\n", "            except ValueError as e:\n", "                print(f\"Error: {str(e)}\")\n", "                return pd.NA  # Return NA if zipcode retrieval fails\n", "\n", "        # Apply the function to origin and destination zipcodes\n", "        # self.df[\"origin_zip\"] = self.df.apply(\n", "        #     lambda row: handle_missing_zip(row, \"origin\"), axis=1\n", "        # )\n", "        # self.df[\"destination_zip\"] = self.df.apply(\n", "        #     lambda row: handle_missing_zip(row, \"destination\"), axis=1\n", "        # )\n", "\n", "        self.df[[\"origin_lat\", \"origin_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"origin_city\"],\n", "                row[\"origin_state\"],\n", "                row[\"origin_country\"],\n", "                row[\"origin_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        self.df[[\"destination_lat\", \"destination_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"destination_city\"],\n", "                row[\"destination_state\"],\n", "                row[\"destination_country\"],\n", "                row[\"destination_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        print(\n", "            f\"Time to enrich with lat/lng: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "        self.df[\"origin_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"origin_close_time\"], state=row[\"origin_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.df[\"destination_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                price_date=row[\"destination_close_time\"], state=row[\"destination_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        print(\n", "            f\"Time to enrich with diesel price: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        self.df[\"cpm\"] = (\n", "            self.df[\"origin_close_time\"].dt.strftime(\"%Y-%m-%d\").apply(get_daily_cpm)\n", "        )\n", "\n", "        print(\n", "            f\"Time to enrich with cpm: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "\n", "        self.df[\"lane_id\"] = (\n", "            self.df[\"origin_city\"]\n", "            + \"_\"\n", "            + self.df[\"origin_state\"]\n", "            + \"_\"\n", "            + self.df[\"destination_city\"]\n", "            + \"_\"\n", "            + self.df[\"destination_state\"]\n", "        )\n", "\n", "        self.update_cache()\n", "\n", "\n", "start_time = time.perf_counter()\n", "ml_scrape_pipeline = MLScrapePipeline(\"MLScrapePipeline\")\n", "ml_scrape_df = pd.read_csv(ml_data_dump_raw_file_path)\n", "ml_scrape_pipeline.ingest(ml_scrape_df)\n", "print(f\"Time to ingest data: {time.perf_counter() - start_time:.2f} seconds\")\n", "start_time = time.perf_counter()\n", "ml_scrape_pipeline.preprocess()\n", "print(f\"Time to preprocess data: {time.perf_counter() - start_time:.2f} seconds\")\n", "start_time = time.perf_counter()\n", "ml_scrape_pipeline.enrich()\n", "print(f\"Time to enrich data: {time.perf_counter() - start_time:.2f} seconds\")\n", "ml_scrape_pipeline.df"]}, {"cell_type": "code", "execution_count": null, "id": "4b03ba56", "metadata": {}, "outputs": [], "source": ["save_df_to_s3(ml_scrape_pipeline.df, \"ml_scrape_full.csv\", index=False)"]}, {"cell_type": "markdown", "id": "251a2148", "metadata": {}, "source": ["# !!Typically Stop Here for a ML Pricer Retrain!!"]}, {"cell_type": "markdown", "id": "a7ddb27e", "metadata": {}, "source": ["# Experimental Code"]}, {"cell_type": "markdown", "id": "e4da0450-9a13-49ff-befc-bff970db7686", "metadata": {"tags": []}, "source": ["### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "0e2aebbb-c972-4727-ba3b-3a3a07178384", "metadata": {}, "outputs": [], "source": ["from preprocessing import standardize_state_code, derive_country_from_state\n", "\n", "\n", "class SheerDataPipeline(DataPipeline):\n", "    def __init__(self, name: str):\n", "        super().__init__(\"SheerDataPipeline\")\n", "        self.gmaps_utils = GoogleMapsUtils()\n", "        self.diesel_price = DieselPrice()\n", "\n", "    def update_cache(self):\n", "        self.gmaps_utils.save_and_upload_cache_files()\n", "        self.diesel_price.save_and_upload()\n", "\n", "    def map_columns(self, df: pd.DataFrame):\n", "        sheer_col_map = {\n", "            \"Primary Reference\": \"id\",\n", "            \"Origin City\": \"origin_city\",\n", "            \"Origin State\": \"origin_state\",\n", "            \"Origin Zip\": \"origin_zip\",\n", "            \"Dest City\": \"destination_city\",\n", "            \"Dest State\": \"destination_state\",\n", "            \"Dest Zip\": \"destination_zip\",\n", "            \"Carrier Name\": \"OUTPUT_carrier_name\",\n", "            \"Carrier Total Line Haul\": \"OUTPUT_cogs_total\",\n", "            \"Carrier Distance\": \"distance_miles\",\n", "            \"Actual Ship\": \"origin_close_time\",\n", "            \"Actual Delivery\": \"destination_close_time\",\n", "        }\n", "        df_renamed = df.rename(columns=sheer_col_map)\n", "        self.df = df_renamed[list(sheer_col_map.values())]\n", "\n", "    def assign_dtypes(self):\n", "        self.df[\"OUTPUT_cogs_total\"] = (\n", "            self.df[\"OUTPUT_cogs_total\"].str.replace(\",\", \"\").astype(float)\n", "        )\n", "        self.df[\"origin_close_time\"] = pd.to_datetime(\n", "            self.df[\"origin_close_time\"], errors=\"coerce\"\n", "        )\n", "        self.df[\"destination_close_time\"] = pd.to_datetime(\n", "            self.df[\"destination_close_time\"], errors=\"coerce\"\n", "        )\n", "\n", "    def clean(self):\n", "        self.df[\"origin_city\"] = self.df[\"origin_city\"].str.title()\n", "        self.df[\"destination_city\"] = self.df[\"destination_city\"].str.title()\n", "        self.df[\"origin_state\"] = self.df[\"origin_state\"].apply(standardize_state_code)\n", "        self.df[\"destination_state\"] = self.df[\"destination_state\"].apply(\n", "            standardize_state_code\n", "        )\n", "\n", "    def fill_missing_cols(self):\n", "        self.df[\"origin_country\"] = self.df[\"origin_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "        self.df[\"destination_country\"] = self.df[\"destination_state\"].apply(\n", "            derive_country_from_state\n", "        )\n", "\n", "    def filter_rows(self):\n", "        self.df = self.df.dropna(subset=[\"OUTPUT_cogs_total\"])\n", "        self.df = self.df[\n", "            (self.df[\"distance_miles\"] != 0) & (self.df[\"OUTPUT_cogs_total\"] != 0)\n", "        ]\n", "        self.df = self.df[\n", "            (self.df[\"origin_country\"] == \"US\")\n", "            & (self.df[\"destination_country\"] == \"US\")\n", "        ]\n", "\n", "    def enrich(self):\n", "        self.df[[\"origin_lat\", \"origin_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"origin_city\"],\n", "                row[\"origin_state\"],\n", "                row[\"origin_country\"],\n", "                row[\"origin_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        self.df[[\"destination_lat\", \"destination_lng\"]] = self.df.apply(\n", "            lambda row: self.gmaps_utils.get_lat_lng(\n", "                row[\"destination_city\"],\n", "                row[\"destination_state\"],\n", "                row[\"destination_country\"],\n", "                row[\"destination_zip\"],\n", "            ),\n", "            axis=1,\n", "            result_type=\"expand\",\n", "        )\n", "        self.df[\"origin_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                row[\"origin_close_time\"], row[\"origin_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "        self.df[\"destination_diesel_price\"] = self.df.apply(\n", "            lambda row: self.diesel_price.get_diesel_price(\n", "                row[\"destination_close_time\"], row[\"destination_state\"]\n", "            ),\n", "            axis=1,\n", "        )\n", "\n", "        self.df[\"cpm\"] = (\n", "            self.df[\"origin_close_time\"].dt.strftime(\"%Y-%m-%d\").apply(get_daily_cpm)\n", "        )\n", "        print(\n", "            f\"Time to enrich with cpm: {time.perf_counter() - start_time:.2f} seconds\"\n", "        )\n", "        # separate_time_fields('origin_close_time', self.df)\n", "        # separate_time_fields('destination_close_time', self.df)\n", "        self.df[\"lane_id\"] = (\n", "            self.df[\"origin_city\"]\n", "            + \"_\"\n", "            + self.df[\"origin_state\"]\n", "            + \"_\"\n", "            + self.df[\"destination_city\"]\n", "            + \"_\"\n", "            + self.df[\"destination_state\"]\n", "        )\n", "        self.update_cache()\n", "\n", "sheer_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"sheer-first-dump.xls\",\n", ")\n", "\n", "sheer_pipeline = SheerDataPipeline(\"SheerDataPipeline\")\n", "sheer_df = pd.read_excel(sheer_file_path)\n", "sheer_pipeline.ingest(sheer_df)\n", "sheer_pipeline.preprocess()\n", "sheer_pipeline.enrich()\n", "sheer_pipeline.df  # The processed and enriched DataFrame"]}, {"cell_type": "markdown", "id": "b71c8ab3-9649-4d14-bb88-d5270fe5d066", "metadata": {"tags": []}, "source": ["## Random plots\n", "TODO: Apply these standard sets of plots to all the above df"]}, {"cell_type": "code", "execution_count": null, "id": "103bf627-2fbb-443e-a010-d78a7b601007", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Assuming your DataFrame is named df\n", "df = ml_scrape_pipeline.df\n", "# Group the data by 'origin_close_time' and calculate the mean for 'origin_diesel_price' and 'destination_diesel_price'\n", "grouped = df.groupby(\"origin_close_day_of_year\").agg(\n", "    {\"origin_diesel_price\": \"mean\", \"destination_diesel_price\": \"mean\"}\n", ")\n", "\n", "# Reset the index to convert 'origin_close_time' from the index to a column\n", "grouped = grouped.reset_index()\n", "\n", "# Plot the line graph\n", "plt.plot(\n", "    grouped[\"origin_close_day_of_year\"],\n", "    grouped[\"origin_diesel_price\"],\n", "    label=\"Origin Diesel Price\",\n", ")\n", "plt.plot(\n", "    grouped[\"origin_close_day_of_year\"],\n", "    grouped[\"destination_diesel_price\"],\n", "    label=\"Destination Diesel Price\",\n", ")\n", "\n", "# Add labels and title\n", "plt.xlabel(\"Origin Close Time\")\n", "plt.ylabel(\"Diesel Price\")\n", "plt.title(\"Average Diesel Price by Origin Close Time\")\n", "\n", "# Add legend\n", "plt.legend()\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "93e5a458-0442-42dc-8b51-244f7cb0b70c", "metadata": {"tags": []}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Histogram of OUTPUT_cogs_total\n", "plt.figure(figsize=(10, 6))\n", "sns.histplot(sheer_ml_df[\"OUTPUT_cogs_total\"], bins=30, kde=True)\n", "plt.title(\"Distribution of OUTPUT_cogs_total\")\n", "plt.show()\n", "\n", "# Scatter plot of OUTPUT_cogs_total vs distance_miles\n", "plt.figure(figsize=(10, 6))\n", "sns.scatterplot(x=sheer_ml_df[\"distance_miles\"], y=sheer_ml_df[\"OUTPUT_cogs_total\"])\n", "plt.title(\"OUTPUT_cogs_total vs distance_miles\")\n", "plt.show()\n", "\n", "# Box plot to check for outliers in OUTPUT_cogs_total\n", "plt.figure(figsize=(10, 6))\n", "sns.boxplot(x=sheer_ml_df[\"OUTPUT_cogs_total\"])\n", "plt.title(\"Box plot of OUTPUT_cogs_total\")\n", "plt.show()\n", "\n", "# Correlation matrix heat map\n", "corr = sheer_ml_df.select_dtypes(include=[\"float64\", \"int64\", \"UInt32\"]).corr()\n", "plt.figure(figsize=(12, 9))\n", "sns.heatmap(corr, cmap=\"coolwarm\", annot=True, fmt=\".2f\")\n", "plt.title(\"Correlation Matrix Heatmap\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f49ececf-d678-40a0-aaa8-a55e8dbcc854", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "from geopandas import GeoDataFrame\n", "import matplotlib.pyplot as plt\n", "\n", "# Assuming df is your DataFrame\n", "# If you're loading from a file uncomment below line\n", "# df = pd.read_csv(\"Long_Lats.csv\", delimiter=',', skiprows=0, low_memory=False)\n", "\n", "# Create Points for origin and destination\n", "geometry_origin = [Point(xy) for xy in zip(df[\"origin_lng\"], df[\"origin_lat\"])]\n", "geometry_destination = [\n", "    Point(xy) for xy in zip(df[\"destination_lng\"], df[\"destination_lat\"])\n", "]\n", "\n", "# Create GeoDataFrames for origin and destination\n", "gdf_origin = GeoDataFrame(df, geometry=geometry_origin)\n", "gdf_destination = GeoDataFrame(df, geometry=geometry_destination)\n", "\n", "# Load map of the world\n", "world = gpd.read_file(gpd.datasets.get_path(\"naturalearth_lowres\"))\n", "\n", "# Filter to North America\n", "north_america = world[(world[\"continent\"] == \"North America\")]\n", "\n", "# Plot the origin points\n", "ax = north_america.plot(figsize=(10, 6))\n", "gdf_origin.plot(ax=ax, marker=\"o\", color=\"red\", markersize=15, label=\"Origin\")\n", "\n", "# Plot the destination points\n", "gdf_destination.plot(\n", "    ax=ax, marker=\"x\", color=\"blue\", markersize=15, label=\"Destination\"\n", ")\n", "\n", "# Add a legend\n", "ax.legend()\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4fcf6a04-3051-4f0d-8350-c4d2fb3038c6", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from shapely.geometry import Point, LineString\n", "\n", "# Assuming df is your DataFrame\n", "# If you're loading from a file uncomment below line\n", "# df = pd.read_csv(\"Long_Lats.csv\", delimiter=',', skiprows=0, low_memory=False)\n", "# Sample a fraction of your DataFrame\n", "df_sample = df.sample(frac=0.9)\n", "\n", "# Now use df_sample to create your LineStrings and plot them\n", "# Create LineStrings for each row\n", "lines = [\n", "    LineString([Point(xy1), Point(xy2)])\n", "    for xy1, xy2 in zip(\n", "        zip(df_sample[\"origin_lng\"], df_sample[\"origin_lat\"]),\n", "        zip(df_sample[\"destination_lng\"], df_sample[\"destination_lat\"]),\n", "    )\n", "]\n", "\n", "# Create GeoDataFrame with lines\n", "gdf_lines = gpd.GeoDataFrame(df_sample, geometry=lines)\n", "\n", "# Load map of the world\n", "world = gpd.read_file(gpd.datasets.get_path(\"naturalearth_lowres\"))\n", "\n", "# Filter to North America\n", "us = world[world[\"name\"] == \"United States of America\"]\n", "\n", "# Plot the lines\n", "ax = us.plot(figsize=(20, 15))\n", "gdf_lines.plot(ax=ax, color=\"green\", alpha=0.01)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "id": "770cfc3c-21bb-4f5f-82d6-382b31579310", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["## Splitting and saving code"]}, {"cell_type": "code", "execution_count": null, "id": "eade4439-75a9-4fbb-bbb0-ee8afb48d369", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import datetime\n", "\n", "\n", "def split_df_on_col(ml_df: pd.DataFrame, col: str):\n", "    grouped_data = ml_df.groupby(col)\n", "\n", "    # Set the ratio for the train-val-test split\n", "    train_ratio = 0.7\n", "    val_ratio = 0.15\n", "    # The remaining 15% will be used for the test set\n", "\n", "    # Set the random seed for NumPy\n", "    rng = np.random.default_rng(seed=42)\n", "\n", "    # Convert dict_keys to a list and then create a NumPy array\n", "    lane_ids = np.array(list(grouped_data.groups.keys()))\n", "\n", "    # Shuffle the lane_ids using the seeded random generator\n", "    rng.shuffle(lane_ids)\n", "\n", "    # Split the lane_ids into train, val, and test sets\n", "    train_size = int(len(lane_ids) * train_ratio)\n", "    val_size = int(len(lane_ids) * val_ratio)\n", "    train_lane_ids = lane_ids[:train_size]\n", "    val_lane_ids = lane_ids[train_size : train_size + val_size]\n", "    test_lane_ids = lane_ids[train_size + val_size :]\n", "\n", "    # Create train, val, and test datasets based on the split lane_ids\n", "    train_df = ml_df[ml_df[col].isin(train_lane_ids)]\n", "    val_df = ml_df[ml_df[col].isin(val_lane_ids)]\n", "    test_df = ml_df[ml_df[col].isin(test_lane_ids)]\n", "\n", "    print(\"train/val/test\", len(train_df), len(val_df), len(test_df))\n", "    return train_df, val_df, test_df"]}, {"cell_type": "code", "execution_count": null, "id": "1b9125f4-b3ef-4dfc-b3fd-2500702f9185", "metadata": {}, "outputs": [], "source": ["mystery_train_df, mystery_val_df, mystery_test_df = split_df_on_col(\n", "    mystery_pipeline.df, \"lane_id\"\n", ")\n", "save_df_to_s3(mystery_pipeline.df, \"mystery_full.csv\")\n", "save_df_to_s3(mystery_train_df, \"mystery_train.csv\")\n", "save_df_to_s3(mystery_val_df, \"mystery_val.csv\")\n", "save_df_to_s3(mystery_test_df, \"mystery_test.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "f81b7c09-8ede-4396-991a-6944e3a3d591", "metadata": {}, "outputs": [], "source": ["train_df, val_df, test_df = split_df_on_col(rds_pipeline.df, \"lane_id\")\n", "\n", "save_df_to_s3(rds_pipeline.df, \"rds_pricing_full.csv\")\n", "save_df_to_s3(train_df, \"rds_pricing_train.csv\", ignore_cols=[\"id\"])\n", "save_df_to_s3(val_df, \"rds_pricing_val.csv\", ignore_cols=[\"id\"])\n", "save_df_to_s3(test_df, \"rds_pricing_test.csv\", ignore_cols=[\"id\"])\n", "\n", "cols_in_rds_not_sheer = [\n", "    \"stop_count\",\n", "    \"shipment_rank\",\n", "    \"weight\",\n", "    \"pre_book\",\n", "    \"clt\",\n", "    \"origin_open_hour\",\n", "    \"origin_open_day\",\n", "    \"origin_open_month\",\n", "    \"origin_open_day_of_year\",\n", "    \"origin_open_day_of_week\",\n", "    \"origin_open_week\",\n", "    \"origin_open_year\",\n", "    \"origin_open_is_workday\",\n", "    \"origin_open_is_holiday\",\n", "    \"destination_open_hour\",\n", "    \"destination_open_day\",\n", "    \"destination_open_month\",\n", "    \"destination_open_day_of_year\",\n", "    \"destination_open_day_of_week\",\n", "    \"destination_open_week\",\n", "    \"destination_open_year\",\n", "    \"destination_open_is_workday\",\n", "    \"destination_open_is_holiday\",\n", "]\n", "\n", "save_df_to_s3(\n", "    train_df,\n", "    \"rds_for_sheer_pricing_train.csv\",\n", "    ignore_cols=[\"id\"] + cols_in_rds_not_sheer,\n", ")\n", "save_df_to_s3(\n", "    val_df, \"rds_for_sheer_pricing_val.csv\", ignore_cols=[\"id\"] + cols_in_rds_not_sheer\n", ")\n", "save_df_to_s3(\n", "    test_df,\n", "    \"rds_for_sheer_pricing_test.csv\",\n", "    ignore_cols=[\"id\"] + cols_in_rds_not_sheer,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dccd833a-33a2-4ab9-ad40-46250448112a", "metadata": {}, "outputs": [], "source": ["ml_df[\"sampling_weight\"] = 1\n", "sheer_ml_train[\"sampling_weight\"] = 30\n", "rds_sheer_train_df = pd.concat([ml_df, sheer_ml_train])\n", "sheer_val_test_df = pd.concat([sheer_ml_val, sheer_ml_test])\n", "sheer_val_test_df[\"sampling_weight\"] = 30\n", "\n", "save_df_to_s3(\n", "    rds_sheer_train_df,\n", "    \"rds_sheer_weighted_pricing_train.csv\",\n", "    ignore_cols=[\"id\", \"OUTPUT_carrier_name\", \"lane_id\"] + cols_in_rds_not_sheer,\n", ")\n", "save_df_to_s3(\n", "    sheer_val_test_df,\n", "    \"sheer_pricing_val_test.csv\",\n", "    ignore_cols=[\"id\", \"OUTPUT_carrier_name\", \"lane_id\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7363617e-5164-4835-b66c-37074b2426fc", "metadata": {}, "outputs": [], "source": ["sheer_weighted_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"rds_sheer_weighted_pricing_train.csv\",\n", ")\n", "\n", "pd.read_csv(sheer_weighted_file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "68c74cf4-c441-4d5b-ac33-c7a4c21691d0", "metadata": {}, "outputs": [], "source": ["# from common.s3_cache_file import S3\n", "\n", "# ml_df_filename = 'pricing_ml_df_' + datetime.datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\") + '.csv'\n", "\n", "# ml_df.set_index('id', inplace=True)\n", "# ml_df.to_csv(ml_df_filename)\n", "\n", "# s3_ml_data = S3(\"truce-ml-data\")\n", "# s3_ml_data.upload(ml_df_filename)"]}, {"cell_type": "markdown", "id": "44f94069-5734-4bf0-82f5-c4d67545eeb5", "metadata": {"tags": []}, "source": ["## Feature Experimental Code"]}, {"cell_type": "markdown", "id": "bc89cd1d-b47b-423c-aba9-fd99dc6162ff", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### CityPop"]}, {"cell_type": "code", "execution_count": null, "id": "1ab51de0-915b-43b9-96d9-78124eaa9fce", "metadata": {}, "outputs": [], "source": ["rds_df = pd.read_csv(rds_raw_file_path)\n", "mystery_df = pd.read_csv(mystery_data_file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "4f25dbb1-a4ed-4f6c-b15c-7b6fe7103fe0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "us_cities_file_path = os.path.join(\n", "    PROJECT_ROOT,\n", "    \"machine_learning\",\n", "    \"price_recommendation\",\n", "    \"data\",\n", "    \"uscities.xlsx\",\n", ")\n", "\n", "us_cities = pd.read_excel(us_cities_file_path)\n", "# Convert the city column to lower case for case insensitive matching\n", "us_cities[\"city_ascii\"] = us_cities[\"city_ascii\"].str.lower()\n", "\n", "# Set 'city_ascii' as index\n", "us_cities.set_index(\"city_ascii\", inplace=True)\n", "\n", "us_cities"]}, {"cell_type": "code", "execution_count": null, "id": "ccd11f06-6181-44ce-8715-c1e167d8c616", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "from tqdm import tqdm\n", "\n", "# Assume that us_cities is your DataFrame and has been defined somewhere in the code.\n", "\n", "alts = set()\n", "multiples = set()\n", "\n", "from scipy.spatial import KDTree\n", "import numpy as np\n", "\n", "# Assume that us_cities is your DataFrame and has been defined somewhere in the code.\n", "\n", "# Construct a KDTree for efficient spatial lookup\n", "coordinates = us_cities[[\"lat\", \"lng\"]].values\n", "tree = KDTree(coordinates)\n", "\n", "find_nearest_cache = {}\n", "\n", "\n", "def find_nearest(lat, lng, c, s) -> pd.DataFrame:\n", "    # Check if the result is in the cache\n", "    if (lat, lng) in find_nearest_cache:\n", "        return find_nearest_cache[(lat, lng)]\n", "\n", "    # Find the index of the nearest point to the given latitude and longitude\n", "    dist, idx = tree.query(np.array([lat, lng]).reshape(1, -1))\n", "    result = us_cities.iloc[idx]\n", "\n", "    # Store the result in the cache before returning it\n", "    find_nearest_cache[(lat, lng)] = result\n", "\n", "    assert len(result) == 1\n", "    return result\n", "\n", "\n", "def match_locations(city: str, state: str, lat: float, lng: float):\n", "    try:\n", "        matched_df = us_cities.loc[city.lower()]\n", "    except KeyError:\n", "        matched_df = us_cities[us_cities[\"city_alt\"] == city]\n", "\n", "    if isinstance(matched_df, pd.Series):\n", "        matched_df = matched_df.to_frame().T\n", "\n", "    if len(matched_df) > 1:\n", "        matched_df = matched_df[matched_df[\"state_id\"] == state]\n", "\n", "    # If no match was found, or too many find the nearest city based on lat/lng\n", "    if len(matched_df) == 0 or len(matched_df) > 1:\n", "        matched_df = find_nearest(lat, lng, city, state)\n", "\n", "    assert len(matched_df) == 1\n", "    return matched_df\n", "\n", "\n", "na_counter = 0\n", "na_df = pd.DataFrame()\n", "for i, r in tqdm(rds_df.iterrows(), total=len(rds_df)):\n", "    df = match_locations(\n", "        r[\"origin_city\"], r[\"origin_state\"], r[\"origin_lat\"], r[\"origin_lng\"]\n", "    )\n", "    if pd.isna(df[\"population\"]).all():\n", "        na_counter += 1\n", "        na_df = df\n", "        break\n", "\n", "# for i, r in tqdm(mystery_df.iterrows(), total=len(mystery_df)):\n", "#     df = match_locations(r['origin_city'], r['origin_state'], r['origin_lat'], r['origin_lng'])\n", "#     if pd.isna(df['population']).all():\n", "#         na_counter += 1"]}, {"cell_type": "code", "execution_count": null, "id": "abe71cc1-e94b-49bc-baf7-83166a549c29", "metadata": {}, "outputs": [], "source": ["na_df[na_df.columns[~na_df.isna().values[0]]]"]}, {"cell_type": "markdown", "id": "ac79e9e2-08c4-4eed-a981-9358409742a1", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### CPM"]}, {"cell_type": "code", "execution_count": null, "id": "1502d382-30e7-46b6-845e-3f9c4840c765", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import math\n", "import pandas as pd\n", "\n", "# define which dataframe we want to use for cpm calculation\n", "metadata_df1 = pd.read_csv(\n", "    metadata_file_path,\n", "    parse_dates=[\"originCloseTime\", \"shipmentIngestionTime\"],\n", ")\n", "\n", "# https://github.com/truce-logistics/truce/blob/c2cdc4d8f9ae8e5020e9a0b04ab3f8c7c5f81443/web_backend/lambda/shipment_request_handler_lazy/query_generator.py#L1194-L1277\n", "# Look at filters from here\n", "\n", "# drop the nan values\n", "metadata_df1.dropna(\n", "    axis=0,\n", "    subset=[\"cogsTotal\", \"distanceMiles\", \"shipmentIngestionTime\", \"originCloseTime\"],\n", "    inplace=True,\n", ")\n", "\n", "metadata_df1 = metadata_df1[\n", "    metadata_df1[\"shipmentIngestionTime\"] > datetime.datetime(2023, 4, 23)\n", "]\n", "# replace nan value with 0 for cogsAccessorial and filter out the 0 values within distanceMiles\n", "metadata_df1 = metadata_df1[metadata_df1[\"distanceMiles\"] != 0]\n", "metadata_df1 = metadata_df1[metadata_df1[\"distanceMiles\"] > 200]\n", "metadata_df1[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "metadata_df1[\"cpm\"] = (\n", "    metadata_df1[\"cogsTotal\"] - metadata_df1[\"cogsAccessorial\"]\n", ") / metadata_df1[\"distanceMiles\"]\n", "\n", "# calculate the logged day between shipmentIngestionTime and orginCloseTime\n", "metadata_df1[\"lagged_hours\"] = (\n", "    metadata_df1[\"shipmentIngestionTime\"] - metadata_df1[\"originCloseTime\"]\n", ").dt.total_seconds() / 3600\n", "metadata_df1[\"lagged_days\"] = (\n", "    (\n", "        metadata_df1[\"shipmentIngestionTime\"] - metadata_df1[\"originCloseTime\"]\n", "    ).dt.total_seconds()\n", "    / 3600\n", "    / 24\n", ")\n", "metadata_df1[\"rounded_lagged_days\"] = (\n", "    (\n", "        metadata_df1[\"shipmentIngestionTime\"] - metadata_df1[\"originCloseTime\"]\n", "    ).dt.total_seconds()\n", "    / 3600\n", "    / 24\n", ").apply(math.ceil)\n", "\n", "\n", "# filter out the values with negative Lagged_day\n", "metadata_df1 = metadata_df1[metadata_df1[\"lagged_hours\"] >= 0]\n", "metadata_df1[\"cpm\"][metadata_df1[\"cpm\"] < 10].hist()"]}, {"cell_type": "code", "execution_count": null, "id": "094bfc72-848f-4a34-8a8d-ad836f5e1b39", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "\n", "# Convert the column to datetime if it's not\n", "metadata_df1[\"shipmentIngestionTime\"] = pd.to_datetime(\n", "    metadata_df1[\"shipmentIngestionTime\"]\n", ")\n", "\n", "# Plot a histogram of the 'shipmentIngestionTime' column\n", "fig, ax = plt.subplots(figsize=(10, 8))\n", "metadata_df1[\"shipmentIngestionTime\"].hist(\n", "    bins=50, ax=ax, color=\"skyblue\", edgecolor=\"black\"\n", ")\n", "\n", "# Set x-axis major ticks to monthly interval, on the 1st day of the month\n", "ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))\n", "\n", "# Get only the month to show in the x-axis\n", "ax.xaxis.set_major_formatter(mdates.DateFormatter(\"%b %Y\"))\n", "\n", "# Set x-axis range\n", "xmin = pd.to_datetime(\"2023-04-20\").to_pydatetime()\n", "xmax = pd.to_datetime(\"2023-07-15\").to_pydatetime()\n", "ax.set_xlim([xmin, xmax])\n", "\n", "plt.xticks(rotation=90)\n", "plt.xlabel(\"shipmentIngestionTime\")\n", "plt.ylabel(\"Count\")\n", "plt.title(\"Histogram of shipmentIngestionTime\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ef84ac36-9353-4605-9ac2-177f998143a4", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# TODO(P0): Explore mile capping\n", "\n", "# First, set your 'originCloseTime' column as the index of your dataframe\n", "threshold_date = datetime.datetime(2023, 7, 10)\n", "daily_cpm_df = metadata_df1[metadata_df1[\"originCloseTime\"] < threshold_date].set_index(\n", "    \"originCloseTime\"\n", ")\n", "\n", "# Resample 'cpm' by day and calculate mean\n", "daily_cpm = daily_cpm_df[\"cpm\"].resample(\"D\").mean()\n", "\n", "# Plot 'cpm' as a line chart\n", "plt.figure(figsize=(10, 6))\n", "daily_cpm.plot(kind=\"line\")\n", "plt.title(\"CPM over Time\")\n", "plt.xlabel(\"originCloseTime\")\n", "plt.ylabel(\"CPM\")\n", "plt.show()  # Compute a rolling window of 5-day averages\n", "rolling_cpm = daily_cpm.rolling(window=7).mean()\n", "\n", "# Plot 'cpm' as a line chart\n", "plt.figure(figsize=(10, 6))\n", "rolling_cpm.plot(kind=\"line\")\n", "plt.title(\"weekly Average CPM over Time\")\n", "plt.xlabel(\"originCloseTime\")\n", "plt.ylabel(\"weekly Average CPM\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "52972563-3116-4aae-aee9-974543d86ee0", "metadata": {}, "outputs": [], "source": ["# generate the ingestion time analysis hist figure\n", "import matplotlib.pyplot as plt\n", "\n", "# extract data has less than 200 lagged hours\n", "series1 = metadata_df1[\"lagged_hours\"][metadata_df1[\"lagged_hours\"] < 200]\n", "\n", "# Create the histogram\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(series1, bins=14)\n", "\n", "# Customize the plot\n", "plt.xlabel(\"Lagged Hour\")\n", "plt.ylabel(\"Count\")\n", "plt.title(\"Histogram of Lagged Hour\")\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8b7de3d7-4f37-46db-bb28-d80e36d07626", "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "\n", "def normalize_cpms(df):\n", "    # calcualte the final cpm value for cpm days and return a dataframe which contains final cpm value for all cpm days\n", "    # df: dataframe which we want to use for final cpm calculation\n", "\n", "    # creat a copy of df as reference_df\n", "    reference_df = df.copy()\n", "\n", "    # drop the nan values if cogsTotal , distanceMiles and shipmentIngestionTime has nan\n", "    reference_df.dropna(\n", "        axis=0,\n", "        subset=[\"cogsTotal\", \"distanceMiles\", \"shipmentIngestionTime\"],\n", "        inplace=True,\n", "    )\n", "\n", "    # replace nan value with 0 for cogsAccessorial and filter out the 0 values within distanceMiles\n", "    reference_df = reference_df[reference_df[\"distanceMiles\"] != 0]\n", "    reference_df = reference_df[reference_df[\"cogsTotal\"] != 0]\n", "    reference_df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "    reference_df[\"cpm\"] = (\n", "        reference_df[\"cogsTotal\"] - reference_df[\"cogsAccessorial\"]\n", "    ) / reference_df[\"distanceMiles\"]\n", "\n", "    # extract the time value accordingly\n", "    reference_df[\"shipmentDay\"] = reference_df[\"originCloseTime\"].dt.date\n", "    reference_df[\"shipmentYear\"] = reference_df[\"originCloseTime\"].dt.year\n", "    reference_df[\"shipmentWeek\"] = reference_df[\"originCloseTime\"].dt.isocalendar().week\n", "    reference_df[\"shipmentMonth\"] = reference_df[\"originCloseTime\"].dt.to_period(\"M\")\n", "\n", "    # Combine 'year' and 'week' columns into a single column\n", "    reference_df[\"shipmentYearWeek\"] = (\n", "        reference_df[\"shipmentYear\"].astype(str)\n", "        + \"-\"\n", "        + reference_df[\"shipmentWeek\"].apply(lambda x: \"{:02d}\".format(x))\n", "    )\n", "\n", "    # generate avg_cpm on daily, weekly\n", "    daily_avg_cpm = reference_df.groupby(by=[\"shipmentDay\"])[\"cpm\"].mean()\n", "    weekly_avg_cpm = reference_df.groupby(by=[\"shipmentYearWeek\"])[\"cpm\"].mean()\n", "\n", "    # Map the calculated avgcpm values into the dataframe\n", "    reference_df[\"avgdcpm\"] = reference_df[\"shipmentDay\"].map(daily_avg_cpm)\n", "    reference_df[\"avgwcpm\"] = reference_df[\"shipmentYearWeek\"].map(weekly_avg_cpm)\n", "\n", "    return reference_df[[\"originCloseTime\", \"shipmentYearWeek\", \"avgdcpm\", \"avgwcpm\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "f7b08e33-272e-42b2-be02-0f0834daabe0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "\n", "def compute_cpm_per_lag(df, cpm_date, aggregation_type, max_lag):\n", "    # Compute average cpm (aggregated by agg_type) from start date for each lag day up to max_lag days.\n", "    # df: initial dataframe we want to use\n", "    # cpm_date: day to start looking back from\n", "    # aggregation_type: string for day or week\n", "    # max_lag: number of lag days to try\n", "    # (2.5, 3.5, 5.6, 6.4, 2.3) len == max_lag\n", "    # return list where index == lag_day, value == avg cpm\n", "\n", "    lag_avg_cpm = []  # List to store average cpm values for each lag\n", "    # Filter the dataframe based on the cpm_date\n", "    cpm_date = datetime.datetime.strptime(cpm_date, \"%Y-%m-%d\").date()\n", "    # calculate the final cpm values\n", "    reference_df1 = normalize_cpms(metadata_df1)\n", "    print(cpm_date)\n", "    # if no orders on a cpm day, final cpm values will be 0 otherwiese it will be the average cpm values of the cpm day\n", "    if (\n", "        len(\n", "            reference_df1[\"avgdcpm\"][\n", "                reference_df1[\"originCloseTime\"].dt.date == cpm_date\n", "            ].values\n", "        )\n", "        != 0\n", "    ):\n", "        final_dcpm = float(\n", "            reference_df1[\"avgdcpm\"][\n", "                reference_df1[\"originCloseTime\"].dt.date == cpm_date\n", "            ].values[0]\n", "        )\n", "        final_wcpm = float(\n", "            reference_df1[\"avgwcpm\"][\n", "                reference_df1[\"originCloseTime\"].dt.date == cpm_date\n", "            ].values[0]\n", "        )\n", "        print(\n", "            \"final daily cpm: \"\n", "            + str(final_dcpm)\n", "            + \" final weekily cpm: \"\n", "            + str(final_wcpm)\n", "        )\n", "    else:\n", "        final_dcpm = np.nan\n", "        final_wcpm = np.nan\n", "    for lag in range(1, max_lag + 1):\n", "        # Filter the dataframe based on lag\n", "        lagged_df = df[\n", "            (df[\"originCloseTime\"].dt.date == cpm_date) & (df[\"lagged_days\"] <= lag)\n", "        ]\n", "        # Extract the time value accordingly\n", "        lagged_df[\"shipmentDay\"] = lagged_df[\"originCloseTime\"].dt.date\n", "        lagged_df[\"shipmentYear\"] = lagged_df[\"originCloseTime\"].dt.year\n", "        lagged_df[\"shipmentWeek\"] = (\n", "            lagged_df[\"originCloseTime\"].dt.isocalendar().week.astype(int)\n", "        )\n", "        lagged_df[\"shipmentMonth\"] = lagged_df[\"originCloseTime\"].dt.to_period(\"M\")\n", "        # Combine 'year' and 'week' columns into a single column\n", "        lagged_df[\"shipmentYearWeek\"] = (\n", "            lagged_df[\"shipmentYear\"].astype(str)\n", "            + \"-\"\n", "            + lagged_df[\"shipmentWeek\"].apply(lambda x: \"{:02d}\".format(x))\n", "        )\n", "        # calculate the average cpm based on different conditions and map the average cpm values, then append the average cpm\n", "        if aggregation_type == \"day\":\n", "            grouping_column = \"shipmentDay\"\n", "            daily_avg_cpm = lagged_df.groupby(by=[grouping_column])[\"cpm\"].mean()\n", "            lagged_df[\"avgdcpm\"] = lagged_df[\"shipmentDay\"].map(daily_avg_cpm)\n", "            if len(lagged_df[\"avgdcpm\"].values) == 0:\n", "                lag_avg_cpm.append(np.nan)\n", "                print(\"average daily cpm: nan  \", lag_avg_cpm)\n", "            else:\n", "                print(\n", "                    \"average daily cpm  :\"\n", "                    + str(lagged_df[\"avgdcpm\"].values[0])\n", "                    + \"  lagged day #  \"\n", "                    + str(lag)\n", "                )\n", "                lagged_df[\"avgdcpm\"] = lagged_df[\"avgdcpm\"] / final_dcpm\n", "                lag_avg_cpm.append(lagged_df[\"avgdcpm\"].values[0])\n", "                print(lag_avg_cpm)\n", "        elif aggregation_type == \"week\":\n", "            grouping_column = \"shipmentYearWeek\"\n", "            weekly_avg_cpm = lagged_df.groupby(by=[\"shipmentYearWeek\"])[\"cpm\"].mean()\n", "            lagged_df[\"avgwcpm\"] = lagged_df[\"shipmentYearWeek\"].map(weekly_avg_cpm)\n", "            if len(lagged_df[\"avgwcpm\"].values) == 0:\n", "                lag_avg_cpm.append(np.nan)\n", "                print(\"average weekly cpm: nan  \", lag_avg_cpm)\n", "            else:\n", "                print(\n", "                    \"average weekly cpm  :\"\n", "                    + str(lagged_df[\"avgwcpm\"].values[0])\n", "                    + \"  lagged week #  \"\n", "                    + str(lag)\n", "                )\n", "                lagged_df[\"avgwcpm\"] = lagged_df[\"avgwcpm\"] / final_dcpm\n", "                lag_avg_cpm.append(lagged_df[\"avgwcpm\"].values[0])\n", "        else:\n", "            raise ValueError(\n", "                \"Invalid aggregation_type. Supported values are 'day' and 'week'.\"\n", "            )\n", "    return lag_avg_cpm"]}, {"cell_type": "code", "execution_count": null, "id": "b829a73b-5ffb-4703-ad9e-4f1488e7e764", "metadata": {}, "outputs": [], "source": ["def compute_cpm_lag_daterange(start_day, end_day, lag):\n", "    # Compute cpm values for lag up to max lag for each date in the time range\n", "    # for each day in the range -> list avg cpms returned by compute_cpm_per_lag\n", "    # start_day: day we start to calculate the average cpm\n", "    # end_day: last day we try to calculate the average cpm\n", "    # Columns: lag day, Rows: normalized cpms\n", "    # date   0      1       2    ....\n", "    # ____   2.34   2.45    3.45\n", "    # return df\n", "\n", "    # Create an empty DataFrame to store the results\n", "    result_df = pd.DataFrame(columns=range(lag))\n", "\n", "    # Convert start_day and end_day to datetime.date objects\n", "    start_day = datetime.datetime.strptime(start_day, \"%Y-%m-%d\").date()\n", "    end_day = datetime.datetime.strptime(end_day, \"%Y-%m-%d\").date()\n", "\n", "    # Iterate over the range of days\n", "    for day in range((end_day - start_day).days + 1):\n", "        current_day = start_day + datetime.timedelta(days=day)\n", "        day_str = current_day.strftime(\n", "            \"%Y-%m-%d\"\n", "        )  # Format the current_day as 'YYYY-MM-DD' string\n", "\n", "        # Call compute_cpm_per_lag function to get a list of results\n", "        cpms_list = compute_cpm_per_lag(metadata_df1, day_str, aggregation_type, lag)\n", "\n", "        # Add the cpms_list values to the result DataFrame as rows\n", "        result_df.loc[day] = cpms_list\n", "\n", "    return result_df"]}, {"cell_type": "code", "execution_count": null, "id": "a82c6077-c670-4be1-8608-b594d02e3b9b", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_box_and_whisker(df):\n", "    # plot the box whisker for the entire dataframe to observe the overall converge time(same plot)\n", "    # df: dataframe we want to plot\n", "\n", "    # convert the df shape\n", "    plt.figure(figsize=(25, 12))\n", "    df.boxplot()\n", "    plt.title(\"Inference time analysis box chart\")\n", "    plt.xlabel(\"Lagged Days\")\n", "    plt.ylabel(\"Average CPMs\")\n", "    plt.xticks(rotation=90)  # rotate x labels for better visibility if you have many\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "9c09e65f-3cf3-47fe-b276-a87c3aaa062a", "metadata": {"tags": []}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "df = metadata_df1\n", "start_day = \"2022-11-20\"\n", "end_day = \"2023-6-4\"\n", "aggregation_type = \"day\"\n", "lag = 14\n", "\n", "final_cpm_df = compute_cpm_lag_daterange(start_day, end_day, lag)"]}, {"cell_type": "code", "execution_count": null, "id": "26e88901-ca6f-44f8-abe5-044603859016", "metadata": {}, "outputs": [], "source": ["final_cpm_df"]}, {"cell_type": "code", "execution_count": null, "id": "08dd47ef-8190-429d-a383-1705a6ca39c9", "metadata": {}, "outputs": [], "source": ["plot_box_and_whisker(final_cpm_df)"]}, {"cell_type": "markdown", "id": "365e5752-487d-48d5-90b1-3b352d0ae9cd", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### <PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "4f6265f2-45f3-4f7f-86e0-7a1b08414471", "metadata": {}, "outputs": [], "source": ["# FRED_API_KEY = '8f412422c5f604de62093f0def1cb986'\n", "# from fredapi import Fred\n", "\n", "# fred = <PERSON>(api_key=FRED_API_KEY)\n", "\n", "# # get the latest value of the Heavy Duty Truck Retail Price Index\n", "# df = pd.DataFrame(fred.get_series('FRED/FRGSHPUSM649NCIS'))\n", "\n", "# # extract the latest value\n", "# latest_value = df.iloc[-1]['FRED/FRGSHPUSM649NCIS']\n", "\n", "# print(\"Today's Heavy Duty Truck Retail Price Index is:\", latest_value)"]}, {"cell_type": "markdown", "id": "042efc6d-ab92-40e4-9b5e-d1f568fe2730", "metadata": {"jp-MarkdownHeadingCollapsed": true, "tags": []}, "source": ["### Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "d7bc86ab-26b5-47a0-aff6-5f98fe6df3c7", "metadata": {"tags": []}, "outputs": [], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "df = ml_df\n", "\n", "# Select the object columns to be converted to float type\n", "object_cols = df.select_dtypes(include=[\"object\"]).columns.tolist()\n", "\n", "NUMERIC_COL_NAMES = [\n", "    \"stopCount\",\n", "    \"distanceMiles\",\n", "    # \"cogsLineHaul\",\n", "    # \"revenueLineHaul\",\n", "    # \"cogsFuel\",\n", "    # \"revenueFuel\",\n", "    # \"cogsAccessorial\",\n", "    # \"revenueAccessorial\",\n", "    \"cogsTotal\",\n", "    # \"revenueTotal\",\n", "    \"weight\",\n", "    # \"ltuScore\",\n", "    # \"performanceScore\",\n", "    # \"costScore\",\n", "    # \"preBook\",\n", "    # \"clt\",\n", "    # \"blt\"\n", "    # \"originCloseTime_hourOfDay\", \"originCloseTime_dayOfWeek\", \"originCloseTime_dayOfYear\", \"originCloseTime_monthOfYear\", \"originCloseTime_year\"\n", "    # Time is completely uncorrelated with any other numerical feature\n", "]\n", "\n", "# Convert object columns to float type\n", "df[NUMERIC_COL_NAMES] = df[NUMERIC_COL_NAMES].apply(pd.to_numeric, errors=\"coerce\")\n", "\n", "corr_matrix = df[NUMERIC_COL_NAMES].corr()\n", "\n", "# Plot the correlation matrix as a heatmap\n", "plt.figure(figsize=(30, 30))\n", "sns.heatmap(corr_matrix, annot=True, cmap=\"coolwarm\")\n", "plt.title(\"Correlation Matrix Heatmap\")\n", "plt.show()\n", "\n", "# Sort the correlation values in descending order\n", "# corr_values = corr_matrix[\"cogsTotal\"].sort_values(ascending=False)\n", "\n", "# Print the correlation values for each column\n", "# print(\"\\nCorrelation values for each column:\\n\", corr_values)"]}, {"cell_type": "markdown", "id": "cdaf0171-fac4-4323-81cd-05985e8ea363", "metadata": {"tags": []}, "source": ["##### Visualize our EIA Areas"]}, {"cell_type": "code", "execution_count": null, "id": "ebf5469e-47cb-47ed-93dc-6f6a30c913b0", "metadata": {"tags": []}, "outputs": [], "source": ["us_state_to_abbrev = {\n", "    \"Alabama\": \"AL\",\n", "    \"Alaska\": \"AK\",\n", "    \"Arizona\": \"AZ\",\n", "    \"Arkansas\": \"AR\",\n", "    \"California\": \"CA\",\n", "    \"Colorado\": \"CO\",\n", "    \"Connecticut\": \"CT\",\n", "    \"Delaware\": \"DE\",\n", "    \"Florida\": \"FL\",\n", "    \"Georgia\": \"GA\",\n", "    \"Hawaii\": \"HI\",\n", "    \"Idaho\": \"ID\",\n", "    \"Illinois\": \"IL\",\n", "    \"Indiana\": \"IN\",\n", "    \"Iowa\": \"IA\",\n", "    \"Kansas\": \"KS\",\n", "    \"Kentucky\": \"KY\",\n", "    \"Louisiana\": \"LA\",\n", "    \"Maine\": \"ME\",\n", "    \"Maryland\": \"MD\",\n", "    \"Massachusetts\": \"MA\",\n", "    \"Michigan\": \"MI\",\n", "    \"Minnesota\": \"MN\",\n", "    \"Mississippi\": \"MS\",\n", "    \"Missouri\": \"MO\",\n", "    \"Montana\": \"MT\",\n", "    \"Nebraska\": \"NE\",\n", "    \"Nevada\": \"NV\",\n", "    \"New Hampshire\": \"NH\",\n", "    \"New Jersey\": \"NJ\",\n", "    \"New Mexico\": \"NM\",\n", "    \"New York\": \"NY\",\n", "    \"North Carolina\": \"NC\",\n", "    \"North Dakota\": \"ND\",\n", "    \"Ohio\": \"OH\",\n", "    \"Oklahoma\": \"OK\",\n", "    \"Oregon\": \"OR\",\n", "    \"Pennsylvania\": \"PA\",\n", "    \"Rhode Island\": \"RI\",\n", "    \"South Carolina\": \"SC\",\n", "    \"South Dakota\": \"SD\",\n", "    \"Tennessee\": \"TN\",\n", "    \"Texas\": \"TX\",\n", "    \"Utah\": \"UT\",\n", "    \"Vermont\": \"VT\",\n", "    \"Virginia\": \"VA\",\n", "    \"Washington\": \"WA\",\n", "    \"West Virginia\": \"WV\",\n", "    \"Wisconsin\": \"WI\",\n", "    \"Wyoming\": \"WY\",\n", "    \"District of Columbia\": \"DC\",\n", "    \"American Samoa\": \"AS\",\n", "    \"Guam\": \"GU\",\n", "    \"Northern Mariana Islands\": \"MP\",\n", "    \"Puerto Rico\": \"PR\",\n", "    \"United States Minor Outlying Islands\": \"UM\",\n", "    \"U.S. Virgin Islands\": \"VI\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "778b3d43-63a1-47d8-93cc-36fe04578104", "metadata": {"tags": []}, "outputs": [], "source": ["import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.axes_grid1 import make_axes_locatable\n", "from constants import STATE_TO_EIA_AREA\n", "\n", "# Read US States shapefile\n", "url = \"https://raw.githubusercontent.com/PublicaMundi/MappingAPI/master/data/geojson/us-states.json\"\n", "gdf = gpd.read_file(url)\n", "\n", "# Add EIA area-name column to the GeoDataFrame\n", "gdf[\"state_code\"] = gdf[\"name\"].map(us_state_to_abbrev)\n", "gdf[\"eia_area\"] = gdf[\"state_code\"].map(STATE_TO_EIA_AREA)\n", "\n", "# Plot US States map with different colors for each EIA area-name\n", "fig, ax = plt.subplots(1, 1, figsize=(15, 25))\n", "divider = make_axes_locatable(ax)\n", "cax = divider.append_axes(\"right\", size=\"5%\", pad=0.1)\n", "gdf.plot(\n", "    column=\"eia_area\", legend=True, ax=ax, cax=cax, cmap=\"viridis\", edgecolor=\"black\"\n", ")\n", "ax.set_title(\"EIA Area-Name by US State\")\n", "ax.set_axis_off()\n", "plt.show()\n", "\n", "# https://en.wikipedia.org/wiki/Petroleum_Administration_for_Defense_Districts#/media/File:Petroleum_Administration_for_Defense_Districts.svg"]}, {"cell_type": "code", "execution_count": null, "id": "eb9a7e12", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "\n", "def get_weather_for_sunnyvale():\n", "    # 1. Identify the NWS office and grid coordinates for Sunnyvale, CA\n", "    url_base = \"https://api.weather.gov/points/\"\n", "    lat, lng = 37.0288, -122.0363  # Coordinates for Sunnyvale, CA\n", "    point_response = requests.get(f\"{url_base}{lat},{lng}\").json()\n", "\n", "    # Check for grid data in the response\n", "    if \"properties\" in point_response:\n", "        forecast_office = point_response[\"properties\"][\"cwa\"]\n", "        grid_x = point_response[\"properties\"][\"gridX\"]\n", "        grid_y = point_response[\"properties\"][\"gridY\"]\n", "    else:\n", "        print(\"Error fetching grid data.\")\n", "        return\n", "\n", "    # 2. Use the API to fetch current conditions for the identified grid\n", "    current_weather_url = f\"https://api.weather.gov/gridpoints/{forecast_office}/{grid_x},{grid_y}/forecast\"\n", "    weather_response = requests.get(current_weather_url).json()\n", "\n", "    # Extract current conditions (assuming first period is the current condition)\n", "    if \"properties\" in weather_response and \"periods\" in weather_response[\"properties\"]:\n", "        current_conditions = weather_response[\"properties\"][\"periods\"][0]\n", "        return current_conditions\n", "    else:\n", "        print(\"Error fetching current weather.\")\n", "        return\n", "\n", "\n", "weather_data = get_weather_for_sunnyvale()\n", "if weather_data:\n", "    print(weather_data[\"name\"], \"-\", weather_data[\"detailedForecast\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}