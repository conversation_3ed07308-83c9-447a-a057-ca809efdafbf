{"cells": [{"cell_type": "code", "execution_count": null, "id": "02d17d63", "metadata": {}, "outputs": [], "source": ["!pip install nbformat"]}, {"cell_type": "code", "execution_count": null, "id": "c8d99048", "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()"]}, {"cell_type": "markdown", "id": "98d87529-8d22-42f2-9fa9-b451b8e93a33", "metadata": {"kernelspec": {"display_name": "default:Python", "language": "python", "name": "conda-env-default-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "source": ["## Bound Evaluation\n", "\n", "### Goals\n", "1. Understand if there's a way to derive \"confidence\" from bound sizes.\n", "2. See if we can reasonably tighten bounds without losing performance."]}, {"cell_type": "code", "execution_count": null, "id": "9bf1f0bf-8ce8-4c86-867f-75d1449e79e5", "metadata": {"tags": []}, "outputs": [], "source": ["# Load shipments data and predictions from RDS\n", "\n", "import pandas as pd\n", "from common import query_utils\n", "\n", "records_df = pd.DataFrame()\n", "\n", "QUERY_STR = \"\"\"\n", "    SELECT\n", "        s.*,\n", "        oc.city AS originCity,\n", "        oc.country AS originCountry,\n", "        oc.state AS originState,\n", "        dc.city AS destinationCity,\n", "        dc.country AS destinationCountry,\n", "        dc.state AS destinationState,\n", "        oz.zip as originZip,\n", "        dz.zip as destinationZip\n", "    FROM\n", "        (\n", "            SELECT\n", "                s.*,\n", "                l.originCityId,\n", "                l.destinationCityId,\n", "                l.equipmentType,\n", "                l.<PERSON>\n", "            FROM\n", "                mvp_db_dev.shipments AS s\n", "                JOIN mvp_db_dev.lanes AS l ON s.laneid=l.id\n", "            WHERE\n", "                s.shipmentClass = 'canonical'\n", "        ) AS s\n", "        JOIN mvp_db_dev.cities AS oc ON s.originCityId = oc.id\n", "        JOIN mvp_db_dev.cities AS dc ON s.destinationCityId = dc.id\n", "        JOIN mvp_db_dev.zips AS oz ON s.originZipId = oz.id\n", "        JOIN mvp_db_dev.zips AS dz ON s.destinationZipId = dz.id\n", "\"\"\"\n", "\n", "# Use the utility function to get data in batches\n", "records_df = query_utils.get_data_in_batches(\n", "    \"mvp_db_dev\", 1000, QUERY_STR, use_tqdm=True\n", ")\n", "\n", "# Save the DataFrame to a CSV file\n", "records_df.to_csv(\"data/rds_raw.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "0c7add38-6005-42c1-9cdf-b4fb02e3a714", "metadata": {}, "outputs": [], "source": ["# Load and clean data\n", "import pandas as pd\n", "\n", "records_df = pd.read_csv(\"data/rds_raw.csv\")\n", "\n", "print(len(records_df), \" before mlprice na filtering\")\n", "records_df = records_df[~records_df[\"mlPrice\"].isna()]\n", "print(len(records_df), \" after mlprice na filtering\")\n", "\n", "records_df[\"cogsAccessorial\"].fillna(0.0, inplace=True)\n", "records_df[\"OUTPUT_cogs_total\"] = (\n", "    records_df[\"cogsTotal\"] - records_df[\"cogsAccessorial\"]\n", ")\n", "\n", "\"RDS avg mae\", (abs(records_df[\"OUTPUT_cogs_total\"] - records_df[\"mlPrice\"])).mean()"]}, {"cell_type": "code", "execution_count": null, "id": "c215055d", "metadata": {}, "outputs": [], "source": ["# Load ML scrape data\n", "# Run inference_api.py ml scrape analyzer to generate predictions.npy.\n", "import numpy as np\n", "\n", "ml_scrape_df = pd.read_csv(\"data/ml_scrape_full.csv\")\n", "\n", "values = np.load(\"data/predictions.npy\")\n", "lower_bounds = np.load(\"data/predictions_lower.npy\")\n", "upper_bounds = np.load(\"data/predictions_upper.npy\")\n", "ml_scrape_df[\"mlPrice\"] = values\n", "ml_scrape_df[\"mlPriceLowerBound\"] = lower_bounds\n", "ml_scrape_df[\"mlPriceUpperBound\"] = upper_bounds\n", "\n", "\"Scrape avg mae\", (\n", "    abs(ml_scrape_df[\"OUTPUT_cogs_total\"] - ml_scrape_df[\"mlPrice\"])\n", ").mean()"]}, {"cell_type": "code", "execution_count": null, "id": "2b89317f", "metadata": {}, "outputs": [], "source": ["# Combine dfs\n", "\n", "records_df[\"source\"] = \"RDS\"\n", "ml_scrape_df[\"source\"] = \"ML Scrape\"\n", "\n", "records_df = pd.concat([records_df, ml_scrape_df])"]}, {"cell_type": "markdown", "id": "ffd25176-420c-4201-8fb0-6d02d0f36669", "metadata": {}, "source": ["### Tightening Bounds\n", "We also gather other interesting metrics to explore.\n", "\n", "By setting `lower_bound` and `upper_bound` to various values, we can see what proportion of shipments fall within bounds.\n", "This is our northstar metric for choosing our bound tightening technique. \n", "\n", "We decide not to tighten bounds, and to instead not display them on the frontend."]}, {"cell_type": "code", "execution_count": null, "id": "016da7a6-a8dd-4422-83d1-37a5460be8a6", "metadata": {"tags": []}, "outputs": [], "source": ["# This block helps us evaluate if we can write different lower_bound/upper_bound code and how that\n", "# affects performance.\n", "\n", "# Initialize various interesting counters + lists\n", "count_within_bounds = 0\n", "err_when_in_bounds = []\n", "err_when_out_bounds = []\n", "err_when_low_bounds = []\n", "err_when_high_bounds = []\n", "out_of_bounds_lower = 0\n", "out_of_bounds_upper = 0\n", "import collections\n", "\n", "broker_out_of_bounds_counts = collections.defaultdict(lambda: 0)\n", "broker_total_counts = collections.defaultdict(lambda: 0)\n", "mae_list = []\n", "mape_list = []\n", "bound_size_list = []\n", "bound_ratio_list = []\n", "# The \"true value\" or OUTPUT_cogs_total\n", "actuals = []\n", "\n", "# Iterate over actual values and predicted results\n", "for i, r in records_df.iterrows():\n", "    actual = r[\"OUTPUT_cogs_total\"]\n", "    if actual == 0 or r[\"mlPrice\"] == 0:\n", "        continue\n", "\n", "    actuals.append(actual)\n", "\n", "    lower_bound = r[\"mlPriceLowerBound\"]\n", "    upper_bound = r[\"mlPriceUpperBound\"]\n", "\n", "    ml_price = r[\"mlPrice\"]\n", "    bound_size = upper_bound - lower_bound\n", "    bound_size_list.append(bound_size)\n", "\n", "    # Consider defining bound_ratio as bound_size / ml_price for FreightGPT\n", "    bound_ratio = bound_size / actual\n", "    bound_ratio_list.append(bound_ratio)\n", "\n", "    # Calculate the Mean Absolute Error (MAE)\n", "    mae = abs(actual - r[\"mlPrice\"])\n", "    mae_list.append(mae)\n", "    mape = mae / actual\n", "    mape_list.append(mape)\n", "\n", "    # Check if the actual value is within the predicted bounds\n", "    if lower_bound > actual:\n", "        out_of_bounds_lower += 1\n", "        err_when_low_bounds.append(abs(r[\"OUTPUT_cogs_total\"] - r[\"mlPrice\"]))\n", "    if upper_bound < actual:\n", "        out_of_bounds_upper += 1\n", "        err_when_high_bounds.append(abs(r[\"OUTPUT_cogs_total\"] - r[\"mlPrice\"]))\n", "    if lower_bound <= actual <= upper_bound:\n", "        # err_when_in_bounds.append(abs(r['OUTPUT_cogs_total'] - r['mlPrice']))\n", "        count_within_bounds += 1\n", "    else:\n", "        err_when_out_bounds.append(abs(r[\"OUTPUT_cogs_total\"] - r[\"mlPrice\"]))\n", "        # broker_out_of_bounds_counts[r[\"brokerId\"]] += 1\n", "    # broker_total_counts[r[\"brokerId\"]] += 1\n", "\n", "# Calculate the proportion of values within bounds\n", "proportion_within_bounds = count_within_bounds / len(records_df)\n", "\n", "print(\"Proportion of values within bounds:\", proportion_within_bounds)\n", "print(\"lower bound fail:\", out_of_bounds_lower)\n", "print(\"upper bound fail:\", out_of_bounds_upper)"]}, {"cell_type": "markdown", "id": "79541938-b13d-44e2-81d2-298214fe9214", "metadata": {}, "source": ["### Plots to determine confidence\n", "We plot bound size/bound ratio by mae/mape. If there is an obvious dropoff that helps us define our cutoff.\n", "\n", "We expect error to correlate with higher bounds, and we find that to be true."]}, {"cell_type": "code", "execution_count": null, "id": "5aa0ea8c-d175-49eb-af55-29941f8873ec", "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Create a DataFrame\n", "df = pd.DataFrame(\n", "    {\n", "        \"Bound Size\": bound_size_list,\n", "        \"Bound Ratio\": bound_ratio_list,\n", "        \"MAE\": mae_list,\n", "        \"MAPE\": mape_list,\n", "    }\n", ")\n", "\n", "# Define the bin edges for the bound sizes and ratios\n", "size_bins = np.arange(0, 3000, 100)\n", "ratio_bins = np.arange(0, 3, 0.1)\n", "\n", "# Cut the data into bins and add them as new columns to the DataFrame\n", "df[\"Bound Size Bin\"] = pd.cut(df[\"Bound Size\"], bins=size_bins)\n", "df[\"Bound Ratio Bin\"] = pd.cut(df[\"Bound Ratio\"], bins=ratio_bins)\n", "\n", "# Calculate the average MAE and MAPE for each bin\n", "average_mae_per_size_bin = df.groupby(\"Bound Size Bin\")[\"MAE\"].mean()\n", "average_mape_per_size_bin = df.groupby(\"Bound Size Bin\")[\"MAPE\"].mean()\n", "average_mae_per_ratio_bin = df.groupby(\"Bound Ratio Bin\")[\"MAE\"].mean()\n", "average_mape_per_ratio_bin = df.groupby(\"Bound Ratio Bin\")[\"MAPE\"].mean()\n", "\n", "# Create subplots\n", "fig, axs = plt.subplots(2, 2, figsize=(20, 12))\n", "\n", "# Create bar plots\n", "sns.barplot(\n", "    x=average_mae_per_size_bin.index, y=average_mae_per_size_bin.values, ax=axs[0, 0]\n", ")\n", "sns.barplot(\n", "    x=average_mape_per_size_bin.index, y=average_mape_per_size_bin.values, ax=axs[0, 1]\n", ")\n", "sns.barplot(\n", "    x=average_mae_per_ratio_bin.index, y=average_mae_per_ratio_bin.values, ax=axs[1, 0]\n", ")\n", "sns.barplot(\n", "    x=average_mape_per_ratio_bin.index,\n", "    y=average_mape_per_ratio_bin.values,\n", "    ax=axs[1, 1],\n", ")\n", "\n", "# Rotate the x labels to make them more readable\n", "for ax in axs.flat:\n", "    ax.set_xticklabels(ax.get_xticklabels(), rotation=90)\n", "\n", "# Label the axes\n", "axs[0, 0].set(xlabel=\"Bound Size\", ylabel=\"Average MAE\")\n", "axs[0, 1].set(xlabel=\"Bound Size\", ylabel=\"Average MAPE\")\n", "axs[1, 0].set(xlabel=\"Bound Ratio\", ylabel=\"Average MAE\")\n", "axs[1, 1].set(xlabel=\"Bound Ratio\", ylabel=\"Average MAPE\")\n", "\n", "# Display the plots\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "c9d7c9c8-4b31-48f1-b51c-34bffb8fc46d", "metadata": {}, "source": ["#### All Shipments Plot\n", "\n", "We show all shipments in this plot. Some of our observations\n", "\n", "- If BoundSize>2*Price, MAPE is low\n", "- If bound size<1.5*price we are confident?\n", "- Bound size > 1200 seems to imply MAE likelier > 50\n", "- By applying bound filtering, we remove many of the high errors"]}, {"cell_type": "code", "execution_count": null, "id": "d6c42a52-339a-41f8-ba4b-7d7672fd521e", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Create a DataFrame\n", "df = pd.DataFrame(\n", "    {\n", "        \"Bound Size\": bound_size_list,\n", "        \"Bound Ratio\": bound_ratio_list,\n", "        \"Actual Price\": actuals,\n", "        \"MAE\": mae_list,\n", "        \"MAPE\": mape_list,\n", "    }\n", ")\n", "\n", "# Apply filtering to see the diff\n", "# df = df[df['Bound Size'] < 1900]\n", "# df = df[df['Bound Ratio'] < 1.9]\n", "\n", "# Create a scatter plot\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(\n", "    df[\"Actual Price\"], df[\"Bound Size\"], c=df[\"MAPE\"], cmap=\"viridis\", vmin=0, vmax=1\n", ")\n", "\n", "# Label the axes\n", "plt.ylabel(\"Bound Size\")\n", "plt.xlabel(\"Actual Price\")\n", "\n", "# Add a color bar\n", "plt.colorbar(label=\"MAPE\")\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "aa7cd1b7", "metadata": {}, "outputs": [], "source": ["# TODO(P0): figure out why lower bound 0.01 isnt showing up\n", "# - also color ml scrape data points differently\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "df = pd.DataFrame(\n", "    {\n", "        \"Bound Size\": bound_size_list,\n", "        \"Bound Ratio\": bound_ratio_list,\n", "        \"Actual Price\": actuals,\n", "        \"MAE\": mae_list,\n", "        \"MAPE\": mape_list,\n", "    }\n", ")\n", "\n", "BOUND_SIZE_UPPER_BOUND = 1000\n", "BOUND_RATIO_UPPER_BOUND = 2\n", "BOUND_RATIO_LOWER_BOUND = 0\n", "\n", "# Lists to store data for plotting\n", "tp_values = []\n", "tn_values = []\n", "anti_maes = []\n", "filtered_maes = []\n", "bound_sizes = []\n", "bound_ratios = []\n", "bound_ratios_lower = []\n", "num_filtered = []\n", "anti_mapes = []\n", "filtered_mapes = []\n", "thresholds = []\n", "point_sizes = []\n", "LARGER_SIZE = 20  # Size for emphasized points\n", "DEFAULT_SIZE = 5  # Default size for other points\n", "\n", "# Try different upper bound values for bound size and bound ratio\n", "for THRESHOLD in range(75, 301, 25):  # iterate from 75 to 300 in steps of 25\n", "    for BOUND_RATIO_LOWER_BOUND in [0]:  # Replaced 0 with 0.01\n", "        for BOUND_SIZE_UPPER_BOUND in [\n", "            1000,\n", "            1500,\n", "            2000,\n", "            2500,\n", "            3000,\n", "            3500,\n", "            4000,\n", "            4500,\n", "            5000,\n", "        ]:\n", "            for BOUND_RATIO_UPPER_BOUND in [\n", "                0.6,\n", "                0.8,\n", "                1.0,\n", "                1.2,\n", "                1.4,\n", "                1.5,\n", "                1.6,\n", "                1.8,\n", "                2.0,\n", "            ]:\n", "                filtered_df = df[df[\"Bound Size\"] < BOUND_SIZE_UPPER_BOUND]\n", "                filtered_df = filtered_df[\n", "                    filtered_df[\"Bound Ratio\"] < BOUND_RATIO_UPPER_BOUND\n", "                ]\n", "                filtered_df = filtered_df[\n", "                    filtered_df[\"Bound Ratio\"] >= BOUND_RATIO_LOWER_BOUND\n", "                ]\n", "\n", "                anti_df = df[df[\"Bound Size\"] >= BOUND_SIZE_UPPER_BOUND]\n", "                anti_df_1 = anti_df[anti_df[\"Bound Ratio\"] >= BOUND_RATIO_UPPER_BOUND]\n", "                anti_df_2 = anti_df[anti_df[\"Bound Ratio\"] < BOUND_RATIO_LOWER_BOUND]\n", "                anti_df = pd.concat([anti_df_1, anti_df_2])\n", "\n", "                # Use THRESHOLD instead of hardcoded 225\n", "                fps = len(anti_df[anti_df[\"MAE\"] < THRESHOLD])\n", "                tps = len(anti_df) - fps\n", "                fns = len(filtered_df[filtered_df[\"MAE\"] >= THRESHOLD])\n", "                tns = len(filtered_df) - fns\n", "\n", "                # Storing data for plotting\n", "                tp_values.append(tps / len(anti_df) * 100)\n", "                tn_values.append(tns / len(filtered_df) * 100)\n", "                filtered_maes.append(filtered_df[\"MAE\"].mean())\n", "                filtered_mapes.append(round(filtered_df[\"MAPE\"].mean() * 100))\n", "                anti_maes.append(anti_df[\"MAE\"].mean())\n", "                anti_mapes.append(round(anti_df[\"MAPE\"].mean() * 100))\n", "                bound_sizes.append(BOUND_SIZE_UPPER_BOUND)\n", "                bound_ratios.append(BOUND_RATIO_UPPER_BOUND)\n", "                bound_ratios_lower.append(BOUND_RATIO_LOWER_BOUND)\n", "                num_filtered.append(len(anti_df))\n", "                thresholds.append(str(THRESHOLD))"]}, {"cell_type": "code", "execution_count": null, "id": "1001c045", "metadata": {}, "outputs": [], "source": ["# ... [previous code]\n", "\n", "# Define sizes for the scatter points\n", "point_sizes = []\n", "LARGER_SIZE = 10  # Size for emphasized points\n", "DEFAULT_SIZE = 5  # Default size for other points\n", "\n", "for size, ratio in zip(bound_sizes, bound_ratios):\n", "    if size == 3500 and ratio == 1.2:\n", "        point_sizes.append(LARGER_SIZE)\n", "    else:\n", "        point_sizes.append(DEFAULT_SIZE)\n", "\n", "# Plotting with <PERSON><PERSON>ly\n", "fig = px.scatter(\n", "    color=thresholds,\n", "    x=tp_values,\n", "    y=tn_values,\n", "    size=point_sizes,  # Use point_sizes for the size of each point\n", "    hover_data={\n", "        \"Filtered MAE\": filtered_maes,\n", "        \"Anti MAE\": anti_maes,\n", "        \"Bound Size\": bound_sizes,\n", "        \"Bound Ratio\": bound_ratios,\n", "        \"Bound Ratio Lower Bound\": bound_ratios_lower,\n", "        \"Number Filtered\": num_filtered,\n", "        \"Filtered MAPE\": filtered_mapes,\n", "        \"Anti MAPE\": anti_mapes,\n", "    },\n", "    labels={\"x\": \"True Positives (%)\", \"y\": \"True Negatives (%)\"},\n", "    title=\"True Positives vs True Negatives\",\n", ")\n", "\n", "# Set the sizemode to 'diameter' and sizeref to 1 to use absolute sizes\n", "fig.update_traces(marker=dict(sizemode=\"diameter\", sizeref=1))\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "429e4868", "metadata": {}, "source": ["### Conclusion on Bound Selection 9/10/23\n", "```\n", "Bound Size=1000\n", "Bound Ratio=2\n", "Filtered MAE=95\n", "Filtered MAPE=9%\n", "Anti MAE=1038\n", "Anti MAPE=233%\n", "no. shipments filtered=215\n", "```\n", "\n", "This was selected to minimize FNs, while still preserving a decent FP rate.\n", "(at MAE threshold of 200, 86% TPR and 88% TNR).\n", "\n", "##### Conclusion on Confidence Jul' 23\n", "Only show ml price on 98% of shipments where Bound Size < 1900 and bound Ratio < 1.9.\n", "\n", "This filtered set has `$87` MAE, `6.4%` error compared to the set that was filtered out which has `$731` MAE, `119.3%` error\n", "\n", "v2 should include CLT"]}, {"cell_type": "markdown", "id": "a92dccfc-b11b-4f96-bb5e-97f9ec64badf", "metadata": {}, "source": ["#### Broker by Broke<PERSON> Performance"]}, {"cell_type": "code", "execution_count": null, "id": "3fd9c374-49be-4423-8fb2-7e429acaa4ef", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import time\n", "from common import query_utils\n", "from tqdm import tqdm\n", "import numpy as np\n", "\n", "records_df = pd.DataFrame()\n", "\n", "QUERY_STR = \"\"\"SELECT\n", "    * FROM brokers;\"\"\"\n", "\n", "records = query_utils.query(QUERY_STR, \"mvp_db_dev\")\n", "brokers_df = query_utils.append_records_to_df(records, records_df)\n", "brokers_df"]}, {"cell_type": "code", "execution_count": null, "id": "83dcf52d-d431-4842-aa76-e5c32caa81b2", "metadata": {}, "outputs": [], "source": ["# Gather data into list of tuples\n", "broker_data = []\n", "for i in broker_total_counts:\n", "    broker = brokers_df[brokers_df[\"id\"] == i][\"name\"].values[0]\n", "    percentage = 100 - round(\n", "        broker_out_of_bounds_counts[i] / broker_total_counts[i] * 100, 2\n", "    )\n", "    broker_data.append((broker, percentage))\n", "\n", "# Sort list of tuples by the percentage (from high to low)\n", "broker_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "# Print the data nicely formatted\n", "print(\"{:<30} {:<10}\".format(\"Broker\", \"% In Bounds\"))\n", "for broker, percentage in broker_data:\n", "    print(\"{:<30} {:<10.2f}\".format(broker, percentage))"]}, {"cell_type": "markdown", "id": "2847b3aa-6628-48e9-82c0-6d8bab23eb4a", "metadata": {}, "source": ["# Other Code"]}, {"cell_type": "markdown", "id": "88a8e886-5b4e-4337-b721-5e7edf3cdc48", "metadata": {"tags": []}, "source": ["### Vertex AI Realtime API"]}, {"cell_type": "code", "execution_count": null, "id": "15cdf757-e47e-4203-bce8-f43a7d8b95d6", "metadata": {}, "outputs": [], "source": ["# Copyright 2020 Google LLC\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#     https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n", "\n", "# [START aiplatform_predict_tabular_classification_sample]\n", "from typing import Dict\n", "\n", "from google.cloud import aiplatform\n", "from google.protobuf import json_format\n", "from google.protobuf.struct_pb2 import Value\n", "\n", "\n", "def predict_tabular_classification_batch_sample(\n", "    project: str,\n", "    endpoint_id: str,\n", "    instances_list: list,  # This will now be a list of dictionaries\n", "    location: str = \"us-central1\",\n", "    api_endpoint: str = \"us-central1-aiplatform.googleapis.com\",\n", "):\n", "    client_options = {\"api_endpoint\": api_endpoint}\n", "    client = aiplatform.gapic.PredictionServiceClient(client_options=client_options)\n", "\n", "    # Create an empty list to store the instance objects\n", "    instances = []\n", "\n", "    # Iterate over each instance_dict in instances_list\n", "    for instance_dict in instances_list:\n", "        instance = json_format.ParseDict(instance_dict, Value())\n", "        instances.append(instance)\n", "\n", "    parameters_dict = {}\n", "    parameters = json_format.ParseDict(parameters_dict, Value())\n", "    endpoint = client.endpoint_path(\n", "        project=project, location=location, endpoint=endpoint_id\n", "    )\n", "\n", "    response = client.predict(\n", "        endpoint=endpoint, instances=instances, parameters=parameters\n", "    )\n", "\n", "    return response.predictions"]}, {"cell_type": "code", "execution_count": null, "id": "17c39349-e052-49a5-9e4e-49b766c9f771", "metadata": {}, "outputs": [], "source": ["from price_predictor import VertexAutoMLPricePredictor\n", "\n", "predictor = VertexAutoMLPricePredictor()\n", "batch_predictions = predictor.batch_predict([r.to_dict()])\n", "batch_predictions"]}, {"cell_type": "code", "execution_count": null, "id": "eb623f62-71f9-47a8-8cf0-9abb5874ba16", "metadata": {}, "outputs": [], "source": ["data = {\n", "    \"distance_miles\": str(r[\"distanceMiles\"]),\n", "    \"origin_city\": r[\"originCity\"],\n", "    \"origin_state\": r[\"originState\"],\n", "    \"origin_country\": r[\"originCountry\"],\n", "    \"origin_zip\": r[\"originZip\"],\n", "    \"destination_city\": r[\"destinationCity\"],\n", "    \"destination_state\": r[\"destinationState\"],\n", "    \"destination_country\": r[\"destinationCountry\"],\n", "    \"destination_zip\": r[\"destinationZip\"],\n", "    \"origin_open_time\": r[\"originOpenTime\"],\n", "    \"origin_close_time\": r[\"originCloseTime\"],\n", "    \"destination_open_time\": r[\"destinationOpenTime\"],\n", "    \"destination_close_time\": r[\"destinationCloseTime\"],\n", "    \"origin_lat\": \"40.5853\",\n", "    \"origin_lng\": \"-105.0844\",\n", "    \"destination_lat\": \"39.7684\",\n", "    \"destination_lng\": \"-86.1581\",\n", "    \"origin_diesel_price\": \"4.128\",\n", "    \"destination_diesel_price\": \"4.128\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2e33434c-1b0b-44b6-a7a3-3c18b58633ab", "metadata": {}, "outputs": [], "source": ["# data = {\n", "#     \"distance_miles\": \"232.93528\",\n", "#     \"origin_city\": \"Huntley\",\n", "#     \"origin_state\": \"IL\",\n", "#     \"origin_country\": \"US\",\n", "#     \"origin_zip\": \"60142\",\n", "#     \"destination_city\": \"Grand Rapids\",\n", "#     \"destination_state\": \"MI\",\n", "#     \"destination_country\": \"US\",\n", "#     \"destination_zip\": \"49501\",\n", "#     \"origin_open_time\": \"\",\n", "#     \"origin_close_time\": \"2023-07-11 17:00:00\",\n", "#     \"destination_open_time\": \"\",\n", "#     \"destination_close_time\": \"2023-07-11 21:00:00\",\n", "#     \"origin_lat\": \"42.16808\",\n", "#     \"origin_lng\": \"-88.428141\",\n", "#     \"destination_lat\": \"42.971329\",\n", "#     \"destination_lng\": \"-85.673666\",\n", "#     \"origin_diesel_price\": \"3.742\",\n", "#     \"destination_diesel_price\": \"3.742\"\n", "# }\n", "\n", "\n", "results = predict_tabular_classification_batch_sample(\n", "    project=\"208407975297\",\n", "    endpoint_id=\"6478634772158480384\",\n", "    location=\"us-central1\",\n", "    instances_list=[data],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "accf8a2f-a264-4b19-9a00-1a52ca16c132", "metadata": {}, "outputs": [], "source": ["for i in results:\n", "    result = dict(i)\n", "    print(result)"]}, {"cell_type": "code", "execution_count": null, "id": "eb7ec6f2-6696-4fdd-90a6-710c879f659d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "\n", "# Shuffle the dataframe\n", "shuffled_test_df = test_df.sample(frac=1, random_state=42).reset_index(drop=True)\n", "\n", "# Initialize the results list\n", "test_batch_input = []\n", "\n", "# Iterate over the first 100 rows\n", "for _, row in shuffled_test_df.iloc[:1000].iterrows():\n", "    # Convert the row to dictionary\n", "    data = row.to_dict()\n", "\n", "    # Ensure all values are converted to appropriate types for JSON serialization\n", "    for key, value in data.items():\n", "        data[key] = str(value)\n", "\n", "    test_batch_input.append(data)"]}, {"cell_type": "code", "execution_count": null, "id": "23442c89-b213-45f6-a151-884081158efe", "metadata": {}, "outputs": [], "source": ["results = predict_tabular_classification_batch_sample(\n", "    project=\"208407975297\",\n", "    endpoint_id=\"6478634772158480384\",\n", "    location=\"us-central1\",\n", "    instances_list=test_batch_input,\n", ")"]}, {"cell_type": "markdown", "id": "6fd2122c-4cd3-43bc-ab41-c196315d4f52", "metadata": {"tags": []}, "source": ["#### random analysis"]}, {"cell_type": "code", "execution_count": null, "id": "588fe672-7cdc-4420-953e-f2cadf49d1ee", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import mean_absolute_error\n", "\n", "# Get the first 100 actual values\n", "actual_values = shuffled_test_df[\"OUTPUT_cogs_total\"].iloc[:1000].values\n", "\n", "# Extract the predicted values from the results\n", "predicted_values = [dict(result)[\"value\"] for result in results]\n", "\n", "# Calculate Mean Absolute Error (MAE)\n", "mae = mean_absolute_error(actual_values, predicted_values)\n", "\n", "print(\"Mean Absolute Error:\", mae)"]}, {"cell_type": "code", "execution_count": null, "id": "ae3264ee-7838-406c-a100-fbed1c78243c", "metadata": {}, "outputs": [], "source": ["# Initialize counter for values within bounds\n", "count_within_bounds = 0\n", "\n", "# Iterate over actual values and predicted results\n", "for actual, result in zip(actual_values, results):\n", "    predicted = dict(result)\n", "\n", "    # Check if the actual value is within the predicted bounds\n", "    if predicted[\"lower_bound\"] <= actual <= predicted[\"upper_bound\"]:\n", "        count_within_bounds += 1\n", "\n", "# Calculate the proportion of values within bounds\n", "proportion_within_bounds = count_within_bounds / len(actual_values)\n", "\n", "print(\"Proportion of values within bounds:\", proportion_within_bounds)"]}, {"cell_type": "code", "execution_count": null, "id": "97980199-7e01-4a00-9fcb-a76d780a746c", "metadata": {}, "outputs": [], "source": ["# Initialize the total bounds size\n", "total_bounds_size = 0\n", "\n", "# Iterate over the predicted results\n", "for result in results:\n", "    predicted = dict(result)\n", "\n", "    # Calculate the size of the bounds for this prediction\n", "    bounds_size = predicted[\"upper_bound\"] - predicted[\"lower_bound\"]\n", "\n", "    # Add the bounds size to the total\n", "    total_bounds_size += bounds_size\n", "\n", "# Calculate the average size of the bounds\n", "average_bounds_size = total_bounds_size / len(results)\n", "\n", "print(\"Average size of bounds:\", average_bounds_size)"]}, {"cell_type": "code", "execution_count": null, "id": "24848f7a-be85-4a48-9b6e-fe4a997959e6", "metadata": {}, "outputs": [], "source": ["for i in results:\n", "    result = dict(i)\n", "    print(result)\n", "    break"]}, {"cell_type": "code", "execution_count": null, "id": "3186d8fe-63fe-4460-a9a6-4ea5b8441870", "metadata": {"tags": []}, "outputs": [], "source": ["def is_result_in_bounds(actual_value, result, bound_size, midpoint=None):\n", "    # Parse the predicted result\n", "    predicted = dict(result)\n", "\n", "    if not midpoint:\n", "        midpoint = predicted[\"value\"]\n", "\n", "    # Adjust the predicted bounds based on the given bound size\n", "    lower_bound = midpoint - (bound_size / 2)\n", "    upper_bound = midpoint + (bound_size / 2)\n", "\n", "    return lower_bound <= actual_value <= upper_bound\n", "\n", "\n", "def evaluate_accuracy_based_on_bound_size(actual_values, results, bound_size):\n", "    # Initialize counter for values within bounds\n", "    count_within_bounds = 0\n", "\n", "    # Iterate over actual values and predicted results\n", "    for actual, result in zip(actual_values, results):\n", "        result_dict = dict(result)\n", "        if not result_dict:\n", "            continue\n", "\n", "        midpoint = (result_dict[\"upper_bound\"] + result_dict[\"lower_bound\"]) / 2\n", "\n", "        if is_result_in_bounds(\n", "            actual, result, bound_size\n", "        ):  # optionally pass in midpoint\n", "            count_within_bounds += 1\n", "\n", "    # Calculate the proportion of values within bounds\n", "    proportion_within_bounds = count_within_bounds / len(actual_values)\n", "\n", "    return proportion_within_bounds\n", "\n", "\n", "bound_sizes = range(100, 1100, 100)\n", "for bound_size in bound_sizes:\n", "    accuracy = evaluate_accuracy_based_on_bound_size(actual_values, results, bound_size)\n", "    print(f\"Accuracy for bound size {bound_size}:\", accuracy)"]}, {"cell_type": "code", "execution_count": null, "id": "630a8b11-3c65-437e-b730-059f242864f6", "metadata": {}, "outputs": [], "source": ["def evaluate_confidence(actual_values, results, fraction):\n", "    # Initialize counter for values within bounds\n", "    count_within_fraction = 0\n", "\n", "    # Iterate over actual values and predicted results\n", "    for actual, result in zip(actual_values, results):\n", "        predicted = dict(result)\n", "\n", "        # Calculate the midpoint of the predicted bounds\n", "        predicted_value = predicted[\"value\"]\n", "\n", "        # Calculate the prediction interval width\n", "        bound_width = predicted[\"upper_bound\"] - predicted[\"lower_bound\"]\n", "\n", "        # Calculate the error\n", "        error = abs(predicted_value - actual)\n", "\n", "        # Check if the error is within the specified fraction of the bound width\n", "        if error <= fraction * bound_width:\n", "            count_within_fraction += 1\n", "\n", "    # Calculate the proportion of errors within the specified fraction of the bound width\n", "    proportion_within_fraction = count_within_fraction / len(actual_values)\n", "\n", "    return proportion_within_fraction"]}, {"cell_type": "code", "execution_count": null, "id": "6a88fade-6103-43f2-9030-0280f5b8201c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_accuracy_vs_bound_size(actual_values, results, chosen_bound_size):\n", "    # Calculate given bound sizes\n", "    given_bound_sizes = [\n", "        dict(result)[\"upper_bound\"] - dict(result)[\"lower_bound\"] for result in results\n", "    ]\n", "\n", "    # Create bins for bound sizes\n", "    bins = np.arange(0, max(given_bound_sizes) + 100, 100)\n", "\n", "    # Bucketize the bound sizes\n", "    bucket_indices = np.digitize(given_bound_sizes, bins)\n", "\n", "    # Prepare lists to store results\n", "    bucket_midpoints = []\n", "    bucket_accuracies = []\n", "\n", "    # Iterate over each bucket\n", "    for i in range(1, len(bins)):\n", "        # Find the indices of the predictions in this bucket\n", "        indices_in_bucket = [\n", "            index\n", "            for index, bucket_index in enumerate(bucket_indices)\n", "            if bucket_index == i\n", "        ]\n", "\n", "        if indices_in_bucket:  # Check if there are any predictions in this bucket\n", "            # Calculate the midpoint of this bucket\n", "            bucket_midpoint = (bins[i - 1] + bins[i]) / 2\n", "            bucket_midpoints.append(bucket_midpoint)\n", "\n", "            # Find the actual values and results in this bucket\n", "            actual_values_in_bucket = [\n", "                actual_values[index] for index in indices_in_bucket\n", "            ]\n", "            results_in_bucket = [results[index] for index in indices_in_bucket]\n", "\n", "            # Calculate the accuracy for this bucket\n", "            bucket_accuracy = sum(\n", "                is_result_in_bounds_value_based(actual_value, result, chosen_bound_size)\n", "                for actual_value, result in zip(\n", "                    actual_values_in_bucket, results_in_bucket\n", "                )\n", "            ) / len(indices_in_bucket)\n", "            bucket_accuracies.append(bucket_accuracy)\n", "\n", "    # Create subplot for accuracy vs. bound size\n", "    fig, ax1 = plt.subplots()\n", "    ax1.plot(bucket_midpoints, bucket_accuracies, marker=\"o\", color=\"b\")\n", "    ax1.set_xlabel(\"Given Uncertainty (Bound Size)\")\n", "    ax1.set_ylabel(\n", "        f\"Accuracy (Proportion Within {chosen_bound_size} Bounds)\", color=\"b\"\n", "    )\n", "    ax1.tick_params(\"y\", colors=\"b\")\n", "\n", "    # Create subplot for histogram of bound sizes\n", "    ax2 = ax1.twinx()\n", "    ax2.hist(given_bound_sizes, bins=bins, color=\"r\", alpha=0.5)\n", "    ax2.set_ylabel(\"Frequency\", color=\"r\")\n", "    ax2.tick_params(\"y\", colors=\"r\")\n", "\n", "    fig.tight_layout()\n", "    plt.title(\"Given Uncertainty vs Accuracy & Frequency of Bound Sizes\")\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "\n", "chosen_bound_size = 100\n", "plot_accuracy_vs_bound_size(actual_values, results, chosen_bound_size)"]}, {"cell_type": "code", "execution_count": null, "id": "16bdc505-db88-416f-aa66-a7676d6ae33c", "metadata": {}, "outputs": [], "source": ["chosen_bound_sizes = range(100, 1100, 100)\n", "\n", "for chosen_bound_size in chosen_bound_sizes:\n", "    plot_accuracy_vs_bound_size(actual_values, results, chosen_bound_size)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}