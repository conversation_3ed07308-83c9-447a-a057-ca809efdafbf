{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "\n", "from common import google_maps_utils\n", "from machine_learning.price_recommendation.price_predictor import (\n", "    VertexAutoMLPricePredictor,\n", ")\n", "import os\n", "\n", "os.environ[\"ENV\"] = \"DEV\"\n", "\n", "PRICER_ENDPOINT = VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_FREIGHTGPT_DEV\n", "GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()\n", "PRICER = VertexAutoMLPricePredictor(PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "required_columns = [\n", "    \"origin_city\",\n", "    \"origin_state\",\n", "    \"destination_city\",\n", "    \"destination_state\",\n", "    \"equipment_type\",\n", "]\n", "\n", "optional_columns = [\n", "    \"origin_close_date\",\n", "    \"distance_miles\",\n", "    \"origin_zip\",\n", "    \"destination_zip\",\n", "]\n", "\n", "# write a template csv file\n", "template_df = pd.DataFrame(columns=required_columns + optional_columns)\n", "template_df.to_csv(\"data/template.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# List of potential encodings\n", "encodings = [\"utf-8\", \"ISO-8859-1\", \"utf-16\", \"windows-1252\", \"latin-1\"]\n", "\n", "# Path to your CSV file\n", "file_path = \"data/6a344df.csv\"\n", "\n", "# Try reading the file with different encodings\n", "for encoding in encodings:\n", "    try:\n", "        df = pd.read_csv(file_path, encoding=encoding)\n", "        print(f\"Successfully read file with encoding: {encoding}\")\n", "        break  # Stop the loop if successful\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed with encoding: {encoding}\")\n", "\n", "# Construct list of dictionaries to pass to pricer batch_predict\n", "from machine_learning.freightgpt.constants import PRICER_ORIGIN_CLOSE_HOUR_UTC\n", "from machine_learning.price_recommendation import preprocessing\n", "\n", "for col in required_columns:\n", "    if col not in df.columns:\n", "        raise ValueError(f\"Missing required column {col}\")\n", "\n", "# Clean up the data\n", "df[\"origin_city\"] = df[\"origin_city\"].str.title()\n", "df[\"destination_city\"] = df[\"destination_city\"].str.title()\n", "df[\"origin_state\"] = df[\"origin_state\"].str.upper()\n", "df[\"destination_state\"] = df[\"destination_state\"].str.upper()\n", "\n", "# Derive country from state\n", "df[\"origin_country\"] = df[\"origin_state\"].apply(preprocessing.derive_country_from_state)\n", "df[\"destination_country\"] = df[\"destination_state\"].apply(\n", "    preprocessing.derive_country_from_state\n", ")\n", "\n", "df[\"equipment_type\"] = df[\"equipment_type\"].apply(preprocessing.map_equipment_type)\n", "\n", "\n", "def fill_origin_close_date(row):\n", "    if \"origin_close_date\" not in row or pd.isna(row[\"origin_close_date\"]):\n", "        return pd.Timestamp.now(tz=\"UTC\").date().strftime(\"%Y-%m-%d\")\n", "\n", "    return preprocessing.build_timestamp(\n", "        row[\"origin_close_date\"], time_str=f\"{PRICER_ORIGIN_CLOSE_HOUR_UTC}:00:00\"\n", "    )\n", "\n", "\n", "# Check if all of the column is NA\n", "if \"origin_close_date\" not in df.columns or df[\"origin_close_time\"].isna().all():\n", "    print(\"Origin close date is missing, filling with current date.\")\n", "    df[\"origin_close_time\"] = (\n", "        pd.Timestamp.now(tz=\"UTC\")\n", "        .replace(hour=PRICER_ORIGIN_CLOSE_HOUR_UTC, minute=0, second=0, microsecond=0)\n", "        .tz_localize(None)\n", "    )\n", "else:\n", "    df[\"origin_close_time\"] = df.apply(fill_origin_close_date, axis=1)\n", "\n", "\n", "# Filter NAs, we want required columns and countries\n", "df = df.dropna(subset=required_columns + [\"origin_country\", \"destination_country\"])\n", "# Only price US to US\n", "df = df[(df[\"origin_country\"] == \"US\") & (df[\"destination_country\"] == \"US\")]\n", "\n", "\n", "def get_data_dict(row):\n", "    d = {}\n", "    for col in required_columns:\n", "        d[col] = row[col]\n", "    for col in optional_columns + [\"origin_close_time\"]:\n", "        # Ignore zips for now\n", "        if \"zip\" in col or \"origin_close_date\" in col:\n", "            continue\n", "        if col in row:\n", "            d[col] = row[col]\n", "    return d\n", "\n", "\n", "batch_predict_inputs = df.apply(get_data_dict, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_predict_inputs[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prices = PRICER.batch_predict(batch_predict_inputs)\n", "prices[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Join prices to df\n", "\n", "df[\"price\"] = [round(p[\"value\"], 2) for p in prices]\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"data/PricedData.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}