import numpy as np
import pandas as pd
from langchain.tools.base import ToolException

from common import init_main
from common.google_maps_utils import GoogleMapsUtils

PROJECT_ROOT = init_main.initialize()

from machine_learning.price_recommendation.price_predictor import (
    VertexAutoMLPricePredictor,
)
from machine_learning.price_recommendation import price_predictor
from machine_learning.freightgpt.langchain_utils import mean_convolve_1d


def batch_contract_price_historical(
    origin_city: str,
    origin_state: str,
    destination_city: str,
    destination_state: str,
    pricer: price_predictor.PricePredictor,
    gmaps_utils: GoogleMapsUtils,
    start_date: pd.Timestamp,
    end_date: pd.Timestamp,
    equipment_type: str = "Dry Van",
) -> float:
    """
    Uses price_predictor to predict the truck cost from origin to destination from start date to end date.

    Args:
    - origin_city: origin city
    - origin_state: origin state
    - destination_city: destination city
    - destination_state: destination state
    - pricer: price predictor object
    - equipment_type: equipment type
    - start_date: start date for historical price retrieval
    - end_date: end date for historical price retrieval

    Returns:
    - Float: the average price.
    """

    number_of_days = (end_date - start_date).days

    # Precompute miles to avoid multiple calls to Google Maps API
    distance_miles = gmaps_utils.get_distance_miles(
        f"{origin_city}, {origin_state}",
        f"{destination_city}, {destination_state}",
        use_cache=False,
    )
    batch = []
    origin_close_times = []
    for i in range(number_of_days + 1):
        origin_close_time = start_date + pd.Timedelta(days=i)
        origin_close_times.append(origin_close_time.timestamp())
        batch.append(
            {
                "origin_city": origin_city,
                "origin_state": origin_state,
                "origin_country": "US",
                "destination_city": destination_city,
                "destination_state": destination_state,
                "destination_country": "US",
                "equipment_type": equipment_type,
                "origin_close_time": origin_close_time,
                "distance_miles": distance_miles,
                "pricing_type": "contract",
            }
        )

    prices = pricer.batch_predict(batch)

    price_vals = [price["value"] for price in prices if not pd.isna(price["value"])]
    if len(price_vals) == 0:
        raise ToolException("Could not retrieve any valid historical batch prices")

    downsampled_time_prices = []
    mean_convolved_times = mean_convolve_1d(origin_close_times, number_of_days)
    mean_convolved_prices = mean_convolve_1d(price_vals, number_of_days)

    for timestamp, price in zip(mean_convolved_times, mean_convolved_prices):
        downsampled_time_prices.append([timestamp, price])

    return np.mean(price_vals)


if __name__ == "__main__":
    CONTRACT_PRICER_ENDPOINT = "6631583436651888640"
    GMAPS_UTILS = GoogleMapsUtils()
    CONTRACT_PRICER = VertexAutoMLPricePredictor(
        CONTRACT_PRICER_ENDPOINT, gmaps_utils=GMAPS_UTILS
    )

    # Example input
    origin_city = "Los Angeles"
    origin_state = "CA"
    destination_city = "New York"
    destination_state = "NY"
    start_date = pd.Timestamp("2023-01-01")
    end_date = pd.Timestamp("2023-01-31")

    result = batch_contract_price_historical(
        origin_city=origin_city,
        origin_state=origin_state,
        destination_city=destination_city,
        destination_state=destination_state,
        pricer=CONTRACT_PRICER,
        gmaps_utils=GMAPS_UTILS,
        equipment_type="Dry Van",
        start_date=start_date,
        end_date=end_date,
    )
    print("Contract price average:", result)
