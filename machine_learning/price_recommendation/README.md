# Setup
We use SageMaker Studio Lab in order to do our ML development.

1. Please sign up at https://studiolab.sagemaker.aws/ with referral code TRUCE-D4D1D.

2. After signing up, clone the repo https://github.com/truce-logistics/truce.git.

3. You may need to create a [Classic Personal Access Token](https://github.com/settings/tokens), which you should enter in place of your password.

4. Add the credentials file to the common/ directory (ask <EMAIL>).

5. In a terminal, cd in to truce/ and run `pip install .`.

6. If you don't want to keep putting in your token, run `git config --global credential.helper cache && git config --global credential.helper 'cache --timeout=86400'`, where timeout is in seconds.
