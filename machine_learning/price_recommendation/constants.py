GCP_PROJECT_ID = "208407975297"

# Feature Engineering.ipynb, derived_features.py
# US state codes
US_STATES = {
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
    "DC",
}

# Canadian province codes
CANADA_PROVINCES = {
    "AB",
    "BC",
    "MB",
    "NB",
    "NL",
    "NT",
    "NS",
    "NU",
    "ON",
    "PE",
    "QC",
    "SK",
    "YT",
}

# Mexican state codes
MEXICO_STATES = {
    "AG",
    "BC",
    "BS",
    "CC",
    "CH",
    "CL",
    "CM",
    "CS",
    "DF",
    "DG",
    "GR",
    "GT",
    "HG",
    "JA",
    "ME",
    "MI",
    "MO",
    "NA",
    "NL",
    "OA",
    "PU",
    "QE",
    "QR",
    "SI",
    "SL",
    "SO",
    "TB",
    "TL",
    "TM",
    "VE",
    "YU",
    "ZA",
}

STATE_NAME_TO_CODE = {"Jalisco": "JA", "Ontario": "ON", "Puebla": "PU"}

# This API is free, but slow.
EIA_API_KEY = "CDJTiri4eUgBLygmWIbtIrbYlU6s5luF9351Y8tz"
EIA_API_BASE_URL = "https://api.eia.gov/v2/petroleum/pri/gnd/data/"

# https://www.eia.gov/tools/glossary/index.php?id=petroleum%20administration%20for%20defense%20district
STATE_TO_EIA_AREA = {
    "AL": "PADD 3",
    "AK": "PADD 5",
    "AZ": "PADD 5",
    "AR": "PADD 3",
    "CA": "CALIFORNIA",
    "CO": "PADD 4",
    "CT": "PADD 1A",
    "DE": "PADD 1B",
    "DC": "PADD 1B",
    "FL": "PADD 1C",
    "GA": "PADD 1C",
    "HI": "PADD 5",
    "ID": "PADD 4",
    "IL": "PADD 2",
    "IN": "PADD 2",
    "IA": "PADD 2",
    "KS": "PADD 2",
    "KY": "PADD 2",
    "LA": "PADD 3",
    "ME": "PADD 1A",
    "MD": "PADD 1B",
    "MA": "PADD 1A",
    "MI": "PADD 2",
    "MN": "PADD 2",
    "MS": "PADD 3",
    "MO": "PADD 2",
    "MT": "PADD 4",
    "NE": "PADD 2",
    "NV": "PADD 5",
    "NH": "PADD 1A",
    "NJ": "PADD 1B",
    "NM": "PADD 3",
    "NY": "PADD 1B",
    "NC": "PADD 1C",
    "ND": "PADD 2",
    "OH": "PADD 2",
    "OK": "PADD 2",
    "OR": "PADD 5",
    "PA": "PADD 1B",
    "RI": "PADD 1A",
    "SC": "PADD 1C",
    "SD": "PADD 2",
    "TN": "PADD 2",
    "TX": "PADD 3",
    "UT": "PADD 4",
    "VT": "PADD 1A",
    "VA": "PADD 1C",
    "WA": "PADD 5",
    "WV": "PADD 1C",
    "WI": "PADD 2",
    "WY": "PADD 4",
}


# Evaluation.ipynb, price_predictor.py
STR_INPUT_COLS = ["lane_id"]
NUMERIC_INPUT_COLS = [
    "distance_miles",
    "origin_close_hour",
    "origin_close_day",
    "origin_close_month",
    "origin_close_day_of_year",
    "origin_close_day_of_week",
    "origin_close_week",
    "origin_close_year",
    "origin_close_is_workday",
    "origin_close_is_holiday",
    "destination_close_hour",
    "destination_close_day",
    "destination_close_month",
    "destination_close_day_of_year",
    "destination_close_day_of_week",
    "destination_close_week",
    "destination_close_year",
    "destination_close_is_workday",
    "destination_close_is_holiday",
    "origin_lat",
    "origin_lng",
    "destination_lat",
    "destination_lng",
    "origin_diesel_price",
    "destination_diesel_price",
]
OUTPUT_COLS = ["OUTPUT_cogs_total"]

TIME_CYCLIC_MAX_VALUES = {
    "hour": 23,
    "day": 31,
    "month": 12,
    "day_of_year": 365,
    "day_of_week": 6,
    "week": 52,
}

# TODO(P1): Consider re-adding some of these features.
# numeric_cols = ['stop_count', 'dist_mi',
#        'weight', 'pre_book', 'blt', 'clt', 'origin_open_hour',
#        'origin_open_day', 'origin_open_month', 'origin_open_day_of_year',
#        'origin_open_is_holiday', 'origin_open_is_workday',
#        'origin_open_day_of_week', 'origin_open_week', 'origin_open_year',
#        'origin_close_hour', 'origin_close_day', 'origin_close_month',
#        'origin_close_day_of_year', 'origin_close_is_holiday',
#        'origin_close_is_workday', 'origin_close_day_of_week',
#        'origin_close_week', 'origin_close_year', 'destination_open_hour',
#        'destination_open_day', 'destination_open_month',
#        'destination_open_day_of_year', 'destination_open_is_holiday',
#        'destination_open_is_workday', 'destination_open_day_of_week',
#        'destination_open_week', 'destination_open_year',
#        'destination_close_hour', 'destination_close_day',
#        'destination_close_month', 'destination_close_day_of_year',
#        'destination_close_is_holiday', 'destination_close_is_workday',
#        'destination_close_day_of_week', 'destination_close_week',
#        'destination_close_year', 'origin_lat', 'origin_lng', 'destination_lat',
#        'destination_lng', 'origin_diesel_price', 'destination_diesel_price', 'OUTPUT_cogs_total']

# inputs: equipment type, city, state, origin close, destination close, weight (default value)
# add customer direct eventually
# model inputs: derive clt, lat/lng, cpm, diesel price
# assume spot shipment
# ml inputs: maybe remove stop count
# def remove blt, carrier assigned, load creation, load activation
