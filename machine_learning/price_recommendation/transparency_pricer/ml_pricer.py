import argparse
import datetime
import logging
import time
import typing

import numpy as np
import pandas as pd
from tqdm import tqdm

from common import ecr_job_util
from common import query_utils
from common.cloudwatch_log_utils import CloudWatchLogger
from common.constants import AURORA_DB_DEV_NAME
from common.constants import AURORA_DB_NAME
from machine_learning.price_recommendation.price_predictor import PricePredictor
from machine_learning.price_recommendation.price_predictor import (
    VertexAutoMLPricePredictor,
)


parser = argparse.ArgumentParser()

parser.add_argument(
    "-v",
    "--verbose",
    help="Level of verbosity to log from set DEBUG|INFO|WARN|ERROR.",
    default="INFO",
)
parser.add_argument(
    "-l",
    "--log_to_stdout",
    action="store_true",
    help="Whether to log to console output (stdout).",
)
parser.add_argument(
    "--dev_db",
    action="store_true",
    help="Whether to ingest to the dev db. DO NOT REMOVE WHEN MANUALLY RUNNING INGESTION.",
)
parser.add_argument("--use_tqdm", action="store_true", help="Whether to use tqdm.")
parser.add_argument(
    "--dry_mode", action="store_true", help="Read only mode, does not update to RDS"
)

args = parser.parse_args()

# Select shipments that have not been priced by the current model.
ML_PRICER_QUERY_STR = """
    SELECT
        s.*,
        oc.city AS originCity,
        oc.country AS originCountry,
        oc.state AS originState,
        dc.city AS destinationCity,
        dc.country AS destinationCountry,
        dc.state AS destinationState,
        oz.zip as originZip,
        dz.zip as destinationZip
    FROM
        (
            SELECT
                s.*,
                l.originCityId,
                l.destinationCityId,
                l.equipmentType,
                l.shipmentMode
            FROM
                {db_name}.shipments AS s
                JOIN {db_name}.lanes AS l ON s.laneid=l.id
            WHERE
                s.shipmentClass = 'canonical'
                AND (s.originCloseTime <= CURRENT_DATE)
                AND (s.originCloseTime >= CURRENT_DATE - INTERVAL 10 DAY)
                AND l.shipmentMode = 'TL'
                AND (s.mlPrice IS NULL
                OR (s.modelName IS NULL OR s.modelName != '{model_name}'
                OR s.modelSource IS NULL OR s.modelSource != '{model_source}'))
        ) AS s
        JOIN {db_name}.cities AS oc ON s.originCityId = oc.id
        JOIN {db_name}.cities AS dc ON s.destinationCityId = dc.id
        JOIN {db_name}.zips AS oz ON s.originZipId = oz.id
        JOIN {db_name}.zips AS dz ON s.destinationZipId = dz.id;
"""


# TODO(P2): Add column lists to docstrings whenever a df is passed or returned.
def load_shipments_from_db(
    db_name: str, model_source: str, model_name: str
) -> pd.DataFrame:
    # Fill in your database name, model name, and model source in the query
    query_str = ML_PRICER_QUERY_STR.format(
        db_name=db_name, model_name=model_name, model_source=model_source
    )
    records_df = query_utils.get_data_in_batches(db_name, 1000, query_str)
    return records_df


def ml_pricer(
    records_df: pd.DataFrame, predictor: PricePredictor, batch_size=1000
) -> pd.DataFrame:
    """Predicts prices for shipments using a PricePredictor object.

    :param records_df: A DataFrame containing shipments to be priced.
    :param predictor: A PricePredictor object.
    :param batch_size: The number of rows to be processed in each batch.
    :return: A DataFrame containing the shipments with their predicted prices."""

    # Initialize an empty DataFrame to store results
    predictions_df = pd.DataFrame()

    # Create batched dataframes. The second argument is the number of batches.
    batched_dfs = np.array_split(records_df, len(records_df) // batch_size + 1)

    for df in tqdm(batched_dfs):
        # Run batch_predict on df rows, converting to correct types
        batch_predictions = predictor.batch_predict(
            df.to_dict("records"), data_source="rds_pricing_full"
        )
        batch_predictions_df = pd.DataFrame(batch_predictions)

        # Since batch predict preserves order, we can use df to set the id
        batch_predictions_df["id"] = df["id"].values
        predictions_df = pd.concat(
            [predictions_df, batch_predictions_df], ignore_index=True
        )

    # Reset "id" to be a column instead of the index
    return predictions_df


def update_shipments_in_db(
    db_name: str,
    predictions_df: pd.DataFrame,
    model_name: str,
    model_source: str,
    batch_size=2000,
):
    # Replace all 'nan' values in the DataFrame with None
    predictions_df = predictions_df.replace({pd.NA: None})
    update_query = f"""
        UPDATE {db_name}.shipments
        SET
            mlPrice = :mlPrice,
            mlPriceUpperBound = :mlPriceUpperBound,
            mlPriceLowerBound = :mlPriceLowerBound,
            modelName = :modelName,
            mlPricedTime = :mlPricedTime,
            modelSource = :modelSource
        WHERE
            id = :id
    """

    # Consider multithreading queries
    batched_dfs = np.array_split(predictions_df, len(predictions_df) // batch_size + 1)

    for batch_df in tqdm(batched_dfs):
        # Create a list to hold all the parameter sets
        parameter_sets = []
        for _, row in batch_df.iterrows():
            # Prepare the parameters for the current row and append them to the list
            parameters = [
                {"name": "id", "value": {"stringValue": row["id"]}},
                {"name": "mlPrice", "value": {"doubleValue": row["value"]}},
                {
                    "name": "mlPriceLowerBound",
                    "value": {"doubleValue": row["lower_bound"]},
                },
                {
                    "name": "mlPriceUpperBound",
                    "value": {"doubleValue": row["upper_bound"]},
                },
                {
                    "name": "mlPricedTime",
                    "value": {
                        "stringValue": datetime.datetime.now(
                            datetime.timezone.utc
                        ).strftime("%Y-%m-%d %H:%M:%S")
                    },
                },
                {"name": "modelName", "value": {"stringValue": model_name}},
                {"name": "modelSource", "value": {"stringValue": model_source}},
            ]
            parameter_sets.append(parameters)

        # Execute the batch query
        response = query_utils.batch_query(update_query, db_name, parameter_sets)

        # Check for errors in the response
        if "updateResults" in response:
            for result in response["updateResults"]:
                if "error" in result:
                    logging.error(f"Error: {result['error']}")


def evaluate_predictions_and_clean_df(
    records_df: pd.DataFrame, predictions_df: pd.DataFrame
) -> typing.Tuple[float, float, pd.DataFrame]:
    # TODO(P0): Split by equipment type and data source.
    mae_df = pd.merge(records_df, predictions_df, on="id", how="inner")
    mae_df["cogsAccessorial"] = mae_df["cogsAccessorial"].fillna(0)
    mae_df["OUTPUT_cogs_total"] = mae_df["cogsTotal"] - mae_df["cogsAccessorial"]
    mae_df = mae_df[mae_df["OUTPUT_cogs_total"] > 0]
    error = np.abs(mae_df["OUTPUT_cogs_total"] - mae_df["value"])
    mae = np.mean(error)
    mape = np.mean(error / mae_df["OUTPUT_cogs_total"])
    return (
        mae,
        mape,
        mae_df[["id", "value", "lower_bound", "upper_bound", "OUTPUT_cogs_total"]],
    )


# TODO(P1): We should probably let it price, but filter bounds in the frontend.
# Right now we keep trying to price shipments that are out of bounds.
def filter_df_based_on_bounds(
    mae_df: pd.DataFrame, BOUND_SIZE_UPPER_BOUND=1000, BOUND_RATIO_UPPER_BOUND=2
) -> pd.DataFrame:
    # Bounds determined in Bound Evaluation.ipynb notebook
    # Store the initial number of rows for logging
    logging.info("Initial number of rows: %s", str(len(mae_df)))

    # Create temporary columns in the DataFrame for 'bound_size' and 'bound_ratio'
    bound_size = mae_df["upper_bound"] - mae_df["lower_bound"]
    # To avoid division by zero errors, replace zeros with NaN in 'OUTPUT_cogs_total' and 'mlPrice'
    actual = mae_df["OUTPUT_cogs_total"].replace(0, np.nan)
    bound_ratio = bound_size / actual

    mask = (bound_size < BOUND_SIZE_UPPER_BOUND) & (
        bound_ratio < BOUND_RATIO_UPPER_BOUND
    )

    logging.debug("bound size: %s", str(bound_size))
    logging.debug("bound ratio: %s", str(bound_ratio))
    logging.debug("mask: %s", str(mask))

    filtered_df = mae_df[mask]

    # Log the number of rows after filtering and how many rows were filtered out
    logging.info("Number of rows after filtering: %s", str(len(filtered_df)))

    return filtered_df


def main():
    JOB_NAME = "ML Pricer"
    (
        job_start_time,
        job_start_timestr_central,
        mail_api,
    ) = ecr_job_util.set_up(JOB_NAME)

    dev_str = "_dev" if args.dev_db else ""
    stream_name = f"{'_'.join(JOB_NAME.split(' ')).lower()}{dev_str}_{job_start_time.strftime('%Y_%m_%d_%H_%M_%S')}"
    cw_logger = CloudWatchLogger(
        log_group="ml-pricer",
        log_to_stdout=args.log_to_stdout,
        verbose_level=args.verbose,
    )
    cw_logger.set_stream(stream_name)

    try:
        # Set up variables for ml pricer.
        db_name = AURORA_DB_DEV_NAME if args.dev_db else AURORA_DB_NAME
        endpoint = (
            VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_TRANSPARENCY_DEV
            if args.dev_db
            else VertexAutoMLPricePredictor.PULL_FROM_GSHEETS_TRANSPARENCY_PROD
        )
        model_name = "rds-mystery-cheating"
        predictor = VertexAutoMLPricePredictor(endpoint)
        model_source = predictor.__class__.__name__
        mail_body = ""

        start_time = time.perf_counter()
        # Select unpriced shipments from the database.
        records_df = load_shipments_from_db(
            db_name=db_name,
            model_source=model_source,
            model_name=model_name,
        )

        run_time_str = str(datetime.timedelta(seconds=time.perf_counter() - start_time))
        mail_body += (
            f"Number of shipments loaded: {len(records_df)} in {run_time_str}.\n"
        )
        logging.info(
            "Number of shipments loaded: %s in %s", str(len(records_df)), run_time_str
        )

        mae = pd.NA

        if len(records_df) == 0:
            logging.warning("No shipments to price.")
        else:
            start_time = time.perf_counter()
            # Predict prices for the shipments, using AI predictor.
            predictions_df = ml_pricer(records_df, predictor)

            run_time_str = str(
                datetime.timedelta(seconds=time.perf_counter() - start_time)
            )
            mail_body += f"Number of shipments priced: {len(predictions_df)} in {run_time_str} seconds.\n"
            logging.info(
                "Number of shipments priced: %s in %s",
                str(len(predictions_df)),
                run_time_str,
            )

            mae, mape, predictions_df = evaluate_predictions_and_clean_df(
                records_df, predictions_df
            )
            mail_body += f"MAE: ${mae:.2f}, MAPE: {mape:.2f}%\n"
            logging.info("MAE: $%s, MAPE: %s%%", str(mae), str(mape))
            print(f"MAE: ${mae:.2f}, MAPE: {mape:.2f}%")

            # Consider taking out.
            if len(predictions_df) > 100 and mape > 0.15:
                raise ValueError(
                    f"MAPE is high: {round(mape * 100)}% > 15%. Please investigate."
                )

            # Disable filtering for now. We want to do this in the frontend anyways.
            # predictions_df = filter_df_based_on_bounds(predictions_df)

            if args.dry_mode:
                logging.info("Dry mode, not updating shipments in db.")
            else:
                start_time = time.perf_counter()
                # Update the shipments in the database with the predictions.
                update_shipments_in_db(
                    db_name=db_name,
                    predictions_df=predictions_df,
                    model_name=model_name,
                    model_source=model_source,
                )

                run_time_str = str(
                    datetime.timedelta(seconds=time.perf_counter() - start_time)
                )
                mail_body += f"Number of shipments updated: {len(predictions_df)} in {run_time_str} seconds.\n"
                logging.info(
                    "Number of shipments updated: %s in %s",
                    str(len(predictions_df)),
                    run_time_str,
                )

        mail_body += f"Log: {cw_logger.log_url}"

        ecr_job_util.success_email(
            mail_api=mail_api,
            dev=args.dev_db,
            job_name=JOB_NAME,
            job_start_time=job_start_time,
            job_start_timestr_central=job_start_timestr_central,
            mail_header=f" MAE: ${mae:.2f}",
            mail_body=mail_body,
        )
    except Exception as e:
        mail_body += f"Log: {cw_logger.log_url}"
        ecr_job_util.failure_email(
            e=e,
            mail_api=mail_api,
            dev=args.dev_db,
            job_name=JOB_NAME,
            job_start_time=job_start_time,
            job_start_timestr_central=job_start_timestr_central,
            mail_body=mail_body,
        )


if __name__ == "__main__":
    main()

# Sample command: python .\machine_learning\price_recommendation\transparency_pricer\ml_pricer.py --dry_mode --dev_db
