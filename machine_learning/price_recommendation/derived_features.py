import datetime
import logging
from io import BytesIO

import pandas as pd
import requests

from common import s3_cache_file
from machine_learning.price_recommendation.constants import CANADA_PROVINCES
from machine_learning.price_recommendation.constants import EIA_API_BASE_URL
from machine_learning.price_recommendation.constants import EIA_API_KEY
from machine_learning.price_recommendation.constants import MEXICO_STATES
from machine_learning.price_recommendation.constants import STATE_TO_EIA_AREA


def create_diesel_prices_csv():
    """Create a CSV file of weekly diesel prices from EIA data.

    This has been already run manually and saved to S3, so it should not be necessary to run again.
    """
    # TODO(P1): Support canada with https://www150.statcan.gc.ca/t1/tbl1/en/tv.action?pid=1810000101&pickMembers%5B0%5D=2.6&cubeTimeFrame.startMonth=01&cubeTimeFrame.startYear=2021&cubeTimeFrame.endMonth=12&cubeTimeFrame.endYear=2023&referencePeriods=20210101%2C20231201
    # The URL of the Excel file.
    url = "https://www.eia.gov/petroleum/gasdiesel/xls/psw18vwall.xls"

    # Send a HTTP request to the URL of the file, read it in
    data = requests.get(url).content

    # Load data into pandas DataFrame from specific sheet (skip first two rows)
    df = pd.read_excel(BytesIO(data), sheet_name="Data 1", skiprows=2)

    # Mapping
    COL_MAP = {
        "Date": "date",
        "Weekly New England (PADD 1A) No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 1A",
        "Weekly Central Atlantic (PADD 1B) No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 1B",
        "Weekly Lower Atlantic (PADD 1C) No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 1C",
        "Weekly Midwest No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 2",
        "Weekly Gulf Coast No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 3",
        "Weekly Rocky Mountain No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 4",
        "Weekly West Coast No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 5",
        "Weekly California No 2 Diesel Retail Prices  (Dollars per Gallon)": "CALIFORNIA",
        "Weekly West Coast (PADD 5) Except California No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 5XCA",
        "Weekly U.S. No 2 Diesel Retail Prices  (Dollars per Gallon)": "US",
        "Weekly East Coast No 2 Diesel Retail Prices  (Dollars per Gallon)": "PADD 1",
    }

    # Rename columns
    df = df.rename(columns=COL_MAP).set_index("date")

    diesel_prices = s3_cache_file.S3CacheFile(
        "truce-ml-data", "data/diesel_prices.csv", "date"
    )
    diesel_prices.df = df
    diesel_prices.save_and_upload(pull=False)


class DieselPrice:
    """Get diesel prices from EIA data.

    The EIA data is for every Monday, so we check cache for the closest Monday
    to the given date. If the closest Monday is more than a week away, we need
    to use the API to get the price and update the cache.

    https://www.eia.gov/petroleum/gasdiesel/diesel_proc-methods.php

    Example:
        diesel_price = DieselPrice()
        diesel_price.get_diesel_price(datetime.datetime(2021, 1, 1), "CA")
        diesel_price.get_diesel_price(datetime.datetime(2021, 1, 1), "TX")

        diesel_price.save_and_upload()
    """

    def __init__(self) -> None:
        self.idx = "date"
        self.cache_file = s3_cache_file.S3CacheFile(
            "truce-ml-data", "data/diesel_prices.csv", idx=[self.idx]
        )
        self.cache_file.pull()
        self.cache_file.df = pd.read_csv(
            self.cache_file.filename, parse_dates=[self.idx], index_col=[self.idx]
        )
        # TODO(P1): This is a hack to always get the latest data. Remove this, and add a cron job to update the cache.
        import time

        s = time.time()
        logging.info("Trying to get today's diesel price.")
        # Consider removing line below.
        # self.update_cache_with_api(datetime.datetime.now())
        self.get_diesel_price(datetime.datetime.now(), "CA")
        logging.info("Got today's diesel in %s seconds.", time.time() - s)

    def save_and_upload(self):
        self.cache_file.save_and_upload()

    def __update_cache_with_api(self):
        logging.warning("Attempting to update diesel price cache with EIA API.")
        week_before = self.price_date - datetime.timedelta(weeks=1)
        week_after = self.price_date + datetime.timedelta(weeks=1)

        payload = {
            "api_key": EIA_API_KEY,
            "frequency": "weekly",
            "data[]": "value",
            "facets[product][]": "EPD2D",
            "start": week_before.strftime("%Y-%m-%d"),
            "end": week_after.strftime("%Y-%m-%d"),
            "sort[0][column]": "product",
            "sort[0][direction]": "desc",
            "offset": 0,
            "length": 5000,
        }

        response = requests.get(EIA_API_BASE_URL, params=payload, timeout=120)

        if response.status_code == 200:
            data = response.json()
            # Which monday the prices are for.
            price_by_eia_area = {}
            logging.info("Updating cache df with new data.")
            for item in data["response"]["data"]:
                monday_date = pd.Timestamp(item["period"])
                if monday_date not in price_by_eia_area:
                    price_by_eia_area[monday_date] = {}
                price_by_eia_area[monday_date][item["area-name"]] = float(item["value"])

            logging.warning("EIA API response: %s", str(price_by_eia_area))
            for monday_date in price_by_eia_area:
                self.cache_file.df.loc[monday_date] = price_by_eia_area[monday_date]
            self.save_and_upload()
        else:
            logging.error(f"Error: {response.status_code}")
            return pd.NA

    def get_diesel_price(self, price_date: datetime.datetime, state: str) -> float:
        """Return the closest diesel price to the given date for the given state.

        Data is from gathered by EIA at 8a local time every Monday."""
        if pd.isna(price_date) or pd.isna(state):
            logging.error(f"Invalid input: price_date={price_date}, state={state}.")
            return pd.NA

        self.price_date = price_date
        # Get today's date
        today = datetime.datetime.now()

        if self.price_date > today:
            logging.info(f"Date {self.price_date} is in the future. Returning NA.")
            return pd.NA

        # Calculate the number of days to get to the next Monday
        days_to_next_monday = 7 - today.weekday()

        # Create a datetime object for next Monday
        next_monday = today + datetime.timedelta(days=days_to_next_monday)

        if self.price_date > next_monday:
            logging.info(f"Date {self.price_date} is after next Monday. Returning NA.")
            return pd.NA

        if state not in STATE_TO_EIA_AREA:
            if state in MEXICO_STATES or CANADA_PROVINCES:
                logging.info(f"State {state} is not in the US. Returning NA.")
                return pd.NA
            else:
                logging.error(f"State {state} not found in STATE_TO_EIA_AREA.")
            return pd.NA

        # Find the closest date to the price date. The df index time is 8a local time every Monday.
        # To be most correct, we should convert the price_date to local time, but date-closest is good enough.
        # TODO(P2): Convert price_date (UTC) to local time.
        deltas = abs(self.cache_file.df.index - self.price_date)

        # If the closest date is more than a week away, use the API to get the price.
        if deltas.min() > datetime.timedelta(days=9):
            logging.warning(
                "Local diesel price cache file has no data within one week of the price date."
            )

            self.__update_cache_with_api()

            # Try again to find the closest date, now that the cache has been updated.
            deltas = abs(self.cache_file.df.index - self.price_date)

            # If the closest date is still more than a week away, return NA.
            if deltas.min() > datetime.timedelta(days=8):
                logging.error(
                    f"Diesel Price API failed, could not find any data within one week of date {self.price_date}."
                )
                logging.warning("Let's try to get the price for the closest Monday.")
                return pd.NA
        closest_date = self.cache_file.df.index[deltas.argmin()]
        value = self.cache_file.df.loc[closest_date][STATE_TO_EIA_AREA.get(state)]
        if not isinstance(value, float):
            logging.error(
                f"Error: Diesel price for {state} on {closest_date} is not a float: {value}, and has type {type(value)}."
            )
            if isinstance(value, str):
                logging.warn(f"Trying to convert {value} to a float from a string.")
                return float(value)
            if isinstance(value, pd.Series):
                logging.warn(f"Trying to convert {value} to a float from a series.")
                return float(value.iloc[0])
            else:
                raise ValueError(
                    f"Diesel price for {state} on {closest_date} is not a float: {value}, and has type {type(value)}."
                )
        if pd.isna(value):
            logging.error(f"Diesel price for {state} on {closest_date} is NA.")
        return value


if __name__ == "__main__":
    date = datetime.datetime(2023, 5, 14)
    state = "CA"
    diesel_price = DieselPrice()
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")

    date = datetime.datetime(2023, 5, 18)
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")

    date = datetime.datetime(2023, 5, 14)
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")

    date = datetime.datetime(2023, 5, 9)
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")

    date = datetime.datetime(2025, 5, 30)
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")

    date = datetime.datetime.now()
    price = diesel_price.get_diesel_price(date, state)
    print(f"Weekly diesel price for {state} on {date}: {price}")
