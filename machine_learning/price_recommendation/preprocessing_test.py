from common import init_main

PROJECT_ROOT = init_main.initialize()
import unittest
import pandas as pd
from machine_learning.price_recommendation.preprocessing import (
    camel_to_snake,
    separate_time_fields,
    standardize_state_code,
    derive_country_from_state,
    build_timestamp,
)


class TestBuildTimestamp(unittest.TestCase):
    # Test camel_to_snake
    def test_camel_to_snake(self):
        self.assertEqual(camel_to_snake("camelCase"), "camel_case")
        self.assertEqual(camel_to_snake("CamelCase"), "camel_case")
        self.assertEqual(camel_to_snake("camel"), "camel")

    # # Test encode_cyclic_features
    # def test_encode_cyclic_features(self):
    #     df = pd.DataFrame({"col_hour": [0, 1, 2, 3]})
    #     numeric_columns = ["col_hour"]
    #     encode_cyclic_features(df, {"hour": 4}, numeric_columns)
    #     print(df.columns)
    #     self.assertTrue("sin_col_hour" in df.columns)
    #     self.assertTrue("cos_col_hour" in df.columns)

    # Test separate_time_fields
    def test_separate_time_fields(self):
        df = pd.DataFrame({"some_time": [pd.Timestamp("2023-08-31 14:30:00")]})
        separate_time_fields("some_time", df)
        self.assertTrue("some_hour" in df.columns)
        self.assertTrue("some_day" in df.columns)

    # Test standardize_state_code
    def test_standardize_state_code(self):
        self.assertTrue(pd.isna(standardize_state_code("invalid_state")))

    # Test derive_country_from_state
    def test_derive_country_from_state(self):
        self.assertEqual(derive_country_from_state("CA"), "US")
        self.assertTrue(pd.isna(derive_country_from_state("invalid_state")))

    # Test build_timestamp
    def test_build_timestamp(self):
        self.assertEqual(
            build_timestamp("2023-08-31", "14:30"), pd.Timestamp("2023-08-31 14:30")
        )
        self.assertTrue(pd.isna(build_timestamp("", "14:30")))
        self.assertEqual(build_timestamp("2023-08-31", ""), pd.Timestamp("2023-08-31"))

        result = build_timestamp("2023-08-31", "14:30")
        self.assertEqual(result, pd.Timestamp("2023-08-31 14:30"))

        result = build_timestamp("", "14:30")
        self.assertTrue(pd.isna(result))

        result = build_timestamp("2023-08-31", "")
        self.assertEqual(result, pd.Timestamp("2023-08-31"))

        result = build_timestamp("", "")
        self.assertTrue(pd.isna(result))

        self.assertTrue(pd.isna(build_timestamp("", "15:00")))

        self.assertEqual(
            build_timestamp("18/08/2023", ""), pd.Timestamp("2023-08-18 00:00:00")
        )

        self.assertTrue(pd.isna(build_timestamp("", "")))

        self.assertEqual(
            build_timestamp("18/08/2023", "15:00 (CDT)Requested Pickup"),
            pd.Timestamp("2023-08-18 15:00:00"),
        )

        self.assertEqual(
            build_timestamp("18/08/2023 some text", "15:00"),
            pd.Timestamp("2023-08-18 15:00:00"),
        )

        self.assertEqual(
            build_timestamp("18/08/2023", "15:00"), pd.Timestamp("2023-08-18 15:00:00")
        )

        self.assertTrue(pd.isna(build_timestamp("invalid_date", "15:00")))

        self.assertEqual(
            build_timestamp("18/08/2023", "invalid_time"),
            pd.Timestamp("2023-08-18 00:00:00"),
        )


if __name__ == "__main__":
    unittest.main()
