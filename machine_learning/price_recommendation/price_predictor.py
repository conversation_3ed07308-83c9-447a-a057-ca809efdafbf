import json
import logging
import time
import typing

import numpy as np
import pandas as pd

start_time = time.perf_counter()
from google.cloud import aiplatform

print(
    "│   ┌───Time to load aiplatform: %s seconds." % (time.perf_counter() - start_time)
)
from google.protobuf import json_format
from google.protobuf.struct_pb2 import Value

from common import google_sheets_utils, s3_cache_file
from common.google_maps_utils import GoogleMapsUtils
from machine_learning.price_recommendation.constants import GCP_PROJECT_ID
from machine_learning.price_recommendation.constants import NUMERIC_INPUT_COLS
from machine_learning.price_recommendation.constants import TIME_CYCLIC_MAX_VALUES
from machine_learning.price_recommendation.derived_features import DieselPrice
from machine_learning.price_recommendation.preprocessing import camel_to_snake
from machine_learning.price_recommendation.preprocessing import encode_cyclic_features
from machine_learning.price_recommendation.preprocessing import separate_time_fields


# TODO(P0): #1267 Use this as input for all the below models
class PricePredictorInput:
    def __init__(
        self,
        price_predictor_input=None,
        **kwargs,
    ):
        if price_predictor_input:
            self.__dict__ = vars(price_predictor_input).copy()
        else:
            self.origin_city = kwargs.get("origin_city")
            self.origin_state = kwargs.get("origin_state")
            self.destination_city = kwargs.get("destination_city")
            self.destination_state = kwargs.get("destination_state")
            self.origin_country = kwargs.get("origin_country")
            self.origin_zip = kwargs.get("origin_zip")
            self.destination_country = kwargs.get("destination_country")
            self.destination_zip = kwargs.get("destination_zip")
            self.origin_open_time = kwargs.get("origin_open_time")
            self.origin_close_time = kwargs.get("origin_close_time")
            self.destination_open_time = kwargs.get("destination_open_time")
            self.destination_close_time = kwargs.get("destination_close_time")
            self.distance_miles = kwargs.get("distance_miles")
            self.equipment_type = kwargs.get("equipment_type")
            self.data_source = kwargs.get("data_source")
            self.weight = kwargs.get("weight")
            self.pricing_type = kwargs.get("pricing_type")
            # Ignore the rest of the kwargs

        required_str_fields = [
            "origin_city",
            "origin_state",
            "destination_city",
            "destination_state",
        ]
        for field in required_str_fields:
            if getattr(self, field) is None:
                raise TypeError(
                    f"Must provide either a PricePredictorInput instance or {', '.join(required_str_fields)}"
                )
            if not isinstance(getattr(self, field), str):
                raise TypeError(f"{field} must be a string")
        self.clean()

    def clean(self):
        # Clean up the input
        self.origin_city = self.origin_city.strip().title()
        self.origin_state = self.origin_state.strip().upper()
        self.destination_city = self.destination_city.strip().title()
        self.destination_state = self.destination_state.strip().upper()
        self.origin_open_time = (
            pd.NA
            if pd.isna(self.origin_open_time)
            else pd.Timestamp(self.origin_open_time)
        )
        self.origin_close_time = (
            pd.NA
            if pd.isna(self.origin_close_time)
            else pd.Timestamp(self.origin_close_time)
        )
        self.destination_open_time = (
            pd.NA
            if pd.isna(self.destination_open_time)
            else pd.Timestamp(self.destination_open_time)
        )
        self.destination_close_time = (
            pd.NA
            if pd.isna(self.destination_close_time)
            else pd.Timestamp(self.destination_close_time)
        )

    def compare(self, other):
        for k, v in vars(self).items():
            other_value = getattr(other, k)
            if pd.isna(v) and pd.isna(other_value):
                continue
            elif pd.isna(v) or pd.isna(other_value):
                return False
            if v != other_value:
                return False

        return True

    def to_serializable_dict(self):
        d = {}
        for k, v in vars(self).items():
            if pd.isna(v):
                continue
            if isinstance(v, pd.Timestamp):
                v = v.strftime("%Y-%m-%d %H:%M:%S")
            d[k] = v
        return d

    def __str__(self):
        return json.dumps(self.to_serializable_dict(), indent=4)


class AugmentedPricePredictorInput(PricePredictorInput):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.origin_lat = kwargs.get("origin_lat")
        self.origin_lng = kwargs.get("origin_lng")
        self.destination_lat = kwargs.get("destination_lat")
        self.destination_lng = kwargs.get("destination_lng")
        self.origin_diesel_price = kwargs.get("origin_diesel_price")
        self.destination_diesel_price = kwargs.get("destination_diesel_price")
        self.cpm = kwargs.get("cpm")


class PricePredictor:
    def __init__(self, gmaps_utils: GoogleMapsUtils = None) -> None:
        # TODO(P1): Do proper model management and checkpoints.
        self.gmaps_utils = gmaps_utils if gmaps_utils else GoogleMapsUtils()
        self.diesel_price = DieselPrice()

        self.daily_cpm = s3_cache_file.S3CacheFile(
            "truce-ml-data", "data/daily_cpm.csv", "date"
        )
        self.daily_cpm.pull()

    def save_and_upload_cache_files(self):
        # TODO(P0): Don't allow saving and uploading cache to happen more than once per 10 minutes.
        self.gmaps_utils.save_and_upload_cache_files()
        self.diesel_price.save_and_upload()
        logging.info("cache saved and uploaded")

    def update_cachefiles(self):
        """Update the daily cpm cache file."""
        self.daily_cpm.pull()

    def augment_input(self, raw: PricePredictorInput) -> AugmentedPricePredictorInput:
        """
        Augments the raw PricePredictorInput with additional data.

        Args:
            raw (PricePredictorInput): The raw input to be augmented.

        Returns:
            AugmentedPricePredictorInput: The augmented input.
        """
        aug = AugmentedPricePredictorInput(raw)

        aug.origin_lat, aug.origin_lng = self.gmaps_utils.get_lat_lng(
            raw.origin_city, raw.origin_state, raw.origin_country, raw.origin_zip
        )
        aug.destination_lat, aug.destination_lng = self.gmaps_utils.get_lat_lng(
            raw.destination_city,
            raw.destination_state,
            raw.destination_country,
            raw.destination_zip,
        )

        origin_str = f"{raw.origin_city}, {raw.origin_state}"
        origin_str += f" {raw.origin_zip}" if raw.origin_zip else ""
        destination_str = f"{raw.destination_city}, {raw.destination_state}"
        destination_str += f" {raw.destination_zip}" if raw.destination_zip else ""

        if pd.isna(aug.distance_miles):
            aug.distance_miles = self.gmaps_utils.get_distance_miles(
                origin_str, destination_str, use_cache=False
            )

        aug.origin_diesel_price = self.diesel_price.get_diesel_price(
            raw.origin_close_time, raw.origin_state
        )
        if pd.isna(raw.destination_close_time):
            aug.destination_diesel_price = self.diesel_price.get_diesel_price(
                raw.origin_close_time, raw.destination_state
            )
        else:
            aug.destination_diesel_price = self.diesel_price.get_diesel_price(
                raw.destination_close_time, raw.destination_state
            )

        aug.cpm = pd.NA
        if raw.pricing_type == "spot":
            # Find the first date in the daily_cpm dataset that is before the origin_close_time
            # This only works for present and past dates, future predictions should use NA.
            for date in reversed(self.daily_cpm.df.index):
                if date <= raw.origin_close_time.strftime("%Y-%m-%d"):
                    if raw.origin_close_time - pd.Timestamp(date) > pd.Timedelta(
                        days=7
                    ):
                        logging.error(
                            f"Close time {raw.origin_close_time} is too far in the future to use for cpm"
                        )
                        break
                    aug.cpm = self.daily_cpm.df.loc[date]["cpm"]
                    break

        return aug

    def predict(
        self,
        price_predictor_input: PricePredictorInput,
    ):
        raise NotImplementedError


class LocalModelPricePredictor(PricePredictor):
    def __init__(self) -> None:
        super().__init__()

        start_time = time.perf_counter()
        import tensorflow as tf

        print(
            "\t┌───Time to tensorflow: %s seconds." % (time.perf_counter() - start_time)
        )
        self.tf = tf

        from machine_learning.price_recommendation.metrics import R2Score

        self.model = self.tf.keras.models.load_model(
            "best_model.h5", custom_objects={"R2Score": R2Score}
        )

    def predict(self, price_predictor_input: PricePredictorInput):
        ml_input = self.augment_input(price_predictor_input)
        input_fields = [
            "distance_miles",
            "origin_close_time",
            "destination_close_time",
            "origin_lat",
            "origin_lng",
            "destination_lat",
            "destination_lng",
            "origin_diesel_price",
            "destination_diesel_price",
            "cpm",
        ]
        tf_input_df = pd.DataFrame(
            {
                k: v
                for k, v in ml_input.to_serializable_dict().items()
                if k in input_fields
            },
            index=[0],
        )

        separate_time_fields("origin_close_time", tf_input_df)
        separate_time_fields("destination_close_time", tf_input_df)

        numeric_input_cols = NUMERIC_INPUT_COLS.copy()
        encode_cyclic_features(tf_input_df, TIME_CYCLIC_MAX_VALUES, numeric_input_cols)

        # Convert NumPy array to TensorFlow Tensor
        input_tensor = self.tf.convert_to_tensor(
            np.array(tf_input_df[numeric_input_cols]), dtype=self.tf.float32
        )

        # Use the input_tensor in the predict() method
        return self.model.predict(input_tensor)


def timestamp_to_string(data: typing.Any) -> str:
    """
    Converts a timestamp input to a standardized string format.
    Handles different types of inputs including strings, pd.Timestamp, and NaN values.

    :param data: The input data to be converted.
    :return: A string representation of the timestamp in the "%Y-%m-%d %H:%M:%S" format.
    """
    desired_format = "%Y-%m-%d %H:%M:%S"
    if not data:
        return ""
    elif isinstance(data, str):
        try:
            return pd.Timestamp(data).strftime(desired_format)
        except ValueError:
            logging.error("Invalid string format for timestamp: %s", data)
            raise ValueError("Invalid string format for timestamp.")
    elif isinstance(data, pd.Timestamp):
        return data.strftime(desired_format)
    else:
        logging.error(
            "Unexpected type for timestamp: %s with value %s", type(data), data
        )
        raise TypeError("Unexpected type for timestamp.")


class VertexAutoMLPricePredictor(PricePredictor):
    # From rds-mystery late 07/2024 with OUTPUT_revenue_total
    CONTRACT_PRICER_ENDPOINT = "6631583436651888640"
    # From rds-mystery-subset
    GCP_ENDPOINT_1_JUL_10_23_ENDPOINT = "6478634772158480384"
    # From test-rds-dataset
    PRICER_SEP_5_23_ENDPOINT = "4438088525564346368"
    # From test-rds-dataset
    LESS_COLS_NOZIP_SEP_7_23_ENDPOINT = "7714457254476382208"
    # rds-mystery-cpm dataset trained 12/14/2023
    PRICER_CPM_DEC_14_23_ENDPOINT = "4504243941184372736"
    # rds-mystery-cpm dataset trained 02/24/2024, with cheating (test split not chronological)
    PRICER_CHEATING_FEB_02_24_ENDPOINT = "3466428109866139648"
    # PULL_FROM_GSHEETS
    PULL_FROM_GSHEETS_FREIGHTGPT_DEV = "FREIGHTGPT_DEV"
    PULL_FROM_GSHEETS_FREIGHTGPT_PROD = "FREIGHTGPT_PROD"
    # For the transparency pricers
    PULL_FROM_GSHEETS_TRANSPARENCY_DEV = "TRANSPARENCY_DEV"
    PULL_FROM_GSHEETS_TRANSPARENCY_PROD = "TRANSPARENCY_PROD"
    PULL_FROM_GSHEETS = {
        PULL_FROM_GSHEETS_FREIGHTGPT_DEV,
        PULL_FROM_GSHEETS_FREIGHTGPT_PROD,
        PULL_FROM_GSHEETS_TRANSPARENCY_DEV,
        PULL_FROM_GSHEETS_TRANSPARENCY_PROD,
    }

    NUMBER_COLS = [
        "origin_lat",
        "origin_lng",
        "destination_lat",
        "destination_lng",
        "origin_diesel_price",
        "destination_diesel_price",
        "cpm",
        "distance_miles",
    ]
    TIME_COLS = [
        "origin_open_time",
        "origin_close_time",
        "destination_open_time",
        "destination_close_time",
    ]

    def __init__(
        self,
        endpoint_id: str = PRICER_CHEATING_FEB_02_24_ENDPOINT,
        gmaps_utils: GoogleMapsUtils = None,
    ) -> None:
        super().__init__(gmaps_utils=gmaps_utils)
        self.endpoint_id = (
            self.__load_endpoint_id_from_gsheets(endpoint_id)
            if endpoint_id in self.PULL_FROM_GSHEETS
            else endpoint_id
        )
        self.__prediction_cache = {}

    def __load_endpoint_id_from_gsheets(self, environment: str) -> str:
        logging.info("Loading endpoint_id from Google Sheets for env %s", environment)
        gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
        # https://docs.google.com/spreadsheets/d/1ZeZISewVoK1U1P4LgEazbAk4CR9q0648O7O8-KYqFk8/edit#gid=0
        endpoints_df = gsheets_utils.load_sheet_into_dataframe(
            "1ZeZISewVoK1U1P4LgEazbAk4CR9q0648O7O8-KYqFk8", "Endpoints"
        )
        endpoint_dict = endpoints_df.set_index("Environment").to_dict(orient="index")
        if environment not in endpoint_dict:
            logging.error(f"Environment {environment} not found in Google Sheets.")
            raise ValueError(f"Environment {environment} not found.")
        endpoint_id = endpoint_dict[environment]["Pricer Endpoint"]
        logging.info("Loaded endpoint_id %s from Google Sheets", endpoint_id)
        return endpoint_id

    def __predict_tabular_classification_batch_sample(
        self,
        project: str,
        endpoint_id: str,
        instances_list: list,  # This will now be a list of dictionaries
        location: str = "us-central1",
        api_endpoint: str = "us-central1-aiplatform.googleapis.com",
    ):
        client_options = {"api_endpoint": api_endpoint}
        client = aiplatform.gapic.PredictionServiceClient(client_options=client_options)

        # Create an empty list to store the instance objects
        instances = []

        # Iterate over each instance_dict in instances_list
        for instance_dict in instances_list:
            instance = json_format.ParseDict(instance_dict, Value())
            instances.append(instance)

        # TODO(P2): Remove parameters if unused.
        # https://cloud.google.com/python/docs/reference/aiplatform/1.18.2/google.cloud.aiplatform_v1beta1.services.prediction_service.PredictionServiceClient#google_cloud_aiplatform_v1beta1_services_prediction_service_PredictionServiceClient_predict
        parameters_dict = {}
        parameters = json_format.ParseDict(parameters_dict, Value())
        endpoint = client.endpoint_path(
            project=project, location=location, endpoint=endpoint_id
        )

        response = client.predict(
            endpoint=endpoint, instances=instances, parameters=parameters
        )

        return response.predictions

    def predict(
        self,
        price_predictor_input: PricePredictorInput,
    ) -> typing.Dict[str, float]:
        """
        Predicts the price of a shipment.

        Returns:
        - Dict[str, float]: A dictionary containing:
            - "value": the truck cost
            - "lower_bound": lower bound given by model trying to make sure value is above this
            - "upper_bound": upper bound given by model trying to make sure value is below this

        """
        logging.info("Price Predictor Input: %s", price_predictor_input)
        cache_key = str(price_predictor_input)
        if cache_key in self.__prediction_cache:
            # Index using str dict representation
            prediction_time = self.__prediction_cache[cache_key]["prediction_time"]
            if pd.Timestamp.now() - prediction_time < pd.Timedelta(minutes=10):
                return dict(self.__prediction_cache[cache_key]["prediction"])

        # import time

        # s = time.perf_counter()
        self.augmented_input = self.augment_input(price_predictor_input)
        ml_input = self.augmented_input.to_serializable_dict()
        # print("    PREPROCESSING SPEED", time.perf_counter() - s)
        # s = time.perf_counter()

        for col in self.NUMBER_COLS:
            ml_input[col] = str(ml_input.get(col, ""))

        for col in self.TIME_COLS:
            ml_input[col] = timestamp_to_string(ml_input.get(col, ""))

        # print("    CONVERTING SPEED", time.perf_counter() - s)
        # s = time.perf_counter()
        api_results = self.__predict_tabular_classification_batch_sample(
            project=GCP_PROJECT_ID,
            endpoint_id=self.endpoint_id,
            location="us-central1",
            instances_list=[ml_input],
        )
        # print("    API PREDICTION SPEED", time.perf_counter() - s)
        assert len(api_results) == 1

        prediction = dict(api_results[0])

        self.__prediction_cache[cache_key] = {
            "prediction": prediction,
            "prediction_time": pd.Timestamp.now(),
        }
        logging.info("Price Predictor Output: %s", prediction)

        return dict(prediction)

        # self.save_and_upload_cache_files()

    def batch_predict(
        self,
        shipments: typing.List[typing.Dict[str, typing.Any]],
        data_source: str = "",
    ) -> typing.List[typing.Dict[str, float]]:
        BATCH_SIZE = 2000  # Lower if we exceed request size limit
        all_results = []

        for i in range(0, len(shipments), BATCH_SIZE):
            batched_shipments = shipments[i : i + BATCH_SIZE]
            ml_inputs = []

            for s in batched_shipments:
                raw_input = PricePredictorInput(
                    **{camel_to_snake(k): v for k, v in s.items()},
                    data_source=data_source,
                )
                ml_input = self.augment_input(raw_input).to_serializable_dict()
                for snake_col in self.NUMBER_COLS:
                    col = camel_to_snake(snake_col)
                    ml_input[col] = str(ml_input.get(col, ""))
                for snake_col in self.TIME_COLS:
                    col = camel_to_snake(snake_col)
                    ml_input[col] = timestamp_to_string(ml_input.get(col, ""))
                ml_inputs.append(ml_input)

            api_results = self.__predict_tabular_classification_batch_sample(
                project=GCP_PROJECT_ID,
                endpoint_id=self.endpoint_id,
                location="us-central1",
                instances_list=ml_inputs,
            )

            assert len(api_results) == len(batched_shipments)
            all_results.extend([dict(i) for i in api_results])

        # TODO(P1): Figure out why this causes errors.
        # self.save_and_upload_cache_files()
        return all_results
