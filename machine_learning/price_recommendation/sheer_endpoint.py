"""
<PERSON><PERSON>t for invoking and testing AWS sagemaker endpoints.
Endpoint name must correspond to that of the endpoint being tested.
At the time of writing, sheer model 78 is the only endpoint to be tested.

Must have access to credentials via `common`.
Specifically `sagemaker_aws_access_key_id` and `sagemaker_aws_secret_access_key`.
"""
import boto3

from common import credentials

CREDENTIALS = credentials.Credentials()
SAGEMAKER_AWS_ACCESS_KEY_ID = CREDENTIALS.sagemaker_aws_access_key_id
SAGEMAKER_AWS_SECRET_ACCESS_KEY = CREDENTIALS.sagemaker_aws_secret_access_key
ENDPOINT_NAME = "sheer-78-err-endpoint"

TEST_DATA_DICT = {
    "id": "ST289714 (Brokerage Load Number)",
    "origin_city": "ST CHARLES",
    "origin_state": "MO",
    "origin_zip": "63301",
    "destination_city": "MORENO VALLEY",
    "destination_state": "CA",
    "destination_zip": "92551",
    "OUTPUT_carrier_name": "OFO Express Inc",
    "OUTPUT_cogs_total": 3100.00,
    "distance_miles": 1751,
    "origin_country": "US",
    "destination_country": "US",
    "origin_lat": 41.3922726,
    "origin_lng": -84.1252243,
    "destination_lat": 34.9303695,
    "destination_lng": -78.7249469,
    "origin_close_hour": 10,
    "origin_close_day": 26,
    "origin_close_month": 4,
    "origin_close_day_of_year": 116,
    "origin_close_day_of_week": 7,
    "origin_close_week": 40,
    "origin_close_year": 2023,
    "origin_close_is_workday": 1,
    "origin_close_is_holiday": 0,
    "destination_close_hour": 23,
    "destination_close_day": 1,
    "destination_close_month": 9,
    "destination_close_day_of_year": 191,
    "destination_close_day_of_week": 1,
    "destination_close_week": 18,
    "destination_close_year": 2023,
}

TEST_DATA_DICT = ",".join([str(x) for x in TEST_DATA_DICT])


def access_endpoint(
    access_id: str, secret_key: str, endpoint_name: str, test_data: dict
):
    """
    Invokes given endpoint using assigned key constants and test data.
    Returns endpoint response; sagemaker model's output value(s).
    """
    runtime = boto3.client(
        "runtume.sagemaker",
        aws_access_key_id=access_id,
        aws_secret_access_key=secret_key,
    )

    response = runtime.invoke_endpoint(
        EndpointName=endpoint_name,
        ContentType="text/csv",
        Body=test_data,
    )

    print(response["Body"].read().decode())


if __name__ == "__main__":
    access_endpoint(
        SAGEMAKER_AWS_ACCESS_KEY_ID,
        SAGEMAKER_AWS_SECRET_ACCESS_KEY,
        ENDPOINT_NAME,
        TEST_DATA_DICT,
    )
