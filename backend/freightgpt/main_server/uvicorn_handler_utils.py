import copy
import datetime
import logging
import os
import uuid
from typing import Any
from typing import Dict
from typing import <PERSON>ple

import aioboto3
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from requests import Request

from backend.freightgpt.constants import CloudResources
from backend.freightgpt.constants import DATE_FORMAT
from backend.freightgpt.constants import MAX_HITS
from backend.freightgpt.constants import MAX_HOURS
from backend.freightgpt.constants import SECONDS_PER_HOUR
from backend.freightgpt.constants import SESSION_FAILURE_EVENT
from backend.freightgpt.constants import (
    SUCCESS_RESPONSE,
)
from backend.freightgpt.main_server.langsmith_feedback_manager import (
    LangsmithFeedbackClient,
)
from backend.freightgpt.main_server.session_manager import ChatbotSessionManager
from common import dynamodb_utils
from common import mail
from common.cloudwatch_log_utils import CloudWatchLogger
from common.cognito_utils import CognitoUserData
from common.cognito_utils import FREIGHTGPT_DEV_COGNITO_USER_POOL_ID
from common.cognito_utils import FREIGHTGPT_PROD_COGNITO_USER_POOL_ID
from machine_learning.freightgpt.async_stream_handler import send_close_event
from machine_learning.freightgpt.async_stream_handler import send_data
from machine_learning.freightgpt.langchain_agent import LangChainAgent


# Obtain relevant cloud resource names
RESOURCES = getattr(CloudResources, os.getenv("ENV")).value

# Set the LANGCHAIN_API_KEY environment variable to the appropriate value
os.environ["LANGCHAIN_API_KEY"] = RESOURCES.LANGCHAIN_API_KEY.value

# Server resources: aiboto3 session, session manager, feedback client, user data, cpm cachefile
SESSION = aioboto3.Session()
SESSION_MANAGER = ChatbotSessionManager(RESOURCES.SESSIONS_TABLE_NAME.value)
MAIL_API = mail.MailAPI(gmail=False)
user_pool_id = (
    FREIGHTGPT_DEV_COGNITO_USER_POOL_ID
    if os.getenv("ENV") == "DEV"
    else FREIGHTGPT_PROD_COGNITO_USER_POOL_ID
)
COGNITO_USER_DATA = CognitoUserData(
    user_pool_id=user_pool_id,
    is_dev_env=os.getenv("ENV") == "DEV",
    mail_api=MAIL_API,
)
# Pulls data from all users and writes to disk
COGNITO_USER_DATA.pull_all_users()

LANGSMITH_FEEDBACK_CLIENT = LangsmithFeedbackClient(
    langchain_project=RESOURCES.LANGSMITH_PROJECT.value,
    cognito_user_data=COGNITO_USER_DATA,
    mail_api=MAIL_API,
)


def initialize_logger(
    log_to_stdout: bool, log_only: bool, batch_logs: bool
) -> CloudWatchLogger:
    """Create CloudWatchLogger Instance

    Args:
        log_to_stdout (bool): specifies if logs should be printed to stdout
        log_only (bool): if True, logs are pushed to CloudWatch
        batch_logs (bool): specifies if logs should be batched

    Returns:
        CloudWatchLogger: CloudWatch logging instance
    """
    return CloudWatchLogger(
        RESOURCES.CLOUDWATCH_GROUP.value,
        log_to_stdout=log_to_stdout,
        log_only=log_only,
        batch_logs=batch_logs,
    )


async def parse_request(request: Request):
    """Parses HTTP request for body and headers."""
    request_headers = request.headers
    request_body = await request.json()
    return request_body, request_headers


async def initialize_connection(
    request_headers: Dict[str, any],
    request_body: Dict[str, any],
    force_session_id: str = "",
    is_public_session: bool = False,
) -> Dict[str, Any]:
    """Initializes or creates a new session based on the request details."""
    user_id = request_body.get("user_id")

    # If the session is private and there is no user id then return failure
    if not user_id and not is_public_session:
        return SESSION_FAILURE_EVENT

    user_id = user_id if user_id else str(uuid.uuid4())
    conversation_id = request_body.get("conversation_id") or str(uuid.uuid4())
    old_session_id = request_body.get("session_id")
    tags = request_body.get("tags", [])

    # Cancel a task if necessary and delete old session
    SESSION_MANAGER.cancel_user_active_task(user_id)
    SESSION_MANAGER.delete_session(old_session_id)

    # Determine user name based on session type
    if is_public_session:
        user_name = "public_user_" + user_id
        tags.append("public")
        project_name = CloudResources.PUBLIC.value.LANGSMITH_PROJECT.value
    elif os.getenv("ENV") == "TEST":
        user_name = "integration_test_user"
        project_name = RESOURCES.LANGSMITH_PROJECT.value
    else:
        user_data = COGNITO_USER_DATA.get_cognito_user_data(user_id)
        user_name = user_data.get("name", "")
        project_name = RESOURCES.LANGSMITH_PROJECT.value

    tags += [user_name]

    # Initialize a new agent and create a session
    new_agent = LangChainAgent(
        streaming=True,
        user_id=user_id,
        conversation_id=conversation_id,
        chats_table_name=(
            RESOURCES.CONVO_TABLE_NAME.value if not is_public_session else None
        ),
        events_table_name=(
            RESOURCES.EVENTS_TABLE_NAME.value if not is_public_session else None
        ),
        tags=tags,
        mail_api=MAIL_API,
        langsmith_feedback_client=LANGSMITH_FEEDBACK_CLIENT,
        project_name=project_name,
    )
    new_session_id = force_session_id or str(uuid.uuid4())
    creation_status = SESSION_MANAGER.create_session(new_session_id, user_id, new_agent)

    if not creation_status:
        return SESSION_FAILURE_EVENT

    return {
        "event": {
            "name": "session_status",
            "status": "SUCCESS",
            "session_id": new_session_id,
            "conversation_id": conversation_id,
            "cookie": request_headers.get("cookie"),
        }
    }


async def create_session(
    session_details: Dict[str, Any], close_socket: bool, connection_id: str
) -> JSONResponse:
    """Creates a new session based on the request details."""
    await send_data(
        connection_id=connection_id,
        data=session_details,
        session=SESSION,
    )

    if close_socket:
        await send_close_event(SESSION, connection_id)

    try:
        await SESSION_MANAGER.publish_session_status()
    except Exception as e:
        logging.warning(f"Failed to publish session status with {e}")

    success_response = copy.deepcopy(SUCCESS_RESPONSE)
    success_response["content"]["conversation_id"] = session_details["event"][
        "conversation_id"
    ]
    success_response["content"]["session_id"] = session_details["event"]["session_id"]
    return JSONResponse(**success_response)


def has_exceeded_ip_limit(
    request_headers: Dict[str, Any], update_count: bool = True
) -> Tuple[int, bool]:
    """
    Check if the IP limit has been exceeded for the current client.

    Args:
        request_headers (dict): The headers from the incoming request.
            - (str) sourceIP: The client's IP address.
    Returns:
        Tuple: Current hit count and True if the IP limit has been exceeded, False otherwise.
    """
    client_ip = request_headers.get("sourceIp")
    table_name = RESOURCES.IP_LIMIT_TABLE_NAME.value

    # Retrieve the current entry for this IP from the database
    ip_entry = dynamodb_utils.read_item(table_name, {"ip": client_ip})
    current_time = datetime.datetime.now(datetime.timezone.utc)

    # If there's no entry, initialize it
    if ip_entry is None:
        if update_count:
            ip_entry = {"ip": client_ip, "hits": 1}
            dynamodb_utils.insert_item(table_name, ip_entry)
            return ip_entry["hits"], False
        return 0, False

    # Calculate the time difference since the last hit
    last_modified = datetime.datetime.strptime(
        ip_entry["last_updated_timestamp"], DATE_FORMAT
    )
    hours_elapsed = (current_time - last_modified).total_seconds() / SECONDS_PER_HOUR
    if hours_elapsed > MAX_HOURS:
        if update_count:
            ip_entry["hits"] = 1
            dynamodb_utils.insert_item(table_name, ip_entry)
            return ip_entry["hits"], False
        return 0, False
    if ip_entry["hits"] >= MAX_HITS:
        return ip_entry["hits"], True

    # Increment the hit counter and update the last updated time
    # Note: last updated time is added by insert_item function
    if update_count:
        ip_entry["hits"] += 1
        dynamodb_utils.insert_item(table_name, ip_entry)

    return ip_entry["hits"], False
