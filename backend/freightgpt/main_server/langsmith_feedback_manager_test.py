import unittest

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()
from backend.freightgpt.main_server.langsmith_feedback_manager import (
    LangsmithFeedbackClient,
)

import os

os.environ["LANGCHAIN_PROJECT"] = "unit-test"

langsmith_feedback_client = LangsmithFeedbackClient()


class TestFeedbackManager(unittest.TestCase):
    # Test extraneous whitespace and non ascii characters in feedback.
    @parameterized.expand(
        [
            ("             thumbs_up        ", "thumbs_up"),
            ("æthumbs_downæ", "thumbs_down"),
        ]
    )
    def test_unclean_feedback(self, unclean_string, clean_string):
        langsmith_feedback_client.feedback = unclean_string
        self.assertEqual(langsmith_feedback_client.feedback, clean_string)

    # Test script insertion, spaces, and non ascii characters in comment.
    @parameterized.expand(
        [
            (
                "     Some commentæææææææ with speciæal          ææææcharacters<script>alert('xss');</script>\nNew line here.   ",
                "Some comment with special characters New line here.",
            ),
        ]
    )
    def test_unclean_comment(self, unclean_string, clean_string):
        langsmith_feedback_client.comment = unclean_string
        self.assertEqual(langsmith_feedback_client.comment, clean_string)

    # Test non positive or negative feedback
    @parameterized.expand(
        [
            ("roop",),
            (None,),
        ]
    )
    def test_bad_feedback(self, bad_feedback):
        with self.assertRaises(ValueError):
            langsmith_feedback_client.feedback = bad_feedback


# Run the tests
if __name__ == "__main__":
    unittest.main()
