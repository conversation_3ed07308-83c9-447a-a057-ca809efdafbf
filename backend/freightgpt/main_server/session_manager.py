import asyncio
import collections
import logging
import os
import time
import typing

from common import dynamodb_utils


class ChatbotSessionManager:
    """A class that manages chatbot sessions.

    Args:
        table_name: the sessions dynamo table to publish session information to.

    Attributes:
        sessions (dict): A dictionary to store session information.
    """

    # Thread shared sessions in class scope.
    sessions = collections.defaultdict(dict)
    user_active_tasks = {}

    def __init__(
        self,
        table_name: str,
    ) -> None:
        self.table_name = table_name
        self.user_last_creation_times = {}  # Miliseconds since epoch.

    # TODO (P0): find a better way to prevent new session spamming or smarter grace period.
    def create_session(
        self,
        session_id: str,
        user_id: str,
        chatbot: object,
        grace_period_seconds: int = 0.05,
    ) -> bool:
        """Create a new session in RAM with any specified chatbot object.

        Args:
            session_id (str): The ID of the session.
            user_id (str): The ID of the user.
            chatbot (object): The chatbot object associated with the session.
            grace_period_seconds (int, optional): The minimum allowable time between successive creation requests in seconds.

        """
        creation_time = time.time()

        if user_id is None:
            logging.error("Cannot create session with null user id.")
            return False

        # Prevent session spamming
        if user_id in self.user_last_creation_times:
            last_creation = self.user_last_creation_times[user_id]

            if creation_time - last_creation < grace_period_seconds:
                logging.error("Session creation under grace period.")
                return False
        self.user_last_creation_times[user_id] = creation_time

        # All times in miliseconds since epoch.
        self.sessions[session_id]["user_id"] = user_id
        self.sessions[session_id]["creation_time"] = creation_time
        self.sessions[session_id]["last_update_time"] = creation_time
        self.sessions[session_id]["chatbot"] = chatbot

        return True

    def get_session(self, session_id: str) -> dict:
        """Get the session information.

        Args:
            session_id (str): The ID of the session.

        Returns:
            dict: The session information.

        """
        return self.sessions.get(session_id)

    def delete_session(self, session_id: str):
        """Delete a session.

        Args:
            session_id (str): The ID of the session.
            aioboto3_session: The aioboto3 session.

        """
        if session_id is not None and session_id in self.sessions:
            # Delete session from RAM
            del self.sessions[session_id]

    def update_session_data(self, session_id: str, attribute: str, value: typing.Any):
        """Update the session data.

        Args:
            session_id (str): The ID of the session.
            attribute (str): The attribute to update.
            value (typing.Any): The new value for the attribute.

        """
        self.sessions[session_id][attribute] = value

    async def publish_session_status(self):
        """Publish all new sessions to DynamoDB.

        Args:
            aioboto3_session: The aioboto3 session.

        """

        if os.getenv("DRY_MODE"):
            logging.info(
                "Env variable 'DRY_MODE' set. Will not upload session statuses."
            )
            return

        logging.info("Publishing session status!")
        items = []
        for session_id in self.sessions:
            session = self.sessions[session_id]

            item = {
                "user_id": session["user_id"],
                "session_id": session_id,
                "creation_time": session["creation_time"],
                "last_update_time": session["last_update_time"],
            }
            items.append(item)

        await dynamodb_utils.async_batch_upload(self.table_name, items)

    def set_user_active_task(self, user_id: str, task: asyncio.Task) -> None:
        """Sets the current active task for this user."""
        self.user_active_tasks[user_id] = task

    def cancel_user_active_task(self, user_id: str) -> None:
        """Cancels current active task for user"""
        if user_active_task := self.user_active_tasks.get(user_id):
            logging.info("Canceling task")
            user_active_task.cancel()
