FROM common:latest

# Environment provided with --build_arg
ARG ENV_VALUE
ENV ENV=$ENV_VALUE

# Copy python code to environment
ADD machine_learning machine_learning
ADD machine_learning/freightgpt machine_learning/freightgpt
ADD machine_learning/price_recommendation machine_learning/price_recommendation
ADD backend/freightgpt/constants.py backend/freightgpt/constants.py
ADD backend/freightgpt/main_server backend/freightgpt/main_server

# Copy and install requirements.txt to environment
RUN pip3 install -r machine_learning/price_recommendation/requirements.txt
RUN pip3 install -r machine_learning/freightgpt/requirements.txt
RUN python -m unittest discover -p '*_test.py'

CMD [ "python3", "backend/freightgpt/main_server/uvicorn_handler.py" ]
