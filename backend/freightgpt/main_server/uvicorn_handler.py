"""
A server for FreightGPT based on FastAPI and Uvicorn. Handles all requests for the FreightGPT platform.
FastAPI is a lightweight alternative to flask that allows asynchronous code execution under endpoints.
Also initializes background managers for sessions, feedback, and logging.

Args:
    1. `--env`: This argument specifies the environment in which the server should run. It accepts either "DEV" or "PROD".
    2. `--dry_mode`: Instead of using cloud resources, use local memory.
    3. `--log_to_stdout`: Directs all output to the standard output (usually the console).
    4. `--publish_logs`: Publishes logs to CloudWatch.

Endpoints:
    1. `/health`: This is a GET endpoint that returns a JSON response indicating the server is reachable.
    2. `/new_session`: This is a POST endpoint that creates a new session for login or convo load.
    3. `/feedback`: This is a POST endpoint that handles feedback submissions.
    4. `/chat_message`: This is a POST endpoint that handles incoming chat messages and runs them through the langchain agent.
"""
from common import init_main

PROJECT_ROOT = init_main.initialize()

import argparse
import os
import logging
import uuid

# Server args
parser = argparse.ArgumentParser()
parser.add_argument(
    "--env",
    type=lambda x: x.upper(),  # Ensure capitalization is consistent
    choices=["TEST", "DEV", "PROD"],
    default="DEV",
    help="Run server against dev or prod backend resources",
)
parser.add_argument(
    "--dry_mode",
    action="store_true",
    default=False,
    help="Store/access resources in cloud, or simply use local memory.",
)
parser.add_argument(
    "--log_to_stdout",
    action="store_true",
    default=False,
    help="Log all output to standard out.",
)
parser.add_argument(
    "--publish_logs",
    action="store_true",
    default=False,
    help="Publish logs to cloudwatch.",
)
args = parser.parse_args()

# Set ubiquitous variables in environment.
os.environ["ENV"] = args.env
if args.dry_mode:
    os.environ["DRY_MODE"] = str(args.dry_mode)

    # Disable langsmith tracing in dry mode.
    os.environ["LANGCHAIN_TRACING_V2"] = "false"


# from backend.freightgpt.main_server.uvicorn_handler_utils import ACTIVE_CONNECTION_IDS
from backend.freightgpt.main_server.uvicorn_handler_utils import create_session
from backend.freightgpt.main_server.uvicorn_handler_utils import initialize_connection
from backend.freightgpt.main_server.uvicorn_handler_utils import has_exceeded_ip_limit
from backend.freightgpt.main_server.uvicorn_handler_utils import (
    LANGSMITH_FEEDBACK_CLIENT,
)
from backend.freightgpt.main_server.uvicorn_handler_utils import MAIL_API
from backend.freightgpt.main_server.uvicorn_handler_utils import parse_request
from backend.freightgpt.main_server.uvicorn_handler_utils import SESSION
from backend.freightgpt.main_server.uvicorn_handler_utils import SESSION_MANAGER
from backend.freightgpt.main_server.uvicorn_handler_utils import initialize_logger


from backend.freightgpt.constants import (
    SUCCESS_RESPONSE,
    FAILURE_RESPONSE,
    MAX_LIMIT_EVENT,
    IP_LIMIT_BYPASS_TOKEN,
)

from machine_learning.freightgpt.async_stream_handler import (
    stream_response,
    send_close_event,
    send_data,
)
from machine_learning.freightgpt.carrier_tool import carrier_search

from machine_learning.freightgpt.langchain_tools import PRICER
from machine_learning.freightgpt.langchain_utils import asyncify
from machine_learning.freightgpt.rfp_pricer import rfp_main


import uvicorn
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi import FastAPI

import asyncio

import time
import json
import logging

# FastAPI is a lightweight alternative to flask that allows asynchronous code execution under endpoints.
# Asynchronous endpoints are required due to the nature of token streaming.
# For reference, see stream_response of LangChainAgent.
APP = FastAPI()

# Logging
# If publish logs is not set, logs will not be pushed to cloudwatch (log_only == True).
# If publishing logs, logs will be batched.
CWL_LOGGER = initialize_logger(
    log_to_stdout=args.log_to_stdout,
    log_only=not args.publish_logs,
    batch_logs=args.publish_logs,
)


# ----------------------------------------------------------------------
#         SERVER ENDPOINTS
# ----------------------------------------------------------------------


@APP.get("/health")
async def health():
    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/new_session")
async def new_session(
    request: Request, force_session_id="", close_socket=True, is_public=False
):
    """
    New session happens whenever you load a convo or first login.

    NOTE: If it's too slow there will be time between when the user logs in and when they can send the first message.
    Keep latency under 1 second if possible.
    """
    request_body, request_headers = await parse_request(request)
    session_details = await initialize_connection(
        request_headers=request_headers,
        request_body=request_body,
        force_session_id=force_session_id,
        is_public_session=is_public,
    )
    return await create_session(
        session_details=session_details,
        close_socket=close_socket,
        connection_id=request_headers.get("connectionid"),
    )


@APP.post("/feedback")
async def feedback(request: Request):
    request_body, request_headers = await parse_request(request)

    connection_id = request_headers.get("connectionid")
    run_id = request_body.get("run_id")
    feedback_type = request_body.get("feedback_type")
    comment = request_body.get("comment", None)

    # Email / optional params.
    user_id = request_body.get("user_id", "")
    conversation_id = request_body.get("conversation_id", "")
    human_query = request_body.get("query", "")
    ai_response = request_body.get("response", "")
    flags = request_body.get("flags", [])

    # Setting properties will automatically sanitize them.
    LANGSMITH_FEEDBACK_CLIENT.feedback = feedback_type
    LANGSMITH_FEEDBACK_CLIENT.comment = comment

    # Create feedback in langsmith.
    LANGSMITH_FEEDBACK_CLIENT.create_feedback(
        run_id,
        LANGSMITH_FEEDBACK_CLIENT.feedback,
        comment=LANGSMITH_FEEDBACK_CLIENT.comment,
        score=LANGSMITH_FEEDBACK_CLIENT.is_feedback_positive(),
        source_info={"user_id": user_id, "conversation_id": conversation_id},
    )
    for flag in flags:
        LANGSMITH_FEEDBACK_CLIENT.create_feedback(
            run_id,
            flag,
            source_info={"user_id": user_id, "conversation_id": conversation_id},
        )

    # Send feedback email
    LANGSMITH_FEEDBACK_CLIENT.send_feedback_email(
        user_id=user_id,
        conversation_id=conversation_id,
        run_id=run_id,
        human_query=human_query,
        ai_response=ai_response,
        flags=flags,
    )

    await send_close_event(SESSION, connection_id)
    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/close_sessions")
async def close_sessions(request: Request):
    request_body, _ = await parse_request(request)
    sessions_to_close = request_body.get("sessions_to_close", [])
    # Delete chatbots under session id
    for session_id in sessions_to_close:
        logging.info(f"Deleting session: {session_id}")
        SESSION_MANAGER.delete_session(session_id)

    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/pull_cpm_file")
async def pull_cpm_file(_: Request):
    logging.info("Pulling daily cpm file.")
    PRICER.update_cachefiles()
    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/chat_message")
async def chat_message(request: Request):
    request_body, request_headers = await parse_request(request)

    message = request_body.get("message")
    connection_id = request_headers.get("connectionid")
    is_public_chat = request_body.get("isPublicChat", False)

    bypass_ip_limit = request_body.get("bypass_ip_limit")
    bypass_ip_token = request_body.get("bypass_ip_token")
    bypass_ip_check = bypass_ip_limit and (bypass_ip_token == IP_LIMIT_BYPASS_TOKEN)

    # If the message is for the public chat, check to make sure hit count is within threshold
    if is_public_chat:
        hits, is_exceeded = has_exceeded_ip_limit(
            request_headers, update_count=not bypass_ip_check
        )

        if is_exceeded and not bypass_ip_check:
            await send_data(
                connection_id=connection_id,
                data=MAX_LIMIT_EVENT,
                session=SESSION,
            )
            return JSONResponse(**FAILURE_RESPONSE)

    session_id = request_body.get("session_id")
    user_id = request_body.get("user_id")
    conversation_id = request_body.get("conversation_id")

    # Create logging stream
    stream_id = f"{user_id}/{conversation_id}"
    CWL_LOGGER.set_stream(stream_id)

    # If we access without an existing session, create one.
    if SESSION_MANAGER.get_session(session_id) is None:
        logging.info("Access without a session. Creating new session.")
        json_response = await new_session(
            request,
            force_session_id=session_id,
            close_socket=False,
            is_public=is_public_chat,
        )
        response_body = json.loads(json_response.body.decode("utf-8"))
        session_id = response_body["session_id"]

    chatbot = SESSION_MANAGER.get_session(session_id).get("chatbot")
    SESSION_MANAGER.update_session_data(session_id, "last_update_time", time.time())

    # API Gateway has a default timeout of 29 seconds if it does not receive a response.
    # Run stream in background and immediately send boilerplate response back to
    # prevent a message timeout and premature websocket close.
    # Any errors in the chain should be communicated through the websocket.
    current_task = asyncio.create_task(
        stream_response(
            chatbot=chatbot,
            message=message,
            connection_id=connection_id,
            session=SESSION,
            mail_api=MAIL_API,
        )
    )

    if is_public_chat:
        # Send current hit count for frontend to display
        await send_data(
            connection_id=connection_id,
            data={"event": {"type": "hits", "data": str(hits)}},
            session=SESSION,
        )

    current_task.add_done_callback(CWL_LOGGER.upload_logs_store)
    SESSION_MANAGER.set_user_active_task(user_id=user_id, task=current_task)

    # Clear logs / contextvar
    CWL_LOGGER.clear_stream(stream_id)

    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/run_carrier_search")
async def run_carrier_search(request: Request):
    request_body, request_headers = await parse_request(request)
    connection_id = request_headers.get("connectionid")
    is_public_chat = request_body.pop("isPublicChat", False)

    # If the message is for the public chat, check to make sure hit count is within threshold
    if is_public_chat:
        hits, is_exceeded = has_exceeded_ip_limit(request_headers)
        if is_exceeded:
            await send_data(
                connection_id=connection_id,
                data=MAX_LIMIT_EVENT,
                session=SESSION,
            )
            return JSONResponse(**FAILURE_RESPONSE)

    # Delete action parameter from request body
    if "action" in request_body:
        del request_body["action"]

    try:
        (results, _, _), _ = carrier_search.run_carrier_search(**request_body)
    except Exception as e:
        # TODO Send email on error
        logging.error(f"Carrier search filter failed with exception {e}")
        await send_data(
            connection_id=connection_id,
            data={
                "event": {"type": "error", "data": "Carrier search filters failed"},
                "message_id": "",
            },
            session=SESSION,
        )
        return JSONResponse(**FAILURE_RESPONSE)

    await send_data(connection_id=connection_id, data=results, session=SESSION)
    await send_close_event(SESSION, connection_id)

    # Send current hit count for frontend to display
    if is_public_chat:
        data = {"event": {"type": "hits", "data": str(hits)}}
        await send_data(connection_id=connection_id, data=data, session=SESSION)

    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/get_hit_count")
async def get_hit_count(request: Request):
    _, request_headers = await parse_request(request)
    connection_id = request_headers.get("connectionid")

    hits, _ = has_exceeded_ip_limit(request_headers, False)

    # Send current hit count
    await send_data(
        connection_id=connection_id,
        data={"event": {"type": "hits", "data": str(hits)}},
        session=SESSION,
    )

    return JSONResponse(**SUCCESS_RESPONSE)

@APP.post("/new_rfp_session")
async def new_rfp_session(request: Request):
    _, request_headers = await parse_request(request)    

    new_session_id = str(uuid.uuid4())
    logging.info(f"Starting new rfp session with id: {new_session_id}")
    connection_id = request_headers.get("connectionid")

    # Send current hit count
    await send_data(
        connection_id=connection_id,
        data={"rfp_session_id": new_session_id},
        session=SESSION,
    )

    return JSONResponse(**SUCCESS_RESPONSE)

@APP.post("/map_and_validate_source_file")
async def map_and_validate_source_file(request: Request):
    async def async_map_and_validate_source_file(request_body: dict) -> None:
        """Asynchronously run the map and validate source file function and send the results over the websocket."""
        nonlocal connection_id
        results = await asyncify(rfp_main.map_and_validate_source_file)(request_body)
        await send_data(connection_id=connection_id, data=results, session=SESSION)
        await send_close_event(SESSION, connection_id)
        logging.info("Mapping endpoint is successfully completed.")

    request_body, request_headers = await parse_request(request)
    connection_id = request_headers.get("connectionid")
    user_id = request_body.get("user_id")
    session_id = request_body.get("rfp_session_id")
    
    # Create logging stream
    stream_id = f"{user_id}/{session_id}"
    CWL_LOGGER.set_stream(stream_id)

    asyncio.create_task(async_map_and_validate_source_file(request_body)).add_done_callback(CWL_LOGGER.upload_logs_store)

    CWL_LOGGER.clear_stream(stream_id)

    return JSONResponse(**SUCCESS_RESPONSE)


@APP.post("/price_validated_file")
async def price_validated_file(request: Request):
    async def async_price_validated_file(request_body: dict) -> None:
        """Asynchronously run the price validated file function and send the results over the websocket."""
        nonlocal connection_id
        results = await asyncify(rfp_main.price_validated_file)(request_body)
        await send_data(connection_id=connection_id, data=results, session=SESSION)
        await send_close_event(SESSION, connection_id)
        logging.info("Pricing endpoint is successfully completed.")

    request_body, request_headers = await parse_request(request)
    connection_id = request_headers.get("connectionid")
    user_id = request_body.get("user_id")
    session_id = request_body.get("rfp_session_id")
    
    # Create logging stream
    stream_id = f"{user_id}/{session_id}"
    CWL_LOGGER.set_stream(stream_id)

    asyncio.create_task(async_price_validated_file(request_body)).add_done_callback(CWL_LOGGER.upload_logs_store)

    CWL_LOGGER.clear_stream(stream_id)

    return JSONResponse(**SUCCESS_RESPONSE)


# API Gateway has a default timeout of 29 seconds if it does not receive a response.
# But the websocket connection is still open after that for around 10 minutes.
# So we do not need to worry about the timeout for the above endpoints.
# But in the future if we have an endpoint for which the response time is more than the lifetime of the websocket connection,
# we can use the below endpoint to refresh the connection by creating a new websocket and sending its connection id in the request body.
# @APP.post("/refresh_connection")
# async def refresh_connection(request: Request):
#     request_body, request_headers = await parse_request(request)
#     connection_id = request_headers.get("connectionid")
#     rfp_request_id = request_body.get("RFPRequestId")
#     if rfp_request_id not in ACTIVE_CONNECTION_IDS:
#         return JSONResponse(**FAILURE_RESPONSE)
#     ACTIVE_CONNECTION_IDS[rfp_request_id] = connection_id
#     print("Active connection ids (refresh connection): ", ACTIVE_CONNECTION_IDS)
#     return JSONResponse(**SUCCESS_RESPONSE)


def main():
    uvicorn.run(
        "uvicorn_handler:APP",
        host="0.0.0.0",
        port=(8081 if os.getenv("ENV") == "DEV" else 8080),
    )


if __name__ == "__main__":
    main()
