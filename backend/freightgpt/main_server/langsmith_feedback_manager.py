import base64
import datetime
import logging
import os
import re
import sys
import traceback
from enum import Enum
from typing import Dict
from typing import List
from typing import Optional

import langsmith
import pandas as pd
import requests
import urllib3

from backend.freightgpt.constants import FEEDBACK_EMAIL_LIST
from common import google_sheets_utils
from common import init_main
from common import mail
from common.cognito_utils import CognitoUserData
from common.cognito_utils import FREIGHTGPT_DEV_COGNITO_USER_POOL_ID
from common.cognito_utils import FREIGHTGPT_PROD_COGNITO_USER_POOL_ID
from machine_learning.freightgpt.constants import LOG_TEMPLATE_URL

PROJECT_ROOT = init_main.initialize()
# https://docs.google.com/spreadsheets/d/1nVM5ZCoMiiT5AX3hxHNQTFhZKxj67K3Q7yBHhSluXRw/edit#gid=0
FEEDBACK_SHEET_ID = "1nVM5ZCoMiiT5AX3hxHNQTFhZKxj67K3Q7yBHhSluXRw"
FEEDBACK_SHEET_NAME = "User Feedback"


class FeedbackType(Enum):
    """An enumeration representing the type of feedback.

    Attributes:
        POSITIVE (str): The positive feedback type represented by "thumbs_up".
        NEGATIVE (str): The negative feedback type represented by "thumbs_down".
    """

    POSITIVE = "thumbs_up"
    NEGATIVE = "thumbs_down"


class LangsmithFeedbackClient(langsmith.Client):
    """A client for providing feedback using the Langsmith API.

    This client allows users to provide feedback on a certain topic using the Langsmith API.
    It supports positive and negative feedback.

    Args:
        langchain_project: the langchain project to submit feedback to.
        api_url (str, optional): The URL of the Langsmith API. Defaults to None.
        api_key (str, optional): The API key for accessing the Langsmith API. Defaults to None.
        retry_config (Retry, optional): The retry configuration for API requests. Defaults to None.
        timeout_ms (int, optional): The timeout in milliseconds for API requests. Defaults to None.
        web_url (str, optional): The URL of the web interface for the Langsmith API. Defaults to None.
        session (requests.Session, optional): The session object to use for API requests. Defaults to None.
    """

    def __init__(
        self,
        langchain_project: str = None,
        api_url: Optional[str] = None,
        *,
        api_key: Optional[str] = None,
        retry_config: Optional[urllib3.util.Retry] = None,
        timeout_ms: Optional[int] = None,
        web_url: Optional[str] = None,
        session: Optional[requests.Session] = None,
        mail_api: Optional[mail.MailAPI] = None,
        cognito_user_data: Optional[CognitoUserData] = None,
    ) -> None:
        super().__init__(
            api_url,
            api_key=api_key,
            retry_config=retry_config,
            timeout_ms=timeout_ms,
            web_url=web_url,
            session=session,
        )

        # Ensure all environment variables are set.
        if os.getenv("LANGCHAIN_TRACING_V2") is None and not os.getenv("DRY_MODE"):
            raise OSError("LANGCHAIN_TRACING_V2 environment variable is not set.")
        if os.getenv("LANGCHAIN_ENDPOINT") is None:
            raise OSError("LANGCHAIN_ENDPOINT environment variable is not set.")
        if os.getenv("LANGCHAIN_API_KEY") is None:
            raise OSError("LANGCHAIN_API_KEY environment variable is not set.")

        # Override langchain project from parameter
        if langchain_project:
            os.environ["LANGCHAIN_PROJECT"] = langchain_project

        # Regardless of override, ensure it is set.
        if os.getenv("LANGCHAIN_PROJECT") is None:
            raise OSError("LANGCHAIN_PROJECT environment variable is not set.")

        self._feedback = ""
        self._comment = ""
        self.mail_api = mail_api if mail_api else mail.MailAPI(gmail=False)

        # Instantiate Cognito user data if not provided
        if not cognito_user_data:
            user_pool_id = (
                FREIGHTGPT_DEV_COGNITO_USER_POOL_ID
                if os.getenv("ENV") == "DEV"
                else FREIGHTGPT_PROD_COGNITO_USER_POOL_ID
            )
            cognito_user_data = CognitoUserData(
                user_pool_id=user_pool_id,
                is_dev_env=os.getenv("ENV") == "DEV",
                mail_api=self.mail_api,
            )
        self.cognito_user_data = cognito_user_data

    @property
    def comment(self):
        return self._comment

    @property
    def feedback(self):
        return self._feedback

    @comment.setter
    def comment(self, comment: str):
        "Sets the comment value."
        self._comment = self.sanitize(comment)

    @feedback.setter
    def feedback(self, value: str):
        """Set the feedback value.

        Args:
            value (str): The feedback value to set.

        Raises:
            ValueError: If the provided value is not one of the allowed feedback values.
        """
        value = self.sanitize(value)
        if not isinstance(value, str) or value == "":
            raise ValueError("Improper value submitted.")

        if FeedbackType.POSITIVE.value in value.lower():
            self._feedback = FeedbackType.POSITIVE.value
        elif FeedbackType.NEGATIVE.value in value.lower():
            self._feedback = FeedbackType.NEGATIVE.value
        else:
            raise ValueError(
                "Feedback submitted is not positive or negative. Feedback should be either thumbs_up or thumbs_down."
            )

    def is_feedback_positive(self) -> bool:
        """Check if the feedback is positive.

        Returns:
            bool: True if the feedback is positive, False otherwise.
        """
        return self.feedback == FeedbackType.POSITIVE.value

    def sanitize(self, value: str) -> str:
        """
        Sanitizes a given string by performing several cleaning operations.

        This function performs the following operations on the input string:
            1. Normalizes whitespace by replacing multiple spaces, tabs, and newlines with a single space.
            2. Removes any non-ASCII characters. This step is optional and can be adjusted based on specific requirements.
            3. Removes HTML tags and their content. This is done to prevent potential script injection attacks.

        Parameters:
            value (str): The string to be sanitized.

        Returns:
            str: The sanitized string with whitespace normalized, non-ASCII characters removed, and HTML tags and their content eliminated.
        """
        if value is None:
            return ""

        # Normalize whitespace: replace multiple spaces/tabs/newlines with a single space
        value = re.sub(r"\s+", " ", value).strip()

        # Remove any non-ASCII characters (optional, depends on your requirement)
        value = value.encode("ascii", "ignore").decode()

        # Remove HTML tags and everything inside (potential script injection)
        clean_tags = re.compile("<.*?>.*?</.*?>")
        value = re.sub(clean_tags, "", value)

        return value

    def send_feedback_email(
        self,
        user_id: str,
        conversation_id: str,
        run_id: str,
        human_query: str,
        ai_response: str,
        flags: List[str],
    ) -> None:
        """
        Sends a feedback email with details of a user's interaction with the AI, including
        the AI's query and response, as well as a link to the relevant CloudWatch logs.

        This function constructs a URL to the specific CloudWatch log stream based on the
        user ID and conversation ID, which are URL-encoded to handle special characters.
        All parameters are optional - feedback and comment if set will always be sent.

        Parameters:
        - user_id (str): The unique identifier of the user.
        - conversation_id (str): The unique identifier of the conversation.
        - run_id (str): The run id of the interaction.
        - human_query (str): The query that was sent to the AI.
        - ai_response (str): The AI's response to the query.
        """

        if os.getenv("ENV") == "TEST":
            return

        # Construct cloudwatch URL
        log_url = LOG_TEMPLATE_URL.format(
            log_group="freightgpt-" + os.getenv("ENV").lower(),
            user_id=user_id,
            conversation_id=conversation_id,
        )

        # Dev subject
        feedback_datetime_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        user_attributes = self.cognito_user_data.get_cognito_user_data(user_id)
        if user_attributes:
            user_name = user_attributes["name"] if "name" in user_attributes else ""
            user_email = user_attributes["email"] if "email" in user_attributes else ""
        else:
            user_name = ""
            user_email = ""

        try:
            langsmith_url = self.read_run(run_id).url
        except Exception as e:
            logging.error("Unable to retrieve Langsmith URL due to error:\n%s", e)
            langsmith_url = ""
        # Send feedback email
        self.mail_api.send_email_aws(
            frm="<EMAIL>",
            to=FEEDBACK_EMAIL_LIST,
            subject=f"[FreightGPT][Response Feedback Submitted][{self.feedback}][{os.getenv('ENV').title()}] - {feedback_datetime_str}",
            body=f"""
                You have received feedback! Feedback sheet: https://docs.google.com/spreadsheets/d/1nVM5ZCoMiiT5AX3hxHNQTFhZKxj67K3Q7yBHhSluXRw/edit#gid=0

                Human Query: {human_query}
                AI Response Message: {ai_response}
                User Name: {user_name}
                User Email: {user_email}
                User/Convo Id: {user_id}/{conversation_id}
                Run Id: {run_id}
                Feedback: {self.feedback}
                Comment: {self.comment}
                Flags: {flags}
                Langsmith URL: {langsmith_url}
                Log URL: {log_url}
            """,
            attachments=[],
        )

        sheet_upload_dict = {
            "User Query": human_query,
            "AI Response": ai_response,
            "Feedback": self.feedback,
            "Comment": self.comment,
            "User Name": user_name,
            "User Email": user_email,
            "User ID": user_id,
            "Convo ID": conversation_id,
            "Log URL": log_url,
            "Langsmith URL": langsmith_url,
            "Feedback Time": feedback_datetime_str,
            "Environment": os.getenv("ENV").title(),
            "Flags": flags,
        }
        sheet_upload_dict = {k: (v if v else "") for k, v in sheet_upload_dict.items()}
        self.append_feedback_to_sheet(sheet_upload_dict, feedback_datetime_str, log_url)

    def append_feedback_to_sheet(
        self,
        sheet_upload_dict: Dict[str, str],
        feedback_datetime_str: str,
        log_url: str,
    ) -> None:
        """
        Appends feedback to the feedback sheet, sending an email if the upload fails.

        https://docs.google.com/spreadsheets/d/1nVM5ZCoMiiT5AX3hxHNQTFhZKxj67K3Q7yBHhSluXRw/edit#gid=0
        """
        try:
            gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
            gsheets_utils.append_df_to_sheet(
                pd.DataFrame(sheet_upload_dict, index=[0]),
                FEEDBACK_SHEET_ID,
                FEEDBACK_SHEET_NAME,
            )
            # Fix row heights
            gsheets_utils.set_row_height(
                FEEDBACK_SHEET_ID, 0, 0, 10000
            )  # If we have >10k rows, move to a new system
        except Exception as e:
            logging.error("Unable to update sheet due to error:\n%s", e)
            self.mail_api.send_email_aws(
                frm="<EMAIL>",
                to=FEEDBACK_EMAIL_LIST,
                subject=f"[FreightGPT][Feedback Sheet Error][{self.feedback}]{feedback_datetime_str}",
                body=f'Feedback sheets upload failed with severe error "{type(e).__name__}": {e}Error:\n\ttype: "{sys.exc_info()[0]}"\n\tvalue: "{sys.exc_info()[1]}"\n\ttraceback:\n{traceback.format_exc()}\nCheck log: {log_url}.',
            )


####################################################################################################
#                            Script for uploading feedback emails to sheet                         #
####################################################################################################


def extract_dict_from_mail(
    mail_body: str, mail_header: str, client: langsmith.Client
) -> Dict[str, str]:
    """
    Extracts key information from an email's body and header.

    Args:
        mail_body (str): The body of the email.
        mail_header (str): The header of the email.

    Returns:
        Dict[str, str]: A dictionary containing extracted information such as user query, AI response, and feedback details.
    """
    # Adjusting patterns to correctly capture text between specified fields
    user_query_pattern = re.compile(
        r"Human Query:\s*(.*?)\s*(?:AI Response Message:|$)", re.DOTALL
    )
    ai_response_pattern = re.compile(
        r"AI Response Message:\s*(.*?)\s*(?:User/Convo Id:|$)", re.DOTALL
    )
    comment_pattern = re.compile(r"Comment:\s*(.*?)\s*(?:Log URL:|$)", re.DOTALL)

    # Extracting data using the patterns
    user_query_match = user_query_pattern.search(mail_body)
    user_query = user_query_match.group(1).strip() if user_query_match else ""

    ai_response_match = ai_response_pattern.search(mail_body)
    ai_response = ai_response_match.group(1).strip() if ai_response_match else ""

    comment_match = comment_pattern.search(mail_body)
    comment = comment_match.group(1).strip() if comment_match else ""

    run_id_match = re.search(r"Run Id:\s*(.*?)\s*(?:Feedback:|$)", mail_body)
    run_id = run_id_match.group(1).strip() if run_id_match else ""
    try:
        langsmith_url = client.read_run(run_id).url if run_id else ""
    except Exception as e:
        logging.error("Unable to retrieve Langsmith URL due to error:\n%s", e)
        langsmith_url = ""

    # Extracting other fields
    feedback_match = re.search(r"Feedback:\s*(.*?)\s*(?:Comment:|$)", mail_body)
    feedback = feedback_match.group(1).strip() if feedback_match else ""

    user_convo_id_match = re.search(
        r"User/Convo Id:\s*(.*?)\s*(?:Run Id:|$)", mail_body
    )
    user_id, convo_id = (
        user_convo_id_match.group(1).strip().split("/")
        if user_convo_id_match
        else ["", ""]
    )

    log_url_match = re.search(r"Log URL:\s*(.*)", mail_body)
    log_url = log_url_match.group(1).strip() if log_url_match else ""

    is_dev_env = "dev" in mail_header.lower().strip()
    datetime_pattern = r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}"
    matched_datetime = re.search(datetime_pattern, mail_header)
    datetime_str = matched_datetime.group() if matched_datetime else ""

    # Instantiate Cognito user data
    cognito_user_data = CognitoUserData(is_dev_env=os.getenv("ENV") == "DEV")

    user_attributes = cognito_user_data.get_cognito_user_data(user_id)
    if user_attributes:
        user_name = user_attributes["name"] if "name" in user_attributes else ""
        user_email = user_attributes["email"] if "email" in user_attributes else ""
    else:
        user_name = ""
        user_email = ""

    return_dict = {
        "User Query": user_query,
        "AI Response": ai_response,
        "Feedback": feedback,
        "Comment": comment,
        "User Name": user_name,
        "User Email": user_email,
        "User ID": user_id,
        "Convo ID": convo_id,
        "Log URL": log_url,
        "Langsmith URL": langsmith_url,
        "Feedback Time": datetime_str,
        "Environment": "Dev" if is_dev_env else "Prod",
    }
    return {k: (v if v else "") for k, v in return_dict.items()}


####################################################################################################
#                            Script for uploading feedback emails to sheet                         #
####################################################################################################


def process_email_messages(
    mail_api: mail.MailAPI, filter_str: str
) -> List[Dict[str, str]]:
    """
    Processes email messages based on a filter string and extracts relevant information.

    Args:
        mail_api: An instance of the MailAPI class.
        filter_str (str): A filter string to search for specific email messages.

    Returns:
        List[Dict[str, str]]: A list of dictionaries, each containing data extracted from an email.
    """
    client = langsmith.Client()
    processed_data = []
    results = mail_api.search_messages(filter_str)
    for result in reversed(results):
        message = (
            mail_api.gmail_service.users()
            .messages()
            .get(userId="me", id=result["id"])
            .execute()
        )
        message_body = base64.urlsafe_b64decode(
            message["payload"]["body"]["data"]
        ).decode("utf-8")
        headers = message["payload"]["headers"]
        subject = [i["value"] for i in headers if i["name"] == "subject"][0]

        new_row = extract_dict_from_mail(message_body, subject, client)
        processed_data.append(new_row)
        print(subject)
    return processed_data


def update_feedback_sheet(
    gsheets_utils: google_sheets_utils.GoogleSheetsUtils, feedback_df: pd.DataFrame
) -> None:
    """
    Updates the feedback sheet with new data.

    Args:
        gsheets_utils: An instance of the GoogleSheetsUtils class.
        feedback_df (pd.DataFrame): A DataFrame containing feedback data to be uploaded.

    """

    try:
        feedback_df = feedback_df.drop_duplicates(
            subset=["User Query", "AI Response", "Log URL", "Feedback Time"],
            keep="last",
        )
        feedback_df = feedback_df.sort_values(by=["Feedback Time"], ascending=True)
        feedback_df = feedback_df.replace(pd.NA, "")
        gsheets_utils.write_df_to_sheet(
            feedback_df, FEEDBACK_SHEET_ID, FEEDBACK_SHEET_NAME, skiprows=1
        )
        gsheets_utils.set_row_height(
            FEEDBACK_SHEET_ID, 0, 0, 1000
        )  # Adjust these parameters as needed
    except Exception as e:
        logging.error("Unable to update sheet due to error:\n%s", e)
        import IPython

        IPython.embed()
        raise


def upload_feedback_emails_to_sheet() -> None:
    """
    Uploads feedback emails to a Google Sheet.

    This function retrieves emails based on a specific filter, processes them to extract relevant data,
    and then uploads this data to a specified Google Sheet.
    """

    gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
    mail_api = mail.MailAPI(
        creds_path=os.path.join(PROJECT_ROOT, "common", "roop_gmail_token.pickle")
    )

    feedback_df = gsheets_utils.load_sheet_into_dataframe(
        FEEDBACK_SHEET_ID, FEEDBACK_SHEET_NAME, 1
    )

    filter_str = (
        "subject:[FreightGPT][Response Feedback Submitted] from:<EMAIL>"
    )
    email_data = process_email_messages(mail_api, filter_str)

    for new_row in email_data:
        feedback_df = pd.concat(
            [feedback_df, pd.DataFrame(new_row, index=[0])], ignore_index=True
        )

    update_feedback_sheet(gsheets_utils, feedback_df)


def main():
    upload_feedback_emails_to_sheet()


if __name__ == "__main__":
    main()

# Usage:
# python machine_learning/handlers/langsmith_feedback_manager.py
