module.exports = {
    apps: [
        {
            name: 'Load<PERSON>alancer',
            script: 'ts-node',
            args: './load_balancer.ts',
            interpreter: 'none',
            watch: false, // Enable watch in development
            env: {
                NODE_ENV: 'development',
                TASK_PORT: 8081
            },
            env_production: {
                NODE_ENV: 'production',
                TASK_PORT: 8080
            }
        },
        {
            name: 'TaskMonitor',
            script: 'ts-node',
            args: './task_monitor.ts',
            interpreter: 'none',
            watch: false, // Enable watch in development
            env: {
                NODE_ENV: 'development',
                SERVICE_NAME: 'freightgpt-service-alb-dev',
                TASK_PORT: 8081
            },
            env_production: {
                NODE_ENV: 'production',
                SERVICE_NAME: 'freightgpt-service-alb',
                TASK_PORT: 8080
            }
        }
    ]
};
