import * as http from 'http';
import * as httpProxy from 'http-proxy';
import * as fs from 'fs';
import { COOKIE_TTL_SECONDS } from './load_balancer_constants';
import {
    readServersFromFile,
    hashString,
    mapHashToServer,
    formatServerIp,
    createCookie,
    getEnvValue
} from './utils';

// Read servers from file.
let SERVERS: string[] = [];
readServersFromFile('servers.json').then((servers: string[]) => {
    SERVERS = servers;
});

// Get port
const PORT = getEnvValue('TASK_PORT', 8081);

// Watch the file for changes
fs.watch('servers.json', async (event: string, filename: string) => {
    if (filename && event === 'change') {
        console.log('Server config file changed. Updating server list...');
        SERVERS = await readServersFromFile(filename);
        console.log(`New servers: ${SERVERS}`);
    }
});

// Create a proxy server with custom application logic
const proxy: httpProxy = httpProxy.createProxyServer({});

// On proxy response, log status.
proxy.on(
    'proxyRes',
    (
        proxyRes: http.IncomingMessage,
        req: http.IncomingHttpHeaders,
        res: http.ServerResponse
    ) => {
        console.log('Received proxy response', proxyRes.statusCode);
    }
);

// Before proxying request to server, add cookie to headers.
proxy.on(
    'proxyReq',
    (
        proxyReq: http.ClientRequest,
        req: http.IncomingMessage,
        res: http.ServerResponse,
        options: httpProxy.ServerOptions
    ) => {
        if (options.cookie && !proxyReq.getHeader('cookie')) {
            console.log(`Setting cookie in header ${options.cookie}`);
            proxyReq.setHeader('cookie', options.cookie);
        }
        console.log('Proxying request!');
    }
);

/**
 * Proxies an incoming HTTP request to a specified server.
 *
 * This function is responsible for forwarding an incoming HTTP request to another server.
 * HTTP is a streamed protocol, so we cannot buffer.
 *
 * @param {http.IncomingMessage} req - The incoming HTTP request to be proxied.
 * @param {http.ServerResponse} res - The HTTP response object to send back the response.
 * @param {string} server - The server IP to which the request should be proxied.
 * @param {string} [cookie] - Optional. A cookie to be used in the proxy request.
 */
const proxyRequest = (
    req: http.IncomingMessage,
    res: http.ServerResponse,
    server: string,
    cookie?: string
): void => {
    // Call proxy.web here with the determined server
    const target = `${formatServerIp(server, PORT)}`;
    console.log(`Proxying request to ${target}`);

    // Proxy request
    proxy.web(req, res, { target: target, cookie: cookie }, (error: Error) => {
        console.error(`Failed to proxy request ${error}`);
    });
};

/**
 * Extracts the ALB hash from a cookie string.
 *
 * This function parses a cookie string to find and return the value associated with
 * the 'ALB' cookie name. The cookie string is split into individual cookies, and each
 * cookie is further split into its name and value.
 *
 * Example:
 * "...; ALB=asdfasdfas; ..." returns asdfasdfas.
 *
 * @param {string} cookieString - The string containing the cookie data.
 * @returns {string | null} The value of the 'ALB' cookie if found, otherwise null.
 */
const getALBHash = (cookieString: string): string | null => {
    const cookies: string[] = cookieString.split(';');
    for (let i = 0; i < cookies.length; i++) {
        const cookiePair: string[] = cookies[i].trim().split('=');
        if (cookiePair[0] === 'ALB') {
            return cookiePair[1];
        }
    }
    return null;
};

// Creates the proxy server.
const server = http.createServer(
    (req: http.IncomingMessage, res: http.ServerResponse) => {
        console.log('Request Headers:', req.headers);
        console.log('Available Servers:', SERVERS);
        console.log('Request URL', req.url);

        let cookie: any;
        let hash: string;

        // If cookie is in headers, simply access for the hash.
        if ('cookie' in req.headers) {
            cookie = req.headers['cookie'];
            hash = getALBHash(cookie);
        }
        // If no cookie in headers (it has expired), create one.
        else {
            // Ensuring connectionid is a string before passing it to hashString
            const connectionId: string =
                typeof req.headers.connectionid === 'string'
                    ? req.headers.connectionid
                    : '';

            // Hash on connection id - will be most random attribute.
            hash = hashString(connectionId);
            cookie = createCookie('ALB', hash, COOKIE_TTL_SECONDS);
        }

        // Select server. If no hash, random node.
        const serverTarget: string =
            hash != null
                ? mapHashToServer(hash, SERVERS)
                : SERVERS[Math.floor(Math.random() * SERVERS.length)];

        proxyRequest(req, res, serverTarget, cookie);
    }
);

console.log(`Listening on port ${PORT}`);
server.listen(PORT);
