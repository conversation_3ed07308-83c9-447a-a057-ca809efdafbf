{"name": "load_balancer", "version": "1.0.0", "description": "", "main": "load_balancer.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@types/node": "^20.10.7", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "arg": "^5.0.2", "aws-sdk": "^2.1532.0", "axios": "^1.6.5", "body-parser": "^1.20.2", "crypto": "^1.0.1", "express": "^4.18.2", "http-proxy": "^1.18.1", "pm2": "^5.3.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "eslintConfig": {"root": true, "env": {"node": true, "es6": true}, "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"no-unused-vars": "off"}}}