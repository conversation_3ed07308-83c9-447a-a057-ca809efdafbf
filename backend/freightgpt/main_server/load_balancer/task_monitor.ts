import * as AWS from 'aws-sdk';
import type { ECS } from 'aws-sdk';
import type {
    DescribeTasksRequest,
    DescribeTasksResponse,
    ListTasksRequest,
    ListTasksResponse
} from 'aws-sdk/clients/ecs';
import {
    CLUSTER_NAME,
    HEALTH_CHECK_INTERVAL_MS,
    HEALTH_CHECK_REQUEST_TIMEOUT,
    SERVER_SCAN_INTERVAL_MS
} from './load_balancer_constants';
import { writeFile } from 'fs/promises';
import {
    readServersFromFile,
    formatServerIp,
    getEnvValue,
    deepEqualArrays
} from './utils';
import axios from 'axios';

// Configure AWS region
AWS.config.update({ region: 'us-east-1' });

// Create an ECS service object
const ecs: ECS = new AWS.ECS({ apiVersion: '2014-11-13' });

// Task monitor mode
let SCAN = false;
let CURRENT_INTERVAL: NodeJS.Timeout | undefined;

// Service name and port
const PORT = getEnvValue('TASK_PORT', 8081);
const SERVICE_NAME = getEnvValue('SERVICE_NAME', 'freightgpt-service-alb-dev');

// Read servers from file.
let SERVERS: string[] = [];
readServersFromFile('servers.json').then((servers: string[]) => {
    SERVERS = servers;
});

/**
 * Gets private IPs of all input task ARNs.
 *
 * @param {string} [name='taskArns'] - List of task ARNs to get private ip addresses.
 * @returns {Promise<string[]>} list of private IPs for each task in a promise.
 */
const getECSTaskIPs = async (taskArns: string[]): Promise<string[]> => {
    const describeTasksParams: DescribeTasksRequest = {
        cluster: CLUSTER_NAME,
        tasks: taskArns
    };

    try {
        // Describe all ECS task ARNs.
        const data: DescribeTasksResponse = await ecs
            .describeTasks(describeTasksParams)
            .promise();
        const privateIPs: string[] = [];

        // Iterate over each task ARN to get the IP.
        data.tasks.forEach((task) => {
            // Perform null checking
            const attachment = task.attachments?.[0];
            const privateIPDetail = attachment?.details?.find(
                (detail) => detail.name === 'privateIPv4Address'
            );
            const privateIP: string | undefined = privateIPDetail?.value;

            // Add IP
            if (privateIP) {
                console.log(`Task ${task.taskArn} - Private IP: ${privateIP}`);
                console.log(`Task status: ${task.lastStatus}`);
                if (task.lastStatus === 'RUNNING') {
                    privateIPs.push(privateIP);
                }
            }
        });

        return privateIPs;
    } catch (err) {
        console.error('Error describing tasks', err);
        throw err; // Re-throw to allow caller to handle the error
    }
};

/**
 * Gets all private IPs associated with a given service.
 *
 * @returns {Promise<string[]>} list of private IPs for a service in a promise.
 */
const getActiveECSServiceIPs = async (): Promise<string[]> => {
    const listTasksParams: ListTasksRequest = {
        cluster: CLUSTER_NAME,
        serviceName: SERVICE_NAME
    };

    try {
        // List all tasks in the service
        const tasksResponse: ListTasksResponse = await ecs
            .listTasks(listTasksParams)
            .promise();

        // Get private IPs
        if (tasksResponse.taskArns && tasksResponse.taskArns.length > 0) {
            const privateIPs: string[] = await getECSTaskIPs(
                tasksResponse.taskArns
            );
            return privateIPs;
        } else {
            console.log('No tasks found for the specified service.');
            return [];
        }
    } catch (err: any) {
        console.error('Error in getting tasks for service.', err);
        throw err;
    }
};

/**
 * Check server health by hitting the /health endpoint of the server.
 *
 * @param {string} [name='server'] - Server IP in format 192.168.X.X
 * @returns {Promise<boolean>} - boolean on whether the server's /health endpoint is reachable.
 */
const checkServerHealth = async (server: string): Promise<boolean> => {
    try {
        const serverUrl = `${formatServerIp(server, PORT)}/health`; // Format URL to /health endpoint
        const response = await axios.get(serverUrl, {
            timeout: HEALTH_CHECK_REQUEST_TIMEOUT
        });
        if (response.status === 200) {
            console.log(`Server ${server} is healthy.`);
        }
        return response.status === 200;
    } catch (error: any) {
        console.error(`Health check failed for ${server}:`, error);
        return false;
    }
};

/**
 * Iterate through all servers and ensure each is healthy. Write healthy servers to file.
 *
 * @param {string[]} [name='servers'] - Array of server IPs in format 192.168.X.X
 * @returns {boolean} - true if there are no healthy servers, indicating a switch to SCAN.
 */
const monitorServers = async (servers: string[]): Promise<boolean> => {
    const taskIPs: string[] = await getActiveECSServiceIPs();
    const healthyServers: string[] = [];

    for (const server of servers) {
        if (taskIPs.includes(server)) {
            console.log(`Checking server health for ${server}`);
            const isHealthy: boolean = await checkServerHealth(server);
            if (isHealthy) {
                healthyServers.push(server);
            }
        } else {
            console.log(`Server ${server} is no longer in status RUNNING`);
        }
    }

    // Update healthy servers
    if (!deepEqualArrays(healthyServers, SERVERS)) {
        SERVERS = healthyServers;
        const data: string = JSON.stringify(healthyServers, null, 2);
        await writeFile('servers.json', data, 'utf8');
    }

    return healthyServers.length === 0 || SERVERS.length !== taskIPs.length; // Returns true if no healthy servers found
};

/**
 * Scan for any healthy servers on the ECS cluster.
 *
 * @returns {boolean} - true if there are no healthy servers, indicating we stay in SCAN.
 */
const scanForHealthyServers = async (): Promise<boolean> => {
    const taskIPs: string[] = await getActiveECSServiceIPs();
    const healthyServers: string[] = [];

    for (const newServer of taskIPs) {
        console.log(`New task detected! ${newServer}`);
        const isHealthy: boolean = await checkServerHealth(newServer);
        if (isHealthy) {
            healthyServers.push(newServer);
        }
    }

    // Update healthy servers
    if (!deepEqualArrays(healthyServers, SERVERS)) {
        SERVERS = healthyServers;
        const data: string = JSON.stringify(healthyServers, null, 2);
        await writeFile('servers.json', data, 'utf8');
    }

    return healthyServers.length === 0; // Returns true if no healthy servers found
};

/**
 * Begin monitoring cluster. Switch between SCAN for healthy servers,
 * if health check fails for all servers, or simply run the health check.
 *
 */
const startMonitoring = async (): Promise<void> => {
    if (CURRENT_INTERVAL) clearInterval(CURRENT_INTERVAL);

    // If SCAN, begin scanning for healthy servers.
    if (SCAN) {
        CURRENT_INTERVAL = setInterval(async () => {
            console.log(
                'Warning, no healthy servers. Scanning for new servers!'
            );
            SCAN = await scanForHealthyServers();
            startMonitoring();
        }, SERVER_SCAN_INTERVAL_MS);
    }
    // Otherwise, run health check against servers.
    else {
        CURRENT_INTERVAL = setInterval(async () => {
            console.log('Running scheduled server health check...');
            const servers: string[] = await readServersFromFile('servers.json');
            SCAN = await monitorServers(servers);
            startMonitoring();
        }, HEALTH_CHECK_INTERVAL_MS);
    }
};

startMonitoring();
