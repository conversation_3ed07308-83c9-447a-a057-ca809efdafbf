import { readFile } from 'fs/promises';
import { createHash } from 'crypto';

/**
 * Checks whether two arrays contain the same elements in any order.
 *
 * This function deeply copies and sorts both arrays before comparing them.
 * It returns true if both arrays contain the same elements, regardless of the order,
 * and false otherwise.
 *
 * Note: This function is not suitable for arrays containing complex structures like
 * objects or other arrays, as it uses a simple comparison and sort method.
 *
 * @param {any[]} arr1 - The first array to be compared.
 * @param {any[]} arr2 - The second array to be compared.
 * @returns {boolean} - True if both arrays contain the same elements in any order, false otherwise.
 */
export const deepEqualArrays = (arr1: any[], arr2: any[]): boolean => {
    if (arr1.length != arr2.length) return false;

    const sortedArr1 = arr1.slice().sort();
    const sortedArr2 = arr2.slice().sort();

    for (let i = 0; i < sortedArr1.length; i++) {
        if (sortedArr1[i] != sortedArr2[i]) return false;
    }

    return true;
};

/**
 * Retrieves the value of a PM2 environment variable or returns a default value if not set.
 *
 * @param {string} key - The name of the environment variable to retrieve.
 * @param {string} defaultValue - The default value to return if the environment variable is not set.
 * @returns {string} The value of the environment variable if set; otherwise, the `defaultValue`.
 */
export const getEnvValue = (key: string, defaultValue: any): string => {
    return process.env[key] !== undefined
        ? (process.env[key] as string)
        : defaultValue;
};

/**
 * Reads JSON servers file and return a list of servers on read and parse success, else [].
 *
 * @param {string} [name='filename'] - filename to servers file.
 * @returns {Promise<any[]>} list of servers.
 */
export const readServersFromFile = async (filename: string): Promise<any[]> => {
    try {
        const data: string = await readFile(filename, 'utf8');
        return JSON.parse(data);
    } catch (error: any) {
        console.error('Error reading servers from file:', error);
        return [];
    }
};

/**
 * Create cryptographic hash (SHA256) of input string.
 *
 * @param {string} [name='str'] - string to hash.
 * @returns {string} SHA256 hash.
 */
export const hashString = (str: string): string => {
    return createHash('sha256').update(str).digest('hex');
};

/**
 * Maps a given hash to a server.
 *
 * Calculates an index by converting the first 8 characters
 * of the hash to an integer (base 16) and then taking the modulus with
 * the length of the servers array.
 *
 * @param {string} hash - The hash string used to determine the server.
 * @param {string[]} servers - An array of server strings.
 * @returns {string} - The selected server from the servers array.
 */
export const mapHashToServer = (hash: string, servers: string[]): any => {
    // Use the first 8 characters, as they are random enough and quick to convert.
    const index: number = parseInt(hash.substring(0, 8), 16) % servers.length;

    return servers[index];
};

/**
 * Formats a server IP address and port into a URL string.
 * Example: 192.168.X.X, 8000 -> http://192.168.X.X:8000
 *
 * @param {string} server - The IP address or hostname of the server.
 * @param {number} port - The port number on which the server is running.
 * @returns {string} - A string representing the formatted URL.
 */
export const formatServerIp = (server: string, port: any): string => {
    return `http://${server}:${port}`;
};

/**
 * Creates a cookie string with a name, value, and optional time-to-live (TTL).
 *
 * This function constructs a cookie string suitable for HTTP headers. It can
 * optionally include a Max-Age attribute, which specifies the cookie's
 * expiration time in seconds.
 *
 * @param {string} cookieName - The name of the cookie.
 * @param {string} cookieValue - The value of the cookie.
 * @param {number} [ttlSeconds] - (Optional) Time-to-live in seconds. If provided,
 *                                the cookie will expire after this duration.
 * @returns {string} - The constructed cookie string.
 */
export const createCookie = (
    cookieName: string,
    cookieValue: string,
    ttlSeconds?: number
): string => {
    const expiration: string = ttlSeconds ? `; Max-Age=${ttlSeconds}` : '';
    return `${cookieName}=${cookieValue}${expiration}`;
};
