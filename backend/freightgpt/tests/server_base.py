import sys
from typing import Any
from typing import Dict
from typing import List
from typing import <PERSON><PERSON>
from unittest import mock

import fastapi

from backend.freightgpt.tests.constants import CONVO_TABLE
from backend.freightgpt.tests.constants import DEFAULT_USER
from backend.freightgpt.tests.constants import DRY_IMPORT_ARGS
from backend.freightgpt.tests.constants import EVENTS_TABLE
from backend.freightgpt.tests.constants import IntegrationTestModes
from backend.freightgpt.tests.constants import SESSIONS_TABLE
from backend.freightgpt.tests.constants import WET_IMPORT_ARGS
from backend.freightgpt.tests.resource_utils import DynamoResourceManager
from backend.freightgpt.tests.resource_utils import LangsmithResourceManager
from backend.freightgpt.tests.resource_utils import ValidDynamoDBEntry
from backend.freightgpt.tests.test_utils import construct_message_body
from backend.freightgpt.tests.test_utils import is_valid_uuid
from backend.freightgpt.tests.test_utils import validate_event_response
from backend.freightgpt.tests.test_utils import validate_token_response
from backend.freightgpt.tests.test_utils import websocket_post_request


def track_dynamo_resources(
    dynamo_resource_manager: DynamoResourceManager,
    session_id: str,
    conversation_id: str,
) -> None:
    """
    Track the DynamoDB resources created for a session and conversation.
    Tracks entries in the three relevant tables: sessions, events, and convos.

    Args:
        dynamo_resource_manager (DynamoResourceManager): An instance of the DynamoResourceManager class.
        session_id (str): The ID of the session.
        conversation_id (str): The ID of the conversation.
    """
    dynamo_resource_manager.add_created_resource(
        ValidDynamoDBEntry(SESSIONS_TABLE, pk_value=DEFAULT_USER, sk_value=session_id)
    )
    dynamo_resource_manager.add_created_resource(
        ValidDynamoDBEntry(EVENTS_TABLE, pk_value=conversation_id)
    )
    dynamo_resource_manager.add_created_resource(
        ValidDynamoDBEntry(CONVO_TABLE, pk_value=DEFAULT_USER, sk_value=conversation_id)
    )


class UvicornBaseTest:
    """
    A base test class for running integration tests against the fastapi app uvicorn_handler.
    The test mode can be 'dry' (work with no cloud modifications) or 'wet' (create resources).

    Contains the following methods for tests:
        - new_session: opens a new session on server similar to a user.
        - query: runs a human message through the server similar to a user.

    """

    def setup_method(self, method, mode: str = "dry"):
        """
        Set up the method for a test case.

        Args:
            method (str): The name of the method being set up.
            mode (str, optional): The mode of the test. Defaults to 'dry'.
        """

        assert any(mode == item.value for item in IntegrationTestModes)
        self.mode = mode

        # Import fastapi app, mock sys args for what the app takes.
        mock_args = DRY_IMPORT_ARGS if self.mode != "wet" else WET_IMPORT_ARGS
        with mock.patch.object(
            sys,
            "argv",
            mock_args,
        ):
            from backend.freightgpt.main_server.uvicorn_handler import APP

            self.app = APP

    async def new_session(
        self, session_ids_only: bool = False
    ) -> Tuple[str, str] | Tuple[
        "fastapi.status.HTTP_200_OK", List[Dict], List[Dict], Tuple[str, str]
    ]:
        """
        Create a new session on the running server, i.e. a fresh langchain agent.

        Args:
            session_ids_only (bool): If True, only return the session_id and conversation_id values.
                                    Default is False.

        Returns:
            tuple: A tuple containing the server_response, tokens, events, and the session_id
                and conversation_id (when session_ids_only is False).

        Raises:
            AssertionError: If the length of events is not equal to 1.
            AssertionError: If the session_id or conversation_id is not a valid uuid.
            AssertionError: If the name of the event is not 'session_status' or the status is not 'success'.
        """

        # Make websocket call to new session.
        server_response, tokens, events = await websocket_post_request(
            self.app,
            "/new_session",
            data={"user_id": DEFAULT_USER},
        )

        # Check event properties of the new session event.
        assert len(events) == 1
        event_data = events[0]["event"]

        session_id = event_data["session_id"]
        conversation_id = event_data["conversation_id"]
        assert is_valid_uuid(session_id)
        assert is_valid_uuid(conversation_id)
        assert events[0]["event"]["name"] == "session_status"
        assert events[0]["event"]["status"].lower() == "success"

        # If session_ids_only, only return the session id and the conversation id.
        if session_ids_only:
            return event_data["session_id"], event_data["conversation_id"]

        server_response = server_response.json()
        assert server_response["status"].lower() == "success"
        return server_response, tokens, events, (session_id, conversation_id)

    async def query(
        self,
        query: str,
        endpoint: str = "/chat_message",
        append_query_data: Dict[str, Any] = {},
        overwrite_query_data: Dict[str, Any] = {},
        events_structure: List = [],
        validate_tokens: bool = True,
        validate_events: bool = True,
        dynamo_resources: DynamoResourceManager = None,
        langsmith_resources: LangsmithResourceManager = None,
        session: Tuple[str, str] = None,
        check_feedback: bool = False,
    ):
        """
        Query a chat message to the server.

        Args:
            query (str): The text of the human query / message to try.
            endpoint (str, optional): The APP endpoint to send the query to. Default is '/chat_message'.
            append_query_data (dict, optional): Additional query data to append to the message body, in the case of other endpoints.
            overwrite_query_data (dict, optional): Query data to overwrite the message body entirely, if required.
            events_structure (list, optional): A list specifying the expected structure of the returned event data.
            validate_tokens (bool, optional): Whether to validate the token response. Default is True.
            validate_events (bool, optional): Whether to validate the event response. Default is True.
            dynamo_resources (object, optional): DynamoResourceTracker instance to track created DynamoDB resources. Default is None.
            langsmith_resources (object, optional): LangsmithResourceTracker instance to track new Langsmith runs. Default is None.
            session (tuple, optional): A tuple containing session_id and conversation_id to be used as the current session (i.e. if one is already active). Default is None.
            check_feedback (bool, optional): Whether to check for feedback in the Langsmith resource tracker when verifying the run. Default is False.

        Returns:
            tuple: A tuple containing the following elements:
                tokens (str): The token response.
                events (list): The event response.
                session (tuple): A tuple containing session_id and conversation_id.
        """

        # If a session is not provided, request one. Otherwise, use the provided session and convo id.
        if not session:
            session_id, conversation_id = await self.new_session(session_ids_only=True)

            # If in wet mode, track the created dynamo resources.
            if self.mode == "wet" and dynamo_resources:
                track_dynamo_resources(dynamo_resources, session_id, conversation_id)
        else:
            session_id, conversation_id = session

        # Send the human message to the server.
        # Default will be a regular query to chat_message. Data can be appended to the object or it can be overwritten entirely.
        query_data = construct_message_body(session_id, conversation_id, query)
        if overwrite_query_data:
            query_data = overwrite_query_data
        elif append_query_data:
            query_data = {**query_data, **append_query_data}

        # APP request with query.
        _, tokens, events = await websocket_post_request(
            self.app,
            endpoint,
            data=query_data,
        )

        print(f"------------------------ EVENTS {query} ------------------------")
        print("Events", events)
        print(f"Events length: {len(events)}")
        for event in events:
            print("Received event type", event["event"]["type"])

        # Validate token response.
        if validate_tokens:
            validate_token_response(tokens)

        # Validate received events.
        if validate_events and events_structure:
            validate_event_response(events, events_structure)

        # If wet mode, a langsmith run will be created. Track the run.
        if (
            self.mode == "wet"
            and langsmith_resources
            and len(events) > 0
            and (
                run_id := events[0].get("event", {}).get("data", {}).get("run_id")
            )  # Run id is provided in the first event.
            is not None
        ):
            langsmith_resources.add_run(run_id, check_feedback=check_feedback)

        return tokens, events, (session_id, conversation_id)
