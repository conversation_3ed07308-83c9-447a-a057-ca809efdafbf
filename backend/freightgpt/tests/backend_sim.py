# Used to manually test the end-to-end uvicorn server and langchain agent.
from common import init_main

PROJECT_ROOT = init_main.initialize()

import asyncio
import sys
from unittest import mock
import argparse
import time
from backend.freightgpt.tests.test_utils import (
    websocket_post_request,
)
import pprint
import json

# Can run script in dry mode or not.
# Running without dry mode will create resources in the test environment.
parser = argparse.ArgumentParser()
parser.add_argument(
    "--dry_mode",
    action="store_true",
    default=False,
    help="Store/access resources in cloud, or simply use local memory.",
)
args = parser.parse_args()

import os

os.environ["ENV"] = "TEST"

mock_args = ["freightgpt/backend/main_server/uvicorn_handler.py", "--env", "TEST"]
if args.dry_mode:
    mock_args += ["--dry_mode"]


DEFAULT_USER = "integration_test_user"

# Import uvicorn handler, which will run current local freightgpt.
with mock.patch.object(
    sys,
    "argv",
    mock_args,
):
    from backend.freightgpt.main_server.uvicorn_handler import APP

# Instructions
print("-" * 120)
print("You may now ask the agent queries.")
print("Send a message or json object by typing in endpoint [message | request body]")
print("Please initiate a session with /new_session")
print("Example: /chat_message hello world!")
print("Example: /feedback {'user_id':'blah', ...} ")
print("Use quit or quit() to exit.")


# Allow user to send queries to chatbot.
async def chatbot_loop():
    current_session_id = None
    current_convo_id = None
    while True:
        text = input("User: ")
        if text.lower() in ["quit", "quit()"]:
            break

        # Parse out endpoint and body.
        endpoint = text.split(" ")[0].lower()
        body = " ".join(text.split(" ")[1:]).lower()

        events, tokens = [], []

        # If no endpoint provided, assume chat_message.
        if endpoint[0] != "/":
            print("Warning: no endpoint provided. Assuming chat_message.")
            body = text
            endpoint = "/chat_message"

        # If a dictionary is provided after the endpoint, send this as the body directly.
        try:
            body = json.loads(body)
            print("Received raw request_data. Sending as is")
        except Exception:
            pass

        if isinstance(body, dict):
            response, _, _ = await websocket_post_request(
                APP,
                endpoint,
                data=body,
            )
            print(response.json())

        # Special case, new_session, create new session.
        elif endpoint == "/new_session":
            print("Invoking new session!")
            server_response, _, _ = await websocket_post_request(
                APP,
                endpoint,
                data={"user_id": DEFAULT_USER},
            )

            response = server_response.json()

            current_convo_id = response["conversation_id"]
            current_session_id = response["session_id"]
            print(
                f"Created new session with id {current_session_id} on conversation {current_convo_id}."
            )
            continue

        # Special case, chat_message, send a chat message.
        elif endpoint == "/chat_message":
            if not current_convo_id or not current_session_id:
                print(
                    "Convo id or session id is not set. Please create a session through /new_session."
                )
                continue
            _, tokens, events = await websocket_post_request(
                APP,
                endpoint,
                data={
                    "user_id": DEFAULT_USER,
                    "session_id": current_session_id,
                    "conversation_id": current_convo_id,
                    "message": body,
                },
            )

        else:
            print("Unfamiliar endpoint invoked. Please try again.")

        # Print results.
        if events:
            print("\n---- Events Received ----")
            for event in events:
                pprint.pprint(str(event)[:100])

        if tokens:
            print("\n---- Token response received ---")
            print("".join(str(token["token"]) for token in tokens))

        s = time.perf_counter()
        print("-" * 120)
        print(f"Response time: {time.perf_counter() - s}\n")


asyncio.run(chatbot_loop())
