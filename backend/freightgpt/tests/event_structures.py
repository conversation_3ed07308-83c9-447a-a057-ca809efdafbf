# Allows tests to check JSON events against a pre-defined key structure.
from typing import Any

from machine_learning.freightgpt.constants import VisualizationType


class BaseEventStructure:
    """
    Base event structure.
    https://github.com/truce-logistics/truce/blob/cd6213d378550e29374d69a9e3e17ca2aee795cd/machine_learning/freightgpt/async_stream_handler.py#L234-L236
    """

    def __init__(self) -> None:
        self._event_structure = {"event": {"type": None, "data": None}}

    @property
    def event(self):
        return self._event_structure

    def set_event_type(self, event_type: str) -> None:
        """Set the event type"""
        self._event_structure["event"]["type"] = event_type

    def set_event_data(self, event_data: Any) -> None:
        """Set the event data"""
        self._event_structure["event"]["data"] = event_data


class LoadingDescriptionEvent(BaseEventStructure):
    """
    Event fired for loading description.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/async_stream_handler.py#L116-L119
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type("loading_description")
        self.set_event_data(str)


class SummaryEvent(BaseEventStructure):
    """
    Event fired for conversation summary.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/async_stream_handler.py#L336
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type("summary")
        self.set_event_data(str)


class FollowupEvent(BaseEventStructure):
    """
    Event fired for followup suggestions.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/async_stream_handler.py#L121-L124
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type("followup_suggestions")
        self.set_event_data(list)


class RunIdEvent(BaseEventStructure):
    """
    Event fired for run id.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/async_stream_handler.py#L178
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type("run_id")
        self.set_event_data({"run_id": str})


class GmapsPolylineEvent(BaseEventStructure):
    """
    Event fired for google maps with polyline.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/langchain_utils.py#L67-L76
    """

    def __init__(self) -> None:
        super().__init__()

        self.set_event_type(VisualizationType.GMAPS)
        self.set_event_data(
            {
                "active_type": "active",
                "polyline": str,
                "bounds": {
                    "northeast": {"lat": float, "lng": float},
                    "southwest": {"lat": float, "lng": float},
                },
                "origin_city": str,
                "destination_city": str,
                "total_distance": int,
            },
        )


class GmapsPricerCardEvent(BaseEventStructure):
    """
    Event fired for pricer card with gmaps.
    https://github.com/truce-logistics/truce/blob/967f84ff44d97485af96ed305e7cddd91c551eb7/machine_learning/freightgpt/langchain_utils.py#L327-L356
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type("pricer_polyline")
        self.set_event_data(
            {
                "active_type": "active",
                "polyline": str,
                "bounds": {
                    "northeast": {"lat": float, "lng": float},
                    "southwest": {"lat": float, "lng": float},
                },
                "origin_city": str,
                "destination_city": str,
                "total_distance": int,
                "all_in_rate": float,
                "equipment_type": str,
                "fuel_cost": float,
                "batch_price_historical_data": {
                    "average": float,
                    "high": float,
                    "low": float,
                    "period": int,
                    "time_series": list,
                },
            },
        )


class GmapsPolygonEvent(BaseEventStructure):
    """
    Event fired for google map with polygons.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/carrier_tool.py#L447-L452
    """

    def __init__(self, regions=[]) -> None:
        super().__init__()
        self.set_event_type(VisualizationType.POLYGON)
        self.set_event_data(
            {
                "active_type": "active",
                "region": str,
                "coordinates": {
                    region: {"coordinates": list, "count": int} for region in regions
                },
            },
        )


class WindyMapsEvent(BaseEventStructure):
    """
    Event fired for windy map with polyline.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/langchain_utils.py#L67-L76
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type(VisualizationType.WINDY)
        self.set_event_data(
            {
                "active_type": "active",
                "polyline": str,
                "bounds": {
                    "northeast": {"lat": float, "lng": float},
                    "southwest": {"lat": float, "lng": float},
                },
                "origin_city": str,
                "destination_city": str,
                "total_distance": int,
            },
        )


class HistoricalPriceEvent(BaseEventStructure):
    """
    Event fired for historical price tool.
    https://github.com/truce-logistics/truce/blob/14b676b5458d5e38fe5baadeaf5e68ff0dc432b2/machine_learning/freightgpt/langchain_utils.py#L67-L76
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type(VisualizationType.GRAPH_SERIES)
        self.set_event_data(
            {
                "time_series": [(float, float)],
                "lane": str,
                "roundtrip": bool,
            },
        )


class TransitMarkersEvent(BaseEventStructure):
    """
    Event fired for markers.
    https://github.com/truce-logistics/truce/blob/7b924225c9d07c06154c8635446ccbe2a295468b/machine_learning/freightgpt/transit_time_tool/transit_time.py#L356
    """

    def __init__(self, num_markers: int) -> None:
        super().__init__()

        self.set_event_type(VisualizationType.TRANSIT_MARKERS)
        self.set_event_data(
            {
                "origin": {
                    "lat": float,
                    "lng": float,
                    "city_name": str,
                    "time": str,
                },
                "destination": {
                    "lat": float,
                    "lng": float,
                    "city_name": str,
                    "time": str,
                },
                "markers": [
                    {
                        "lat": float,
                        "lng": float,
                        "nearest_city": str,
                        "rest_start_time": str,
                        "rest_end_time": str,
                    }
                    for _ in range(num_markers)
                ],
            },
        )


class CarrierSearchEvent(BaseEventStructure):
    """
    Event fired for carrier search.
    """

    def __init__(self) -> None:
        super().__init__()
        self.set_event_type(VisualizationType.CARRIER_SEARCH_RESULTS)
        self.set_event_data(
            {
                # Keys in list of dicts can be completely variable.
                # TODO (P1): account for variable keys in dict or create optional keys.
                "search_results": list,
                "filters": dict,
            },
        )


# Pre instantiate event structures.
LOADING_STRUCTURE = LoadingDescriptionEvent()
SUMMARY_STRUCTURE = SummaryEvent()
FOLLOWUP_STRUCTURE = FollowupEvent()
RUNID_STRUCTURE = RunIdEvent()
GMAPS_POLYLINE_STRUCTURE = GmapsPolylineEvent()
GMAPS_PRICER_CARD_STRUCTURE = GmapsPricerCardEvent()
GMAPS_POLYGON_STRUCTURE = lambda regions: GmapsPolygonEvent(  # noqa: E731
    regions=regions
)
MARKERS_STRUCTURE = lambda num_markers: TransitMarkersEvent(  # noqa: E731
    num_markers=num_markers
)
WINDY_POLYLINE_STRUCTURE = WindyMapsEvent()
HIST_PRICE_STRUCTURE = HistoricalPriceEvent()
CARRIER_SEARCH_EVENT = CarrierSearchEvent()
