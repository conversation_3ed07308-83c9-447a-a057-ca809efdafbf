import collections
import uuid
from typing import Any
from typing import Dict
from typing import List

from langsmith import Client

from common import dynamodb_utils


class DynamoTable:
    """
    A class abstraction representing a DynamoDB table.

    Attributes:
        name (str): The name of the DynamoDB table.
        pk_name (str): The name of the primary key for the table.
        sk_name (str, optional): The name of the sort key for the table. Default is None.
        cols (list): A list of column names for the table.
    """

    def __init__(
        self, name: str, pk_name: str, sk_name: str = None, cols: List[str] = []
    ) -> None:
        self.name = name
        self.cols = cols
        self.PK = pk_name
        self.SK = sk_name

        assert self.cols
        assert pk_name in self.cols, "Primary key is not reflected in cols."
        if sk_name:
            assert sk_name in self.cols, "Sort key is not reflected in cols."


class ValidDynamoDBEntry:
    """
    A class representing a valid entry in a DynamoDB table.

    Attributes:
        table (object): The DynamoDB table object.
        pk_value (any): The value of the primary key for the entry.
        sk_value (any, optional): The value of the sort key for the entry, if applicable.
    """

    def __init__(self, table: DynamoTable, pk_value: Any, sk_value: Any = None) -> None:
        self._table = table
        self._pk_value = pk_value
        self._sk_value = sk_value

    def construct_key(self) -> Dict[str, Any]:
        """
        Construct a key for a table based on the primary key and sort key from the table, and their respective values.

        Returns:
            dict: The constructed key, with the primary key and optionally the sort key.

        Raises:
            None
        """
        key = {self._table.PK: self._pk_value}

        if self._table.SK and self._sk_value:
            key = {**key, **{self._table.SK: self._sk_value}}

        return key

    def validate_item_exists(self) -> bool:
        """
        Validate if an item actually exists in a DynamoDB table with those keys and column.

        Returns:
            bool: True if the item exists, False otherwise.
        """
        item = dynamodb_utils.read_item(self._table.name, self.construct_key())

        # Ensure table columns match what they should.
        if self._table.cols:
            return item is not None and sorted(item.keys()) == sorted(self._table.cols)

        return item is not None

    def delete_item(self):
        """
        Deletes an item from the DynamoDB table.
        """
        dynamodb_utils.delete_item(self._table.name, self.construct_key())


class DynamoResourceManager:
    """
    Tracks all created dynamo entries, and can clear all created resources.

    Attributes:
        created_resources (defaultdict): A dictionary-like container that stores created DynamoDB resources.
    """

    def __init__(self) -> None:
        self.created_resources = collections.defaultdict()

    def check_resource_creation(self) -> bool:
        """
        Check if all tracked / created resources exist.

        Returns:
            bool: True if all created resources exist, False otherwise.
        """
        return all(
            self.created_resources[resource_id].validate_item_exists()
            for resource_id in self.created_resources
        )

    def add_created_resource(self, dynamo_db_entry: ValidDynamoDBEntry) -> None:
        """
        Tracks a created dynamodb entry under a resource id.

        Args:
            dynamo_db_entry (ValidDynamoDBEntry): The object representing the DynamoDB entry.
        """
        resource_id = str(uuid.uuid4())
        self.created_resources[resource_id] = dynamo_db_entry

    def clear_resources(self) -> None:
        """
        Clear all created resources from tables.
        """
        for resource_id in self.created_resources:
            self.created_resources[resource_id].delete_item()
        self.created_resources.clear()


class LangsmithResourceManager:
    """
    Tracks all langsmith runs for the test. Can verify the run exists with certain tags and feedback.
    Note: cannot delete run.

    Attributes:
        langsmith_client (Client): The Langsmith client used to interact with the Langsmith API.
        runs (dict): A dictionary containing information about the runs, with the run id as the key and a boolean indicating if feedback should be checked as the value.
    """

    def __init__(self) -> None:
        self.langsmith_client = Client()
        self.runs = {}

    def add_run(self, run_id: str, check_feedback: bool = False) -> None:
        """
        Add a new run to storage.

        Args:
            run_id (int): The ID of the run being added.
            check_feedback (bool, optional): Flag to indicate if feedback should be checked. Default is False.
        """
        self.runs[run_id] = check_feedback

    def verify_tagged_runs_exist(
        self,
        user_tag: str,
        feedback_thumbs_up: str = "thumbs_up",
        feedback_thumbs_down: str = "thumbs_down",
    ):
        """
        Verifies if runs exist for a given user tag. If the stored run contains check_feedback as True, it will check for
        feedback keys in the feedback_stats.

        Args:
            user_tag (str): The user tag to check for.
            feedback_thumbs_up (str): feedback stat key for thumbs up.
            feedback_thumbs_down (str): feedback stat key for thumbs down.

        Returns:
            bool: True if tagged runs exist for the given user tag, False otherwise.
        """
        run_count = 0

        for run in self.langsmith_client.list_runs(id=list(self.runs.keys())):
            # Verify the run is tagged to the right user.
            if user_tag not in run.tags:
                continue

            # Check feedback if flag is true.
            if self.runs[str(run.id)]:
                feedback_stats = run.feedback_stats
                if (
                    feedback_thumbs_up not in feedback_stats
                    or feedback_thumbs_down not in feedback_stats
                ):
                    continue

            run_count += 1

        return run_count == len(
            self.runs
        )  # Number of langsmith runs should equal what is stored if using this class to evaluate.
