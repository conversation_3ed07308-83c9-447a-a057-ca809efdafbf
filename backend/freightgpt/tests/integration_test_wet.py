# Test basic LLM and server function, including main query types, agent memory, and feedback.
# Used to ensure not only that the LLM / server / events are functioning correctly, but that connections to cloud resources work as well.
# Also tests auxillary features such as feedback.
# Creates resources in dynamo and langsmith.
# NOTE: Langsmith resources cannot be deleted.
from common import init_main

PROJECT_ROOT = init_main.initialize()

import pytest
from backend.freightgpt.tests.server_base import (
    UvicornBaseTest,
)

from backend.freightgpt.tests.event_structures import (
    LOADING_STRUCTURE,
    MARKERS_STRUCTURE,
    SUMMARY_STRUCTURE,
    FOLLOWUP_STRUCTURE,
    RUNID_STRUCTURE,
    GMAPS_POLYLINE_STRUCTURE,
    GMAPS_POLYGON_STRUCTURE,
    WINDY_POLYLINE_STRUCTURE,
    CARRIER_SEARCH_EVENT,
    GMAPS_PRICER_CARD_STRUCTURE,
)
from backend.freightgpt.tests.resource_utils import (
    DynamoResourceManager,
    LangsmithResourceManager,
)
from backend.freightgpt.tests.constants import (
    IntegrationTestModes,
)


# Create a fixture to store and manage created resources.
@pytest.fixture(scope="session")
def session_data():
    data_store = {
        "dynamo_resources": DynamoResourceManager(),
        "langsmith_resources": LangsmithResourceManager(),
        "current_session": None,
    }
    yield data_store

    # Runs after the test - checks created dynamo resources and langsmith resources, and clear them.
    dynamo_resource_manager = data_store["dynamo_resources"]
    assert dynamo_resource_manager.check_resource_creation()
    dynamo_resource_manager.clear_resources()

    langsmith_resource_manager = data_store["langsmith_resources"]
    assert langsmith_resource_manager.verify_tagged_runs_exist("integration_test_user")


class TestUvicornWet(UvicornBaseTest):
    def setup_method(self, method):
        super().setup_method(method, mode=IntegrationTestModes.WET.value)

    ####################################################################
    #         TEST 1 - BASIC QUERIES
    ####################################################################

    @pytest.mark.asyncio
    @pytest.mark.basic_wet
    @pytest.mark.order(1)
    @pytest.mark.parametrize(
        "query, events_structure",
        [
            # Pricing query.
            (
                "What is the cost from sf to la?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Multi lane - this one sometimes times out, resource issues?
            (
                "What is the cost from nyc to chicago and sf to la?",
                [RUNID_STRUCTURE.event]
                + [
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                ]
                * 2
                + [SUMMARY_STRUCTURE.event],
            ),
            # Roundtrip cost.
            # Flaky test - sometimes sends two pricer events, sometimes one.
            (
                "What is the roundtrip cost from nyc to chicago?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Weather along route.
            (
                "What is the weather along route from miami to orlando?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    WINDY_POLYLINE_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Carrier query - also timing out on occasion.
            (
                "What is inspection information for dot number 2271487?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_POLYGON_STRUCTURE(
                        regions=[
                            "Cook,IL",
                            "Kane,IL",
                            "Walworth,WI",
                            "Porter,IN",
                            "Van Buren,MI",
                            "Stephenson,IL",
                            "Jo Daviess,IL",
                            "Berrien,MI",
                            "Winnebago,IL",
                        ]
                    ).event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Transit time query.
            (
                "What is the transit time from nyc to chicago?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_POLYLINE_STRUCTURE.event,
                    MARKERS_STRUCTURE(num_markers=1).event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Carrier Info Query
            (
                "Tell me about dot 3380603.",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
        ],
    )
    async def test_basic_queries(self, session_data, query, events_structure):
        await self.query(
            query=query,
            events_structure=events_structure,
            dynamo_resources=session_data["dynamo_resources"],
            langsmith_resources=session_data["langsmith_resources"],
        )

    ####################################################################
    #         TEST 2 - CONVERSATION MEMORY - DISABLED FOR NOW
    ####################################################################
    # @pytest.mark.asyncio
    # @pytest.mark.memory_wet
    # @pytest.mark.order(2)
    # @pytest.mark.parametrize(
    #     "memory_eval_identifier, query, events_structure, save_session",
    #     [
    #         # Pricing query.
    #         (
    #             "price_1",
    #             "What is the cost of portland to seattle?",
    #             [
    #                 RUNID_STRUCTURE.event,
    #                 LOADING_STRUCTURE.event,
    #                 FOLLOWUP_STRUCTURE.event,
    #                 GMAPS_POLYLINE_STRUCTURE.event,
    #                 SUMMARY_STRUCTURE.event,
    #             ],
    #             True,
    #         ),
    #         # Follow up.
    #         # This is a little flaky due to memory issues:
    #         #   - Sometimes we get a loading event, sometimes we don't.
    #         #   - Sometimes the agent says "I'd be happy to do that, what lane?"
    #         #   - Most of the time, we pass.
    #         (
    #             "price_2",
    #             "What was the last thing I asked you?",
    #             [RUNID_STRUCTURE.event],
    #             False,
    #         ),
    #     ],
    # )
    # async def test_conversation_memory(
    #     self,
    #     session_data,
    #     memory_eval_identifier,
    #     query,
    #     events_structure,
    #     save_session,
    # ):
    #     tokens, _, session = await self.query(
    #         query=query,
    #         events_structure=events_structure,
    #         dynamo_resources=session_data["dynamo_resources"],
    #         langsmith_resources=session_data["langsmith_resources"],
    #         session=session_data["current_session"],
    #     )

    #     response = "".join(str(token["token"]) for token in tokens)

    #     # Capture first price.
    #     if memory_eval_identifier == "price_1":
    #         price = find_prices_in_string(response)
    #         session_data["price"] = price

    #     # Ensure a 15% margin has indeed been applied.
    #     if memory_eval_identifier == "price_2":
    #         prices = find_prices_in_string(response, single_price=False)
    #         increased_price_found = False
    #         for price in prices:
    #             if math.ceil(price) == math.ceil(session_data.get("price", 0) * 1.15):
    #                 increased_price_found = True

    #         assert increased_price_found

    #     session_data["current_session"] = session if save_session else None

    #     await asyncio.sleep(10)

    ####################################################################
    #         TEST 3 - FEEDBACK ENDPOINT
    ####################################################################

    @pytest.mark.asyncio
    @pytest.mark.feedback_wet
    @pytest.mark.order(3)
    @pytest.mark.parametrize(
        "query, events_structure",
        [
            ("Hello!", [RUNID_STRUCTURE.event, SUMMARY_STRUCTURE.event]),
        ],
    )
    async def test_feedback(self, session_data, query, events_structure):
        tokens, events, session = await self.query(
            query=query,
            events_structure=events_structure,
            dynamo_resources=session_data["dynamo_resources"],
            langsmith_resources=session_data["langsmith_resources"],
        )

        # After run, make call to feedback endpoint.
        run_id = events[0]["event"]["data"]["run_id"]
        feedback_type = "thumbs_up"
        response = " ".join(str(token["token"]) for token in tokens)
        flags = ["overPriced"]
        comment = "Integration test"

        await self.query(
            "",
            endpoint="/feedback",
            append_query_data={
                "run_id": run_id,
                "feedback_type": feedback_type,
                "query": query,
                "response": response,
                "comment": comment,
                "flags": flags,
            },
            validate_tokens=False,
            validate_events=False,
            session=session,
        )

    ####################################################################
    #         TEST 4 - Carrier Search
    ####################################################################

    @pytest.mark.asyncio
    @pytest.mark.carrier_search
    @pytest.mark.order(4)
    @pytest.mark.parametrize(
        "query, events_structure",
        [
            # Text match query with multiple results
            (
                "Can you tell me about carrier roop?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    CARRIER_SEARCH_EVENT.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Query with geopoint and equipment type
            (
                "Give me carriers near Dallas, TX that haul reefer.",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    CARRIER_SEARCH_EVENT.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Numerical query
            (
                "Can you tell me carriers that have been in business at least 4 years and have 20 trucks?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    CARRIER_SEARCH_EVENT.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
        ],
    )
    async def test_carrier_search_queries(self, session_data, query, events_structure):
        await self.query(
            query=query,
            events_structure=events_structure,
            dynamo_resources=session_data["dynamo_resources"],
            langsmith_resources=session_data["langsmith_resources"],
        )

    ####################################################################
    #         TEST 5 - Heavy / Other dfault prompts
    ####################################################################

    @pytest.mark.asyncio
    @pytest.mark.heavy
    @pytest.mark.order(2)
    @pytest.mark.parametrize(
        "query, events_structure",
        [
            # Pricer examples.
            # TODO (P0) - also flaky, errors, and bizarre event structure.
            (
                "Roundtrip price van LA to Stockton, CA?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            (
                "What is the CPM of a Dry Van from Chicago, IL to St. Louis, MO",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Weather example.
            (
                "What is the weather in Fruitland, ID?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Carrier examples
            # TODO (P0) - erroring out entirely
            (
                "What is the crash history for MC115495?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_POLYGON_STRUCTURE(
                        regions=[
                            "Natrona,WY",
                            "Marion,OR",
                            "Monroe,IN",
                            "Wharton,TX",
                            "Walworth,WI",
                            "Marion,IN",
                        ]
                    ).event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
        ],
    )
    async def test_example_prompts(self, session_data, query, events_structure):
        await self.query(
            query=query,
            events_structure=events_structure,
            dynamo_resources=session_data["dynamo_resources"],
            langsmith_resources=session_data["langsmith_resources"],
        )


# pytest -s backend/freightgpt/tests/integration_test_wet.py
# Can test subset groups from `pytest.ini`
# pytest -s backend/freightgpt/tests/integration_test_wet.py -m basic_wet
