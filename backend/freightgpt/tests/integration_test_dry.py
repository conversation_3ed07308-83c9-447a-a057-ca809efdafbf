# Test basic LLM and server function, including truck cost, weather, and carrier queries.
# Used to ensure queries are processed correctly and that events are firing right (i.e. the right tools are invoked).
# Does not create resources at all.
from common import init_main

PROJECT_ROOT = init_main.initialize()

import pytest

from backend.freightgpt.tests.server_base import (
    UvicornBaseTest,
)
from backend.freightgpt.tests.constants import (
    IntegrationTestModes,
)
from backend.freightgpt.tests.event_structures import (
    LOADING_STRUCTURE,
    MARKERS_STRUCTURE,
    SUMMARY_STRUCTURE,
    FOLLOWUP_STRUCTURE,
    RUNID_STRUCTURE,
    GMAPS_POLYLINE_STRUCTURE,
    GMAPS_POLYGON_STRUCTURE,
    WINDY_POLYLINE_STRUCTURE,
    GMAPS_PRICER_CARD_STRUCTURE,
)


class TestUvicornDry(UvicornBaseTest):
    def setup_method(self, method):
        super().setup_method(method, mode=IntegrationTestModes.DRY.value)

    ####################################################################
    #         TEST 1 - BASIC QUERIES: TRUCK COST, WEATHER, CARRIER
    ####################################################################

    @pytest.mark.asyncio
    @pytest.mark.query_dry
    @pytest.mark.order(1)
    @pytest.mark.parametrize(
        "query, events_structure",
        [
            # Pricing query.
            (
                "What is the cost from sf to la?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_PRICER_CARD_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Weather along route.
            (
                "What is the weather along route from nyc to chicago?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    WINDY_POLYLINE_STRUCTURE.event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Carrier query.
            (
                "What is crash information for dot number 2271487?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_POLYGON_STRUCTURE(
                        regions=["Milwaukee,WI", "Winnebago,IL", "Cook,IL"]
                    ).event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
            # Transit time query.
            (
                "What is the transit time from nyc to chicago?",
                [
                    RUNID_STRUCTURE.event,
                    LOADING_STRUCTURE.event,
                    FOLLOWUP_STRUCTURE.event,
                    GMAPS_POLYLINE_STRUCTURE.event,
                    MARKERS_STRUCTURE(num_markers=1).event,
                    SUMMARY_STRUCTURE.event,
                ],
            ),
        ],
    )
    async def test_basic_query(self, query, events_structure):
        await self.query(query=query, events_structure=events_structure)


# pytest -s backend/freightgpt/tests/integration_test_dry
# Can test subset groups from `pytest.ini`
# pytest -s backend/freightgpt/tests/integration_test_dry.py -m query_dry
