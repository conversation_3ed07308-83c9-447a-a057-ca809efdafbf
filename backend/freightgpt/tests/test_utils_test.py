import unittest

from parameterized import parameterized

from common import init_main

PROJECT_ROOT = init_main.initialize()

from backend.freightgpt.tests.test_utils import (
    has_same_structure,
)


class TestEventStructure(unittest.TestCase):
    @parameterized.expand(
        [
            (
                "Markers Event",
                {
                    "type": "markers",
                    "data": {
                        "origin": {
                            "lat": 40.7126802,
                            "lng": -74.00657629999999,
                            "city_name": "New York City, NY",
                            "time": "Mar 22, 16:46 EDT",
                        },
                        "destination": {
                            "lat": 41.878114,
                            "lng": -87.6297778,
                            "city_name": "Chicago, IL",
                            "time": "Mar 23, 17:46 CDT",
                        },
                        "markers": [
                            {
                                "lat": 41.73573,
                                "lng": -84.99666,
                                "rest_start_time": "Mar 23, 03:46 EDT",
                                "rest_end_time": "Mar 23, 13:46 EDT",
                                "nearest_city": "Jamestown, IN",
                            }
                        ],
                    },
                },
                {
                    "type": "markers",
                    "data": {
                        "origin": {
                            "lat": float,
                            "lng": float,
                            "city_name": str,
                            "time": str,
                        },
                        "destination": {
                            "lat": float,
                            "lng": float,
                            "city_name": str,
                            "time": str,
                        },
                        "markers": [
                            {
                                "lat": float,
                                "lng": float,
                                "nearest_city": str,
                                "rest_start_time": str,
                                "rest_end_time": str,
                            }
                        ],
                    },
                },
            ),
            (
                "Run ID Event",
                {
                    "event": {
                        "type": "run_id",
                        "data": {"run_id": "93fc0003-9110-4a49-8982-77b139d32624"},
                    }
                },
                {"event": {"type": "run_id", "data": {"run_id": str}}},
            ),
            # Add more test cases here as needed
        ]
    )
    def test_has_same_structure(self, name, event, structure):
        # Test if the event matches the structure
        self.assertTrue(has_same_structure(event, structure))


if __name__ == "__main__":
    unittest.main()
