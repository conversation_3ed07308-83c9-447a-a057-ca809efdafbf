import json
import logging
import re
import time
import uuid
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import Union

import fastapi
import httpx
import websockets

from backend.freightgpt.tests import event_structures
from backend.freightgpt.tests.constants import DEFAULT_SERVER_URL
from backend.freightgpt.tests.constants import DEFAULT_USER
from backend.freightgpt.tests.constants import DEFAULT_WEBSOCKET_URL


####################################################################
#         BASIC TEST UTILS
####################################################################
def find_prices_in_string(string: str, single_price: bool = True) -> list[str]:
    """
    Find prices in a given string in the format $xx.xx..

    Args:
        string (str): The input string to search for prices.
        single_price (bool, optional): If True, only a single price is expected in the input string. Defaults to True.

    Returns:
       List[str] or float: If single_price is True, returns the price as a float. If single_price is False, returns a list of prices as floats.
    """
    pattern = r"\$\d+(?:\.\d{1,2})?"
    prices = re.findall(pattern, string)

    if single_price:
        assert len(prices) == 1
        return float(prices[0][1:])

    return [float(price[1:]) for price in prices]


def is_valid_uuid(uuid_to_test: uuid.UUID, version: int = 4) -> bool:
    """
    Check if a given string is a valid UUID.

    Args:
        uuid_to_test (str): A string representation of a UUID.
        version (int, optional): The UUID version to validate against. Defaults to 4.

    Returns:
        bool: True if the input string is a valid UUID, False otherwise.
    """
    try:
        uuid_obj = uuid.UUID(uuid_to_test, version=version)
    except ValueError:
        return False
    return str(uuid_obj).lower() == uuid_to_test.lower()


def has_same_structure(event: Dict | List, structure: Dict | List) -> bool:
    """
    Check if two dictionaries have the same key structure, allowing for any number of nested dicts/lists.

    Args:
        event (Dict): The actual event dictionary emitted, with actual values.
        structure (Dict): The theoretical structure of the event dictionary, with values as the type.

    Returns:
        bool: True if the dictionaries have the same key structure, False otherwise.
    """
    if isinstance(event, dict) and isinstance(structure, dict):
        if set(event.keys()) != set(structure.keys()):
            return False

        for key, event_value in event.items():
            structure_value = structure[key]
            # Handle special keys
            if key in ["active_type", "type"]:
                if isinstance(structure_value, type):
                    # If structure_value is a type, check if event_value is an instance of it
                    if not isinstance(event_value, structure_value):
                        return False
                else:
                    # Otherwise, check for direct equality
                    if event_value != structure_value:
                        return False
            elif isinstance(event_value, dict) and isinstance(structure_value, dict):
                if not has_same_structure(event_value, structure_value):
                    return False
            elif isinstance(event_value, list) and isinstance(structure_value, list):
                if not all(
                    has_same_structure(ev, structure_value[0]) for ev in event_value
                ):
                    return False
            elif not isinstance(event_value, structure_value):
                return False

    elif isinstance(event, list) and isinstance(structure, list):
        if not all(has_same_structure(ev, structure[0]) for ev in event):
            return False
    else:
        if not isinstance(event, structure):
            return False

    return True


####################################################################
#         SERVER / NETWORK UTILS
####################################################################
async def websocket_post_request(
    APP: fastapi.FastAPI,
    endpoint: str,
    ws_uri: str = DEFAULT_WEBSOCKET_URL,
    data: Dict[str, str] = {},
    timeout_s: int = 180,  # Double the normal agent timeout
) -> Tuple[Dict[str, Any], List[str], List[Dict[str, Any]]]:
    """
    Asynchronous function for making a POST request to a fastapi server and then asyncronously listening to traffic on a websocket.

    Args:
        APP (fastapi.FastAPI): The running instance of a fastapi app.
        endpoint (str): The endpoint URL on the fastapi app for the POST request.
        ws_uri (str, optional): The WebSocket server URL to listen in on.
        data (Dict[str, str], optional): The JSON payload for the POST request.
        timeout_s (int): The request timeout in seconds.

    Returns:
        Tuple[Dict[str, Any], List[str], List[Dict[str, Any]]]: A tuple containing the POST response, a list of tokens, and a list of events.
    """

    async def post_request(connection_id):
        """Makes a post request to the fastapi app."""
        async with httpx.AsyncClient(app=APP, base_url=DEFAULT_SERVER_URL) as client:
            response = await client.post(
                endpoint,
                json=data,
                headers={"connectionid": connection_id},
            )
            assert response.status_code == 200

        return response

    # Listen in on any websocket traffic.
    async with websockets.connect(ws_uri) as ws:
        await ws.send(json.dumps({"action": "id"}))  # Get websocket connection id.

        tokens = []
        events = []

        query_start_time = time.perf_counter()

        while time.perf_counter() - query_start_time < timeout_s:
            message = await ws.recv()
            message_json = json.loads(message)

            # Sort received traffic by key into events and tokens, and listen to special events.
            if "connectionId" in message_json:
                connection_id = message_json["connectionId"]

                # If we get back a websocket connection id to identify this user, send a request to the app.
                response = await post_request(connection_id)
                assert response.status_code == 200

            elif "token" in message_json:
                tokens.append(message_json)
            elif "event" in message_json:
                events.append(message_json)
            elif "close" in message_json:
                break
            else:
                print("Received non-token non-event message: ", message_json)
    return response, tokens, events


def construct_message_body(
    session_id: str, conversation_id: str, message: str
) -> Dict[str, str]:
    """
    Construct the message body for a typical user query to freightgpt.

    Args:
        session_id (str): The session ID for the conversation.
        conversation_id (str): The ID of the conversation.
        message (str): The content of the message.

    Returns:
        dict: A dictionary containing the message body with the following keys:
            - 'user_id': The ID of the default user.
            - 'session_id': The session ID for the conversation.
            - 'conversation_id': The ID of the conversation.
            - 'message': The content of the message.
    """
    return {
        "user_id": DEFAULT_USER,
        "session_id": session_id,
        "conversation_id": conversation_id,
        "message": message,
    }


def validate_token_response(token_array: List[Dict[str, str]]) -> None:
    """
    Validate a token response by ensuring all entries in the array contain the key 'token'.

    Args:
        token_array (List[Dict[str, str]]): A list of dictionaries representing token objects.
                                            in the format {"token":"blah"}
    """
    assert len(token_array) > 0
    assert all(list(d.keys()) == ["token"] for d in token_array)


def validate_event_response(
    events_array: List[Dict[str, Any]],
    required_structures: List[
        Optional[
            Union[
                event_structures.LoadingDescriptionEvent,
                event_structures.FollowupEvent,
                event_structures.SummaryEvent,
                event_structures.RunIdEvent,
                event_structures.GmapsPolygonEvent,
                event_structures.GmapsPolylineEvent,
                event_structures.WindyMapsEvent,
            ]
        ]
    ],
) -> None:
    """
    Validate the response of events against required structures with certain keys and certain nesting.

    Args:
        events_array (List[Dict[str, Any]]): A list of event dictionaries.
        required_structures: A list of required event structures to validate the events against.
    """
    assert len(events_array) == len(
        required_structures
    ), "Structures and events should have same length."

    for i in range(len(events_array)):
        event = events_array[i]
        structure = required_structures[i]
        if not has_same_structure(event, structure):
            logging.error(f"Event: {event}")
            logging.error(f"Structure: {structure}")
            raise ValueError("Event does not have the correct structure.")
