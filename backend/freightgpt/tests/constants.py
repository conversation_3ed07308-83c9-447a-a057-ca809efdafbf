from enum import Enum

from backend.freightgpt.constants import CloudResources
from backend.freightgpt.tests.resource_utils import (
    DynamoTable,
)

# Server constants
DEFAULT_WEBSOCKET_URL = "wss://wps3vtux75.execute-api.us-east-1.amazonaws.com/beta-1"
DEFAULT_SERVER_URL = "http://0.0.0.0:8081"
DEFAULT_USER = "integration_test_user"

# Uvicorn import args
DRY_IMPORT_ARGS = [
    "backend/freightgpt/tests/server_base.py",
    "--env",
    "TEST",
    "--log_to_stdout",
    "--dry_mode",  # Run server in dry mode
]

WET_IMPORT_ARGS = [
    "backend/freightgpt/tests/server_base.py",
    "--env",
    "TEST",
    "--log_to_stdout",
]

# Dynamo resources
RESOURCES = getattr(CloudResources, "TEST").value
SESSIONS_TABLE = DynamoTable(
    name=RESOURCES.SESSIONS_TABLE_NAME.value,
    pk_name="user_id",
    sk_name="session_id",
    cols=["user_id", "session_id", "creation_time", "last_update_time"],
)

EVENTS_TABLE = DynamoTable(
    name=RESOURCES.EVENTS_TABLE_NAME.value,
    pk_name="conv_id",
    cols=["conv_id", "Entries", "last_updated_timestamp"],
)

CONVO_TABLE = DynamoTable(
    name=RESOURCES.CONVO_TABLE_NAME.value,
    pk_name="user_id",
    sk_name="conv_id",
    cols=["user_id", "conv_id", "CreationTimestamp", "History", "Summary"],
)


# Integration test modes
class IntegrationTestModes(Enum):
    WET = "wet"
    DRY = "dry"
