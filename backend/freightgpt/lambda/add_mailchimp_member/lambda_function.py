import os

import mailchimp_marketing as MailchimpMarketing
from mailchimp_marketing.api_client import ApiClientError

from common import init_main

PROJECT_ROOT = init_main.initialize()

MAILCHIMP_API_KEY = os.getenv("MAILCHIMP_API_KEY")
MAILCHIMP_SERVER_PREFIX = os.getenv("MAILCHIMP_SERVER_PREFIX")
MAILCHIMP_LIST_ID = os.getenv("MAILCHIMP_LIST_ID")


def lambda_handler(event, context):
    try:
        print("Incoming event:", event)

        # Extract user attributes from the event
        body = event["request"]["userAttributes"]
        email = body.get("email")
        full_name = body.get("name", "")
        newsletter_consent = body.get("custom:newsletter_consent", "0")
        source = body.get("custom:referralSource", "postsignup_aws")

        if not email:
            print("Missing email in user attributes")
            return event  # Return the event for Cognito to handle gracefully

        # Determine subscription status
        subscription_status = (
            "subscribed" if newsletter_consent == "1" else "unsubscribed"
        )

        # Split the name into first name and last name since we are getting full name in the signup.
        name_parts = full_name.split()
        if len(name_parts) > 1:
            first_name = " ".join(name_parts[:-1])  # All words except the last
            last_name = name_parts[-1]  # The last word
        else:
            first_name = full_name  # Treat the full name as the first name if no spaces
            last_name = ""  # Empty last name

        # Mailchimp configuration
        api_key = MAILCHIMP_API_KEY
        server_prefix = MAILCHIMP_SERVER_PREFIX
        list_id = MAILCHIMP_LIST_ID

        # Initialize Mailchimp client
        client = MailchimpMarketing.Client()
        client.set_config({"api_key": api_key, "server": server_prefix})

        # Prepare subscriber data
        subscriber_data = {
            "email_address": email,
            "status": subscription_status,
            "merge_fields": {"FNAME": first_name, "LNAME": last_name},
            "source": source,
            "tags": ["SignedUp"],
        }

        # Add the subscriber to the audience list
        response = client.lists.add_list_member(list_id, subscriber_data)
        print("Successfully added user to the Mailchimp audience: ", response)

    except ApiClientError as error:
        # Log all attributes of the error for debugging
        print("Mailchimp API Client Error: ", error)
        print("Error Attributes: ")
        if hasattr(error, "__dict__"):
            for key, value in error.__dict__.items():
                print(f"{key}: {value}")

        # Extract error details if available
        error_message = str(error)
        error_details = getattr(
            error, "text", None
        )  # For Mailchimp API error responses
        error_status = getattr(error, "status", None)
        error_detail = getattr(error, "detail", None)

        print(
            "Failed to add user to Mailchimp. Error details:",
            {
                "error": "Failed to add user to Mailchimp",
                "message": error_message,
                "details": error_details,
                "status": error_status,
                "detail": error_detail,
            },
        )

    except Exception as e:
        print(f"Unexpected Error: {e}")

    # Return the event object for Cognito to proceed with the user signup flow
    return event
