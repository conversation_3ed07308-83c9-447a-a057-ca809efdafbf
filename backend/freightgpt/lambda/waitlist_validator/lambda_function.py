from common import google_sheets_utils


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    :param event: Signup event from cognito
    :param context: Ignored for now.
    :return: Object with status code and notification data under ['body'].
    """
    print(event)
    gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
    email = event.get("request").get("userAttributes").get("email")
    try:
        print("checking waitlist for ", email)
        is_present = gsheets_utils.search_sheet_for_value(
            email, "1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4", "Sheet1"
        )
    except Exception as e:
        raise Exception("Error validating email", e)
    if not is_present:
        raise Exception("Email is not on the waitlist")
    print(event)
    return event


def main():
    e = {
        "version": "1",
        "region": "us-east-1",
        "userPoolId": "us-east-1_OtlXNpE6h",
        "userName": "google_104120290195976873226",
        "callerContext": {
            "awsSdkVersion": "aws-sdk-unknown-unknown",
            "clientId": "1g3p7f2tlu10k64epoeqoi7j0s",
        },
        "triggerSource": "PreSignUp_ExternalProvider",
        "request": {
            "userAttributes": {
                "email_verified": "false",
                "cognito:email_alias": "",
                "name": "Joey Kim",
                "cognito:phone_number_alias": "",
                "email": "<EMAIL>",
            },
            "validationData": {},
        },
        "response": {
            "autoConfirmUser": False,
            "autoVerifyEmail": False,
            "autoVerifyPhone": False,
        },
    }

    lambda_handler(e, None)


if __name__ == "__main__":
    main()
