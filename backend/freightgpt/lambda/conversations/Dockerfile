FROM common-lambda:latest

# Environment provided with --build_arg
ARG ENV_VALUE
ENV ENV=$ENV_VALUE

# Install lambda requirements.
COPY backend/freightgpt/lambda/conversations/requirements.txt .
RUN pip3 install -r requirements.txt

COPY machine_learning/freightgpt/langchain_events.py machine_learning/freightgpt/langchain_events.py
COPY machine_learning/freightgpt/constants.py machine_learning/freightgpt/constants.py
COPY backend/freightgpt/constants.py backend/freightgpt/constants.py
COPY backend/freightgpt/lambda/conversations/lambda_function.py .

CMD [ "truce/lambda_function.lambda_handler" ]
