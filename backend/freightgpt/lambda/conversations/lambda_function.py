import os

from backend.freightgpt.constants import CloudResources
from common import dynamodb_utils
from common import init_main
from machine_learning.freightgpt.langchain_events import (
    merge_and_filter_chat_history_with_events,
)

PROJECT_ROOT = init_main.initialize()

# Chats table and events table
resources = getattr(CloudResources, os.getenv("ENV", "DEV")).value
CHAT_TABLE_NAME = resources.CONVO_TABLE_NAME.value
EVENTS_TABLE_NAME = resources.EVENTS_TABLE_NAME.value

# Primary key and Sort key
PK = "user_id"
SK = "conv_id"  # Events table is indexed by conv_id.


def lambda_handler(event: dict, context) -> dict:
    """Handles the lambda function for retrieving chat history.

    Args:
        event (dict): The event data passed to the lambda function.
        context: The runtime information of the lambda function.

    Returns:
        dict: The response containing the status code and chat history.

    Example:
        >>> event = {
        ...     "user_id": "12345",
        ...     "conversation_id": "67890"
        ... }
        >>> lambda_handler(event, None)
        {
            "statusCode": 200,
            "body": [
                {
                    ...convo_history
                }
            ]
        }
    """
    status_code = 200
    user_id = event.get("user_id")
    conversation_id = event.get("conversation_id")
    delete = event.get("delete")

    # Chats table is indexed by user id and conversation id
    chat_table_key = {PK: user_id, SK: conversation_id}

    # Events table is indexed by conversation id
    events_table_key = {SK: conversation_id}

    try:
        # Conversation deletion
        if conversation_id and delete:
            dynamodb_utils.delete_item(CHAT_TABLE_NAME, chat_table_key)
            chat_history = []

        # If conversation id is provided, get that conversation and associated events.
        elif conversation_id:
            chat_params = {"table_name": CHAT_TABLE_NAME, "key": chat_table_key}
            events_params = {"table_name": EVENTS_TABLE_NAME, "key": events_table_key}

            # Access both tables simultaneously.
            chat, events = dynamodb_utils.thread_execute_dynamo_operations(
                [
                    (dynamodb_utils.read_item, chat_params),
                    (dynamodb_utils.read_item, events_params),
                ]
            )
            chat = {} if chat is None else chat
            events = {} if events is None else events

            # Merge chats and events history.
            clean_combined_history = merge_and_filter_chat_history_with_events(
                chat.get("History", []), events.get("Entries", [])
            )
            chat_history = [{"History": clean_combined_history}]

        # Return all conversation ids for a specified user_id
        else:
            chat_history = dynamodb_utils.get_items_with_primary_key(
                CHAT_TABLE_NAME, PK, user_id
            )
            chat_history = [
                {
                    "conv_id": item["conv_id"],
                    "summary": item.get("Summary", ""),
                    "creation_timestamp": item.get("CreationTimestamp", ""),
                }
                for item in chat_history
            ]

    except Exception as e:
        print(e)

        # Return an error response. Cannot provide exception in response or we may reveal sensitive information.
        # Exception can be checked in logs.
        chat_history = ["An error occurred, please try again later."]
        status_code = 500

    response = {
        "statusCode": status_code,
        "body": chat_history,
    }

    return response


# if __name__ == "__main__":
#     print(
#         lambda_handler(
#             {
#                 "user_id": "b5fb2072-da16-4b66-8c48-dc0bdc0273ec",
#                 "conversation_id": "f2a63071-2fc3-41b0-b5e4-47ef1a62c394",
#             },
#             None,
#         )
#     )
