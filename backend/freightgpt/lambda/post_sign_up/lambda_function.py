import datetime
import json
from typing import Dict
from typing import List

import pandas as pd

from common import google_sheets_utils
from common import mail

GSHEETS_UTILS = google_sheets_utils.GoogleSheetsUtils()
MAIL_API = mail.MailAPI(gmail=False)

# https://docs.google.com/spreadsheets/d/1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4/edit#gid=89101665
FEEDBACK_SHEET_ID = "1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4"
FEEDBACK_SHEET_NAME = "Prod Sign Up Tracker"


def lambda_handler(event: Dict[str, List[Dict[str, str]]], context) -> dict:
    """
    :param event: Signup event from SQS
    :param context: Ignored for now.
    :return: Object with status code and notification data under ['body'].
    """
    for record in event["Records"]:
        # Extract the body of the message
        message_data = json.loads(record["body"])
        if "password" in message_data:
            del message_data["password"]
        print(f"Received message: {message_data}")

        attributes = message_data.get("attributes", {})
        if not attributes or not isinstance(attributes, dict):
            raise Exception("Malformed request or userAttributes.")

        # Get user properties
        email = attributes.get("email", "")
        name = attributes.get("name", "")
        user_status = message_data.get("user_status", "")
        # Get identity provider if SSO
        identity_provider = message_data.get("identity_provider", "")
        # Optional properties (not available at trigger time for SSO)
        # TODO (P1): Find out how to fill these in for SSO

        company = attributes.get("custom:company", "")
        role = attributes.get("custom:customer_type", "")
        mc_number = attributes.get("custom:mc_number", "")
        referral_source = attributes.get("custom:referralSource", "")

        # Extract URL and parameters
        url = message_data.get("url", "")

        # Send email
        signup_datetime_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        MAIL_API.send_email_aws(
            frm="<EMAIL>",
            to=["<EMAIL>"],
            subject=f"[FreightGPT][New User][{name}][{email}] - {signup_datetime_str}",
            body=f"""
                You have received a new FreightGPT signup! User tracking sheet: https://docs.google.com/spreadsheets/d/1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4/edit#gid=89101665

                Email: {email}
                Name: {name}
                User Status: {user_status}
                Company: {company}
                Role: {role}
                MC Number: {mc_number}
                Identity Provider (SSO): {identity_provider}
                How Did You Hear About Us?: {referral_source}
                Source URL: {url}
            """,
            attachments=[],
        )

        # Update sheet
        sheet_upload_dict = {
            "Signup Date": signup_datetime_str,
            "Email": email,
            "Name": name,
            "User Status": user_status,
            "Company": company,
            "Role": role,
            "MC Number": mc_number,
            "Identity Provider (SSO)": identity_provider,
            "How Did You Hear About Us?": referral_source,
            "Source URL": url,
        }
        GSHEETS_UTILS.append_df_to_sheet(
            pd.DataFrame(sheet_upload_dict, index=[0]),
            FEEDBACK_SHEET_ID,
            FEEDBACK_SHEET_NAME,
        )

    return {
        "statusCode": 200,
        "body": json.dumps("Successfully processed SQS Signup event"),
    }
