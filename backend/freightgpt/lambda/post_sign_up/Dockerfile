FROM common-lambda:latest

# Filesystem Type - RO (Read Only) or RW (Read and Write)
ARG FS_TYPE
ENV FS_TYPE=$FS_TYPE

# Copy and install requirements.txt to environment
COPY backend/freightgpt/lambda/post_sign_up/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY backend/freightgpt/lambda/post_sign_up/lambda_function.py .

# Set CMD to handler
CMD [ "lambda_function.lambda_handler" ]
