# FreightGPT Backend

Backend infrastructure for the FreightGPT platform.

## Table of Contents
- [Server Introduction](#server)
- [Backend Components](#components)
- [Installation](#installation)
- [Deploy](#Deploy)
- [Running Server Locally / Tests](#local-server-and-tests)
- [Jobs](#jobs)
- [Lambdas](#lambdas)
- [Load Balancer](#load-balancer)


## Installation
Simply install the common library and the freightgpt requirements to run the server.

`pip install .`

`pip install -r machine_learning/freightgpt/requirements.txt`

Integration tests and the test script has its own set of requirements. Install these if required.

- `pip install -r backend/freightgpt/tests/requirements.txt`

## Server
The freightgpt server is a [FastAPI](https://fastapi.tiangolo.com/) / [Uvicorn](https://www.uvicorn.org/) application that hosts endpoints for all freightgpt functionality. The server uses the concept of asyncronicity in order to support performant event and token streaming through websocket.

Endpoints:
- `/health (GET)`: endpoint for health checks on server.
- `/new_session (POST)`: creates a new session, or an instance of a new langchain agent.
- `/feedback (POST)`: submits feedback on a response.
- `/close_sessions (POST)`: deletes created sessions from a node.
- `/chat_message (POST)`: runs a user query and streams back response.

### Adding new Endpoints
New endpoints will have to be configured through the command line in order to receive ConnectionIds (if required)
`aws apigatewayv2 get-integrations --api-id wps3vtux75`(Replace the last value with the appropriate gateway idif necessary)
`aws apigatewayv2 update-integration --integration-id <INSERT_ID> --api-id <INSERT_APP_ID> --request-parameters '"integration.request.header.connectionid"="context.connectionId"'` 
<INSERT_ID> should be replaced with the id of the endpoint that you can find from running the first command
<INSERT_APP_ID> should contain the original id of the api gateway, (e.g. wps3vtux75)

## Create New Endpoint
https://docs.google.com/document/d/1QaVUChUFRkVBM8kOo1P9AWovuRQr3B0iAvvPw1WVeKk/edit

For endpoints that require connection id:
aws apigatewayv2 update-integration --api-id wps3vtux75 --integration-id <integration id> --request-parameters '"integration.request.header.connectionid"="context.connectionId"'

## Components
The backend directory contains the following components:
```
.
├── jobs
│   ├── clean_session
│   └── cpm_pull
├── lambda
│   ├── conversations
│   └── waitlist_validator
├── main_server
│   └── load_balancer
└── tests
```

- `jobs/`:  Contains any background jobs or tasks that run asyncronously to the freightgpt server.
- `lambda/`: Any lambda functions for the freightgpt playform.
- `tests/`: Contains integration tests and scripts to run the server.
- `main_server`: Contains the main files for the server.


## Deploy
To deploy the freightgpt server, use the following command:

`./deploy/machine_learning/freightgpt[_dev].bat`

## Local Server and Tests

### Running server test script
To test the server, you may run the server test script
```
python backend/freightgpt/tests/backend_sim.py [OPTIONAL --dry_mode]
```

You send requests to the server by typing \<ENDPOINT\> \<BODY\>. Begin by making a call to the `/new_session` endpoint, no body is required for this endpoint. You must call `/new_session` at least once, and can call it whenever you want to create a new conversation. An example set of queries would be:
- `/new_session`
- `/chat_message Hello world!`
- `/new_session` (create new convo)
- `hello world!` (if no endpoint is provided, `/chat_message` is assumed.)
- `what is the cost from sf to la`
- `/feedback {'user_id':'blah', ...}`

If no endpoint is provided, `/chat_message` is assumed. If you would like to send raw data to the server instead of a message, you may pass in a json object instead of a text message.

After running the query, you should see events and tokens printed.

Another way to test the server is running the uvicorn handler:
```
python backend/freightgpt/main_server/uvicorn_handler.py
```

To establish a WebSocket connection with the server, you can use the following command in the command-line tool:
```
wscat -c wss://wps3vtux75.execute-api.us-east-1.amazonaws.com/beta-1
```

To get the connection ID for the WebSocket connection, you can send the following JSON payload to the WebSocket:

```json
{
  "action": "id"
}
```

To send a request using Postman, follow these steps:

1. Open Postman and create a new request.
2. Set the request method to GET/POST.
3. Enter the endpoint URL for the desired API endpoint.
4. Add the required headers to the request. Include the "connectionid" header with the appropriate value.
5. Set the request body according to the API endpoint requirements.
6. Click on the "Send" button to send the request.

After sending the request, you should see events and tokens printed.


### Hosting server on the web to test with frontend deployment
This requires an ngrok account, with which you should be able to claim a free static domain. This still works with the temp domain, but is more annoying.
You can sign up and set things up from [here](https://dashboard.ngrok.com/signup)
Follow their OS customized setup commands to install ngrok.
Then set your configuration with the security token they give you.
`ngrok config add-authtoken <TOKEN>`

If you didn't claim your static domain after signup and installation, you can claim it [here](https://dashboard.ngrok.com/cloud-edge/domains)

1. Start the ngrok command line tool to host the local port 8081 on the web.
`ngrok http http://localhost:8081` if you have no static domain
`ngrok http --domain=<STATIC DOMAIN> 8081` if you do

----------------------------------------------------------------------------------

You will only have to execute the next 4 instructions once if you use a static domain:

2. Copy the newly hosted ngrok url and go to [this AWS api gateway](https://us-east-1.console.aws.amazon.com/apigateway/main/apis/4mp7e4uxth/routes?api=4mp7e4uxth&region=us-east-1).

3. Navigate to the "Integration Request" tab, and edit the settings to use said URL as their endpoint for ONLY the following endpoints:
    - [ ] closeSession
    - [ ] newSessionDev
    - [ ] postMessageDev

4. Now click on "Deploy API" (big yellow button, top right), and create a new stage for yourself (or pick your existing one). Take note of this stage name.

5. Go to your [.env](../../frontend/freightgpt/web/.env) file, located in `frontend/freightgpt/web`, and create an entry as follows: `VUE_APP_STAGE_NAME=<YOUR_STAGE_NAME>`. If you don't have one, reach <NAME_EMAIL>.

----------------------------------------------------------------------------------

6. Start up your frontend as usual `npm run serve` from `frontend/freightgpt/web`
7. Edit the variable websocket_api inside of [async_stream_handler.py](../../machine_learning/freightgpt/async_stream_handler.py) to be `https://4mp7e4uxth.execute-api.us-east-1.amazonaws.com/<YOUR_STAGE_NAME>/`
8. Edit the IP address that the local backend is hosted on by editing the `host='0.0.0.0'` value in [uvicorn_handler.py](../../backend/freightgpt/main_server/uvicorn_handler.py)'s main function to instead be `host='localhost'`
9. Start up the server on a local port with `python ./backend/freightgpt/main_server/uvicorn_handler.py --env DEV --log_to_stdout`

TODO(P1): Add `--env LOCAL` option to have websocket_api and host be automatically set.

You should now have 3 terminals active for the frontend, backend, and ngrok tool.

Note that if you want to go back to using the dev backend from your local frontend deployment, you'll have to remove/comment out the VUE_APP_STAGE_NAME variable in your .env file.
And it should go without saying that the modifications to backend variables (host ip and websocket url) should NOT be committed or deployed.

### FreightGPT Integration Tests
A set of integration tests for freightgpt built using `pytest`, including our uvicorn server, our langchain agent, events, and cloud resource connections.

### Tests
- `uvicorn_dry`: test basic llm function, including truck cost, weather, and carrier queries, along with all fired events.
- `uvicorn_wet`: test more complex llm function with main query types, evaluating agent performance, memory, events, event and chat storage in the cloud, and the feedback endpoint.

### Run test
You can now run tests using the `pytest` command.
```
pytest -s backend/freightgpt/tests/integration_test_wet.py
```

### Run test group
If you simply want to run an individual test, you can do this by denoting the test marker (see `pytest.ini`).
```
pytest -s backend/freightgpt/tests/integration_test_wet.py -m <test_group i.e. basic_dry>
```

## Jobs

### Clean Session Job
Cleans expired sessions on freightgpt nodes to preserve node RAM and manages session entries in database concurrently.

### Cloud Resources:
- Prod sessions table: https://us-east-1.console.aws.amazon.com/dynamodbv2/home?region=us-east-1#item-explorer?operation=SCAN&table=SessionStoreProd
- Dev sessions table: https://us-east-1.console.aws.amazon.com/dynamodbv2/home?region=us-east-1#item-explorer?operation=SCAN&table=SessionStoreDev
- Freightgpt cluster: https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/freightgpt/services?region=us-east-1

### Procedure:
1. Obtains ipv4 targets of all nodes currently running.
2. Obtains stale sessions in dynamo (sessions that have been untouched for 1 hour)
3. Makes post requests to all nodes to delete the stale session if present on the node.
4. Clears stale sessions from dynamodb

### Deploy:
From truce directory:

`./deploy/machine_learning/jobs/session_cleaner[_dev].bat`

### Scheduled Task Info:
- Scheduled Tasks: https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/freightgpt/scheduled-tasks?region=us-east-1
- Task definition dev: https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/session_clean_dev?status=ACTIVE&region=us-east-1
- Task definition prod: https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/session_clean_job/1/containers?region=us-east-1
- Runs every 4 hours.

## Lambda

### Conversation Lambda
Responsible for loading conversations in the frontend.

### Cloud Resources
- Prod Convo Table: https://us-east-1.console.aws.amazon.com/dynamodbv2/home?region=us-east-1#table?name=ChatStoreProd
- Dev Convo Table: https://us-east-1.console.aws.amazon.com/dynamodbv2/home?region=us-east-1#table?name=ChatStoreDev

### Event Parameters
- `user_id`: the id of the user.
- `conversation_id (str)`: the id of the conversation to pull.
- `delete (bool)`: if provided, will delete the conversation.

### Rules:
- If both `user_id` and `conversation_id` are provided, will pull just this conversation
- If only `user_id` is provided, will pull all conversations for this user.
- If `delete` is provided along with a `conversation_id`, then this conversation will be deleted.

### Deploy:

`./deploy/machine_learning/lambda/conversation_lambda[_dev].bat`

## Load Balancer
Custom layer 7 (application layer) load balancer designed to work with Amazon ECS services.
Replacement of AWS ALB to give greater control over hashing / cookie. Written in Node JS.

### Dependencies
- `node==v14.21.3`
- `npm==6.14.18`

### Setup
Install packages:<br>
- `npm install`
- `npm install -g typescript`<br>
- `npm install -g ts-node`<br>
- `npm install -g pm2`

### Load Balancer Info:
- https://docs.aws.amazon.com/elasticloadbalancing/latest/application/introduction.html
- https://www.imperva.com/learn/application-security/osi-model/

A load balancer consists of three parts:
- A health check
- A load balancing algorithm
- A transmitted cookie for the client "stickiness" based on a hash.

We are writing an application load balancer, which means we work on OSI Layer 7, or the application layer.
This means we will perform our load balancing / hashing on anything in the HTTP headers of the request.

### Task Monitor
The task monitor operates with the following steps:
1. Perform health check on all servers at regularly scheduled intervals. Updates `servers.json` with healthy servers if there is a difference.
2. If no servers are healthy, goes into scan mode.
3. During scan mode, query the `freightgpt` cluster and appropriate service for new task IPs.
4. If IPs are found and they pass a health check, write new healthy servers to `servers.json`.
5. Switch back to regularly scheduled health check (return to step 1).

### Load Balancer
The load balancer operates with the following steps:
0. Listen to the `servers.json` file. If there is a change, update available servers.
1. On receiving a request, check if it contains a cookie in the headers. If it does, then simply use the hash to pick a target server and proxy the request.
2. If it does not have a cookie, then hash on connection id, attach this to the headers, and proxy the request.

### Running locally
You can run files locally with ts-node.<br>

Running load balancer:<br>
- `ts-node load_balancer.ts`

Running task_monitor.ts<br>
- `ts-node task_monitor.ts`

Note that the task_monitor won't work unless in the FreightGPT VPC with access to the ECS cluster.

### Running entire project
We use pm2 to manage server processes. This will run the task monitor and load balancer simultaneously:

- To run: `pm2 start ecosystem.config.js --env development | production`
- To get processes: `pm2 list`
- To get logs: `pm2 logs <PID>`
- To flush logs: `pm2 flush`
- To stop server: `pm2 stop all | <PID>`

### Deploying to fresh EC2 instance
Ask @vivvyk for load balancer pem key. Run from `~/truce` directory, or root directory of project<br>
`./deploy/load_balancer.bat <ECS_URL>`
