import asyncio
import json
import time

import boto3
import websockets
from botocore.exceptions import ClientError
from botocore.exceptions import NoCredentialsError

s3 = boto3.client("s3")
security_token = "65a09>a14?-d5^22-4#ef2-9$1e1-a8@8d31^d1&ff0e"

bucket_name = "freightgpt7e5e8d79022340cb8d28bac25e79b88e211850-dev"
file_name = "prepopulated_data.json"

websocket_uri = "wss://wps3vtux75.execute-api.us-east-1.amazonaws.com/beta-1/"


async def query_public_socket(message):
    async with websockets.connect(websocket_uri) as websocket:
        message_object = {
            "action": "postMessageDev",
            "message": message,
            "isPublicChat": 1,
            "bypass_ip_limit": 1,
            "bypass_ip_token": "65a09>a14?-d5^22-4#ef2-9$1e1-a8@8d31^d1&ff0e",
        }
        timeout = time.time() + 120
        result = {
            "events": [],
            "tokens": "",
            "prompt": message,
        }
        try:
            await websocket.send(json.dumps(message_object))
            while True:
                response = json.loads(await websocket.recv())
                if timeout < time.time():
                    print("Timed out after 120 seconds.")
                    break
                if "close" in response:
                    print("Received close message. Closing connection.")
                    break
                elif "event" in response:
                    result["events"].append(response["event"])
                elif "token" in response:
                    result["tokens"] = result["tokens"] + response["token"]
            print("Resulting tokens:", result["tokens"])
            print("-" * 50)
            print()
            return result
        except Exception as e:
            print("error", e)


def upload_to_s3(object):
    try:
        # Initialize the S3 client
        s3_client = boto3.client("s3")
        # Convert the JSON object to a string
        json_data = json.dumps(object)
        # Upload the JSON string as a file to S3
        s3_client.put_object(Bucket=bucket_name, Key=file_name, Body=json_data)
        print(f"File {file_name} uploaded to bucket {bucket_name}.")
    except NoCredentialsError:
        print("Credentials not available.")
    except ClientError as e:
        print(f"Failed to upload file to S3: {e}")


if __name__ == "__main__":
    demo_prompts = {
        "ReeferRatesDemo": [
            "What is the reefer rate from Chicago, IL to Miami, FL?",
            "Reefer price from Atlanta, GA to Los Angeles, CA",
            "Reefer rate from Dallas, TX to New York, NY",
        ],
        "DryVanRatesDemo": [
            "What is the dry van rate from Chicago, IL to Miami, FL?",
            "Dry Van price from Atlanta, GA to Los Angeles, CA",
            "Dry Van rate from Dallas, TX to New York, NY",
        ],
        "MCLookupDemo": [
            "MC579500",
            "MC775710",
        ],
        "DOTLookupDemo": [
            "DOT 54283",
            "DOT 2271487",
        ],
        "CarrierSearchDemo": [
            "Find me Los Angeles, CA to Phoenix, AZ carriers with equipment type reefer",
            "Find New York, NY to Boston, MA flatbed carriers with a max fleet size of 60",
            "Carriers near LA with min insurance coverage of $1,000",
        ],
    }
    demo_responses = {}
    for prompt_category, prompt_list in demo_prompts.items():
        demo_responses[prompt_category] = []
        for prompt in prompt_list:
            print("getting response for prompt: ", prompt)
            demo_response = asyncio.run(query_public_socket(prompt))
            demo_responses[prompt_category].append(demo_response)
    print(demo_responses.keys())
    for val in demo_responses.values():
        print(len(val))
    upload_to_s3(demo_responses)
