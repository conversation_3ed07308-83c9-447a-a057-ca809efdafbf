import argparse
import datetime
import json
import logging
from decimal import Decimal
from typing import List
from typing import Tuple

import requests

from backend.freightgpt.constants import CloudResources
from backend.freightgpt.constants import FREIGHTGPT_CLUSTER_NAME
from common import dynamodb_utils
from common import ecr_job_util
from common import ecs_task_util
from common import init_main

PROJECT_ROOT = init_main.initialize()

ENDPOINT = "close_sessions"
JOB_NAME = "Node Session Clean"

parser = argparse.ArgumentParser()
parser.add_argument(
    "--env",
    type=lambda x: x.upper(),
    choices=["LOCAL", "DEV", "PROD"],
    default="DEV",
    help="Run job against local, dev or prod backend resources",
)
parser.add_argument(
    "--send_mail",
    action="store_true",
    default=False,
    help="Send a failure or success email on node update.",
)
parser.add_argument(
    "--port",
    type=int,
    default=8081,
    help="Optional port number to use",
)
args = parser.parse_args()


def get_stale_sessions(
    table_name: str, session_ttl_hours: int = 1
) -> List[Tuple[str, str]]:
    # Calculate the cutoff timestamp
    """
    Gets stale sessions from given DynamoDB sessions table.

    Args:
        table_name (str): The name of the DynamoDB table.
        session_ttl_hours (int, optional): The time to live for a session in hours. Default is 1 hour.

    Returns:
        list: A list of tuples containing the user_id and session_id of each stale session.

    Examples:
        >>> get_stale_sessions('session_table', 2)
        [('user1', 'session1'), ('user2', 'session2')]
    """

    # Compute staleness cutoff
    cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=session_ttl_hours)
    cutoff_timestamp = cutoff_time.timestamp()
    logging.info(f"Finding stale sessions before {cutoff_timestamp}")

    # Initialize the scan parameters
    scan_params = {
        "FilterExpression": "last_update_time < :val",
        "ExpressionAttributeValues": {":val": Decimal(cutoff_timestamp)},
    }

    # Scan
    stale_sessions = dynamodb_utils.scan_table(table_name, scan_params)

    return [
        (session.get("user_id"), session.get("session_id"))
        for session in stale_sessions
    ]


def send_close_session_request(
    target_url: str, stale_sessions: List[Tuple[str, str]]
) -> None:
    """
    Send a request to the target URL / node to clear all its sessions.

    Args:
        target_url (str): Fully resolved private IP w/ endpoint: http://<IP>:<PORT>/<ENDPOINT>.
        stale_sessions (list): A list of tuples containing user_id, session ids of stale sessions.

    Raises:
        AssertionError: If the response status code is not 200.
    """
    logging.info(f"Sending clear request to target {target_url}")
    response = requests.post(
        target_url,
        data=json.dumps(
            {"sessions_to_close": [session_id for _, session_id in stale_sessions]}
        ),
        headers={"Content-Type": "application/json"},
    )
    assert (
        response.status_code == 200
    ), f"Expected status code 200, but got {response.status_code}"


def clear_dynamo_stale_sessions(
    table_name: str, stale_sessions: List[Tuple[str, str]]
) -> None:
    """
    Clears stale sessions from a DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        stale_sessions (list): A list of tuples containing the user ID (PK) and session ID (SK) of the stale sessions.
    """
    logging.info("Clearing sessions from dynamo.")
    deletion_keys = [
        {"user_id": user_id, "session_id": session_id}
        for user_id, session_id in stale_sessions
    ]
    dynamodb_utils.batch_delete_items(
        table_name, deletion_keys, "user_id", sort_keys=["session_id"]
    )


def main():
    (job_start_time, job_start_timestr_central, mail_api) = ecr_job_util.set_up(
        JOB_NAME, log_to_stdout=True
    )

    # Select appropriate service name
    is_dev_env = args.env != "PROD"
    is_local_env = args.env == "LOCAL"
    resources = getattr(CloudResources, args.env if not is_local_env else "DEV").value
    service_name = resources.SERVICE_NAME.value
    sessions_table_name = resources.SESSIONS_TABLE_NAME.value

    # Obtain targets
    targets = ecs_task_util.list_private_ipv4s(
        cluster_name=FREIGHTGPT_CLUSTER_NAME,
        service_name=service_name,
        endpoint_details=(args.port, ENDPOINT),
        is_local=is_local_env,
    )

    # Obtain stale_sessions
    stale_sessions = get_stale_sessions(sessions_table_name)
    logging.info(f"Discovered {len(stale_sessions)} stale sessions.")

    # Send POST request to each target server.
    successful_clears = []
    failed_clears = []
    for target in targets:
        try:
            send_close_session_request(target, stale_sessions)
            successful_clears.append(target.rsplit("/", 1)[0])
        except Exception as e:
            failed_clears.append((target.rsplit("/", 1)[0], e))

    # Clear from dynamo
    try:
        clear_dynamo_stale_sessions(sessions_table_name, stale_sessions)
    except Exception as e:
        ecr_job_util.failure_email(
            e=e,
            mail_api=mail_api,
            dev=is_dev_env,
            job_name=JOB_NAME,
            job_start_time=job_start_time,
            job_start_timestr_central=job_start_timestr_central,
            mail_header=" Failed to update Dynamo sessions table.",
        )

    # Send mail with status.
    if args.send_mail:
        if len(failed_clears) == 0:
            ecr_job_util.success_email(
                mail_api=mail_api,
                dev=is_dev_env,
                job_name=JOB_NAME,
                job_start_time=job_start_time,
                job_start_timestr_central=job_start_timestr_central,
                mail_header=" Successfully cleared sessions on all nodes.",
                mail_body=f"Updated file on targets: {' '.join(successful_clears)}",
            )
        else:
            ecr_job_util.failure_email(
                e=failed_clears[0][1],  # Just grab first exception.
                mail_api=mail_api,
                dev=is_dev_env,
                job_name=JOB_NAME,
                job_start_time=job_start_time,
                job_start_timestr_central=job_start_timestr_central,
                mail_header=" Failed to clear sessions on all nodes.",
                mail_body=f"Failed on targets: {' '.join([f[0] for f in failed_clears])}",
            )


if __name__ == "__main__":
    main()
