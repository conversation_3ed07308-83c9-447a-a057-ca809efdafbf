# Daily CPM Pull Job

Pulls a new daily CPM file on freightgpt nodes.

### Cloud Resources:
- Freightgpt cluster: https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/freightgpt/services?region=us-east-1

### Internal resources:
- Uvicorn handler: https://github.com/truce-logistics/truce/blob/dev/machine_learning/handlers/uvicorn_handler.py

### Procedure:
1. Obtains ipv4 targets of all nodes currently running.
2. Sends post request to all nodes to pull a new daily CPM file.

### Deploy:
From truce directory: <br />
```sudo ./deploy/machine_learning/jobs/cpm_pull[_dev].bat```

### Scheduled Task Info:
- Scheduled Tasks: https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/freightgpt/scheduled-tasks?region=us-east-1
- Task definition dev: https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/cpm_pull_job_dev?status=ACTIVE&region=us-east-1
- Task definition prod: https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions/cpm_pull_job?status=ACTIVE&region=us-east-1
- Runs every day at midnight CST.
