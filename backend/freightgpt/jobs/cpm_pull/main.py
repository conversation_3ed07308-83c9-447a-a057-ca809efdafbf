import argparse

from common import ecr_job_util
from common import init_main

PROJECT_ROOT = init_main.initialize()

from common import ecs_task_util
from backend.freightgpt.constants import FREIGHTGPT_CLUSTER_NAME
from backend.freightgpt.constants import CloudResources
import requests
import logging

ENDPOINT = "pull_cpm_file"
JOB_NAME = "Daily Node CPM File Pull"

parser = argparse.ArgumentParser()
parser.add_argument(
    "--env",
    type=lambda x: x.upper(),
    choices=["LOCAL", "DEV", "PROD"],
    default="DEV",
    help="Run job against local, dev or prod backend resources",
)
parser.add_argument(
    "--send_mail",
    action="store_true",
    default=False,
    help="Send a failure or success email on node update.",
)
parser.add_argument(
    "--port",
    type=int,
    default=8081,
    help="Optional port number to use",
)
args = parser.parse_args()


def send_pull_cpm_request(target_url: str) -> None:
    """
    Send a request to the target URL / node to pull a CPM file.

    Args:
        target_url (str): Fully resolved private IP w/ endpoint: http://<IP>:<PORT>/<ENDPOINT>.

    Raises:
        AssertionError: If the response status code is not 200.
    """
    logging.info(f"Sending request to target {target_url} to pull CPM file.")
    response = requests.post(target_url)
    assert (
        response.status_code == 200
    ), f"Expected status code 200, but got {response.status_code}"


def main():
    (job_start_time, job_start_timestr_central, mail_api) = ecr_job_util.set_up(
        JOB_NAME, log_to_stdout=True
    )

    # Select appropriate service name
    is_dev_env = args.env != "PROD"
    is_local_env = args.env == "LOCAL"
    resources = getattr(CloudResources, args.env if not is_local_env else "DEV").value
    service_name = resources.SERVICE_NAME.value

    # Obtain targets
    targets = ecs_task_util.list_private_ipv4s(
        cluster_name=FREIGHTGPT_CLUSTER_NAME,
        service_name=service_name,
        endpoint_details=(args.port, ENDPOINT),
        is_local=is_local_env,
    )

    # Send POST request to each target server.
    successful_pulls = []
    failed_pulls = []
    for target in targets:
        try:
            send_pull_cpm_request(target)
            successful_pulls.append(target.rsplit("/", 1)[0])
        except Exception as e:
            # Pull failed, non 200 response code.
            failed_pulls.append((target.rsplit("/", 1)[0], e))

    # Send mail with status.
    if args.send_mail:
        if len(failed_pulls) == 0:
            ecr_job_util.success_email(
                mail_api=mail_api,
                dev=is_dev_env,
                job_name=JOB_NAME,
                job_start_time=job_start_time,
                job_start_timestr_central=job_start_timestr_central,
                mail_header=" Successfully pulled new CPM file on all nodes.",
                mail_body=f"Updated file on targets: {' '.join(successful_pulls)}",
            )
        else:
            ecr_job_util.failure_email(
                e=failed_pulls[0][1],  # Just grab first exception.
                mail_api=mail_api,
                dev=is_dev_env,
                job_name=JOB_NAME,
                job_start_time=job_start_time,
                job_start_timestr_central=job_start_timestr_central,
                mail_header=" Failed to pull new CPM file on all nodes.",
                mail_body=f"Failed on targets: {' '.join([f[0] for f in failed_pulls])}",
            )


if __name__ == "__main__":
    main()
