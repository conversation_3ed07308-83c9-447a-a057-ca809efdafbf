import os
from enum import Enum

# Conditional import so that containers that use the constants do not have to have
# the dependency fastapi installed.
try:
    from fastapi import status as fastapi_status

    STATUS_OK = fastapi_status.HTTP_200_OK
    STATUS_ERROR = fastapi_status.HTTP_500_INTERNAL_SERVER_ERROR
except ImportError:
    STATUS_OK = 200  # Example fallback
    STATUS_ERROR = 500

# Cluster constants
# Cluster: https://us-east-1.console.aws.amazon.com/ecs/v2/clusters/freightgpt/services?region=us-east-1
FREIGHTGPT_CLUSTER_NAME = "freightgpt"

# Server response dicts
HEADERS = {
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
}

SUCCESS_RESPONSE = {
    "status_code": STATUS_OK,
    "content": {"status": "success"},
    "headers": HEADERS,
}
FAILURE_RESPONSE = {
    "status_code": STATUS_ERROR,
    "content": {
        "status": "failure",
    },  # Replace description_of_error with the actual error description.
    "headers": HEADERS,
}

# Feedback params
FEEDBACK_EMAIL_LIST = ["<EMAIL>"]


# Langchain cloud resources
class CloudResources(Enum):
    class TEST(Enum):
        CONVO_TABLE_NAME = "ChatStoreTest"
        SESSIONS_TABLE_NAME = "SessionStoreTest"
        EVENTS_TABLE_NAME = "EventStoreTest"
        LANGSMITH_PROJECT = "freightgpt-test"
        CLOUDWATCH_GROUP = "freightgpt-test"
        IP_LIMIT_TABLE_NAME = "PublicChatIPsDev"
        LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY_TEST")

    class DEV(Enum):
        SERVICE_NAME = "freightgpt-service-alb-dev"
        CONVO_TABLE_NAME = "ChatStoreDev"
        SESSIONS_TABLE_NAME = "SessionStoreDev"
        EVENTS_TABLE_NAME = "EventStoreDev"
        LANGSMITH_PROJECT = "freightgpt-dev"
        CLOUDWATCH_GROUP = "freightgpt-dev"
        IP_LIMIT_TABLE_NAME = "PublicChatIPsDev"
        LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY_DEV")

    class PROD(Enum):
        SERVICE_NAME = "freightgpt-service-alb"
        CONVO_TABLE_NAME = "ChatStoreProd"
        SESSIONS_TABLE_NAME = "SessionStoreProd"
        EVENTS_TABLE_NAME = "EventStoreProd"
        LANGSMITH_PROJECT = "freightgpt-prod"
        CLOUDWATCH_GROUP = "freightgpt-prod"
        IP_LIMIT_TABLE_NAME = "PublicChatIPs"
        LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY_PROD")

    class PUBLIC(Enum):
        SERVICE_NAME = "freightgpt-service-alb"
        CONVO_TABLE_NAME = "ChatStoreProd"
        SESSIONS_TABLE_NAME = "SessionStoreProd"
        EVENTS_TABLE_NAME = "EventStoreProd"
        LANGSMITH_PROJECT = "freightgpt-public"
        CLOUDWATCH_GROUP = "freightgpt-prod"
        IP_LIMIT_TABLE_NAME = "PublicChatIPs"
        LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY_PUBLIC")


# IP limit constants
MAX_HITS = 5
MAX_HOURS = 24
DATE_FORMAT = "%Y-%m-%dT%H:%M:%S.%f%z"
SECONDS_PER_HOUR = 3600

# Default events
SESSION_FAILURE_EVENT = {"event": {"name": "session_status", "status": "FAILURE"}}
MAX_LIMIT_EVENT = {"event": {"type": "error", "data": "max limit reached"}}

# IP Bypass Token
IP_LIMIT_BYPASS_TOKEN = "65a09>a14?-d5^22-4#ef2-9$1e1-a8@8d31^d1&ff0e"
