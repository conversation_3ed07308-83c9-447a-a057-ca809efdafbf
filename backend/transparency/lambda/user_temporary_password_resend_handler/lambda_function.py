import boto3
from botocore.exceptions import ClientError

TRUCE_USER_POOL = "us-east-1_PJgJ5RUP8"


def lambda_handler(event, context):
    dynamodb = boto3.resource("dynamodb")
    cognito = boto3.client("cognito-idp")

    # get the shipper business unit for the provided email
    SBU_TABLE = dynamodb.Table("GetShipperBusinessUnitDev")
    user_email_with_whitespace = str(event["email"]).lower()
    user_email = "".join(user_email_with_whitespace.split())

    dynamodb_response = SBU_TABLE.get_item(Key={"email": user_email})

    # Only re-sending email if valid user
    if dynamodb_response.get("Item", None) is not None:
        try:
            # Connect to cognito to resend temporary password
            # cognito_response
            _ = cognito.admin_create_user(
                UserPoolId=TRUCE_USER_POOL,
                Username=dynamodb_response["Item"]["email"],
                MessageAction="RESEND",
                DesiredDeliveryMediums=["EMAIL"],
            )
            return {"statusCode": 200, "body": "Success"}
        except ClientError:
            # If user is not in FORCE_PASSWORD_CHANGE state, then forbid email resend
            return {"statusCode": 403, "body": "Forbidden"}

    return {"statusCode": 500, "body": "Error"}
