import copy
import json

import boto3
from botocore.exceptions import ClientError
from query_generator import QueryGeneratorGet
from query_generator import QueryGeneratorPut
from query_generator import QueryGeneratorUpdate

from common import init_main
from common.query_utils import format_rds_response
from common.query_utils import ThreadQuery

PROJECT_ROOT = init_main.initialize()


def send_email(client, toAddresses, subject, message, bccAddresses=None):
    bccAddresses = [] if bccAddresses is None else bccAddresses
    response = client.send_email(
        Source="<EMAIL>",
        Destination={"ToAddresses": toAddresses, "BccAddresses": bccAddresses},
        Message={"Subject": {"Data": subject}, "Body": {"Html": {"Data": message}}},
        ReplyToAddresses=["<EMAIL>"],
    )

    return response


def get_subscription_status(dynamodb, email, is_dev_env):
    table_name = (
        "NotificationSubscribersDev" if is_dev_env else "NotificationSubscribers"
    )
    SBU_TABLE = dynamodb.Table(table_name)
    response = SBU_TABLE.scan()

    for sub in response["Items"]:
        if sub["email"] == email:
            return sub

    return None


def update_notification_subscription_status(
    dynamodb, email, notif_type, value, is_dev_env, shipper_id
):
    table_name = (
        "NotificationSubscribersDev" if is_dev_env else "NotificationSubscribers"
    )
    SBU_TABLE = dynamodb.Table(table_name)
    try:
        item = SBU_TABLE.get_item(Key={"email": email})
        if notif_type in item["Item"]:
            response = SBU_TABLE.update_item(
                Key={"email": email},
                UpdateExpression=f"SET {notif_type}.subscribed=:s",
                ExpressionAttributeValues={":s": bool(value)},
                ReturnValues="UPDATED_NEW",
            )
        else:
            response = SBU_TABLE.update_item(
                Key={"email": email},
                UpdateExpression=f"SET {notif_type}=:notif",
                ExpressionAttributeValues={
                    ":notif": {
                        "id": shipper_id,
                        "subscribed": True,
                        "frequency": "0,1,2,3,4",
                    }
                },
                ReturnValues="UPDATED_NEW",
            )
    except ClientError as err:
        print(err.response["Error"]["Code"], err.response["Error"]["Message"])
        raise
    except Exception as e:
        print("Exception: ", e)
    else:
        return response["Attributes"]


def create_new_subscriber(
    dynamodb, email, notif_type, shipper_id, is_dev_env, customer_type
):
    table_name = (
        "NotificationSubscribersDev" if is_dev_env else "NotificationSubscribers"
    )
    SBU_TABLE = dynamodb.Table(table_name)

    try:
        response = SBU_TABLE.put_item(
            Item={
                "email": email,
                notif_type: {
                    "id": shipper_id,
                    "subscribed": True,
                    "frequency": "0,1,2,3,4",
                },
                "customer_type": customer_type,
            }
        )
    except ClientError as err:
        raise err
    else:
        return response


def update_notification_subscription_frequency(
    dynamodb, email, notif_type, value, is_dev_env
):
    table_name = (
        "NotificationSubscribersDev" if is_dev_env else "NotificationSubscribers"
    )
    SBU_TABLE = dynamodb.Table(table_name)

    try:
        response = SBU_TABLE.update_item(
            Key={"email": email},
            UpdateExpression=f"set {notif_type}.frequency=:s",
            ExpressionAttributeValues={":s": value},
            ReturnValues="UPDATED_NEW",
        )
    except ClientError as err:
        print(err.response["Error"]["Code"], err.response["Error"]["Message"])
        raise
    else:
        return response["Attributes"]


def error_exception(response_template, e, status_code):
    response = copy.deepcopy(response_template)
    response["statusCode"] = status_code
    response["body"] = str(e)
    return response


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    :param event: Frontend parameter with notifications query_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and notification data under ['body']['records'].
    """

    client = boto3.client("rds-data")
    email_client = boto3.client("ses")
    dynamodb = boto3.resource("dynamodb")

    query_params = json.loads(event.get("body", {})).get("query_params")
    path = event["path"]
    is_dev_env = query_params["is_dev_env"]
    required_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,PUT",
    }

    response = {
        "statusCode": 200,
        "headers": required_headers,
        "body": "Default Response",
    }

    if path == "/putNotification":
        try:
            targets = query_params.get("targets")

            qg = QueryGeneratorPut(query_params)

            notification_thread = ThreadQuery(
                name="insert", client=client, args=(qg.insert_notification(),)
            )

            notification_thread.start()

            if query_params.get("email"):
                send_email(
                    email_client,
                    targets,
                    query_params.get("subject"),
                    query_params.get("message"),
                    bccAddresses=query_params.get("bcc"),
                )

            notification_thread.join()

        except Exception as e:
            return error_exception(response, e, 404)

        response["body"] = f"notification_{qg.id}"

    elif path == "/getNotification":
        try:
            qg = QueryGeneratorGet(query_params)

            get_notifs_thread = ThreadQuery(
                name="gn",
                client=client,
                args=(
                    qg.get_new_notifications()
                    if query_params.get("new", False)
                    else qg.get_notifications(),
                ),
            )
            get_notifs_thread.start()

            notifs = format_rds_response(get_notifs_thread.join())
        except Exception as e:
            return error_exception(response, e, 404)

        response["body"] = json.dumps({"records": notifs})

    elif path == "/updateNotificationStatus":
        try:
            qg = QueryGeneratorUpdate(query_params)
            update_notif_thread = ThreadQuery(
                name="un", client=client, args=(qg.update_status(),)
            )
            update_notif_thread.start()
            update_notif_thread.join()
        except Exception as e:
            return error_exception(response, e, 404)

    elif path == "/getSubscriptionStatus":
        try:
            email = query_params.get("email")
            sub_status = get_subscription_status(dynamodb, email, is_dev_env)
        except Exception as e:
            return error_exception(response, e, 404)

        response["body"] = json.dumps(sub_status)

    elif path == "/updateSubscriptionStatus":
        try:
            email = query_params.get("email")
            notification_type = query_params.get("notification_type")
            sub_status = query_params.get("sub_status")
            shipper_id = query_params.get("shipperId")
            update_notification_subscription_status(
                dynamodb, email, notification_type, sub_status, is_dev_env, shipper_id
            )
        except Exception as e:
            return error_exception(response, e, 404)

    elif path == "/createNewSubscriber":
        try:
            email = query_params.get("email")
            notification_type = query_params.get("notification_type")
            shipper_id = query_params.get("shipperId")
            customer_type = query_params.get("customerType")
            create_new_subscriber(
                dynamodb,
                email,
                notification_type,
                shipper_id,
                is_dev_env,
                customer_type,
            )
        except Exception as e:
            return error_exception(response, e, 404)

    elif path == "/updateSubscriptionFrequency":
        try:
            email = query_params.get("email")
            notification_type = query_params.get("notification_type")
            frequency = query_params.get("frequency")
            update_notification_subscription_frequency(
                dynamodb, email, notification_type, frequency, is_dev_env
            )
        except Exception as e:
            return error_exception(response, e, 404)

    return response


def main():
    e = {
        "path": "/putNotification",
        "body": json.dumps(
            {
                "query_params": {
                    "targets": ["<EMAIL>"],
                    "email": 0,
                    "message": "test",
                    "subject": "[Notifications][Dev]Truce High Margin Dev Report - 01/30/23",
                    "sender": "high_margin_notifier",
                    "type": "insight",
                    "is_dev_env": True,
                    "bcc": ["<EMAIL>", "<EMAIL>"],
                }
            }
        ),
    }

    # e = {
    #     "path": "/updateSubscriptionStatus",
    #     "body": json.dumps(
    #         {
    #             "query_params": {"email": "<EMAIL>", "notification_type": "customer_direct", "sub_status": False, "shipperId": "4464e2b8-dd29-4572-8255-b07dcda25af9", "is_dev_env": 1}
    #         }
    #     )
    # }

    lambda_handler(e, None)


if __name__ == "__main__":
    main()
