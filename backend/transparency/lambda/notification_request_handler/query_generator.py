# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.
import datetime
import uuid


class QueryGeneratorPut:
    def __init__(self, query_params):
        # Put parameters
        self.sender = query_params.get("sender", None)
        self.email_status = query_params.get("email", False)
        self.subject = query_params.get("subject", "")
        self.message = query_params.get("message", "")
        self.type = query_params.get("type", "message")
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"
        self.targets = ",".join(query_params.get("targets", []))

    def insert_notification(self):
        self.id = uuid.uuid4()
        creationDate = datetime.datetime.now(datetime.timezone.utc).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        status = "NEW"

        insert = f"""insert into {self.db}.notifications values (
                     '{self.id}',
                     '{self.targets}',
                     '{self.sender}',
                     '{creationDate}',
                     '1999-01-01 00:00:00',
                     '{status}',
                      {int(self.email_status)},
                     '{self.subject}',
                     '{self.message}',
                     0
                   );"""
        return insert


class QueryGeneratorGet:
    def __init__(self, query_params):
        # Get parameters
        self.email = query_params.get("email", None)
        self.start_date = query_params.get("start_date", None)
        self.end_date = query_params.get("end_date", None)
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

    def get_new_notifications(self):
        return f"""
                   SELECT * FROM {self.db}.notifications
                   WHERE target = '{self.email}'
                   AND status = 'NEW'
                   AND loadInWeb <> false
                   ORDER BY notificationCreationDate DESC;
                """

    def get_notifications(self):
        return f"""
                   SELECT * FROM {self.db}.notifications
                   WHERE target = '{self.email}'
                   AND notificationCreationDate >= '{self.start_date}'
                   AND notificationCreationDate <= '{self.end_date}'
                   AND loadInWeb <> false
                   ORDER BY notificationCreationDate DESC;
                """


class QueryGeneratorUpdate:
    def __init__(self, query_params):
        self.notif_list = f"{'(' + str(query_params['notification_ids'])[1:-1] + ')'}"
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

    def update_status(self):
        update = f"""
                    UPDATE {self.db}.notifications
                    SET status = 'ACCESSED', notificationAccessDate = NOW()
                    WHERE notificationId in {self.notif_list};
                  """
        return update
