FROM common-lambda:latest

# Copy and install requirements.txt to environment
COPY backend/transparency/lambda/notification_request_handler/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY backend/transparency/lambda/notification_request_handler/lambda_function.py .
COPY backend/transparency/lambda/notification_request_handler/query_generator.py .

# Set CMD to handler
CMD [ "lambda_function.lambda_handler" ]
