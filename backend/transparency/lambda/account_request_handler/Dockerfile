FROM common-lambda:latest

# Install lambda requirements.
COPY backend/transparency/lambda/account_request_handler/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY backend/transparency/lambda/account_request_handler/lambda_function.py .
COPY backend/transparency/lambda/account_request_handler/query_generator.py .

# Set CMD to handler
CMD [ "truce/lambda_function.lambda_handler" ]
