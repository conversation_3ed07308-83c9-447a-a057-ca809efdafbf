# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.


class QueryGenerator:
    def __init__(self, query_params):
        self.email = query_params.get("email", None)
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"
        self.first_name = query_params.get("first_name", None)
        self.last_name = query_params.get("last_name", None)
        self.email = query_params.get("email", None)
        self.phone_number = query_params.get("phone_number", None)
        self.aggregation_start_date = query_params.get("aggregation_start_date", None)
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"
        self.new_config = query_params.get("new_config", "{}")

    def get_account_details(self):
        return f"""
                   SELECT first_name, last_name, email, phone_number, config FROM {self.db}.accounts
                   WHERE email = '{self.email}';
                """

    def get_account_config(self):
        return f"""
                    SELECT config FROM {self.db}.accounts
                    WHERE email = '{self.email}';
                """

    def update_account_config(self):
        return f"""
                    UPDATE {self.db}.accounts
                    SET config = '{self.new_config}'
                    WHERE email = '{self.email}';
                """

    def insert_account(self):
        insert = f"""
                    INSERT INTO {self.db}.accounts
                    (first_name, last_name, email, phone_number, config)
                    VALUES
                    ('{self.first_name}', '{self.last_name}', '{self.email}', '{self.phone_number}', '{self.new_config}');
                  """
        return insert
