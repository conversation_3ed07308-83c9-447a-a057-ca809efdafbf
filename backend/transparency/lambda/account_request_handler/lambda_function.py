import json

import boto3
from query_generator import QueryGenerator

from common import init_main
from common.query_utils import format_rds_response
from common.query_utils import ThreadQuery

PROJECT_ROOT = init_main.initialize()


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    :param event: Frontend parameter with account query_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and account data under ['body']['records'].
    """
    client = boto3.client("rds-data")

    query_params = json.loads(event.get("body", {})).get("query_params")
    path = event["path"]

    qg = QueryGenerator(query_params)
    if path == "/insertAccountDetails":
        # Add a new account
        query = qg.insert_account()
    elif path == "/updateAccountDetails":
        # Update configs
        query = qg.update_account_config()
    elif "config" in query_params and query_params["config"]:
        # Read configs
        query = qg.get_account_config()
    else:
        # Read account details
        query = qg.get_account_details()

    if path == "/getAccountDetails":
        read_thread = ThreadQuery(name="read_account", client=client, args=(query,))

        try:
            read_thread.start()
            result = format_rds_response(read_thread.join())
            response = {
                "statusCode": 200,
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST,PUT",
                },
                "body": json.dumps({"records": result}),
            }
        except Exception:
            response = {
                "statusCode": 500,
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST,PUT",
                },
                "body": "An error occured",
            }

    elif path == "/updateAccountDetails" or path == "/insertAccountDetails":
        write_thread = ThreadQuery(name="write_account", client=client, args=(query,))
        try:
            write_thread.start()
            write_thread.join()
            response = {
                "statusCode": 200,
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST,PUT"
                    if path == "/updateAccountDetails"
                    else "OPTIONS,POST",
                },
                "body": "Success",
            }
        except Exception:
            response = {
                "statusCode": 500,
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,POST,PUT"
                    if path == "/updateAccountDetails"
                    else "OPTIONS,POST",
                },
                "body": "An error occured",
            }
    return response


def main():
    # get
    # e = {
    #     "path": "/getAccountDetails",
    #     "body": json.dumps(
    #         {
    #             "query_params": {
    #                 "email": "<EMAIL>",
    #                 "is_dev_env": 1,
    #                 "config": 0
    #             }
    #         }
    #     )
    # }

    # update
    # e = {
    #     "path": "/updateAccountDetails",
    #     "body": json.dumps(
    #         {
    #             "query_params": {
    #                 "email": "<EMAIL>",
    #                 "is_dev_env": 1,
    #                 "new_config": json.dumps({"isFullView":{"dashboard":False,"drilldown":False,"laneDrilldown":True}, "aggregationStartDate":"2022-08-02"}),
    #             }
    #         }
    #     )
    # }

    # insert
    e = {
        "path": "/insertAccountDetails",
        "body": json.dumps(
            {
                "query_params": {
                    "first_name": "joey",
                    "last_name": "test",
                    "email": "<EMAIL>",
                    "phone_number": "*********",
                    "new_config": json.dumps(
                        {
                            "full_view": {
                                "dashboard": "0",
                                "drilldown": "0",
                                "lane_drilldown": "1",
                            },
                            "aggregation_start_date": "********",
                        }
                    ),
                    "is_dev_env": 1,
                }
            }
        ),
    }
    lambda_handler(e, None)


if __name__ == "__main__":
    main()
