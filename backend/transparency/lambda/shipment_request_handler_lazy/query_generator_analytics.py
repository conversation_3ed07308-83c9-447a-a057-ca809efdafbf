# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.
from constants import get_denylist_querystr


class QueryGeneratorAnalytics:
    def __init__(self, query_params):
        # Database selection
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

        # Id filter
        # Checks user type for primary id filter
        # Assumes customer is selected if both ids are populated in query
        sub_id_filter_key = None
        sub_id_filter_value = None
        if query_params.get("user_type") == "broker":
            id_filter_key = "brokerId"
            id_filter_value = query_params["brokerId"]

            # Check for customer filtering
            if "shipperId" in query_params:
                sub_id_filter_key = "shipperId"
                sub_id_filter_value = query_params["shipperId"]

        elif query_params.get("user_type") in ("shipper", "admin"):
            id_filter_key = "shipperId"
            id_filter_value = query_params["shipperId"]

            # Check for customer filtering
            if "brokerId" in query_params:
                sub_id_filter_key = "brokerId"
                sub_id_filter_value = query_params["brokerId"]

        else:
            # This shouldn't happen and there should be a better way of communicating it
            raise Exception("Invalid query parameters")

        # User Id filter
        if type(id_filter_value) == str:
            id_filter = f"{id_filter_key} = '{id_filter_value}'"
        elif type(id_filter_value) == list:
            id_filter = f"{id_filter_key} in {'(' + str(id_filter_value)[1:-1] + ')'}"

        # Customer Id filter
        if type(sub_id_filter_value) == str:
            id_filter += f" AND {sub_id_filter_key} = '{sub_id_filter_value}'"
        elif type(sub_id_filter_value) == list:
            id_filter += f" AND {sub_id_filter_key} in {'(' + str(sub_id_filter_value)[1:-1] + ')'}"

        self.id_filter = id_filter

        # Date filter
        start_date_filter = f"DATE(originCloseTime) >= '{query_params['start_date']}'"
        end_date_filter = f"DATE(originCloseTime) <= '{query_params['end_date']}'"
        self.start_date_filter = start_date_filter
        self.end_date_filter = end_date_filter

        # Volume and equipment type filter
        self.volume_filter = f"WHERE volume >= {query_params['volume_threshold']}"
        equipment_type_filter = (
            f"equipmenttype in {'(' + str(query_params['equipment_type'])[1:-1] + ')'}"
            if len(query_params["equipment_type"]) > 0
            else None
        )

        # Query filter constructions
        self.sub_query_filter = (
            f"WHERE {equipment_type_filter}" if equipment_type_filter else ""
        )

        # Shipment Class
        shipment_class_filter = "shipmentClass IN ('canonical')"

        self.global_filter = "WHERE " + " AND ".join(
            [id_filter, start_date_filter, end_date_filter, shipment_class_filter]
        )
        self.network_global_filter = "WHERE " + " AND ".join(
            [start_date_filter, end_date_filter, shipment_class_filter]
        )

        # State Filter
        if "origin_state_filter" in query_params:
            # query_params['origin_state_filter'] is an array, needs to be converted to SQL readable tuple
            self.origin_state_filter = f"""AND cityTableOriginState IN ({str(query_params['origin_state_filter'])[1:-1]})"""
        else:
            self.origin_state_filter = ""

        if "destination_state_filter" in query_params:
            self.destination_state_filter = f"""AND cityTableDestinationState IN ({str(query_params['destination_state_filter'])[1:-1]})"""
        else:
            self.destination_state_filter = ""

        # Accessorial filter
        self.accessorial_filter_cogs = (
            "-COALESCE(cogsAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue = (
            "-COALESCE(revenueAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum = (
            "-COALESCE(SUM(cogsAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum = (
            "-COALESCE(SUM(revenueAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )

        # Denylist
        company_id = (
            query_params["brokerId"]
            if query_params["is_broker_user"]
            else query_params["shipperId"]
        )
        self.denylist = get_denylist_querystr(
            query_params["is_dev_env"], query_params["is_broker_user"], company_id
        )

    ###################################################################################################################
    #
    #              CITY/STATE RETRIEVAL
    #
    ###################################################################################################################

    def get_valid_origins(self):
        get_valid_origins_text = f"""
                            select distinct state
                            from {self.db}.shipments AS s
                            JOIN (SELECT id AS lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            JOIN (SELECT id AS cityTableOriginId, state FROM {self.db}.cities) AS c on l.originCityId = c.cityTableOriginId
                            {self.network_global_filter}
                            """
        # print(get_valid_origins_text)
        return get_valid_origins_text

    def get_valid_destinations(self):
        get_valid_destinations_text = f"""
                            select distinct state
                            from {self.db}.shipments AS s
                            JOIN (SELECT id AS lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            JOIN (SELECT id AS cityTableDestinationId, state FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                            {self.network_global_filter}
                            """
        # print(get_valid_destinations_text)
        return get_valid_destinations_text

    ###################################################################################################################
    #
    #              LINE GRAPHS
    #
    ###################################################################################################################

    def line_graph_query(self):
        line_query_txt = f"""
                        SELECT
                            DATE_ADD(NETWORKTREND.date, INTERVAL - WEEKDAY(NETWORKTREND.date) DAY) as week,
                            (SUM(daily_sum_rev) - SUM(daily_sum_cogs)) / SUM(daily_sum_rev) as margin,
                            (SUM(daily_network_sum_rev) - SUM(daily_network_sum_cogs)) / SUM(daily_network_sum_rev) as network_margin,
                            SUM(daily_sum_cogs) / SUM(daily_sum_dist) as cpm,
                            SUM(daily_sum_rev) / SUM(daily_sum_dist) as spm,
                            SUM(daily_sum_mpl) / SUM(num_shipments) as mpl,
                            SUM(daily_network_sum_cogs) / SUM(daily_network_sum_dist) as network_cpm,
                            SUM(daily_network_sum_rev) / SUM(daily_network_sum_dist) as network_spm,
                            SUM(daily_network_sum_mpl) / SUM(network_num_shipments) as network_mpl,
                            SUM(daily_sum_prebook) / SUM(num_shipments) as prebook,
                            SUM(daily_network_sum_prebook) / SUM(network_num_shipments) as network_prebook,
                            SUM(network_num_shipments_clt_l1) / SUM(network_num_shipments) as network_cltl1,
                            SUM(num_shipments_clt_l1) / SUM(num_shipments) as cltl1,
                            SUM(prebook_percent) / SUM(prebook_percent_count) as prebook_percent,
                            SUM(network_prebook_percent) / SUM(network_prebook_percent_count) as network_prebook_percent
                        FROM

                        (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_network_sum_cogs,
                            (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) as daily_network_sum_rev,
                            (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_network_sum_mpl,
                            SUM(distanceMiles) as daily_network_sum_dist,
                            SUM(prebook) as daily_network_sum_prebook,
                            SUM(CASE
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                    ELSE NULL
                                END
                            ) as network_prebook_percent,
                            COUNT(CASE
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                    ELSE NULL
                                END
                            ) as network_prebook_percent_count,
                            COUNT(*) AS network_num_shipments
                        FROM {self.db}.shipments AS s
                        JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                        JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                        JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                        {self.network_global_filter}{self.origin_state_filter}{self.destination_state_filter}
                        GROUP BY date) AS NETWORKTREND

                        LEFT JOIN

                        (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_sum_cogs,
                            (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) as daily_sum_rev,
                            (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_sum_mpl,
                            SUM(distanceMiles) as daily_sum_dist,
                            SUM(CASE
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                    ELSE NULL
                                END
                            ) as prebook_percent,
                            COUNT(CASE
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                    WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                    ELSE NULL
                                END
                            ) as prebook_percent_count,
                            SUM(prebook) as daily_sum_prebook,
                            COUNT(*) AS num_shipments
                        FROM {self.db}.shipments AS s
                        JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                        JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                        JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                        {self.global_filter}{self.origin_state_filter}{self.destination_state_filter}{self.denylist}
                        GROUP BY date) AS LOCALTREND
                        ON NETWORKTREND.date = LOCALTREND.date

                        LEFT JOIN

                        (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            COUNT(*) as network_num_shipments_clt_l1
                        FROM {self.db}.shipments as si
                        JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS li ON si.laneid = li.lanetableid
                        {self.network_global_filter} AND clt < 20 GROUP BY date) as NETWORKCLTLI1
                        ON NETWORKTREND.date = NETWORKCLTLI1.date

                        LEFT JOIN

                        (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            COUNT(*) as num_shipments_clt_l1
                        FROM {self.db}.shipments as si
                        JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS li ON si.laneid = li.lanetableid
                        {self.global_filter}{self.denylist} AND clt < 20 GROUP BY date) as CLTLI1
                        ON NETWORKTREND.date = CLTLI1.date

                        GROUP BY week;
                        """
        # print(line_query_txt)
        return line_query_txt

    ###################################################################################################################
    #
    #              PIE CHARTS
    #
    ###################################################################################################################

    def dow_volume_query(self):
        query_txt = f"""
                    SELECT NETWORK_BUCKETS.day_of_week, network_volume, volume FROM (
                        SELECT DAYOFWEEK(date(originCloseTime)) as day_of_week,
                            count(*) as network_volume
                        FROM {self.db}.shipments AS s
                        JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                        JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                        JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                        {self.network_global_filter}{self.origin_state_filter}{self.destination_state_filter}
                        GROUP BY day_of_week
                    ) NETWORK_BUCKETS

                    LEFT JOIN

                    (
                        SELECT DAYOFWEEK(date(originCloseTime)) as day_of_week,
                            count(*) as volume
                        FROM {self.db}.shipments AS s
                        JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                        JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                        JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                        {self.global_filter}{self.origin_state_filter}{self.destination_state_filter}{self.denylist}
                        GROUP BY day_of_week
                    ) USER_BUCKETS

                    ON NETWORK_BUCKETS.day_of_week = USER_BUCKETS.day_of_week

                    """
        # print(query_txt)
        return query_txt

    ###################################################################################################################
    #
    #              STACKED GRAPHS
    #
    ###################################################################################################################

    def stacked_graph_query(self):
        stacked_query_txt = f"""
                            select NETWORK_BUCKETS.distBucket, num_shipments, network_num_shipments from
                            (select
                                case when distanceMiles >= 0 and distanceMiles < 100 then '0-99'
                                    when distanceMiles >= 100 and distanceMiles < 250 then '100-249'
                                    when distanceMiles >= 250 and distanceMiles < 500 then '250-499'
                                    when distanceMiles >= 500 and distanceMiles < 899 then '500-899'
                                    else '900+'
                                end distBucket,
                                count(*) as network_num_shipments
                            from {self.db}.shipments as s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                            JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                            {self.network_global_filter}{self.origin_state_filter}{self.destination_state_filter}
                            group by distBucket) NETWORK_BUCKETS

                            LEFT JOIN

                            (select
                                case when distanceMiles >= 0 and distanceMiles < 100 then '0-99'
                                    when distanceMiles >= 100 and distanceMiles < 250 then '100-249'
                                    when distanceMiles >= 250 and distanceMiles < 500 then '250-499'
                                    when distanceMiles >= 500 and distanceMiles < 899 then '500-899'
                                    else '900+'
                                end distBucket,
                                count(*) as num_shipments
                            from {self.db}.shipments as s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) as c on l.originCityId = c.cityTableOriginId
                            JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                            {self.global_filter}{self.origin_state_filter}{self.destination_state_filter}{self.denylist}
                            group by distBucket) BUCKETS

                            ON NETWORK_BUCKETS.distBucket = BUCKETS.distBucket
                            """
        # print(stacked_query_txt)
        return stacked_query_txt

    ###################################################################################################################
    #
    #              LISTS
    #
    ###################################################################################################################

    def increase_list_query(self):
        increase_list_txt = f"""
                                SELECT state_to_state, cpm_per_lane, volume FROM
                                    (SELECT
                                        CONCAT(cityTableOriginState, " -> ", cityTableDestinationState) as state_to_state,
                                        (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) / SUM(distanceMiles) as cpm_per_lane,
                                        COUNT(*) as volume
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    {self.network_global_filter}{self.origin_state_filter}{self.destination_state_filter} AND distanceMiles >= 200 AND s.shipperId != 'f1addb79-bf2e-4275-8abd-b436ac83c4cc'
                                    GROUP BY cityTableOriginState, cityTableDestinationState
                                    ORDER BY cpm_per_lane DESC) TMP
                                WHERE volume > 3
                                LIMIT 5;
                            """
        # print(increase_list_txt)
        return increase_list_txt

    def decrease_list_query(self):
        decrease_list_txt = f"""
                                SELECT state_to_state, cpm_per_lane, volume FROM
                                    (SELECT
                                        CONCAT(cityTableOriginState, " -> ", cityTableDestinationState) as state_to_state,
                                        (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) / SUM(distanceMiles) as cpm_per_lane,
                                        COUNT(*) as volume
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    {self.network_global_filter}{self.origin_state_filter}{self.destination_state_filter} AND distanceMiles >= 200 AND s.shipperId != 'f1addb79-bf2e-4275-8abd-b436ac83c4cc'
                                    GROUP BY cityTableOriginState, cityTableDestinationState
                                    ORDER BY cpm_per_lane ASC) TMP
                                WHERE volume > 3 AND cpm_per_lane is not NULL
                                LIMIT 5;
                            """
        # print(decrease_list_txt)
        return decrease_list_txt
