from constants import FOCUS_LANE_FILTERS
from constants import get_denylist_querystr

# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.


class QueryGenerator:
    def __init__(self, query_params):
        self.df_broker_filter = ""
        # Id filter
        if "brokerId" in query_params:
            id_filter_key = "brokerId"
            id_filter_value = query_params["brokerId"]
            self.df_broker_filter = f"where a.brokerId = '{query_params['brokerId']}'"
            self.dt_user_filter = "shipperId"
            self.data_table = "shippers"

            # Id filter for drilldown
            if query_params["is_drilldown"] and "shipperId" in query_params:
                id_filter_key_drilldown = "shipperId"
                id_filter_value_drilldown = query_params["shipperId"]
            elif query_params["is_drilldown"] and "laneId" in query_params:
                id_filter_key_drilldown = "laneId"
                id_filter_value_drilldown = query_params["laneId"]

        elif "shipperId" in query_params:
            id_filter_key = "shipperId"
            id_filter_value = query_params["shipperId"]
            self.dt_user_filter = "brokerId"
            self.data_table = "brokers"

            # Id filter for drilldown
            if query_params["is_drilldown"] and "brokerId" in query_params:
                id_filter_key_drilldown = "brokerId"
                id_filter_value_drilldown = query_params["brokerId"]
            elif query_params["is_drilldown"] and "laneId" in query_params:
                id_filter_key_drilldown = "laneId"
                id_filter_value_drilldown = query_params["laneId"]

        elif "laneId" in query_params:
            id_filter_key = "laneId"
            id_filter_value = query_params["laneId"]

        # Index filter
        self.index_key = query_params["index_by"]

        if type(id_filter_value) == str:
            id_filter = f"{id_filter_key} = '{id_filter_value}'"
        elif type(id_filter_value) == list:
            id_filter = f"{id_filter_key} in {'(' + str(id_filter_value)[1:-1] + ')'}"

        # Include both ids if in drilldown
        if query_params["is_drilldown"] and type(id_filter_value_drilldown) == str:
            id_filter += (
                f" AND {id_filter_key_drilldown} = '{id_filter_value_drilldown}'"
            )
        elif query_params["is_drilldown"] and type(id_filter_value_drilldown) == list:
            id_filter += f" AND {id_filter_key_drilldown} in {'(' + str(id_filter_value_drilldown)[1:-1] + ')'}"

        self.id_filter = id_filter

        # Date filter
        start_date_filter = f"DATE(originCloseTime) >= '{query_params['start_date']}'"
        end_date_filter = f"DATE(originCloseTime) <= '{query_params['end_date']}'"
        self.start_date_filter = start_date_filter
        self.end_date_filter = end_date_filter

        # Volume and equipment type filter
        self.volume_filter = (
            f"WHERE volume >= {query_params.get('volume_threshold', 0)}"
        )
        default_equipment_types = [
            "Dry Van",
            "Reefer",
            "Flatbed",
            "Power Only",
            "Straight Truck",
        ]
        equipment_type_filter = (
            f"equipmenttype in {'(' + str(query_params.get('equipment_type', default_equipment_types))[1:-1] + ')'}"
            if len(query_params.get("equipment_type", default_equipment_types)) > 0
            else None
        )

        # Page filter
        page_number = query_params.get("page_number", 0)
        page_size = query_params.get("page_size", 100)
        shipment_page_number = query_params.get("shipment_page_number", 0)
        shipment_page_size = query_params.get("shipment_page_size", 8)

        # Query filter constructions
        self.id_filter_key = id_filter_key
        self.order_by_filter = f"ORDER BY {query_params.get('order_by', 'volume')} {query_params.get('order_by_direction', 'DESC')}"
        self.sub_order_by_filter = f"ORDER BY {query_params.get('shipment_order_by', 'originCloseTime')} {query_params.get('shipment_order_by_direction', 'DESC')}"
        self.page_filter = f"LIMIT {page_size} OFFSET {page_number*page_size}"
        self.sub_page_filter = f"LIMIT {shipment_page_size} OFFSET {shipment_page_number*shipment_page_size}"
        self.sub_query_filter = (
            f"WHERE {equipment_type_filter}" if equipment_type_filter else ""
        )
        self.global_filter = "WHERE " + " AND ".join(
            [id_filter, start_date_filter, end_date_filter]
        )
        self.network_global_filter = "WHERE " + " AND ".join(
            [start_date_filter, end_date_filter]
        )

        # Delta filter
        if "delta_start_date" in query_params:
            delta_start_date_filter = (
                f"DATE(originCloseTime) >= '{query_params['delta_start_date']}'"
            )
            delta_end_date_filter = (
                f"DATE(originCloseTime) <= '{query_params['delta_end_date']}'"
            )
            self.delta_filter = "WHERE " + " AND ".join(
                [id_filter, delta_start_date_filter, delta_end_date_filter]
            )
        else:
            self.delta_filter = ""

        # Projection filter - Agg start date -> Current day
        if "projection_start_date" in query_params:
            projection_start_date_filter = (
                f"DATE(originCloseTime) >= '{query_params['projection_start_date']}'"
            )
            projection_end_date_filter = (
                f"DATE(originCloseTime) <= '{query_params['projection_end_date']}'"
            )
            self.projection_filter = "WHERE " + " AND ".join(
                [id_filter, projection_start_date_filter, projection_end_date_filter]
            )
        else:
            self.projection_filter = ""

        # Projection filter - Last 30 days
        if "projection_start_date_30" in query_params:
            projection_start_date_filter = (
                f"DATE(originCloseTime) >= '{query_params['projection_start_date_30']}'"
            )
            projection_end_date_filter = (
                f"DATE(originCloseTime) <= '{query_params['projection_end_date']}'"
            )
            self.projection_filter_30 = "WHERE " + " AND ".join(
                [id_filter, projection_start_date_filter, projection_end_date_filter]
            )
        else:
            self.projection_filter_30 = ""

        # Accessorial filter
        self.accessorial_filter_cogs = (
            "-COALESCE(cogsAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue = (
            "-COALESCE(revenueAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum = (
            "-COALESCE(SUM(cogsAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum = (
            "-COALESCE(SUM(revenueAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum_all = (
            "-COALESCE(SUM(cogsAccessorial_all),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum_all = (
            "-COALESCE(SUM(revenueAccessorial_all),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum_canonical = (
            "-COALESCE(SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsAccessorial ELSE NULL END),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum_canonical = (
            "-COALESCE(SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueAccessorial ELSE NULL END),0)"
            if not query_params["include_accessorials"]
            else ""
        )

        # Small search
        # Contains numbers -> must be ref
        #  Run ref filter

        # Pure char -> could be either, run full query
        #  if str is substring of broker
        #    gather all shipments for that broker (empty filter)
        #  else
        #    run ref filter

        if "dt_search_term" in query_params:
            search_term = query_params["dt_search_term"]
            self.search_term = search_term
            self.is_numeric = query_params["is_numeric"]
            self.search_ref_filter = f"shipperPrimaryReference LIKE '%{search_term}%'"

            if query_params["index_by"] != "laneId":
                self.search_filter = (
                    f" AND (name LIKE '%{search_term}%' OR {self.search_ref_filter})"
                )
            else:
                self.search_filter = f" AND (cityTableOriginCity LIKE '%{search_term}%' OR cityTableDestinationCity LIKE '%{search_term}%' OR cityTableOriginState LIKE '%{search_term}%' OR cityTableDestinationState LIKE '%{search_term}%' OR {self.search_ref_filter})"

        else:
            self.search_filter = ""

        # Database selection
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

        # DAY/WEEK selection
        self.is_week_query = query_params.get("is_week_query")

        # Min distance for LANE/NETWORK CPM graph
        self.min_distance_miles = 125

        # Is Drilldown
        self.is_drilldown = query_params["is_drilldown"]

        # Shipment class filter
        # Should only include shipment class filter if in lane drilldown which means we are indexing by brokerid or shipperid
        self.shipment_class_filter_lane = (
            " AND shipmentClass IN ('canonical')"
            if self.is_drilldown
            and (self.index_key == "brokerId" or self.index_key == "shipperId")
            else ""
        )
        # Should include shipment class filter when drilldown
        # Lane dashboard should only include canonicals
        self.shipment_class_filter = (
            " AND shipmentClass IN ('canonical')"
            if self.is_drilldown
            or (not self.is_drilldown and self.index_key == "laneId")
            else ""
        )

        # Exclude not_shipment class from key metrics
        self.not_shipment_class_filter = " AND shipmentClass NOT IN ('not_shipment')"

        self.export_shipment_class_filter = " AND shipmentClass IN ('canonical')"

        # Handles a selected broker/shipper in the lane drilldown
        self.selected_sb_filter = ""
        if (
            query_params.get("selected_sb")
            and self.is_drilldown
            and (self.index_key == "brokerId" or self.index_key == "shipperId")
        ):
            self.selected_sb = query_params.get("selected_sb")
            self.selected_sb_filter = f" AND {self.index_key} = '{self.selected_sb}'"

        is_broker_user = query_params.get("is_broker_user")

        filter_key_id = "shipperId" if is_broker_user else "brokerId"
        self.filter_key_group_concat = (
            f", GROUP_CONCAT(DISTINCT s.{filter_key_id}) as filterKeyIds"
            if self.is_drilldown
            else ""
        )

        # Get Score Totals
        self.score_color_per = (
            "COUNT(CASE WHEN score > 89 THEN 1 ELSE NULL END)/COUNT(score) AS green_score,"
            "COUNT(CASE WHEN score > 79 AND score <= 89 THEN 1 ELSE NULL END)/COUNT(score) AS blue_score,"
            "COUNT(CASE WHEN score > 69 AND score <= 79 THEN 1 ELSE NULL END)/COUNT(score) AS yellow_score,"
            "COUNT(CASE WHEN score <= 69 THEN 1 ELSE NULL END)/COUNT(score) AS red_score_total"
        )
        # Cutomer Direct (a.k.a White Glove)
        self.customer_direct_concat = (
            ", GROUP_CONCAT(DISTINCT customerDirect) AS customer_directs"
            if query_params.get("is_customer_direct") == 1
            else ""
        )
        self.customer_direct_filter = (
            " AND customerDirect IS NOT NULL"
            if query_params.get("is_customer_direct") == 1
            else ""
        )

        # Focus Lanes - avg_margin , total_margin
        focus_lanes = query_params.get("focus_lanes", [])
        self.focus_lanes_filter = ""
        if len(focus_lanes) != 0:
            self.focus_lanes_filter = " AND "
            focus_filters_list = []

            # Handle general filters
            for f in focus_lanes:
                focus_filters_list.append(FOCUS_LANE_FILTERS[f])

            # Combine all filters into string to add to query
            self.focus_lanes_filter += " OR ".join(focus_filters_list)

        # favorite lanes
        favorite_lanes = query_params.get("favorite_lanes", [])
        favorite_lanes_keyword = (
            "WHERE" if "WHERE" not in self.sub_query_filter else "AND"
        )
        self.favorite_lanes_filter = (
            ""
            if not favorite_lanes
            else f" {favorite_lanes_keyword} id IN {'(' + str(favorite_lanes)[1:-1] + ')'}"
        )

        self.ml_price_cutoff_date = "2024-02-04"

        # Denylist
        company_id = (
            query_params["brokerId"]
            if query_params["is_broker_user"]
            else query_params["shipperId"]
        )
        self.denylist = get_denylist_querystr(
            query_params["is_dev_env"], query_params["is_broker_user"], company_id
        )

    ###################################################################################################################
    #
    #              BASE QUERIES
    #
    ###################################################################################################################

    def score_query(self):
        score_query_txt = f"""
                            SELECT SUM(tmp_score) / SUM(volume) as score,
                                    SUM(tmp_carrier_score) / SUM(volume) as carrier_score,
                                    SUM(tmp_broker_score) / SUM(volume) as broker_score FROM
                            (SELECT
                                SUM(score) AS tmp_score,
                                SUM(carrierScore) as tmp_carrier_score,
                                SUM(brokerScore) as tmp_broker_score,
                                COUNT(*) AS volume
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.shipment_class_filter_lane}{self.selected_sb_filter}{self.customer_direct_filter} AND score >=0
                            {self.denylist}
                            GROUP BY {self.index_key}) as TMP
                            {self.volume_filter};
                            """
        # print(score_query_txt)
        return score_query_txt

    def key_metrics_query(self):
        key_metrics_query_txt = f"""
                                SELECT
                                    ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                    ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / SUM(volume) as avg_margin_dollars,
                                    (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) / SUM(volume) as avg_spend,
                                    SUM(lane_avg_blt) / SUM(lane_avg_blt_count) as avg_blt,
                                    SUM(lane_avg_clt) / SUM(lane_avg_clt_count) as avg_clt,
                                    SUM(lane_avg_prebook) / SUM(lane_avg_prebook_count) as avg_prebook,
                                    SUM(volume) as total_volume,
                                    ((SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) - (SUM(lane_cogs_total_all){self.accessorial_filter_cogs_sum_all})) as total_margin,
                                    (SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) as total_spend,
                                    (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum}) as total_cost,
                                    SUM(lane_otd)/SUM(lane_otd_volume) as avg_otd,
                                    SUM(lane_otp)/SUM(lane_otp_volume) as avg_otp,
                                    SUM(lane_prebook_percent)/SUM(lane_prebook_percent_count) as prebook_percent
                                FROM
                                    (SELECT
                                        laneid,
                                        AVG(revenueTotal{self.accessorial_filter_revenue}) as lane_avg_revenue,
                                        AVG(cogsTotal{self.accessorial_filter_cogs}) as lane_avg_cogs,
                                        SUM(revenueTotal) as lane_rev_total_all,
                                        SUM(revenueAccessorial) as revenueAccessorial_all,
                                        SUM(cogsTotal) as lane_cogs_total_all,
                                        SUM(cogsAccessorial) as cogsAccessorial_all,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN revenueTotal
                                                ELSE NULL
                                            END
                                        ) as lane_rev_total,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN revenueAccessorial
                                                ELSE NULL
                                            END
                                        ) as revenueAccessorial,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN cogsTotal
                                                ELSE NULL
                                            END
                                        ) as lane_cogs_total,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN cogsAccessorial
                                                ELSE NULL
                                            END
                                        ) as cogsAccessorial,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN 1
                                                ELSE NULL
                                            END
                                        ) as volume,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN clt
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_clt,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN clt
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_clt_count,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN blt
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_blt,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN blt
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_blt_count,
                                        SUM(CASE
                                                WHEN shipmentClass = 'canonical' THEN prebook
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_prebook,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN prebook
                                                ELSE NULL
                                            END
                                        ) AS lane_avg_prebook_count,
                                        SUM(CASE
                                                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as lane_otd,
                                        COUNT(CASE
                                                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END) as lane_otd_volume,
                                        SUM(CASE
                                                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as lane_otp,
                                        COUNT(CASE
                                                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END) as lane_otp_volume,
                                        SUM(CASE
                                                WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as lane_prebook_percent,
                                        COUNT(CASE
                                                WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as lane_prebook_percent_count
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                    {self.global_filter}{self.shipment_class_filter_lane}{self.selected_sb_filter}{self.not_shipment_class_filter}{self.customer_direct_filter}
                                    {self.denylist}
                                    GROUP BY {self.index_key}) as avgs
                                    {self.volume_filter};
                                """
        if self.projection_filter:
            key_metrics_query_txt = key_metrics_query_txt.replace(";", "")
            key_metrics_query_txt = f"""
                                    {key_metrics_query_txt}
                                    UNION ALL
                                        SELECT
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / SUM(volume) as avg_margin_dollars,
                                            (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) / SUM(volume) as avg_spend,
                                            SUM(lane_avg_blt) / SUM(lane_avg_blt_count) as avg_blt,
                                            SUM(lane_avg_clt) / SUM(lane_avg_clt_count) as avg_clt,
                                            SUM(lane_avg_prebook) / SUM(lane_avg_prebook_count) as avg_prebook,
                                            SUM(volume) as total_volume,
                                            ((SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) - (SUM(lane_cogs_total_all){self.accessorial_filter_cogs_sum_all})) as total_margin,
                                            (SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) as total_spend,
                                            (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum}) as total_cost,
                                            SUM(lane_otd)/SUM(lane_otd_volume) as avg_otd,
                                            SUM(lane_otp)/SUM(lane_otp_volume) as avg_otp,
                                            SUM(lane_prebook_percent)/SUM(lane_prebook_percent_count) as prebook_percent
                                        FROM
                                            (SELECT
                                                laneid,
                                                AVG(revenueTotal{self.accessorial_filter_revenue}) as lane_avg_revenue,
                                                AVG(cogsTotal{self.accessorial_filter_cogs}) as lane_avg_cogs,
                                                SUM(revenueTotal) as lane_rev_total_all,
                                                SUM(revenueAccessorial) as revenueAccessorial_all,
                                                SUM(cogsTotal) as lane_cogs_total_all,
                                                SUM(cogsAccessorial) as cogsAccessorial_all,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_rev_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as revenueAccessorial,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_cogs_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as cogsAccessorial,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN 1
                                                        ELSE NULL
                                                    END
                                                ) as volume,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook_count,
                                                SUM(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otd,
                                                COUNT(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otd_volume,
                                                SUM(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otp,
                                                COUNT(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otp_volume,
                                                SUM(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent,
                                                COUNT(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent_count
                                            FROM {self.db}.shipments AS s
                                            JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                            {self.projection_filter}{self.shipment_class_filter_lane}{self.selected_sb_filter}{self.not_shipment_class_filter}{self.customer_direct_filter}
                                            {self.denylist}
                                            GROUP BY {self.index_key}) as avgs
                                            {self.volume_filter};
                                    """
        if self.projection_filter_30:
            key_metrics_query_txt = key_metrics_query_txt.replace(";", "")
            key_metrics_query_txt = f"""
                                    {key_metrics_query_txt}
                                    UNION ALL
                                        SELECT
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / SUM(volume) as avg_margin_dollars,
                                            (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) / SUM(volume) as avg_spend,
                                            SUM(lane_avg_blt) / SUM(lane_avg_blt_count) as avg_blt,
                                            SUM(lane_avg_clt) / SUM(lane_avg_clt_count) as avg_clt,
                                            SUM(lane_avg_prebook) / SUM(lane_avg_prebook_count) as avg_prebook,
                                            SUM(volume) as total_volume,
                                            ((SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) - (SUM(lane_cogs_total_all){self.accessorial_filter_cogs_sum_all})) as total_margin,
                                            (SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) as total_spend,
                                            (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum}) as total_cost,
                                            SUM(lane_otd)/SUM(lane_otd_volume) as avg_otd,
                                            SUM(lane_otp)/SUM(lane_otp_volume) as avg_otp,
                                            SUM(lane_prebook_percent)/SUM(lane_prebook_percent_count) as prebook_percent
                                        FROM
                                            (SELECT
                                                laneid,
                                                AVG(revenueTotal{self.accessorial_filter_revenue}) as lane_avg_revenue,
                                                AVG(cogsTotal{self.accessorial_filter_cogs}) as lane_avg_cogs,
                                                SUM(revenueTotal) as lane_rev_total_all,
                                                SUM(revenueAccessorial) as revenueAccessorial_all,
                                                SUM(cogsTotal) as lane_cogs_total_all,
                                                SUM(cogsAccessorial) as cogsAccessorial_all,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_rev_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as revenueAccessorial,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_cogs_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as cogsAccessorial,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN 1
                                                        ELSE NULL
                                                    END
                                                ) as volume,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook_count,
                                                SUM(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otd,
                                                COUNT(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otd_volume,
                                                SUM(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otp,
                                                COUNT(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otp_volume,
                                                SUM(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent,
                                                COUNT(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent_count
                                            FROM {self.db}.shipments AS s
                                            JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                            {self.projection_filter_30}{self.shipment_class_filter_lane}{self.selected_sb_filter}{self.not_shipment_class_filter}{self.customer_direct_filter}
                                            {self.denylist}
                                            GROUP BY {self.index_key}) as avgs
                                            {self.volume_filter};
                                    """
        if self.delta_filter:
            key_metrics_query_txt = key_metrics_query_txt.replace(";", "")
            key_metrics_query_txt = f"""
                                    {key_metrics_query_txt}
                                    UNION ALL
                                        SELECT
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                            ((SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) - (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum})) / SUM(volume) as avg_margin_dollars,
                                            (SUM(lane_rev_total){self.accessorial_filter_revenue_sum}) / SUM(volume) as avg_spend,
                                            SUM(lane_avg_blt) / SUM(lane_avg_blt_count) as avg_blt,
                                            SUM(lane_avg_clt) / SUM(lane_avg_clt_count) as avg_clt,
                                            SUM(lane_avg_prebook) / SUM(lane_avg_prebook_count) as avg_prebook,
                                            SUM(volume) as total_volume,
                                            ((SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) - (SUM(lane_cogs_total_all){self.accessorial_filter_cogs_sum_all})) as total_margin,
                                            (SUM(lane_rev_total_all){self.accessorial_filter_revenue_sum_all}) as total_spend,
                                            (SUM(lane_cogs_total){self.accessorial_filter_cogs_sum}) as total_cost,
                                            SUM(lane_otd)/SUM(lane_otd_volume) as avg_otd,
                                            SUM(lane_otp)/SUM(lane_otp_volume) as avg_otp,
                                            SUM(lane_prebook_percent)/SUM(lane_prebook_percent_count) as prebook_percent
                                        FROM
                                            (SELECT
                                                laneid,
                                                AVG(revenueTotal{self.accessorial_filter_revenue}) as lane_avg_revenue,
                                                AVG(cogsTotal{self.accessorial_filter_cogs}) as lane_avg_cogs,
                                                SUM(revenueTotal) as lane_rev_total_all,
                                                SUM(revenueAccessorial) as revenueAccessorial_all,
                                                SUM(cogsTotal) as lane_cogs_total_all,
                                                SUM(cogsAccessorial) as cogsAccessorial_all,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_rev_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN revenueAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as revenueAccessorial,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsTotal
                                                        ELSE NULL
                                                    END
                                                ) as lane_cogs_total,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN cogsAccessorial
                                                        ELSE NULL
                                                    END
                                                ) as cogsAccessorial,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN 1
                                                        ELSE NULL
                                                    END
                                                ) as volume,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN clt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_clt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN blt
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_blt_count,
                                                SUM(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook,
                                                COUNT(CASE
                                                        WHEN shipmentClass = 'canonical' THEN prebook
                                                        ELSE NULL
                                                    END
                                                ) AS lane_avg_prebook_count,
                                                SUM(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otd,
                                                COUNT(CASE
                                                        WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otd_volume,
                                                SUM(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_otp,
                                                COUNT(CASE
                                                        WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END) as lane_otp_volume,
                                                SUM(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent,
                                                COUNT(CASE
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) < 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 1
                                                        WHEN DATEDIFF(carrierAssignedTime, originCloseTime) >= 0 AND clt >= 15 AND shipmentClass = 'canonical' THEN 0
                                                        ELSE NULL
                                                    END
                                                ) as lane_prebook_percent_count
                                            FROM {self.db}.shipments AS s
                                            JOIN (SELECT id as lanetableid, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                            {self.delta_filter}{self.shipment_class_filter_lane}{self.selected_sb_filter}{self.not_shipment_class_filter}{self.customer_direct_filter}
                                            {self.denylist}
                                            GROUP BY {self.index_key}) as avgs
                                            {self.volume_filter};
                                    """
        # print(key_metrics_query_txt)
        return key_metrics_query_txt

    def data_freshness_query(self):
        data_freshness_query_txt = f"""
                                    SELECT
                                    TIMESTAMPDIFF(MINUTE, o.most_recent, o.now) AS elapsed,
                                    100.0*o.day_count/o.month_count AS percentage,
                                    o.day_count, o.month_count
                                    FROM (SELECT
                                            NOW() AS now,
                                            MAX(sm.lastModifiedTime) AS most_recent,
                                            COUNT(b.id) AS day_count,
                                            COUNT(c.id) AS month_count
                                        FROM {self.db}.shipments AS a
                                        JOIN {self.db}.shipmentmetadata AS sm ON a.id = sm.shipmentid
                                        LEFT JOIN {self.db}.shipmentmetadata AS b ON sm.id = b.id AND b.lastModifiedTime > DATE_SUB(NOW(), interval 1 day)
                                        LEFT JOIN {self.db}.shipmentmetadata AS c ON sm.id = c.id AND c.lastModifiedTime > DATE_SUB(NOW(), interval 1 month)
                                        {self.df_broker_filter}
                                        ) as o
                                    """
        return data_freshness_query_txt

    ###################################################################################################################
    #
    #              DATA TABLE QUERIES
    #
    ###################################################################################################################

    def summary_dt_sb_query(self):
        data_table_query_txt = f"""
                                SELECT * FROM
                                (SELECT
                                    s.{self.id_filter_key},
                                    s.{self.index_key},
                                    name,
                                    AVG(score) as score,
                                    AVG(carrierScore) as avg_carrier_score,
                                    AVG(brokerScore) as avg_broker_score,
                                    AVG(costScore) as avg_cost_score,
                                    AVG(ltuScore) as avg_ltu_score,
                                    AVG(prebookScore) as avg_prebook_score,
                                    AVG(otpScore) as avg_otp_score,
                                    AVG(otdScore) as avg_otd_score,
                                    (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_revenue,
                                    (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_cogs,
                                    (SUM(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL)THEN cogsTotal ELSE NULL END)-COALESCE(SUM(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN cogsAccessorial ELSE NULL END),0))/COUNT(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN 1 ELSE NULL END) as avg_cogs_wo_acc,
                                    COUNT(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN 1 ELSE NULL END) as avg_cogs_wo_acc_count,
                                    ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) AS avg_margin,
                                    ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) AS avg_margin_dollars,
                                    ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) AS total_margin,
                                    AVG(CASE WHEN shipmentClass = 'canonical' THEN clt ELSE NULL END) AS avg_clt,
                                    AVG(CASE WHEN shipmentClass = 'canonical' THEN blt ELSE NULL END) AS avg_blt,
                                    AVG(CASE WHEN shipmentClass = 'canonical' THEN prebook ELSE NULL END) AS avg_prebook,
                                    COUNT(CASE
                                            WHEN shipmentClass = 'canonical' THEN 1
                                            ELSE NULL
                                        END) as volume,
                                    AVG(distanceMiles) as avg_miles,
                                    AVG(CASE
                                            WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                            WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                            ELSE NULL
                                        END
                                    ) as avg_otd,
                                    AVG(CASE
                                            WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                            WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                            ELSE NULL
                                        END
                                    ) as avg_otp,
                                    AVG(CASE
                                            WHEN loadCreationTime is NULL THEN NULL
                                            ELSE 1
                                        END
                                    ) as avg_load_creation_time,
                                    AVG (CASE
                                        WHEN DATE(originCloseTime) > '{self.ml_price_cutoff_date}' THEN
                                            (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 then mlPrice
                                                ELSE NULL
                                            END)
                                        ELSE NULL
                                    END) AS avg_ml_price,
                                    {self.score_color_per}
                                    {self.customer_direct_concat}
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id, name FROM {self.db}.{self.data_table}) AS l ON s.{self.dt_user_filter} = l.id
                                {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY name
                                {self.order_by_filter}) AS TMP
                                {self.volume_filter}
                                {self.page_filter};
                                """
        # print(data_table_query_txt)
        return data_table_query_txt

    def summary_lane_table_query(self):
        data_table_lane_query_txt = f"""
                                     SELECT * FROM
                                    (SELECT
                                        GROUP_CONCAT(distinct s.{self.index_key}) as {self.index_key},
                                        CONCAT(cityTableOriginCity, ", ", cityTableOriginState) as origin, CONCAT(cityTableDestinationCity, ", ", cityTableDestinationState) as destination,
                                        AVG(score) as score,
                                        AVG(carrierScore) as avg_carrier_score,
                                        AVG(brokerScore) as avg_broker_score,
                                        AVG(costScore) as avg_cost_score,
                                        AVG(ltuScore) as avg_ltu_score,
                                        AVG(prebookScore) as avg_prebook_score,
                                        AVG(otpScore) as avg_otp_score,
                                        AVG(otdScore) as avg_otd_score,
                                        equipmenttype,
                                        shipmentmode,
                                        (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_revenue,
                                        (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_cogs,
                                        (SUM(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL)THEN cogsTotal ELSE NULL END)-COALESCE(SUM(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN cogsAccessorial ELSE NULL END),0))/COUNT(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN 1 ELSE NULL END) as avg_cogs_wo_acc,
                                        COUNT(CASE WHEN (shipmentClass = 'canonical' AND DATE(originCloseTime) > '{self.ml_price_cutoff_date}'AND (CASE
                                                WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 THEN mlPrice
                                                ELSE NULL
                                            END)IS NOT NULL) THEN 1 ELSE NULL END) as avg_cogs_wo_acc_count,
                                        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) AS avg_margin,
                                        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) AS avg_margin_dollars,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) AS total_margin,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN clt ELSE NULL END) AS avg_clt,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN blt ELSE NULL END) AS avg_blt,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN prebook ELSE NULL END) AS avg_prebook,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN 1
                                                ELSE NULL
                                            END) as volume,
                                        AVG(distanceMiles) as avg_miles,
                                        AVG(CASE
                                                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otd,
                                        AVG(CASE
                                                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otp,
                                        AVG(CASE
                                                WHEN loadCreationTime is NULL THEN NULL
                                                ELSE 1
                                            END
                                        ) as avg_load_creation_time,
                                        AVG(CASE
                                                WHEN DATE(originCloseTime) > '{self.ml_price_cutoff_date}' THEN
                                                    (CASE
                                                        WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 then mlPrice
                                                        ELSE NULL
                                                    END)
                                                ELSE NULL
                                        END) AS avg_ml_price,
                                        {self.score_color_per}
                                        {self.filter_key_group_concat}
                                        {self.customer_direct_concat}
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter} {self.favorite_lanes_filter}) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.customer_direct_filter}
                                    {self.denylist}
                                    GROUP BY cityTableOriginCity, cityTableOriginState, cityTableDestinationCity, cityTableDestinationState, equipmenttype
                                    {self.order_by_filter}) AS TMP
                                    {self.volume_filter}
                                    {self.focus_lanes_filter}
                                    {self.page_filter};
                                    """
        # print(data_table_lane_query_txt)
        return data_table_lane_query_txt

    def drilldown_dt_shipments_query(self, index_value, name=None):
        sub_id_filter = (
            f"{self.index_key} in {'(' + str(index_value.split(','))[1:-1] + ')'}"
        )

        sub_global_filter = "WHERE " + " AND ".join(
            [
                self.id_filter,
                sub_id_filter,
                self.start_date_filter,
                self.end_date_filter,
            ]
        )

        # Contains numbers -> must be ref
        #  Run ref filter

        # Pure char -> could be either, run full query
        #  if str is substring of broker
        #    gather all shipments for that broker (empty filter)
        #  else
        #    run ref filter
        ref_filter = ""
        if name is not None and (
            self.is_numeric or self.search_term.lower() not in name.lower()
        ):
            ref_filter = " AND " + self.search_ref_filter

        data_table_shipment_query_txt = f"""
                                        SELECT
                                            shipperPrimaryReference,
                                            originCloseTime,
                                            originDepartureTime,
                                            score,
                                            revenueTotal{self.accessorial_filter_revenue} AS revenue,
                                            cogsTotal{self.accessorial_filter_cogs} AS cogs,
                                            cogsTotal - COALESCE(cogsAccessorial,0) AS cogs_wo_acc,
                                            ((revenueTotal{self.accessorial_filter_revenue}) - (cogsTotal{self.accessorial_filter_cogs})) AS margin_dollars,
                                            ((revenueTotal{self.accessorial_filter_revenue}) - (cogsTotal{self.accessorial_filter_cogs})) / (revenueTotal{self.accessorial_filter_revenue}) AS margin,
                                            clt,
                                            blt,
                                            prebook AS prebook,
                                            distanceMiles AS miles,
                                            destinationDepartureTime,
                                            destinationDelayMinutes,
                                            carrierScore AS carrier_score,
                                            brokerScore AS broker_score,
                                            costScore AS cost_score,
                                            ltuScore AS ltu_score,
                                            prebookScore AS prebook_score,
                                            otpScore AS otp_score,
                                            otdScore AS otd_score,
                                            customerDirect AS customer_direct,
                                            (CASE
                                                WHEN DATE(originCloseTime) > '{self.ml_price_cutoff_date}' THEN
                                                    (CASE
                                                        WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 then mlPrice
                                                        ELSE NULL
                                                    END)
                                                ELSE NULL
                                            END) AS ml_price,
                                            mlPriceLowerBound AS lower_bound,
                                            mlPriceUpperBound AS upper_bound
                                        FROM {self.db}.shipments
                                        {sub_global_filter}{ref_filter}{self.shipment_class_filter}{self.customer_direct_filter}
                                        {self.denylist}
                                        {self.sub_order_by_filter}
                                        {self.sub_page_filter}
                                        """
        # print(data_table_shipment_query_txt)
        return data_table_shipment_query_txt

    def drilldown_dt_shipments_aggregation_query(self, index_value, name=None):
        sub_id_filter = (
            f"{self.index_key} in {'(' + str(index_value.split(','))[1:-1] + ')'}"
        )

        sub_global_filter = "WHERE " + " AND ".join(
            [
                self.id_filter,
                sub_id_filter,
                self.start_date_filter,
                self.end_date_filter,
            ]
        )
        data_table_shipments_aggregation_query_txt = f"""
                                                        SELECT
                                                            DATE_ADD(date(originCloseTime), INTERVAL (2 - DAYOFWEEK(date(originCloseTime))) DAY) as week,
                                                            AVG(score) as score,
                                                            COUNT(*) AS volume,
                                                            (SUM(revenueTotal){self.accessorial_filter_revenue_sum})/COUNT(revenueTotal) as revenue,
                                                            (SUM(cogsTotal){self.accessorial_filter_cogs_sum})/COUNT(cogsTotal) as cogs,
                                                            ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) / COUNT(*) AS avg_margin_dollars,
                                                            ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) / (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                                            AVG(clt) as clt,
                                                            AVG(blt) as blt,
                                                            AVG(prebook) as prebook,
                                                            AVG(distanceMiles) as miles,
                                                            AVG(CASE
                                                                    WHEN destinationDelayMinutes <= 30 THEN 1
                                                                    WHEN destinationDelayMinutes > 30 THEN 0
                                                                    ELSE NULL
                                                                END
                                                            ) as avg_otd,
                                                            AVG(carrierScore) as carrier_score,
                                                            AVG(brokerScore) as broker_score,
                                                            AVG(costScore) as cost_score,
                                                            AVG(ltuScore) as ltu_score,
                                                            AVG(prebookScore) as prebook_score,
                                                            AVG(otpScore) as otp_score,
                                                            AVG(otdScore) as otd_score,
                                                            GROUP_CONCAT(DISTINCT customerDirect) AS customer_direct
                                                        FROM {self.db}.shipments AS s
                                                        {sub_global_filter}{self.shipment_class_filter}{self.customer_direct_filter}
                                                        {self.denylist}
                                                        GROUP BY week
                                                        {self.sub_order_by_filter}
                                                        {self.sub_page_filter}
                                                        """
        # print(data_table_shipments_aggregation_query_txt)
        return data_table_shipments_aggregation_query_txt

    ###################################################################################################################
    #
    #              COUNT QUERIES
    #
    ###################################################################################################################

    def count_lanes_query(self):
        count_lane_query_txt = f"""
                                    SELECT COUNT(*) as count FROM
                                    (SELECT
                                        s.laneid,
                                        count(*) as volume,
                                        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) AS avg_margin,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) AS total_margin,
                                        AVG(CASE
                                                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otd,
                                        AVG(CASE
                                                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otp,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN clt ELSE NULL END) AS avg_clt,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN prebook ELSE NULL END) AS avg_prebook
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter} {self.favorite_lanes_filter}) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.customer_direct_filter}
                                    {self.denylist}
                                    GROUP BY cityTableOriginCity, cityTableOriginState, cityTableDestinationCity, cityTableDestinationState, equipmenttype) AS TMP
                                    {self.volume_filter}
                                    {self.focus_lanes_filter};
                                    """
        # print(count_lane_query_txt)
        return count_lane_query_txt

    def count_brokers_query(self):
        count_brokers_query_txt = f"""
                                SELECT COUNT(*) AS count FROM
                                (SELECT
                                    s.{self.id_filter_key},
                                    count(*) as volume
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id, name FROM {self.db}.{self.data_table}) AS l ON s.{self.dt_user_filter} = l.id
                                {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY name) AS TMP
                                {self.volume_filter};
                                """
        # print(count_brokers_query_txt)
        return count_brokers_query_txt

    def drilldown_dt_shipments_query_counts(self, index_value, name=None):
        sub_id_filter = (
            f"{self.index_key} in {'(' + str(index_value.split(','))[1:-1] + ')'}"
        )

        sub_global_filter = "WHERE " + " AND ".join(
            [
                self.id_filter,
                sub_id_filter,
                self.start_date_filter,
                self.end_date_filter,
            ]
        )

        ref_filter = ""
        if name is not None and (
            self.is_numeric or self.search_term.lower() not in name.lower()
        ):
            ref_filter = " AND " + self.search_ref_filter

        data_table_shipment_query_txt = f"""
                                         SELECT
                                             COUNT(DISTINCT id) as count
                                         FROM {self.db}.shipments
                                         {sub_global_filter}{ref_filter}{self.shipment_class_filter}{self.customer_direct_filter}{self.denylist};
                                        """

        # print(data_table_shipment_query_txt)
        return data_table_shipment_query_txt

    def drilldown_dt_shipments_aggregation_query_counts(self, index_value, name=None):
        sub_id_filter = (
            f"{self.index_key} in {'(' + str(index_value.split(','))[1:-1] + ')'}"
        )

        sub_global_filter = "WHERE " + " AND ".join(
            [
                self.id_filter,
                sub_id_filter,
                self.start_date_filter,
                self.end_date_filter,
            ]
        )
        data_table_shipment_query_txt = f"""
                                        SELECT COUNT(*) AS count FROM
                                         (SELECT
                                             DATE_ADD(date(originCloseTime), INTERVAL (2 - DAYOFWEEK(date(originCloseTime))) DAY) as week
                                         FROM {self.db}.shipments
                                         {sub_global_filter}{self.shipment_class_filter}{self.customer_direct_filter}
                                         {self.denylist}
                                         GROUP BY WEEK) as TMP;
                                        """
        # print(data_table_shipment_query_txt)
        return data_table_shipment_query_txt

    ###################################################################################################################
    #
    #              METRIC TREND QUERIES
    #
    ###################################################################################################################

    def vol_driver_query(self):
        if self.is_week_query:
            vol_query_txt = f"""
                            SELECT DATE_ADD(MT1.date, INTERVAL - WEEKDAY(MT1.date) DAY) as week, SUM(volume) as volume, SUM(volume) as metric_sum FROM
                            (SELECT SPLIT_STR(originCloseTime, " ", 1) as date, count(*) as volume from {self.db}.shipments as s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date) as MT1
                            GROUP BY week;
                            """
        else:
            vol_query_txt = f"""
                            SELECT MT1.date as week, SUM(volume) as volume, SUM(volume) as metric_sum FROM
                            (SELECT SPLIT_STR(originCloseTime, " ", 1) as date, count(*) as volume from {self.db}.shipments as s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date) as MT1
                            GROUP BY week;
                            """
        # print(vol_query_txt)
        return vol_query_txt

    def mpl_driver_query(self):
        if self.is_week_query:
            mpl_query_txt = f"""
                            SELECT DATE_ADD(DRIVERS1.date, INTERVAL - WEEKDAY(DRIVERS1.date) DAY) as week, sum(mpl) / sum(volume) as mpl, sum(volume) as denom, sum(mpl) as metric_sum FROM
                            (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) as mpl,
                            count(*) as volume
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date ) AS DRIVERS1
                            GROUP BY week
                            """
        else:
            mpl_query_txt = f"""
                            SELECT DRIVERS1.date as week, sum(mpl) / sum(volume) as mpl, sum(volume) as denom, sum(mpl) as metric_sum FROM
                            (SELECT
                            SPLIT_STR(originCloseTime, " ", 1) as date,
                            ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) as mpl,
                            count(*) as volume
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date ) AS DRIVERS1
                            GROUP BY week
                            """

        # print(mpl_query_txt)
        return mpl_query_txt

    def cpm_driver_query(self):
        if self.is_week_query:
            cpm_query_txt = f"""
                            SELECT DATE_ADD(DRIVERS1.date, INTERVAL - WEEKDAY(DRIVERS1.date) DAY) as week, sum(cpm) / sum(distance) as cpm, sum(spm) / sum(distance) as spm, sum(distance) as denom, sum(cpm) as metric_sum FROM
                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as cpm,
                                (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) as spm,
                                SUM(distanceMiles) as distance
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date) AS DRIVERS1
                            GROUP BY week;
                            """
        else:
            cpm_query_txt = f"""
                            SELECT DRIVERS1.date as week, sum(cpm) / sum(distance) as cpm, sum(spm) / sum(distance) as spm, sum(distance) as denom, sum(cpm) as metric_sum FROM
                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as cpm,
                                (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) as spm,
                                SUM(distanceMiles) as distance
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.selected_sb_filter}{self.customer_direct_filter} AND shipmentClass = 'canonical'
                            {self.denylist}
                            GROUP BY date) AS DRIVERS1
                            GROUP BY week;
                            """

        return cpm_query_txt

    ###################################################################################################################
    #
    #              SEARCH QUERIES
    #
    ###################################################################################################################

    def search_broker_list(self):
        date_filter = (
            " AND " + " AND ".join([self.start_date_filter, self.end_date_filter])
            if self.is_drilldown
            and (self.index_key == "brokerId" or self.index_key == "shipperId")
            else ""
        )

        search_query = f"""
                            SELECT
                                s.{self.index_key},
                                name as broker_name,
                                MAX(sm.shipmentIngestionTime) as max_ingestion_time
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT shipmentId, shipmentIngestionTime from {self.db}.shipmentmetadata) as sm ON s.id = sm.shipmentId
                            JOIN (SELECT id, name FROM {self.db}.{self.data_table}) AS l ON s.{self.dt_user_filter} = l.id
                            WHERE {self.id_filter}{date_filter}{self.denylist}
                            GROUP BY name;
                        """
        # print(search_query)
        return search_query

    def search_lanes_list(self):
        search_query = f"""
                            SELECT
                                GROUP_CONCAT(distinct s.laneId) as laneId,
                                CONCAT(cityTableOriginCity, ", ", cityTableOriginState, " --> ", cityTableDestinationCity, ", ", cityTableDestinationState) AS name,
                                equipmenttype
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                     JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                            WHERE {self.id_filter}{self.denylist}
                            GROUP BY cityTableOriginCity, cityTableOriginState, cityTableDestinationCity, cityTableDestinationState, equipmenttype
                        """
        # print(search_query)
        return search_query

    ###################################################################################################################
    #
    #              LANE GRAPH QUERIES
    #
    ###################################################################################################################

    def cost_spend_volume(self):
        if self.is_week_query:
            cost_spend_query_txt = f"""
                            SELECT DATE_ADD(COST_SPEND_DAILY.date, INTERVAL - WEEKDAY(COST_SPEND_DAILY.date) DAY) as week, sum(spend)/sum(volume) as avg_spend, sum(cost)/sum(volume) as avg_cost, sum(volume) as total_volume FROM
                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) as spend,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as cost,
                                count(*) as volume
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY date ) AS COST_SPEND_DAILY
                            GROUP BY week
                            """
        else:
            cost_spend_query_txt = f"""
                            SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as week,
                                (SUM(revenueTotal){self.accessorial_filter_revenue_sum})/count(*) as avg_spend,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum})/count(*) as avg_cost,
                                count(*) as total_volume
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY week
                            """
        # print(cost_spend_query_txt)
        return cost_spend_query_txt

    def cost_volume_dayOfWeek(self):
        # SUNDAY = 1, SATURDAY = 7
        cost_volume_dayOfWeek_query_txt = f"""
                          SELECT DAYOFWEEK(originCloseTime) as day_of_week,
                             (SUM(cogsTotal){self.accessorial_filter_cogs_sum})/COUNT(*) as avg_cost,
                             (SUM(revenueTotal){self.accessorial_filter_revenue_sum})/COUNT(*) as avg_spend,
                             count(*) as total_volume
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                            {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                            {self.denylist}
                            GROUP BY day_of_week
                         """
        # print(cost_volume_dayOfWeek_query_txt)
        return cost_volume_dayOfWeek_query_txt

    def lane_cpm_vs_network_cpm(self, lane_id):
        cpm_sub_query_filter = (
            self.sub_query_filter + " AND oc.state != dc.state"
            if self.sub_query_filter != ""
            else " WHERE oc.state != dc.state"
        )
        if self.is_week_query:
            lane_cpm_vs_network_cpm_txt = f"""
                            SELECT
                                DATE_ADD(NETWORKTREND.date, INTERVAL - WEEKDAY(NETWORKTREND.date) DAY) as week,
                                (SUM(daily_sum_cogs) / SUM(daily_sum_dist)) as avg_cpm,
                                (SUM(daily_network_sum_cogs) / SUM(daily_network_sum_dist)) as avg_network_cpm
                            FROM

                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_network_sum_cogs,
                                SUM(distanceMiles) as daily_network_sum_dist,
                                COUNT(*) AS network_num_shipments
                            FROM {self.db}.shipments AS s
                            JOIN (
                                    SELECT l.id as lanetableid, l.origincityid, l.destinationcityid, l.shipmentmode, l.equipmenttype
                                    FROM {self.db}.lanes l
                                    JOIN {self.db}.cities oc on l.originCityId=oc.id
                                    JOIN {self.db}.cities dc on l.destinationCityId=dc.id
                                    WHERE l.equipmenttype = (SELECT equipmenttype FROM {self.db}.lanes where id='{lane_id}')
                                    AND oc.state = (SELECT oc.state FROM {self.db}.lanes l JOIN {self.db}.cities oc on l.originCityId=oc.id where l.id='{lane_id}')
                                    AND dc.state = (SELECT dc.state FROM {self.db}.lanes l JOIN {self.db}.cities dc on l.destinationCityId=dc.id where l.id='{lane_id}')
                                    AND oc.state != dc.state
                                    ) AS ln ON s.laneid = ln.lanetableid
                            {self.network_global_filter} AND distanceMiles >= {self.min_distance_miles}{self.shipment_class_filter}{self.customer_direct_filter}
                            GROUP BY date) AS NETWORKTREND

                            LEFT JOIN

                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_sum_cogs,
                                SUM(distanceMiles) as daily_sum_dist,
                                COUNT(*) AS num_shipments
                            FROM {self.db}.shipments AS s
                            JOIN (
                                    SELECT l.id as lanetableid, l.origincityid, l.destinationcityid, l.shipmentmode, l.equipmenttype
                                    FROM {self.db}.lanes l
                                    JOIN {self.db}.cities oc on l.originCityId=oc.id
                                    JOIN {self.db}.cities dc on l.destinationCityId=dc.id
                                    {cpm_sub_query_filter}
                                    ) AS ln ON s.laneid = ln.lanetableid
                            {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                            {self.denylist}
                            GROUP BY date) AS LOCALTREND
                            ON NETWORKTREND.date = LOCALTREND.date

                            GROUP BY week;
                            """
        else:
            lane_cpm_vs_network_cpm_txt = f"""
                         SELECT
                            NETWORKTREND.date as week,
                            daily_sum_cogs / daily_sum_dist as avg_cpm,
                            daily_network_sum_cogs / daily_network_sum_dist as avg_network_cpm
                        FROM

                         (SELECT
                             SPLIT_STR(originCloseTime, " ", 1) as date,
                             (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_network_sum_cogs,
                             SUM(distanceMiles) as daily_network_sum_dist,
                             COUNT(*) AS network_num_shipments
                         FROM {self.db}.shipments AS s
                         JOIN (
                                SELECT l.id as lanetableid, l.origincityid, l.destinationcityid, l.shipmentmode, l.equipmenttype
                                FROM {self.db}.lanes l
                                JOIN {self.db}.cities oc on l.originCityId=oc.id
                                JOIN {self.db}.cities dc on l.destinationCityId=dc.id
                                WHERE l.equipmenttype = (SELECT equipmenttype FROM {self.db}.lanes where id='{lane_id}')
                                AND oc.state = (SELECT oc.state FROM {self.db}.lanes l JOIN {self.db}.cities oc on l.originCityId=oc.id where l.id='{lane_id}')
                                AND dc.state = (SELECT dc.state FROM {self.db}.lanes l JOIN {self.db}.cities dc on l.destinationCityId=dc.id where l.id='{lane_id}')
                                AND oc.state != dc.state
                                ) AS ln ON s.laneid = ln.lanetableid
                         {self.network_global_filter} AND distanceMiles >= {self.min_distance_miles}{self.shipment_class_filter}{self.customer_direct_filter}
                         GROUP BY date) AS NETWORKTREND

                         LEFT JOIN

                         (SELECT
                             SPLIT_STR(originCloseTime, " ", 1) as date,
                             (SUM(cogsTotal){self.accessorial_filter_cogs_sum}) as daily_sum_cogs,
                             SUM(distanceMiles) as daily_sum_dist,
                             COUNT(*) AS num_shipments
                         FROM {self.db}.shipments AS s
                         JOIN (
                                    SELECT l.id as lanetableid, l.origincityid, l.destinationcityid, l.shipmentmode, l.equipmenttype
                                    FROM {self.db}.lanes l
                                    JOIN {self.db}.cities oc on l.originCityId=oc.id
                                    JOIN {self.db}.cities dc on l.destinationCityId=dc.id
                                    {cpm_sub_query_filter}
                                    ) AS ln ON s.laneid = ln.lanetableid
                         {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                         {self.denylist}
                         GROUP BY date) AS LOCALTREND
                         ON NETWORKTREND.date = LOCALTREND.date
                         ;
                         """
        # print(lane_cpm_vs_network_cpm_txt)
        return lane_cpm_vs_network_cpm_txt

    def on_time_pickup_delivery(self):
        if self.is_week_query:
            otpd_query_text = f"""
                            SELECT DATE_ADD(COST_SPEND_DAILY.date, INTERVAL - WEEKDAY(COST_SPEND_DAILY.date) DAY) as week, sum(otp)/sum(volume) as avg_otp, sum(otd)/sum(volume) as avg_otd, sum(volume) as total_volume FROM
                            (SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as date,
                                SUM(CASE
                                        WHEN destinationDelayMinutes <= 0 THEN 1
                                        WHEN destinationDelayMinutes > 0 THEN 0
                                        ELSE NULL
                                    END
                                ) as otd,
                                SUM(CASE
                                        WHEN originDelayMinutes <= 0 THEN 1
                                        WHEN originDelayMinutes > 0 THEN 0
                                        ELSE NULL
                                    END
                                ) as otp,
                                count(*) as volume
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY date ) AS COST_SPEND_DAILY
                            GROUP BY week
                            """
        else:
            otpd_query_text = f"""
                            SELECT
                                SPLIT_STR(originCloseTime, " ", 1) as week,
                                AVG(CASE
                                        WHEN destinationDelayMinutes <= 0 THEN 1
                                        WHEN destinationDelayMinutes > 0 THEN 0
                                        ELSE NULL
                                    END
                                ) as avg_otd,
                                AVG(CASE
                                        WHEN originDelayMinutes <= 0 THEN 1
                                        WHEN originDelayMinutes > 0 THEN 0
                                        ELSE NULL
                                    END
                                ) as avg_otp,
                                count(*) as total_volume
                                FROM {self.db}.shipments AS s
                                JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                {self.global_filter}{self.shipment_class_filter}{self.selected_sb_filter}{self.customer_direct_filter}
                                {self.denylist}
                                GROUP BY week
                            """
        # print(otpd_query_text)
        return otpd_query_text

    ###################################################################################################################
    #
    #              EXPORT SHIPMENTS
    #
    ###################################################################################################################

    def export_shipments_query(self, index_value, name=None):
        sub_global_filter = "WHERE " + " AND ".join(
            [
                self.id_filter,
                self.start_date_filter,
                self.end_date_filter,
            ]
        )

        data_table_shipment_query_txt = f"""
                                        SELECT
                                            sh.name as shipperName,
                                            b.name as brokerName,
                                            shipperPrimaryReference,
                                            oc.city as origin_city,
                                            oc.state as origin_state,
                                            dc.city as destination_city,
                                            dc.state as destination_state,
                                            l.equipmentType,
                                            originCloseTime,
                                            originDepartureTime,
                                            score,
                                            cogsTotal AS cogs_total,
                                            (cogsLineHaul + cogsFuel) as cogs_lh_fuel,
                                            cogsAccessorial as cogs_acc,
                                            revenueTotal AS revenue_total,
                                            (revenueLineHaul + revenueFuel) as revenue_lh_fuel,
                                            revenueAccessorial as revenue_acc,
                                            ((revenueTotal{self.accessorial_filter_revenue}) - (cogsTotal{self.accessorial_filter_cogs})) AS margin_dollars,
                                            ((revenueTotal{self.accessorial_filter_revenue}) - (cogsTotal{self.accessorial_filter_cogs})) / (revenueTotal{self.accessorial_filter_revenue}) AS margin,
                                            clt,
                                            blt,
                                            prebook AS prebook,
                                            distanceMiles AS miles,
                                            destinationDepartureTime,
                                            destinationDelayMinutes,
                                            carrierScore AS carrier_score,
                                            brokerScore AS broker_score,
                                            costScore AS cost_score,
                                            ltuScore AS ltu_score,
                                            prebookScore AS prebook_score,
                                            otpScore AS otp_score,
                                            otdScore AS otd_score,
                                            (CASE
                                                WHEN DATE(originCloseTime) > '{self.ml_price_cutoff_date}' THEN
                                                    (CASE
                                                        WHEN (mlPriceUpperBound - mlPriceLowerBound) < 1900 OR ((mlPriceUpperBound - mlPriceLowerBound) / (cogsTotal - COALESCE(cogsAccessorial,0))) < 1.9 then mlPrice
                                                        ELSE NULL
                                                    END)
                                                ELSE NULL
                                            END) AS ml_price
                                        FROM {self.db}.shipments s
                                        JOIN {self.db}.lanes l ON s.laneId = l.Id
                                        JOIN {self.db}.cities oc ON l.originCityId = oc.id
                                        JOIN {self.db}.cities dc ON l.destinationCityId = dc.id
                                        JOIN {self.db}.brokers b ON s.brokerId = b.id
                                        JOIN {self.db}.shippers sh ON s.shipperId = sh.id
                                        {sub_global_filter}{self.export_shipment_class_filter}{self.denylist}
                                        {self.sub_order_by_filter};
                                        """
        # print(data_table_shipment_query_txt)
        return data_table_shipment_query_txt
