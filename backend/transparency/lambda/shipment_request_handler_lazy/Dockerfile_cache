# Set a base image that includes Lambda runtime API:
# Public docker image: https://gallery.ecr.aws/lambda/python
FROM public.ecr.aws/lambda/python:3.11

# Create the truce directory to hold all files and set it as the working directory
WORKDIR /var/task/truce

# Add the root directory to PYTHONPATH
ENV PYTHONPATH="${PYTHONPATH}:/var/task"

# Copy and install requirements.txt to environment
ADD common common

# Copy and install requirements.txt to environment
COPY setup.py .
RUN pip3 install .
RUN pip3 install -r common/requirements.txt

# Install lambda requirements.
COPY backend/transparency/lambda/shipment_request_handler_lazy/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY backend/transparency/lambda/shipment_request_handler_lazy/lambda_function.py .
COPY backend/transparency/lambda/shipment_request_handler_lazy/query_generator.py .
COPY backend/transparency/lambda/shipment_request_handler_lazy/query_generator_analytics.py .
COPY backend/transparency/lambda/shipment_request_handler_lazy/query_generator_savings.py .
COPY backend/transparency/lambda/shipment_request_handler_lazy/query_generator_favorites.py .
COPY backend/transparency/lambda/shipment_request_handler_lazy/constants.py .

# Set CMD to handler
CMD [ "truce/lambda_function.populate_cache" ]
