import copy
import hashlib
import json
import pprint
import time
from datetime import date
from datetime import datetime
from datetime import timed<PERSON>ta

import boto3
from constants import BASE_QUERY_PARAM
from constants import get_query_params
from query_generator import QueryGenerator
from query_generator_analytics import QueryGeneratorAnalytics
from query_generator_favorites import QueryGeneratorFavorites
from query_generator_savings import QueryGeneratorSavings

from common.query_utils import format_rds_response
from common.query_utils import query
from common.query_utils import ThreadQuery


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    Checks s3 cache (indexed by hashed event). If not in cache, runs query and parses/computes the query response.

    :param event: Frontend parameter with index_by (str) and query_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and shipment data under ['body']['records'].
    """

    start_time = time.time()

    query_params = event["query_params"]
    client = boto3.client("rds-data")
    s3 = boto3.resource("s3")

    use_cache = query_params.get("use_cache", True)
    # Remove cache override key to later calculate correct request_key
    query_params.pop("use_cache", None)

    # Get cache details
    bucket_name = (
        "transparency-cache-dev"
        if query_params.get("is_dev_env")
        else "transparency-cache"
    )
    print("Orignal Query Params: ", query_params)
    print("Merged Query Params: ", (BASE_QUERY_PARAM | query_params))
    request_key = hashlib.sha256(
        json.dumps(BASE_QUERY_PARAM | query_params).lower().encode()
    ).hexdigest()
    file_name = request_key + ".json"
    cache = s3.Bucket(bucket_name)
    files = cache.objects.all()
    bucket_object = s3.Object(bucket_name, file_name)

    # Lookup request in cache when using cache and if found, then return cached result
    if use_cache:
        for file in files:
            if file_name == file.key:
                response = bucket_object.get()["Body"].read().decode("utf-8")
                print(f"CACHED END TIME: {time.time() - start_time}")
                return json.loads(response)

    if "favorites" in query_params:
        response = {"statusCode": 200, "body": {"records": []}}
        if query_params.get("favorites"):
            qgf = QueryGeneratorFavorites(query_params)
            qgf_thread = ThreadQuery(
                name="qgf", client=client, args=(qgf.favorite_lanes(),)
            )
            qgf_thread.start()
            qgf_response = format_rds_response(qgf_thread.join())
            response["body"]["records"] = qgf_response
        return response

    if query_params.get("get_locations", False):
        qga = QueryGeneratorAnalytics(query_params)
        origins_thread = ThreadQuery(
            name="ori", client=client, args=(qga.get_valid_origins(),)
        )
        origins_thread.start()
        destinations_thread = ThreadQuery(
            name="dest", client=client, args=(qga.get_valid_destinations(),)
        )
        destinations_thread.start()

        origins_response = format_rds_response(origins_thread.join())
        destinations_response = format_rds_response(destinations_thread.join())

        response = {
            "statusCode": 200,
            "body": {
                "records": {
                    "origin_states": list({x["state"] for x in origins_response}),
                    "destination_states": list(
                        {x["state"] for x in destinations_response}
                    ),
                }
            },
        }

        print(f"END TIME: {time.time() - start_time}")

        pp = pprint.PrettyPrinter(indent=4)
        pp.pprint(response["body"]["records"])
        return response

    elif query_params.get("is_export", False):
        qg = QueryGenerator(query_params)
        shipments_thread = ThreadQuery(
            name="ship_export",
            client=client,
            batch=True,
            args=(qg.export_shipments_query(query_params.get("indexBy", "")),),
        )
        shipments_thread.start()

        shipments_response = shipments_thread.join()

        response = {
            "statusCode": 200,
            "body": {"records": {"shipments": shipments_response}},
        }
        return response

    elif query_params.get("analytics", False):
        qga = QueryGeneratorAnalytics(query_params)

        stacked_graph_thread = ThreadQuery(
            name="sg", client=client, args=(qga.stacked_graph_query(),)
        )
        stacked_graph_thread.start()

        cpm_graph_thread = ThreadQuery(
            name="cpmg", client=client, args=(qga.line_graph_query(),)
        )
        cpm_graph_thread.start()

        pie_graph_thread = ThreadQuery(
            name="pie", client=client, args=(qga.dow_volume_query(),)
        )
        pie_graph_thread.start()

        list_increase_thread = ThreadQuery(
            name="lig", client=client, args=(qga.increase_list_query(),)
        )
        list_increase_thread.start()

        list_decrease_thread = ThreadQuery(
            name="lig", client=client, args=(qga.decrease_list_query(),)
        )
        list_decrease_thread.start()

        stacked_graph_thread_response = format_rds_response(stacked_graph_thread.join())
        total_shipments = sum(
            [
                bucket["num_shipments"]
                for bucket in stacked_graph_thread_response
                if bucket["num_shipments"] is not None
            ]
        )
        total_network_shipments = sum(
            [
                bucket["network_num_shipments"]
                for bucket in stacked_graph_thread_response
            ]
        )

        for i in range(len(stacked_graph_thread_response)):
            bucket = stacked_graph_thread_response[i]
            num_shipments = (
                0 if bucket["num_shipments"] is None else bucket["num_shipments"]
            )
            network_num_shipments = (
                0
                if bucket["network_num_shipments"] is None
                else bucket["network_num_shipments"]
            )
            if total_shipments == 0:
                total_shipments = 1
                num_shipments = 0
            stacked_graph_thread_response[i]["num_shipments"] = (
                num_shipments / total_shipments
            )
            if total_network_shipments == 0:
                total_network_shipments = 1
                network_num_shipments = 0
            stacked_graph_thread_response[i]["network_num_shipments"] = (
                network_num_shipments / total_network_shipments
            )

        cpm_graph_thread_response = format_rds_response(cpm_graph_thread.join())
        pie_graph_thread_response = format_rds_response(pie_graph_thread.join())
        list_increase_thread_response = format_rds_response(list_increase_thread.join())
        list_decrease_thread_response = format_rds_response(list_decrease_thread.join())

        response = {
            "statusCode": 200,
            "body": {
                "records": {
                    "line_graphs": cpm_graph_thread_response,
                    "stacked_graph": stacked_graph_thread_response,
                    "pie_graph": pie_graph_thread_response,
                    "list_increase": list_increase_thread_response,
                    "list_decrease": list_decrease_thread_response,
                }
            },
        }

        print(f"END TIME: {time.time() - start_time}")

        pp = pprint.PrettyPrinter(indent=4)
        pp.pprint(response["body"]["records"])
        return response

    qg = QueryGenerator(query_params)

    if query_params.get("savings", False):
        qgs = QueryGeneratorSavings(query_params)
        if query_params.get("sum", False):
            qgs_scs_thread = ThreadQuery(
                name="qgs", client=client, args=(qgs.sum_cost_spend(),)
            )
            qgs_scs_thread.start()
            qgs_scs_response = format_rds_response(qgs_scs_thread.join())
            response = {"statusCode": 200, "body": {"records": qgs_scs_response}}
        elif query_params.get("count", False):
            qg_lane_count_thread = ThreadQuery(
                name="qgs", client=client, args=(qg.count_lanes_query(),)
            )
            qg_lane_count_thread.start()
            lane_count = format_rds_response(qg_lane_count_thread.join())
            response = {"statusCode": 200, "body": {"records": lane_count}}
        elif query_params.get("search_list", False):
            customer_list_thread = ThreadQuery(
                name="clt", client=client, args=(qgs.select_customer_list(),)
            )
            customer_list_thread.start()
            customer_list_response = format_rds_response(customer_list_thread.join())
            response = {
                "statusCode": 200,
                "body": {
                    "records": {
                        "search_list": customer_list_response,
                    }
                },
            }
        else:
            qgs_data_table_thread = ThreadQuery(
                name="qgs", client=client, args=(qgs.select_lanes(),)
            )
            qgs_data_table_thread.start()
            qgs_data_table_response = format_rds_response(qgs_data_table_thread.join())
            response = {
                "statusCode": 200,
                "body": {"records": {"savings_table": qgs_data_table_response}},
            }
        pp = pprint.PrettyPrinter(indent=4)
        pp.pprint(response["body"]["records"])
        return response

    # Run Threads
    s_thread = query_params.get("s_query", 0)
    km_thread = query_params.get("km_query", 0)
    df_thread = query_params.get("df_query", 0)
    dt_thread = query_params.get("dt_query", 0)
    mt_thread = query_params.get("mt_query", 0)
    search_thread = query_params.get("search_list", 0)
    lg_thread = query_params.get("lane_graph_query", 0)

    # ############# Drivers Thread #############
    if mt_thread:
        if query_params["metric_trend_type"].lower() == "mpl":
            drivers_gen_func = qg.mpl_driver_query
        elif query_params["metric_trend_type"].lower() == "vol":
            drivers_gen_func = qg.vol_driver_query
        elif query_params["metric_trend_type"].lower() == "cpm":
            drivers_gen_func = qg.cpm_driver_query
        drivers_thread = ThreadQuery(
            name="dr", client=client, args=(drivers_gen_func(),)
        )
        drivers_thread.start()

    # ############# Key Metrics Thread #############
    if km_thread:
        key_metrics_thread = ThreadQuery(
            name="km", client=client, args=(qg.key_metrics_query(),)
        )
        key_metrics_thread.start()

    # ############# Data Freshness Thread #############
    if df_thread:
        data_freshness_thread = ThreadQuery(
            name="df", client=client, args=(qg.data_freshness_query(),)
        )
        data_freshness_thread.start()

    # ############# Data Table Thread #############
    # ############# Count DT Rows Thread #############
    if dt_thread:
        dt_gen_func = (
            qg.summary_dt_sb_query
            if query_params["index_by"] != "laneId"
            else qg.summary_lane_table_query
        )
        dt_thread = ThreadQuery(name="dt", client=client, args=(dt_gen_func(),))
        dt_thread.start()

        dt_count_func = (
            qg.count_brokers_query
            if query_params["index_by"] != "laneId"
            else qg.count_lanes_query
        )
        count_rows_thread = ThreadQuery(
            name="cr", client=client, args=(dt_count_func(),)
        )
        count_rows_thread.start()

    # ############# Score Thread #############
    if s_thread:
        score_thread = ThreadQuery(name="s", client=client, args=(qg.score_query(),))
        score_thread.start()

    # ############# Search Brokers Thread #############
    # ############# Search Lanes Thread #############
    if search_thread:
        search_brokers_thread = ThreadQuery(
            name="bsl", client=client, args=(qg.search_broker_list(),)
        )
        search_brokers_thread.start()

        search_lanes_thread = ThreadQuery(
            name="bll", client=client, args=(qg.search_lanes_list(),)
        )
        search_lanes_thread.start()

    # ############# Lane Graph Thread #############
    if lg_thread:
        lane_graphs_thread = ThreadQuery(
            name="lg", client=client, args=(qg.cost_spend_volume(),)
        )
        lane_graphs_thread.start()

        lane_graphs_bar_thread = ThreadQuery(
            name="lgb", client=client, args=(qg.cost_volume_dayOfWeek(),)
        )
        lane_graphs_bar_thread.start()

        lane_graphs_cpm_thread = ThreadQuery(
            name="lgc",
            client=client,
            args=(qg.lane_cpm_vs_network_cpm(query_params["laneId"]),),
        )
        lane_graphs_cpm_thread.start()

        lane_graphs_otpd_thread = ThreadQuery(
            name="otpd", client=client, args=(qg.on_time_pickup_delivery(),)
        )
        lane_graphs_otpd_thread.start()

    # Block threads

    if mt_thread:
        drivers_response = format_rds_response(drivers_thread.join())

        if len(drivers_response) != 0:
            metric_sum = 0
            volume_sum = 0
            for i, week_driver in enumerate(drivers_response):
                if week_driver["metric_sum"] is None:
                    continue
                metric_sum += float(week_driver["metric_sum"])
                if "volume" in week_driver:
                    volume_sum += 1
                else:
                    volume_sum += float(week_driver["denom"])

                if drivers_response[i].get("metric_sum", False):
                    del drivers_response[i]["metric_sum"]

                if drivers_response[i].get("denom", False):
                    del drivers_response[i]["denom"]

            drivers_avg = metric_sum / volume_sum if volume_sum != 0 else None

            drivers_response[0]["avg_metric"] = drivers_avg
    else:
        drivers_response = None

    score_response = format_rds_response(score_thread.join()) if s_thread else None
    key_metrics_response = (
        format_rds_response(key_metrics_thread.join()) if km_thread else None
    )
    data_freshness_response = (
        format_rds_response(data_freshness_thread.join()) if df_thread else None
    )
    dt_response = format_rds_response(dt_thread.join()) if dt_thread else None
    count_rows_response = (
        format_rds_response(count_rows_thread.join()) if dt_thread else None
    )
    search_brokers_response = (
        format_rds_response(search_brokers_thread.join()) if search_thread else None
    )
    search_lanes_thread = (
        format_rds_response(search_lanes_thread.join()) if search_thread else None
    )
    lane_graphs_thread_response = (
        format_rds_response(lane_graphs_thread.join()) if lg_thread else None
    )
    lane_graphs_bar_thread_response = (
        format_rds_response(lane_graphs_bar_thread.join()) if lg_thread else None
    )
    lane_graphs_cpm_thread_response = (
        format_rds_response(lane_graphs_cpm_thread.join()) if lg_thread else None
    )
    lane_graphs_otpd_thread_response = (
        format_rds_response(lane_graphs_otpd_thread.join()) if lg_thread else None
    )

    response = {
        "statusCode": 200,
        "body": {
            "records": {
                "score": score_response,
                "key_metrics": key_metrics_response,
                "data_freshness": data_freshness_response,
                "data_table": dt_response,
                "drivers": drivers_response,
                "row_count": count_rows_response,
                "search_list_brokers": search_brokers_response,
                "search_list_lanes": search_lanes_thread,
                "lane_graphs": lane_graphs_thread_response,
                "lane_graphs_bar": lane_graphs_bar_thread_response,
                "lane_graphs_cpm": lane_graphs_cpm_thread_response,
                "lane_graphs_otpd": lane_graphs_otpd_thread_response,
            }
        },
    }

    if query_params["is_drilldown"] and dt_thread:
        # Create a list to keep all processes
        processes = []
        index_by = query_params["index_by"]

        for i, row in enumerate(response["body"]["records"]["data_table"]):
            # Run shipments query
            sub_query_func = (
                qg.drilldown_dt_shipments_aggregation_query
                if query_params.get("agg_by_week", 0)
                else qg.drilldown_dt_shipments_query
            )
            sub_query_count_func = (
                qg.drilldown_dt_shipments_aggregation_query_counts
                if query_params.get("agg_by_week", 0)
                else qg.drilldown_dt_shipments_query_counts
            )

            # Start threads

            if "dt_search_term" in query_params:
                if query_params["index_by"] != "laneId":
                    search_ref_narrower = row["name"]
                else:
                    search_ref_narrower = row["origin"] + " " + row["destination"]
            else:
                search_ref_narrower = None

            t = ThreadQuery(
                name="ddt",
                client=client,
                args=(sub_query_func(row[index_by], name=search_ref_narrower),),
            )
            t.start()

            t_count = ThreadQuery(
                name="ddtc",
                client=client,
                args=(sub_query_count_func(row[index_by], name=search_ref_narrower),),
            )
            t_count.start()

            processes.append((i, [t, t_count]))

        # make sure that all processes have finished
        for index, thread in processes:
            s, s_count = thread[0], thread[1]
            response["body"]["records"]["data_table"][index][
                "shipments"
            ] = format_rds_response(s.join())
            response["body"]["records"]["data_table"][index][
                "shipment_row_count"
            ] = format_rds_response(s_count.join())

    print(f"END TIME: {time.time() - start_time}")

    # Store response in cache if not already there or if use_cache is false
    if (file_name not in cache.objects.all()) or not use_cache:
        bucket_object.put(Body=json.dumps(response))

    pp = pprint.PrettyPrinter(indent=4)
    # pp.pprint(response["body"]["records"])
    return response


def main():
    e = {
        "query_params": {
            "is_customer_direct": 0,
            "is_broker_user": 0,
            "lane_graph_query": 0,
            "search_list": 0,
            "mt_query": 0,
            "index_by": "brokerId",
            "shipperId": "111d145e-2ac6-4585-ac5e-fe22095cbc7f",
            "s_query": 0,
            "km_query": 0,
            "dt_query": 1,
            "df_query": 0,
            "is_drilldown": 0,
            "start_date": "2023-01-01",
            "end_date": "2024-01-30",
            "projection_start_date": "2023-01-01",
            "projection_start_date_30": "2023-12-31",
            "projection_end_date": "2024-01-30",
            "volume_threshold": 0,
            "equipment_type": [
                "Dry Van",
                "Reefer",
                "Flatbed",
                "Power Only",
                "Straight Truck",
            ],
            "metric_trend_type": "CPM",
            "include_accessorials": 1,
            "agg_by_week": 0,
            "page_number": 0,
            "page_size": 5,
            "order_by": "volume",
            "order_by_direction": "DESC",
            "shipment_page_number": 0,
            "shipment_page_size": 2,
            "shipment_order_by_direction": "DESC",
            "shipment_order_by": "originCloseTime",
            "is_week_query": 1,
            "is_dev_env": 1,
            "use_cache": False,
        }
    }

    lambda_handler(e, None)


def populate_cache(event, context):
    """
    Populates S3 cache with predefined list of requests.
    This is done to reduce the initial load time of the application.

    Args:
        event (Any): Not used, but needed for lambda
        context (Any): Not used, but needed for lambda
    """
    start_time = time.time()

    # Get first of current year
    first_of_year = date(date.today().year, 1, 1).strftime("%Y-%m-%d")

    # Get current day
    end_date = datetime.today().strftime("%Y-%m-%d")

    # Get date 30 days ago for projection calculations
    projection_start_date_30 = (datetime.now() - timedelta(30)).strftime("%Y-%m-%d")

    env_db_name = "mvp_db_dev"

    # Get list of shipper ids
    GET_SHIPPERS = f"""SELECT id FROM {env_db_name}.shippers"""
    shipper_ids = format_rds_response(query(GET_SHIPPERS, env_db_name))

    # Get list of broker ids
    GET_BROKERS = f"""SELECT id FROM {env_db_name}.brokers"""
    broker_ids = format_rds_response(query(GET_BROKERS, env_db_name))

    # Get list of pre-determined query param sets for shippers
    query_params = get_query_params()

    # For each query param, run it with all shipper and broker ids in order to pre-populate cache
    for query_param in query_params:
        query_param["start_date"] = first_of_year
        query_param["end_date"] = end_date
        query_param["projection_start_date"] = first_of_year
        query_param["projection_start_date_30"] = projection_start_date_30
        query_param["projection_end_date"] = end_date
        # When set to false will force cache to be updated
        query_param["use_cache"] = 0
        # query_param["is_dev_env"] = 0

        for shipper in shipper_ids:
            query_param["shipperId"] = shipper["id"]
            query_param["index_by"] = "brokerId"
            lambda_handler({"query_params": copy.deepcopy(query_param)}, None)

        # Remove shipperId before caching broker queries
        query_param.pop("shipperId", None)

        for broker in broker_ids:
            query_param["brokerId"] = broker["id"]
            query_param["index_by"] = "shipperId"
            query_param["is_broker_user"] = 1
            lambda_handler({"query_params": copy.deepcopy(query_param)}, None)

    print(f"POPULATING END TIME: {time.time() - start_time}")


if __name__ == "__main__":
    main()
    # populate_cache(None, None)
