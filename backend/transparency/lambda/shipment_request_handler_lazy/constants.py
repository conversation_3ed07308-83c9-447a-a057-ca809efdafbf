# Contains criteria for all focus lane filters
FOCUS_LANE_FILTERS = {
    "High Margin": "(avg_margin > 0.15 AND total_margin > 300)",
    "High Margin/Late Delivery": "(avg_margin > 0.15 AND total_margin > 300 AND avg_otd <= 0.9)",
    "Low Margin": "(avg_margin <= 0.03 AND total_margin < 50)",
    "Late Pickup": "avg_otp <= 0.9",
    "Late Delivery": "avg_otd <= 0.9",
    "Low Prebook": "(avg_clt >= 15 AND avg_prebook <= 7)",
    "High Margin/Low Margin": "((avg_margin > 0.15 AND total_margin > 300) OR (avg_margin <= 0.03 AND total_margin < 50))",
}

BASE_QUERY_PARAM = {
    "is_customer_direct": 0,
    "is_broker_user": 0,
    "lane_graph_query": 0,
    "search_list": 0,
    "mt_query": 1,
    "index_by": "brokerId",
    "s_query": 0,
    "km_query": 0,
    "dt_query": 0,
    "df_query": 0,
    "is_drilldown": 0,
    "start_date": "2023-01-01",
    "end_date": "2023-09-24",
    "projection_start_date": "2023-01-01",
    "projection_start_date_30": "2023-08-25",
    "projection_end_date": "2023-09-24",
    "volume_threshold": 0,
    "equipment_type": ["Dry Van", "Reefer", "Flatbed", "Power Only", "Straight Truck"],
    "metric_trend_type": "VOL",
    "include_accessorials": 1,
    "agg_by_week": 0,
    "page_number": 0,
    "page_size": 100,
    "order_by": "volume",
    "order_by_direction": "DESC",
    "shipment_page_number": 0,
    "shipment_page_size": 8,
    "shipment_order_by_direction": "DESC",
    "shipment_order_by": "originCloseTime",
    "is_week_query": 1,
    "is_dev_env": 1,
}


def get_query_params() -> list:
    query_params_list = []

    # Volume metric trend
    qp = {
        "search_list": 0,
        "mt_query": 1,
        "s_query": 0,
        "km_query": 0,
        "dt_query": 0,
        "metric_trend_type": "VOL",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # MPL metric trend
    qp = {
        "search_list": 0,
        "mt_query": 1,
        "s_query": 0,
        "km_query": 0,
        "dt_query": 0,
        "metric_trend_type": "MPL",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # CPM metric trend, score, key metrics, and data table
    qp = {
        "search_list": 0,
        "mt_query": 1,
        "s_query": 1,
        "km_query": 1,
        "dt_query": 1,
        "metric_trend_type": "CPM",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # Search list and mpl
    qp = {
        "search_list": 1,
        "mt_query": 1,
        "s_query": 0,
        "km_query": 0,
        "dt_query": 0,
        "metric_trend_type": "MPL",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # Search list and volume metric trend
    qp = {
        "search_list": 1,
        "mt_query": 1,
        "s_query": 0,
        "km_query": 0,
        "dt_query": 0,
        "metric_trend_type": "VOL",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # Search list, CPM metric trend, score, key metrics, and data table
    qp = {
        "search_list": 1,
        "mt_query": 1,
        "s_query": 1,
        "km_query": 1,
        "dt_query": 1,
        "metric_trend_type": "CPM",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    # Score, key metrics, and data table
    qp = {
        "search_list": 0,
        "mt_query": 0,
        "s_query": 1,
        "km_query": 1,
        "dt_query": 1,
        "metric_trend_type": "CPM",
    }
    query_params_list.append(BASE_QUERY_PARAM | qp)

    return query_params_list


DENYLIST_PROD = {
    "111d145e-2ac6-4585-ac5e-fe22095cbc7f": {
        "0dff4a1a-5439-465a-9767-b16edf8a3733",
        "75cf4698-605e-413c-bd0d-1b18730c3f56",
        "a57c16f2-226d-453d-891d-82e27c98a967",
    }  # Reyes Holdings : [Coyote Logistics, Uber Freight, MX Solutions]
}

DENYLIST_DEV = {
    "111d145e-2ac6-4585-ac5e-fe22095cbc7f": {
        "0dff4a1a-5439-465a-9767-b16edf8a3733",
        "75cf4698-605e-413c-bd0d-1b18730c3f56",
        "50c79a85-113a-4767-8a7f-0f5c93b0ebf6",
    }  # Reyes Holdings : [Coyote Logistics, Uber Freight, MX Solutions]
}


def get_denylist_querystr(
    is_dev_env: bool, is_broker_user: bool, company_id: str
) -> str:
    """
    Get denylist for query string

    Args:
        is_dev_env (bool): indicates env
        is_broker_user (bool): indicates if user is a broker
        client_id (str): company id of user

    Returns:
        str: string to use in query
    """
    # Get denylist for specified env
    denylist = DENYLIST_DEV if is_dev_env else DENYLIST_PROD

    # If user is a broker and their id is in list then return query string
    if is_broker_user and (company_id in denylist):
        return f" AND shipperId NOT IN {get_formatted_list(denylist[company_id])}"
    # If user is a shipper and their id is in list then return query string
    elif not is_broker_user and (company_id in denylist):
        return f" AND brokerId NOT IN {get_formatted_list(denylist[company_id])}"

    # If no match then return empty string
    return ""


def get_formatted_list(list_to_format: str | set | list) -> str:
    """
    Format given parameter for use with IN or NOT IN keywords in query string

    Args:
        list_to_format (str | set | list): list that will be formatted

        ex. set: {'apple', 'orange'}
        ex. list: ['apple', 'orange']
        ex. str: "['apple', 'orange']"

    Returns:
        str: formatted string to be used in query

        ex. "('apple', 'orange')"
    """

    if isinstance(list_to_format, str):
        return "(" + list_to_format[1:-1] + ")"

    return f"""('{"', '".join(list_to_format)}')"""
