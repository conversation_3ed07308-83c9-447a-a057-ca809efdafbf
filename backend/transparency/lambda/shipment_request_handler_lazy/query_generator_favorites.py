from constants import get_denylist_querystr


class QueryGeneratorFavorites:
    def __init__(self, query_params):
        user_type = query_params.get("user_type", False)

        self.index_key = ""
        if user_type and (user_type == "shipper" or user_type == "admin"):
            self.index_key = "brokerId"
            self.data_table = "brokers"
            self.id_filter_key = "shipperId"
        elif user_type and user_type == "broker":
            self.index_key = "shipperId"
            self.data_table = "shippers"
            self.id_filter_key = "brokerId"

        self.id_filter = f"{self.id_filter_key} = '{query_params.get('userId')}'"

        # Date filter
        start_date_filter = f"DATE(originCloseTime) >= '{query_params['start_date']}'"
        end_date_filter = f"DATE(originCloseTime) <= '{query_params['end_date']}'"
        self.start_date_filter = start_date_filter
        self.end_date_filter = end_date_filter

        # Query filter constructions
        self.order_by_filter = f"ORDER BY {query_params.get('order_by', 'volume')} {query_params.get('order_by_direction','DESC')}"

        self.global_filter = "WHERE " + " AND ".join(
            [self.id_filter, start_date_filter, end_date_filter]
        )

        # Accessorial filter
        self.accessorial_filter_cogs = (
            "-COALESCE(cogsAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue = (
            "-COALESCE(revenueAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum = (
            "-COALESCE(SUM(cogsAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum = (
            "-COALESCE(SUM(revenueAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum_all = (
            "-COALESCE(SUM(cogsAccessorial_all),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum_all = (
            "-COALESCE(SUM(revenueAccessorial_all),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum_canonical = (
            "-COALESCE(SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsAccessorial ELSE NULL END),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum_canonical = (
            "-COALESCE(SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueAccessorial ELSE NULL END),0)"
            if not query_params["include_accessorials"]
            else ""
        )

        # Database selection
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

        # Get Score Totals
        self.score_color_per = (
            "COUNT(CASE WHEN score > 89 THEN 1 ELSE NULL END)/COUNT(score) AS green_score,"
            "COUNT(CASE WHEN score > 79 AND score <= 89 THEN 1 ELSE NULL END)/COUNT(score) AS blue_score,"
            "COUNT(CASE WHEN score > 69 AND score <= 79 THEN 1 ELSE NULL END)/COUNT(score) AS yellow_score,"
            "COUNT(CASE WHEN score <= 69 THEN 1 ELSE NULL END)/COUNT(score) AS red_score_total"
        )

        # favorite lanes
        favorite_lanes = query_params.get("favorites")
        self.favorite_lanes_filter = (
            "WHERE (" if "WHERE" not in self.global_filter else " AND ("
        )

        where_columns = []
        for sb_id in favorite_lanes:
            if favorite_lanes[sb_id] == []:
                continue
            param = (
                f"(shipperId = '{sb_id}'"
                if user_type == "broker"
                else f"(brokerId = '{sb_id}'"
            )
            param += f" AND laneId IN {'(' + str(favorite_lanes[sb_id])[1:-1] + ')'})"
            where_columns.append(param)

        self.favorite_lanes_filter += " OR ".join(where_columns)
        self.favorite_lanes_filter += ")"

        # Denylist
        company_id = (
            query_params["brokerId"]
            if query_params["is_broker_user"]
            else query_params["shipperId"]
        )
        self.denylist = get_denylist_querystr(
            query_params["is_dev_env"], query_params["is_broker_user"], company_id
        )

    def favorite_lanes(self):
        data_table_favorite_lane_query_txt = f"""
                                     SELECT * FROM
                                    (SELECT
                                        sb.name,
                                        s.{self.index_key},
                                        s.laneId,
                                        CONCAT(cityTableOriginCity, ", ", cityTableOriginState) as origin, CONCAT(cityTableDestinationCity, ", ", cityTableDestinationState) as destination,
                                        AVG(score) as score,
                                        AVG(carrierScore) as avg_carrier_score,
                                        AVG(brokerScore) as avg_broker_score,
                                        AVG(costScore) as avg_cost_score,
                                        AVG(ltuScore) as avg_ltu_score,
                                        AVG(prebookScore) as avg_prebook_score,
                                        AVG(otpScore) as avg_otp_score,
                                        AVG(otdScore) as avg_otd_score,
                                        equipmenttype,
                                        shipmentmode,
                                        (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_revenue,
                                        (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})/COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) as avg_cogs,
                                        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) AS avg_margin,
                                        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END){self.accessorial_filter_revenue_sum_canonical}) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END){self.accessorial_filter_cogs_sum_canonical})) / COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) AS avg_margin_dollars,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) AS total_margin,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN clt ELSE NULL END) AS avg_clt,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN blt ELSE NULL END) AS avg_blt,
                                        AVG(CASE WHEN shipmentClass = 'canonical' THEN prebook ELSE NULL END) AS avg_prebook,
                                        COUNT(CASE
                                                WHEN shipmentClass = 'canonical' THEN 1
                                                ELSE NULL
                                            END) as volume,
                                        AVG(distanceMiles) as avg_miles,
                                        AVG(CASE
                                                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otd,
                                        AVG(CASE
                                                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1
                                                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0
                                                ELSE NULL
                                            END
                                        ) as avg_otp,
                                        AVG(CASE
                                                WHEN loadCreationTime is NULL THEN NULL
                                                ELSE 1
                                            END
                                        ) as avg_load_creation_time,
                                        {self.score_color_per}
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    JOIN {self.db}.{self.data_table} AS sb ON s.{self.index_key} = sb.id
                                    {self.global_filter}{self.favorite_lanes_filter}{self.denylist}
                                    GROUP BY cityTableOriginCity, cityTableOriginState, cityTableDestinationCity, cityTableDestinationState, equipmenttype, {self.index_key}
                                    {self.order_by_filter}) AS TMP
                                    """
        # print(data_table_favorite_lane_query_txt)
        return data_table_favorite_lane_query_txt
