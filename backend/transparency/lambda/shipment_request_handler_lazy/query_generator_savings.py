# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.
from constants import get_denylist_querystr


class QueryGeneratorSavings:
    def __init__(self, query_params):
        # Id filter
        if "brokerId" in query_params:
            id_filter_key = "brokerId"
            id_filter_value = query_params["brokerId"]
            self.dt_user_filter = "shipperId"
            self.data_table = "shippers"

            # Id filter for drilldown
            if query_params["is_drilldown"] and "shipperId" in query_params:
                id_filter_key_drilldown = "shipperId"
                id_filter_value_drilldown = query_params["shipperId"]
            elif query_params["is_drilldown"] and "laneId" in query_params:
                id_filter_key_drilldown = "laneId"
                id_filter_value_drilldown = query_params["laneId"]

        elif "shipperId" in query_params:
            id_filter_key = "shipperId"
            id_filter_value = query_params["shipperId"]
            self.dt_user_filter = "brokerId"
            self.data_table = "brokers"

            # Id filter for drilldown
            if query_params["is_drilldown"] and "brokerId" in query_params:
                id_filter_key_drilldown = "brokerId"
                id_filter_value_drilldown = query_params["brokerId"]
            elif query_params["is_drilldown"] and "laneId" in query_params:
                id_filter_key_drilldown = "laneId"
                id_filter_value_drilldown = query_params["laneId"]

        elif "laneId" in query_params:
            id_filter_key = "laneId"
            id_filter_value = query_params["laneId"]

        # User id filter
        user_type = query_params.get("user_type", "")
        if "shipper" in user_type:
            self.cust_table_type = "brokers"
            self.cust_table_key = "brokerId"
            self.user_id_filter = f"""shipperId = '{query_params["shipperId"]}'"""
        elif "broker" in user_type:
            self.cust_table_type = "shippers"
            self.cust_table_key = "shipperId"
            self.user_id_filter = f"""brokerId = '{query_params["brokerId"]}'"""

        # Index filter
        self.index_key = query_params["index_by"]

        if type(id_filter_value) == str:
            id_filter = f"{id_filter_key} = '{id_filter_value}'"
        elif type(id_filter_value) == list:
            id_filter = f"{id_filter_key} in {'(' + str(id_filter_value)[1:-1] + ')'}"

        # Include both ids if in drilldown
        if query_params["is_drilldown"] and type(id_filter_value_drilldown) == str:
            id_filter += (
                f" AND {id_filter_key_drilldown} = '{id_filter_value_drilldown}'"
            )
        elif query_params["is_drilldown"] and type(id_filter_value_drilldown) == list:
            id_filter += f" AND {id_filter_key_drilldown} in {'(' + str(id_filter_value_drilldown)[1:-1] + ')'}"

        self.id_filter = id_filter

        # Date filter
        start_date_filter = f"DATE(originCloseTime) >= '{query_params['start_date']}'"
        end_date_filter = f"DATE(originCloseTime) <= '{query_params['end_date']}'"
        self.start_date_filter = start_date_filter
        self.end_date_filter = end_date_filter

        # Volume and equipment type filter
        self.volume_filter = f"WHERE volume >= {query_params['volume_threshold']}"
        equipment_type_filter = (
            f"equipmenttype in {'(' + str(query_params['equipment_type'])[1:-1] + ')'}"
            if len(query_params["equipment_type"]) > 0
            else None
        )

        # Page filter
        page_number = query_params["page_number"]
        page_size = query_params["page_size"]

        # Query filter constructions
        self.id_filter_key = id_filter_key
        self.order_by_filter = (
            f"ORDER BY {query_params['order_by']} {query_params['order_by_direction']}"
        )
        self.page_filter = f"LIMIT {page_size} OFFSET {page_number*page_size}"
        self.sub_query_filter = (
            f"WHERE {equipment_type_filter}" if equipment_type_filter else ""
        )

        # Append list filtration for savings page
        savings_lanes = query_params.get("savings_lanes")
        if "savings_lanes" in query_params and not equipment_type_filter:
            self.sub_query_filter = (
                f"WHERE l.lanetableid IN {'(' + str(savings_lanes)[1:-1] + ')'}"
            )
        elif "savings_lanes" in query_params:
            self.sub_query_filter += (
                f" AND id IN {'(' + str(savings_lanes)[1:-1] + ')'}"
            )

        self.global_filter = "WHERE " + " AND ".join(
            [id_filter, start_date_filter, end_date_filter]
        )
        self.network_global_filter = "WHERE " + " AND ".join(
            [start_date_filter, end_date_filter]
        )

        # Delta filter
        if "delta_start_date" in query_params:
            delta_start_date_filter = (
                f"DATE(originCloseTime) >= '{query_params['delta_start_date']}'"
            )
            delta_end_date_filter = (
                f"DATE(originCloseTime) <= '{query_params['delta_end_date']}'"
            )
            self.delta_filter = "WHERE " + " AND ".join(
                [id_filter, delta_start_date_filter, delta_end_date_filter]
            )
        else:
            self.delta_filter = ""

        # Accessorial filter
        self.accessorial_filter_cogs = (
            "-COALESCE(cogsAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue = (
            "-COALESCE(revenueAccessorial,0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_cogs_sum = (
            "-COALESCE(SUM(cogsAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )
        self.accessorial_filter_revenue_sum = (
            "-COALESCE(SUM(revenueAccessorial),0)"
            if not query_params["include_accessorials"]
            else ""
        )

        # Small search
        # Contains numbers -> must be ref
        #  Run ref filter

        # Pure char -> could be either, run full query
        #  if str is substring of broker
        #    gather all shipments for that broker (empty filter)
        #  else
        #    run ref filter

        if "dt_search_term" in query_params:
            search_term = query_params["dt_search_term"]
            self.search_term = search_term
            self.is_numeric = query_params["is_numeric"]
            self.search_ref_filter = f"shipperPrimaryReference LIKE '%{search_term}%'"

            if query_params["index_by"] != "laneId":
                self.search_filter = (
                    f" AND (name LIKE '%{search_term}%' OR {self.search_ref_filter})"
                )
            else:
                self.search_filter = f" AND (cityTableOriginCity LIKE '%{search_term}%' OR cityTableDestinationCity LIKE '%{search_term}%' OR cityTableOriginState LIKE '%{search_term}%' OR cityTableDestinationState LIKE '%{search_term}%' OR {self.search_ref_filter})"

        else:
            self.search_filter = ""

        self.shipment_class_filter = (
            " AND shipmentClass IN ('canonical')"
            if query_params["is_drilldown"]
            else ""
        )

        # Database selection
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

        # Denylist
        company_id = (
            query_params["brokerId"]
            if query_params["is_broker_user"]
            else query_params["shipperId"]
        )
        self.denylist = get_denylist_querystr(
            query_params["is_dev_env"], query_params["is_broker_user"], company_id
        )

    ###################################################################################################################
    #
    #              SELECT LANES
    #
    ###################################################################################################################

    def select_lanes(self):
        data_table_lane_query_txt = f"""
                                     SELECT * FROM
                                    (SELECT
                                        GROUP_CONCAT(distinct s.{self.index_key}) as {self.index_key},
                                        CONCAT(cityTableOriginCity, ", ", cityTableOriginState) as origin,
                                        GROUP_CONCAT(DISTINCT oz.originZip) AS originZips,
                                        CONCAT(cityTableDestinationCity, ", ", cityTableDestinationState) as destination,
                                        GROUP_CONCAT(DISTINCT dz.destinationZip) AS destinationZips,
                                        equipmenttype,
                                        shipmentmode,
                                        (SUM(revenueTotal){self.accessorial_filter_revenue_sum})/COUNT(revenueTotal) as avg_revenue,
                                        (SUM(cogsTotal){self.accessorial_filter_cogs_sum})/COUNT(cogsTotal) as avg_cogs,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) / (SUM(revenueTotal){self.accessorial_filter_revenue_sum}) AS avg_margin,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) / COUNT(*) AS avg_margin_dollars,
                                        ((SUM(revenueTotal){self.accessorial_filter_revenue_sum}) - (SUM(cogsTotal){self.accessorial_filter_cogs_sum})) AS total_margin,
                                        COUNT(*) as volume,
                                        AVG(distanceMiles) as avg_miles
                                    FROM {self.db}.shipments AS s
                                    JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                    JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                    JOIN (SELECT id AS originZipId, zip AS originZip FROM {self.db}.zips) AS oz ON s.originZipId = oz.originZipId
                                    JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                    JOIN (SELECT id AS destinationZipId, zip AS destinationZip FROM {self.db}.zips) AS dz ON s.destinationZipId = dz.destinationZipId
                                    {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.denylist}
                                    GROUP BY cityTableOriginCity, cityTableOriginState, cityTableDestinationCity, cityTableDestinationState, equipmenttype
                                    {self.order_by_filter}) AS TMP
                                    {self.volume_filter}
                                    {self.page_filter};
                                    """
        # print(data_table_lane_query_txt)
        return data_table_lane_query_txt

    ###################################################################################################################
    #
    #              SELECT AGGREGATES
    #
    ###################################################################################################################

    def sum_cost_spend(self):
        aggregate_cost_spend_query = f"""
                                        SELECT
                                            SUM(s.revenueTotal) as sum_revenue,
                                            SUM(s.cogsTotal) as sum_cogs,
                                            COUNT(*) as sum_vol
                                        FROM {self.db}.shipments AS s
                                        JOIN (SELECT id as lanetableid, origincityid, destinationcityid, shipmentmode, equipmenttype FROM {self.db}.lanes {self.sub_query_filter}) AS l ON s.laneid = l.lanetableid
                                        JOIN (SELECT id AS cityTableOriginId, city AS cityTableOriginCity, state AS cityTableOriginState FROM {self.db}.cities) AS c ON l.originCityId = c.cityTableOriginId
                                        JOIN (SELECT id AS cityTableDestinationId, city AS cityTableDestinationCity, state AS cityTableDestinationState FROM {self.db}.cities) AS cd ON l.destinationCityId = cd.cityTableDestinationId
                                        {self.global_filter}{self.search_filter}{self.shipment_class_filter}{self.denylist}
                                        """
        return aggregate_cost_spend_query

    ###################################################################################################################
    #
    #              SELECT BROKER LIST
    #
    ###################################################################################################################

    def select_customer_list(self):
        date_filter = " AND " + " AND ".join(
            [self.start_date_filter, self.end_date_filter]
        )

        search_query = f"""
                            SELECT
                                s.{self.cust_table_key} AS id,
                                name
                            FROM {self.db}.shipments AS s
                            JOIN (SELECT id, name FROM {self.db}.{self.cust_table_type}) AS l ON s.{self.cust_table_key} = l.id
                            WHERE {self.user_id_filter}{date_filter}{self.denylist}
                            GROUP BY name;
                        """
        # print(search_query)
        return search_query
