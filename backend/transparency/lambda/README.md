# Lambda Infrastructure

## Dependencies/Requirements
- `Docker`: You should be able to run `docker` commands. Docker Installation: https://docs.docker.com/get-started/

- Bash commands/ability to run bash files
- Administrator access (i.e. `sudo`)
- `credentials` file in `shipment_request_handler` directory.

## Running Docker Server
Docker will first build the Docker image with the specified constraints (i.e. library versions, python 3.8). In this case, we are using a special python3.8 image that mimics the AWS lambda environment. Your credentials file will allow you to run the file and access other AWS resources.

To build the image and host on a server:

```
source start-docker-server.sh
```

## Lambda output
The lambda function is now hosted and connected. To run with event; the output:
```
source run-lambda.sh
```

## TODOS:
- Event (i.e. query) is specified in `run-lambda.sh`. Make it so this can be specified from command line.
- Combine two scripts into one.
- Deploying to lambda
