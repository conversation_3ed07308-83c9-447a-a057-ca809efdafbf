import boto3


def lambda_handler(event, context):
    # get the shipper business unit for the provided email
    dynamodb = boto3.resource("dynamodb")
    SBU_TABLE = dynamodb.Table("GetShipperBusinessUnit")
    user_email_with_whitespace = str(event["email"]).lower()
    user_email = "".join(user_email_with_whitespace.split())
    response = SBU_TABLE.get_item(Key={"email": user_email})

    return {"statusCode": 200, "body": response["Item"]}
