# Set a base image that includes Lambda runtime API:
# Public docker image: https://gallery.ecr.aws/lambda/python
FROM public.ecr.aws/lambda/python:3.8

# Copy and install requirements.txt to environment
ADD common common
COPY setup.py .
RUN pip3 install -r common/requirements.txt
RUN pip3 install .
COPY web_backend/lambda/scorecard_request_handler/requirements.txt .
RUN pip3 install -r requirements.txt

# Copy python code to environment
COPY web_backend/lambda/scorecard_request_handler/lambda_function.py .
COPY web_backend/lambda/scorecard_request_handler/query_generator.py .

# Set CMD to handler
CMD [ "lambda_function.lambda_handler" ]
