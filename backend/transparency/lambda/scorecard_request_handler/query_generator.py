# Class to construct / format queries
# Re-invention of sqlalchemy, but fstrings + boto3 are faster than sqlalchemy + aurora data api by order of ~2x
# We are optimizing for raw speed.


class QueryGenerator:
    def __init__(self, query_params):
        self.db = "mvp_db_dev" if query_params.get("is_dev_env") else "mvp_db"

    def carrier_rank_query(self):
        return """
                   SELECT RateType AS carrier_rank, COUNT(*) AS volume
                   FROM utz_data
                   GROUP BY RateType;
               """

    # returns top carrier and total spend across all carriers
    def total_spend(self):
        return """
                   SELECT name, value FROM
                       (SELECT CarrierName AS name, (SELECT SUM(PayableTotalRate) FROM utz_data) AS value
                       FROM
                       (SELECT CarrierName, SUM(PayableTotalRate) AS tot_spend
                       FROM utz_data
                       GROUP BY CarrierName) AS u
                       ORDER BY u.tot_spend DESC
                       LIMIT 1) AS top

                   UNION ALL

                   SELECT name, value FROM
                       (SELECT CarrierName AS name, (SELECT SUM(PayableTotalRate) FROM utz_data) AS value
                       FROM
                       (SELECT CarrierName, SUM(PayableTotalRate) AS tot_spend
                       FROM utz_data
                       GROUP BY CarrierName) AS u
                       ORDER BY u.tot_spend ASC
                       LIMIT 1) AS bottom;
               """

    def average_otp(self):
        return """
                   SELECT name, value FROM
                       (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN origin_delay_seconds <= 0 THEN 1 WHEN origin_delay_seconds > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                       (SELECT CarrierName,
                       AVG(CASE
                           WHEN origin_delay_seconds <= 0 THEN 1
                           WHEN origin_delay_seconds > 0 THEN 0
                           ELSE NULL
                           END) as avg_otp,
                       COUNT(*) as volume
                       FROM utz_data
                       GROUP BY CarrierName) AS ut
                       ORDER BY avg_otp DESC, volume DESC
                       LIMIT 1) AS top

                   UNION ALL

                   SELECT name, value FROM
                       (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN origin_delay_seconds <= 0 THEN 1 WHEN origin_delay_seconds > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                       (SELECT CarrierName,
                       AVG(CASE
                           WHEN origin_delay_seconds <= 0 THEN 1
                           WHEN origin_delay_seconds > 0 THEN 0
                           ELSE NULL
                           END) as avg_otp,
                       COUNT(*) as volume
                       FROM utz_data
                       GROUP BY CarrierName) AS ub
                       ORDER BY avg_otp ASC, volume ASC
                       LIMIT 1) AS bottom;
               """

    def average_otd(self):
        return """
                    SELECT name, value FROM
                        (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN destination_delay_seconds <= 0 THEN 1 WHEN destination_delay_seconds > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                        (SELECT CarrierName,
                        AVG(CASE
                            WHEN destination_delay_seconds <= 0 THEN 1
                            WHEN destination_delay_seconds > 0 THEN 0
                            ELSE NULL
                            END) as avg_otd,
                        COUNT(*) as volume
                        FROM utz_data
                        GROUP BY CarrierName) AS u
                        ORDER BY avg_otd DESC, volume DESC
                        LIMIT 1) AS top

                    UNION ALL

                    SELECT name, value FROM
                        (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN destination_delay_seconds <= 0 THEN 1 WHEN destination_delay_seconds > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                        (SELECT CarrierName,
                        AVG(CASE
                            WHEN destination_delay_seconds <= 0 THEN 1
                            WHEN destination_delay_seconds > 0 THEN 0
                            ELSE NULL
                            END) as avg_otd,
                        COUNT(*) as volume
                        FROM utz_data
                        GROUP BY CarrierName) AS u
                        ORDER BY avg_otd ASC, volume ASC
                        LIMIT 1) AS bottom;
               """

    def average_otd_plan(self):
        return """
                    SELECT name, value FROM
                        (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) <= 0 THEN 1 WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                        (SELECT CarrierName,
                        AVG(CASE
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) <= 0 THEN 1
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) > 0 THEN 0
                            ELSE NULL
                            END) as avg_otd,
                        COUNT(*) as volume
                        FROM utz_data
                        GROUP BY CarrierName) AS u
                        ORDER BY avg_otd DESC, volume DESC
                        LIMIT 1) AS top

                    UNION ALL

                    SELECT name, value FROM
                        (SELECT CarrierName AS name, (SELECT AVG(CASE WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) <= 0 THEN 1 WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) > 0 THEN 0 ELSE NULL END) FROM utz_data) AS value FROM
                        (SELECT CarrierName,
                        AVG(CASE
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) <= 0 THEN 1
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) > 0 THEN 0
                            ELSE NULL
                            END) as avg_otd,
                        COUNT(*) as volume
                        FROM utz_data
                        GROUP BY CarrierName) AS u
                        ORDER BY avg_otd ASC, volume ASC
                        LIMIT 1) AS bottom;
               """

    def volume_per_day(self):
        return """
                    SELECT SPLIT_STR(FirstPickArrivalDate, " ", 1) AS week, COUNT(FirstPickArrivalDate) AS volume
                    FROM utz_data
                    WHERE FirstPickArrivalDate IS NOT NULL AND DATE(FirstPickArrivalDate) > '2022-12-31'
                    GROUP BY week;
               """

    def carrier_table(self):
        return """
                    SELECT CarrierName AS carrier_name, LoadAssetType AS asset_type,
                    SUM(PayableTotalRate) AS total_spend,
                    AVG(CASE
                            WHEN DATEDIFF(FirstPickArrivalDate, FirstPickPlanDateEnd) <= 0 THEN 1
                            WHEN DATEDIFF(FirstPickArrivalDate, FirstPickPlanDateEnd) > 0 THEN 0
                            ELSE NUlL
                        END) as avg_otp_plan,
                    AVG(CASE
                            WHEN origin_delay_seconds <= 0 THEN 1
                            WHEN origin_delay_seconds > 0 THEN 0
                            ELSE NULL
                        END) as avg_otp,
                    AVG(CASE
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) <= 0 THEN 1
                            WHEN DATEDIFF(LastDropArrivalDate, LastDropPlanDateEnd) > 0 THEN 0
                            ELSE NULL
                        END) as avg_otd_plan,
                    AVG(CASE
                            WHEN destination_delay_seconds <= 0 THEN 1
                            WHEN destination_delay_seconds > 0 THEN 0
                            ELSE NULL
                        END) as avg_otd,
                    COUNT(*) AS volume
                    FROM utz_data
                    GROUP BY carrier_name, asset_type;
               """
