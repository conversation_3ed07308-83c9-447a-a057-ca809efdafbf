import pprint

import boto3
from query_generator import QueryGenerator

from common import query_utils


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    :param event: Frontend parameter with account query_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and account data under ['body']['records'].
    """

    query_params = event["query_params"]
    client = boto3.client("rds-data")

    qg = QueryGenerator(query_params)

    carrier_rank_thread = query_utils.ThreadQuery(
        name="cr",
        client=client,
        args=(qg.carrier_rank_query(),),
    )
    carrier_rank_thread.start()

    total_spend_thread = query_utils.ThreadQuery(
        name="ats",
        client=client,
        args=(qg.total_spend(),),
    )
    total_spend_thread.start()

    average_otp_thread = query_utils.ThreadQuery(
        name="otp",
        client=client,
        args=(qg.average_otp(),),
    )
    average_otp_thread.start()

    average_otd_thread = query_utils.ThreadQuery(
        name="otd",
        client=client,
        args=(qg.average_otd(),),
    )
    average_otd_thread.start()

    average_otd_plan_thread = query_utils.ThreadQuery(
        name="otd_plan",
        client=client,
        args=(qg.average_otd_plan(),),
    )
    average_otd_plan_thread.start()

    volume_per_day_thread = query_utils.ThreadQuery(
        name="volume_day",
        client=client,
        args=(qg.volume_per_day(),),
    )
    volume_per_day_thread.start()

    carrier_table_thread = query_utils.ThreadQuery(
        name="ct",
        client=client,
        args=(qg.carrier_table(),),
    )
    carrier_table_thread.start()

    carrier_rank_response = query_utils.format_rds_response(carrier_rank_thread.join())
    total_spend_response = query_utils.format_rds_response(total_spend_thread.join())
    average_otp_response = query_utils.format_rds_response(average_otp_thread.join())
    average_otd_response = query_utils.format_rds_response(average_otd_thread.join())
    average_otd_plan_response = query_utils.format_rds_response(
        average_otd_plan_thread.join()
    )
    volume_per_day_response = query_utils.format_rds_response(
        volume_per_day_thread.join()
    )
    carrier_table_response = query_utils.format_rds_response(
        carrier_table_thread.join()
    )

    response = {
        "statusCode": 200,
        "body": {
            "records": {
                "carrier_rank": carrier_rank_response,
                "total_spend": total_spend_response,
                "average_otp": average_otp_response,
                "average_otd": average_otd_response,
                "average_otd_plan": average_otd_plan_response,
                "volume_per_day": volume_per_day_response,
                "carrier_table": carrier_table_response,
            }
        },
    }

    pp = pprint.PrettyPrinter(indent=4)
    pp.pprint(response["body"]["records"])
    return response


def main():
    # TODO - add sample query params
    e = {"query_params": {"is_dev_env": 1}}
    lambda_handler(e, None)


if __name__ == "__main__":
    main()
