---
swagger: "2.0"
info:
  description: Basic web API.
  version: 1.0.0
  title: Data Access
schemes:
- https
- http
paths:
  /getShipment:
    get:
      tags:
      - shipper
      summary: endpoint for shipment data
      description: Returns shipments indexed by index_by param.
      operationId: getShipperData
      produces:
      - application/json
      parameters:
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/request'
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/response'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/error'
        "401":
          description: invalid user
          schema:
            $ref: '#/definitions/error'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/error'
definitions:
  request:
    type: object
    properties:
      query_params:
        type: object
        description: contains shipper and broker ids for shipment collection
      index_by:
        type: string
        description: specifies index of response array
  response:
    type: object
    properties:
      code:
        type: integer
        format: int32
        description: contains status code
      records:
        type: array
        description: contains all records that matched request criteria
        items:
          type: object
          description: singular record in response array
  error:
    type: object
    properties:
      code:
        type: integer
        format: int32
        description: error status code
      message:
        type: string
        description: error message
