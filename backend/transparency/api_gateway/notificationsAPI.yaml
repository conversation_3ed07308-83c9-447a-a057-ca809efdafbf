basePath: /dev-1
definitions:
  error:
    properties:
      code:
        description: error status code
        format: int32
        type: integer
      message:
        description: error message
        type: string
    type: object
  request:
    properties:
      query_params:
        description: contains notification data
        properties: {}
        type: object
    type: object
  response:
    properties:
      code:
        description: contains status code
        format: int32
        type: integer
      records:
        description: contains all records that matched request criteria
        items:
          description: singular record in response array
          properties: {}
          type: object
        type: array
    type: object
host: ij1yddlun5.execute-api.us-east-1.amazonaws.com
info:
  description: Basic notifications web API.
  title: Notifications Handler Dev
  version: 1.0.0
paths:
  /createNewSubscriber:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    put:
      consumes:
      - application/json
      parameters:
      - in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request'
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
  /getNotification:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
        '400':
          description: 400 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
        '500':
          description: 500 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,POST'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    post:
      consumes:
      - application/json
      operationId: getNotifications
      parameters:
      - in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request'
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
        '400':
          description: 400 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '401':
          description: 401 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '403':
          description: 403 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '500':
          description: 500 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
  /getSubscriptionStatus:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,POST'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    post:
      consumes:
      - application/json
      parameters:
      - in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request'
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
        '400':
          description: 400 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '401':
          description: 401 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '403':
          description: 403 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '500':
          description: 500 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
  /putNotification:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    put:
      consumes:
      - application/json
      operationId: getNotifications
      parameters:
      - in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request'
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
        '400':
          description: 400 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '401':
          description: 401 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '500':
          description: 500 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
  /updateNotificationStatus:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    put:
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
        '400':
          description: 400 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '401':
          description: 401 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
        '500':
          description: 500 response
          headers:
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/error'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
  /updateSubscriptionStatus:
    options:
      consumes:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
      x-amazon-apigateway-integration:
        passthroughBehavior: when_no_match
        requestTemplates:
          application/json: '{"statusCode": 200}'
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
              method.response.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: mock
    put:
      consumes:
      - application/json
      parameters:
      - in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/request'
      produces:
      - application/json
      responses:
        '200':
          description: 200 response
          headers:
            Access-Control-Allow-Headers:
              type: string
            Access-Control-Allow-Methods:
              type: string
            Access-Control-Allow-Origin:
              type: string
          schema:
            $ref: '#/definitions/response'
      x-amazon-apigateway-integration:
        contentHandling: CONVERT_TO_TEXT
        httpMethod: POST
        passthroughBehavior: when_no_match
        responses:
          default:
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: '''*'''
            statusCode: '200'
        type: aws_proxy
        uri: arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:903881895532:function:notification_handler_dev/invocations
schemes:
- https
swagger: '2.0'
x-amazon-apigateway-gateway-responses:
  DEFAULT_4XX:
    responseParameters:
      gatewayresponse.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
      gatewayresponse.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
      gatewayresponse.header.Access-Control-Allow-Origin: '''*'''
  DEFAULT_5XX:
    responseParameters:
      gatewayresponse.header.Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
      gatewayresponse.header.Access-Control-Allow-Methods: '''OPTIONS,PUT'''
      gatewayresponse.header.Access-Control-Allow-Origin: '''*'''
