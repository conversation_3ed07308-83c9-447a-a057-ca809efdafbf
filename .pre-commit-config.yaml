# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        args: [--safe]

-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    # -   id: check-yaml
    -   id: check-added-large-files
    -   id: debug-statements
        language_version: python3

-   repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
    -   id: flake8
        language_version: python3
        args: ["--extend-ignore=E501, F601, E402, W503, E203"]


-   repo: https://github.com/asottile/reorder_python_imports
    rev: v3.9.0
    hooks:
    -   id: reorder-python-imports
        args: [--application-directories=.:src, --py36-plus]

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.3.2
    hooks:
    -   id: pyupgrade
        args: [--py36-plus]

# TODO(P2): Add mypy.
# -   repo: https://github.com/pre-commit/mirrors-mypy
#     rev: v1.2.0
#     hooks:
#     -   id: mypy
#         files: ^src/
#         args: []

-   repo: https://github.com/pre-commit/mirrors-eslint
    rev: 'v8.49.0'  # Use the sha / tag you want to point at
    hooks:
    -   id: eslint
        files: \.([jt]sx?|vue)$  # *.js, *.jsx, *.ts, *.tsx and *.vue
        types: [file]
        exclude: ^machine_learning/freightgpt/web/amplify/backend/types/amplify-dependent-resources-ref.d.ts

# -   repo: https://github.com/pre-commit/mirrors-prettier
#     rev: 'v3.0.3'  # Use the sha / tag you want to point at
#     hooks:
#     -   id: prettier
#         files: \.([jt]sx?|vue)$  # *.js, *.jsx, *.ts, *.tsx and *.vue
#         types: [file]
