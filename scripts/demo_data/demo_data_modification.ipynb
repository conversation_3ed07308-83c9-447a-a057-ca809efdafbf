{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pwd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_demo_data = pd.read_excel(\"./Green Clean Products Demo Data.xlsx\")\n", "initial_demo_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_demo_data.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Metric Trends"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import matplotlib.dates as mdates\n", "\n", "def generate_metric_trends_graph(df):\n", "    # Convert 'Origin Appt Close' to datetime if it isn't already\n", "    df['Origin Appt Close'] = pd.to_datetime(df['Origin Appt Close'])\n", "\n", "    # Ensure that 'Revenue Total', 'COGS Total', and 'Distance Miles' are numeric\n", "    df['Revenue Total'] = pd.to_numeric(df['Revenue Total'], errors='coerce')\n", "    df['COGS Total'] = pd.to_numeric(df['COGS Total'], errors='coerce')\n", "    df['Distance Miles'] = pd.to_numeric(df['Distance Miles'], errors='coerce')\n", "\n", "    # Handle any NaN values (optional, depending on your data)\n", "    df[['Revenue Total', 'COGS Total', 'Distance Miles']] = df[['Revenue Total', 'COGS Total', 'Distance Miles']].fillna(0)\n", "\n", "    df = df[['Origin Appt Close', 'Revenue Total', 'COGS Total', 'Distance Miles']]\n", "\n", "    # Set 'Origin Appt Close' as the index\n", "    df.set_index('Origin Appt Close', inplace=True)\n", "    df = df[df.index >= '2022-01-01']\n", "\n", "    # Resample the data by week starting from January 1st\n", "    weekly_data = df.resample('W', origin='2023-01-01').sum()\n", "\n", "    # Calculate Revenue per Mile and COGS per Mile\n", "    weekly_data['Revenue per Mile'] = weekly_data['Revenue Total'] / weekly_data['Distance Miles']\n", "    weekly_data['COGS per Mile'] = weekly_data['COGS Total'] / weekly_data['Distance Miles']\n", "\n", "    # Plot using Seaborn without resetting the index\n", "    plt.figure(figsize=(12,6))\n", "\n", "    sns.lineplot(x=weekly_data.index, y=weekly_data['Revenue per Mile'], label='Revenue per Mile', marker='o')\n", "    sns.lineplot(x=weekly_data.index, y=weekly_data['COGS per Mile'], label='COGS per Mile', marker='o')\n", "\n", "    plt.xlabel('Week')\n", "    plt.ylabel('Amount per Mile')\n", "    plt.title('Weekly Revenue per Mile and COGS per Mile Over Time')\n", "    plt.legend()\n", "\n", "    # Format x-axis to display weeks properly\n", "    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))\n", "    plt.gca().xaxis.set_major_locator(mdates.WeekdayLocator(byweekday=mdates.MO))\n", "\n", "    plt.xticks(rotation=45)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "generate_metric_trends_graph(initial_demo_data.copy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Assuming a sample DataFrame with the specified columns to calculate metrics\n", "\n", "def generate_key_metrics(df):\n", "    # Calculate the required metrics\n", "    margin_per_load = (df['Revenue Total'].sum() - df['COGS Total'].sum()) / len(df)\n", "    total_margin = df['Revenue Total'].sum() - df['COGS Total'].sum()\n", "    total_spend = df['Revenue Total'].sum()\n", "    spend_per_load = df['Revenue Total'].sum() / len(df)\n", "    volume = len(df)\n", "    df_non_zero_revenue = df[df['Revenue Total'] != 0]\n", "    avg_margin = ((df_non_zero_revenue['Revenue Total'] - df_non_zero_revenue['COGS Total']) / df_non_zero_revenue['Revenue Total']).mean()\n", "\n", "    # Compile results\n", "    metrics = {\n", "        'Margin per Load': margin_per_load,\n", "        'Total Margin': total_margin,\n", "        'Total Spend': total_spend,\n", "        'Spend per Load': spend_per_load,\n", "        'Volume': volume,\n", "        'Avg Margin': avg_margin\n", "    }\n", "    \n", "    return metrics\n", "\n", "generate_key_metrics(initial_demo_data.copy())\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "init_main.initialize()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ingestion.scorer.util import calculate_blt, calculate_clt, calculate_prebook, normalize_datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to calculate the delay in minutes\n", "def calculate_destination_delay(row):\n", "    arrival = row['Destination Carrier Arrival']\n", "    close = row['Destination Appt Close']\n", "    \n", "    if pd.isna(arrival) or pd.isna(close):\n", "        return 0  # Return 0 if either value is NaN\n", "    \n", "    if isinstance(arrival, str):\n", "        arrival_time = datetime.strptime(arrival, \"%Y-%m-%d %H:%M:%S\")\n", "    else:\n", "        arrival_time = arrival\n", "    \n", "    if isinstance(close, str):\n", "        close_time = datetime.strptime(close, \"%Y-%m-%d %H:%M:%S\")\n", "    else:\n", "        close_time = close\n", "    \n", "    # Calculate difference in minutes\n", "    delay_minutes = (arrival_time - close_time).total_seconds() // 60\n", "    return max(0, delay_minutes)  # Return 0 if delay is negative"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "\n", "def generate_data_table(df):\n", "    # Rename columns as expected by CLT, BLT, Prebook helper methods\n", "    df.rename(columns={'Load Creation Date/Time': 'loadCreationTime'}, inplace=True)\n", "    df.rename(columns={'Origin Appt Close': 'originCloseTime'}, inplace=True)\n", "    df.rename(columns={'Load Activation Date/Time': 'loadActivationTime'}, inplace=True)\n", "    df.rename(columns={'Final Carrier Assigned Date/Time': 'carrierAssignedTime'}, inplace=True)\n", "\n", "    # Convert columns to datetime for helper function calculations\n", "    # If you get an error during this, remove troublesome values from 'Final Carrier Assigned Date/Time' column\n", "    df['loadCreationTime'] = pd.to_datetime(df['loadCreationTime'])\n", "    df['originCloseTime'] = pd.to_datetime(df['originCloseTime'])\n", "    df['loadActivationTime'] = pd.to_datetime(df['loadActivationTime'])\n", "    df['carrierAssignedTime'] = pd.to_datetime(df['carrierAssignedTime'])\n", "\n", "    # Apply the helper functions to each row and create new columns for each\n", "    df['CLT'] = df.apply(calculate_clt, axis=1)\n", "    df['BLT'] = df.apply(calculate_blt, axis=1)\n", "    df['Prebook'] = df.apply(calculate_prebook, axis=1)\n", "\n", "    # Apply the delay calculation function to each row\n", "    df['destination_delay_minutes'] = df.apply(calculate_destination_delay, axis=1)\n", "\n", "    # Calculate the Destination Delay Indicator for OTD %\n", "    df['Destination Delay Indicator'] = df['destination_delay_minutes'].apply(\n", "        lambda x: 1 if x > 30 else 0\n", "    )\n", "\n", "    # Group by 'Broker Name' and calculate basic metrics\n", "    basic_metrics = df.groupby('Broker Name').agg(\n", "        Avg_spend=('Revenue Total', 'mean'),\n", "        Avg_Truck_Cost=('COGS Total', 'mean'),\n", "        Total_margin=('Revenue Total', 'sum'),  # Temporary column for margin calc\n", "        Total_cost=('COGS Total', 'sum'),       # Temporary column for cost calc\n", "        Avg_CLT=('CLT', 'mean'),\n", "        Avg_BLT=('BLT', 'mean'),\n", "        Avg_Prebook=('Prebook', 'mean'),\n", "        Avg_OTD_Percent=('Destination Delay Indicator', 'mean'),\n", "        Volume=('Broker Name', 'size')\n", "    ).reset_index()\n", "\n", "    # Calculate custom metrics outside the .agg() method\n", "    basic_metrics['Margin_per_load_dollars'] = (\n", "        basic_metrics['Total_margin'] - basic_metrics['Total_cost']\n", "    ) / df.groupby('Broker Name').size().values\n", "\n", "    basic_metrics['Margin_per_load_percent'] = (\n", "        (basic_metrics['Total_margin'] - basic_metrics['Total_cost']) /\n", "        basic_metrics['Total_margin']\n", "    ) * 100\n", "\n", "    # Apply the modifications: divide Avg_CLT, Avg_BLT, Avg_Prebook by 10\n", "    basic_metrics['Avg_CLT'] /= 10\n", "    basic_metrics['Avg_BLT'] /= 10\n", "    basic_metrics['Avg_Prebook'] /= 10\n", "\n", "    # Subtract Avg_OTD_Percent from 1\n", "    basic_metrics['Avg_OTD_Percent'] = 1 - basic_metrics['Avg_OTD_Percent']\n", "\n", "    # Drop temporary columns if they are no longer needed\n", "    basic_metrics = basic_metrics.drop(columns=['Total_margin', 'Total_cost'])\n", "    basic_metrics = basic_metrics.sort_values(by='Volume', ascending=False).reset_index(drop=True)\n", "    return basic_metrics\n", "\n", "# Display the resulting DataFrame to the user\n", "generate_data_table(initial_demo_data.copy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Recreate Demo Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_demo_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add New Brokers and Equipment Types LTL and Drayage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Recreate Static Columns\n", "import numpy as np\n", "def generate_new_static_rows(df, n, broker_names, equipment_types):\n", "    new_rows = []\n", "    \n", "    for _ in range(n):\n", "        # Randomly select a broker name from the provided list\n", "        broker_name = np.random.choice(broker_names)\n", "        equipment_type = np.random.choice(equipment_types)\n", "\n", "        # Select a random row from the DataFrame for origin-related fields\n", "        lane_row = df.sample(1).iloc[0]\n", "\n", "        # Create new row with consistent values and filled fields\n", "        new_row = {\n", "            \"Broker Name\": broker_name,\n", "            \"Customer Name\": \"Green Clean Products\",\n", "            \"Shipment Status\": np.random.choice(df[\"Shipment Status\"].unique()),\n", "            \"Shipper Primary Reference\": str(np.random.randint(150000000, 160000000)),\n", "            \"Broker Primary Reference\": str(np.random.randint(9000000000, 9999999999)),\n", "            \"Shipment Mode\": \"TL\",\n", "            \"Equipment Type\": equipment_type,\n", "            \"Shipment Rank\": np.random.choice(df[\"Shipment Rank\"].unique()),\n", "            \"Weight\": np.random.choice(df[\"Weight\"]),\n", "            \"Stop Count\": np.random.choice(df[\"Stop Count\"]),\n", "            \n", "            # Origin fields, copied from the randomly sampled row\n", "            \"Origin Facility Name\": lane_row[\"Origin Facility Name\"],\n", "            \"Origin City\": lane_row[\"Origin City\"],\n", "            \"Origin State\": lane_row[\"Origin State\"],\n", "            \"Origin Zip\": lane_row[\"Origin Zip\"],\n", "            \"Origin Country\": lane_row[\"Origin Country\"],\n", "\n", "            # Destination fields, copied from the randomly sampled row\n", "            \"Destination Facility Name\": lane_row[\"Destination Facility Name\"],\n", "            \"Destination City\": lane_row[\"Destination City\"],\n", "            \"Destination State\": lane_row[\"Destination State\"],\n", "            \"Destination Zip\": lane_row[\"Destination Zip\"],\n", "            \"Destination Country\": lane_row[\"Destination Country\"],\n", "\n", "            # Distance of Lane\n", "            \"Distance Miles\": lane_row[\"Distance Miles\"]\n", "        }        \n", "        new_rows.append(new_row)\n", "    \n", "    return pd.DataFrame(new_rows)\n", "\n", "new_row_number = 500\n", "new_brokers = [\"Swiftline Logistics\", \"PrimeHaul Solutions\", \"Velocity Carriers\"]\n", "new_equipment_types = [\"VAN\", \"Drayage\", \"LTL\"]\n", "new_broker_df = generate_new_static_rows(initial_demo_data, new_row_number, new_brokers, new_equipment_types)\n", "new_broker_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine with original data\n", "new_broker_combined_df = pd.concat([initial_demo_data, new_broker_df], axis=0, ignore_index=True)\n", "\n", "# Set financial and time columns to NaN, the will be filled with artificial data\n", "new_broker_combined_df[\"COGS Fuel\"] = pd.NA\n", "new_broker_combined_df[\"COGS Total Accessorial\"] = pd.NA\n", "new_broker_combined_df[\"COGS Line Haul\"] = pd.NA\n", "new_broker_combined_df[\"COGS Total\"] = pd.NA\n", "new_broker_combined_df[\"Revenue Fuel\"] = pd.NA\n", "new_broker_combined_df[\"Revenue Total Accessorial\"] = pd.NA\n", "new_broker_combined_df[\"Revenue Line Haul\"] = pd.NA\n", "new_broker_combined_df[\"Revenue Total\"] = pd.NA\n", "\n", "new_broker_combined_df[\"Origin Appt Open\"] = pd.NA\n", "new_broker_combined_df[\"Origin Appt Close\"] = pd.NA\n", "new_broker_combined_df[\"Destination Appt Open\"] = pd.NA\n", "new_broker_combined_df[\"Destination Appt Close\"] = pd.NA\n", "new_broker_combined_df[\"Origin Carrier Arrival\"] = pd.NA\n", "new_broker_combined_df[\"Origin Carrier Departure\"] = pd.NA\n", "new_broker_combined_df[\"Destination Carrier Arrival\"] = pd.NA\n", "new_broker_combined_df[\"Destination Carrier Departure\"] = pd.NA\n", "new_broker_combined_df[\"Load Creation Date/Time\"] = pd.NA\n", "new_broker_combined_df[\"Load Activation Date/Time\"] = pd.NA\n", "new_broker_combined_df[\"Final Carrier Assigned Date/Time\"] = pd.NA\n", "\n", "new_broker_combined_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df is your DataFrame with the 'Broker Name' column\n", "# and options is the list of names you want to randomly assign\n", "options = ['Rapid Transport', 'Swiftline Logistics', 'Velocity Carriers', 'PrimeHaul Solutions']\n", "\n", "# broker_list is the list of broker names you want to consider for swapping\n", "broker_list = ['CargoCare', 'Reliant Freight Services', 'On-Time Logistics']\n", "\n", "# exclude_list is the list of broker names you do NOT want to swap\n", "exclude_list = ['Transport Dynamics']\n", "\n", "# Create a mask where 'Broker Name' is in broker_list\n", "in_broker_list = new_broker_combined_df['Broker Name'].isin(broker_list)\n", "\n", "# Create a mask where 'Broker Name' is NOT in exclude_list\n", "not_in_exclude_list = ~new_broker_combined_df['Broker Name'].isin(exclude_list)\n", "\n", "# Combine masks to identify eligible brokers for swapping\n", "eligible_brokers_mask = in_broker_list & not_in_exclude_list\n", "\n", "# Generate a random number between 0 and 1 for each row\n", "random_numbers = np.random.rand(len(new_broker_combined_df))\n", "\n", "# Create a boolean mask for rows where the random number is less than 0.15 (15% chance)\n", "random_mask = random_numbers < 0.15\n", "mask = eligible_brokers_mask & random_mask\n", "\n", "# Replace 'Broker Name' in rows where mask is True with a random choice from options\n", "new_broker_combined_df.loc[mask, 'Broker Name'] = np.random.choice(options, size=mask.sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assume 'df' is your DataFrame and 'names_list' is your list of broker names\n", "names_list = ['CargoCare', 'Reliant Freight Services', 'PrimeHaul Solutions']  # Example list of broker names\n", "\n", "# Step 1: Filter rows where 'Broker Name' is in 'names_list'\n", "mask = new_broker_combined_df['Broker Name'].isin(names_list)\n", "filtered_indices = new_broker_combined_df[mask].index\n", "\n", "# Step 2: Randomly select 500 indices from these rows\n", "if len(filtered_indices) >= 500:\n", "    indices_to_delete = np.random.choice(filtered_indices, size=500, replace=False)\n", "else:\n", "    # If fewer than 500 rows are available, select all of them\n", "    indices_to_delete = filtered_indices\n", "\n", "# Step 3: Drop these indices from the DataFrame\n", "new_broker_combined_df = new_broker_combined_df.drop(indices_to_delete).reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fake financial data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# REVENUE:\n", "# --- Flat SPM\n", "# --- High SPL\n", "\n", "# Center SPM around ~$4.50 +/- $0.05\n", "# Randomly break out into Fuel, Accessorial, and Line Haul\n", "new_broker_combined_df[\"Revenue Total\"] =  (4 + np.random.uniform(-0.1, 0.1, len(new_broker_combined_df))) * new_broker_combined_df['Distance Miles']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Broker Adjustments based on an average of $4 per mile\n", "broker_adjustments = {\n", "    \"CargoCare\": 0.8,\n", "    \"Reliant Freight Services\": 1.4,\n", "    \"On-Time Logistics\": 0.9,\n", "    \"Transport Dynamics\": 0.6,\n", "}\n", "\n", "new_broker_combined_df[\"Revenue Total\"] = new_broker_combined_df.apply(\n", "    lambda row: row[\"Revenue Total\"] * broker_adjustments[row[\"Broker Name\"]] if row[\"Broker Name\"] in broker_adjustments else row[\"Revenue Total\"] * np.random.uniform(0.8, 0.85),\n", "    axis=1\n", ")\n", "\n", "avg_spends_per_broker = new_broker_combined_df.groupby('Broker Name').agg(\n", "    Avg_spend=('Revenue Total', 'mean'),\n", "    Volume=('Broker Name', 'size')\n", ").reset_index()\n", "avg_spends_per_broker = avg_spends_per_broker.sort_values(by='Volume', ascending=False).reset_index(drop=True)\n", "\n", "avg_spends_per_broker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "# Starting date (January 1 of the current year)\n", "start_date = datetime(datetime.now().year, 1, 1)\n", "\n", "# Generate a list of dates from January 1st to fill the DataFrame, allowing repeats\n", "dates = [start_date + timedelta(days=i) for i in range(365)]  # 365 days in a year\n", "dates_repeated = dates * (len(new_broker_combined_df) // len(dates)) + dates[:len(new_broker_combined_df) % len(dates)]\n", "\n", "# Generate random timedeltas for hours, minutes, and seconds\n", "random_times = [timed<PERSON>ta(hours=np.random.randint(0, 24), \n", "                          minutes=np.random.randint(0, 60), \n", "                          seconds=np.random.randint(0, 60)) for _ in range(len(dates_repeated))]\n", "\n", "# Add the random times to each date\n", "dates_with_time = [date + time for date, time in zip(dates_repeated, random_times)]\n", "\n", "# Fill the 'Origin Appt Close' column in chronological order with random time included\n", "new_broker_combined_df['Origin Appt Close'] = dates_with_time\n", "new_broker_combined_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spend Breakup\n", "accessorial_range = (0, 0.20)  # 0-20% of Revenue Total\n", "fuel_range = (0.05, 0.10)      # 5-10% of Revenue Total"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# COGS:\n", "# Baseline margin per load - 15%\n", "# Subtract baseline margin per load from revenueTotal\n", "# Add Gaussian noise\n", "# Add an index multiplier to trend downwards\n", "# Define the range for random reduction percentage\n", "marg_reduction_constant = 0.17\n", "reduction_range = (0.00, 0.01)  # 15% ± 5%\n", "\n", "broker_margin_adjustments = {\n", "    \"CargoCare\": 0.14 - marg_reduction_constant,\n", "    \"Reliant Freight Services\": 0.13 - marg_reduction_constant,\n", "    \"On-Time Logistics\": 0.12 - marg_reduction_constant,\n", "    \"Transport Dynamics\": 0.13 - marg_reduction_constant,\n", "    \"Rapid Transport\": 0.13 - marg_reduction_constant\n", "}\n", "\n", "# Calculate initial COGS by reducing Revenue Total\n", "new_broker_combined_df['COGS Total'] = new_broker_combined_df['Revenue Total'] * (1 - np.random.uniform(*reduction_range, len(new_broker_combined_df)))\n", "\n", "new_broker_combined_df[\"COGS Total\"] = new_broker_combined_df.apply(\n", "    lambda row: row[\"Revenue Total\"] * (1 - broker_margin_adjustments[row[\"Broker Name\"]] + np.random.uniform(-0.02, 0.02)) if row[\"Broker Name\"] in broker_margin_adjustments else row[\"Revenue Total\"] * (1 - np.random.uniform(*reduction_range)),\n", "    axis=1\n", ")\n", "\n", "\n", "# Define the pivot date for adjustment\n", "pivot_date = pd.Timestamp('2024-05-15')\n", "\n", "# Calculate the number of days difference from the pivot date\n", "new_broker_combined_df['Days From Pivot'] = (new_broker_combined_df['Origin Appt Close'] - pivot_date).dt.days\n", "\n", "# Define the decay constant k (adjust this value to control the rate of decay)\n", "k = 0.0015  # You can tweak this value to control the steepness of the decay\n", "\n", "# Calculate the Trend Multiplier using an exponential decay function\n", "new_broker_combined_df['Trend Multiplier'] = np.exp(-k * np.abs(new_broker_combined_df['Days From Pivot']))\n", "\n", "# Apply the trend multiplier to adjust COGS Total\n", "new_broker_combined_df['COGS Total'] *= new_broker_combined_df['Trend Multiplier']\n", "\n", "# Clean up by dropping helper columns if they are no longer needed\n", "new_broker_combined_df.drop(['Days From Pivot', 'Trend Multiplier'], axis=1, inplace=True)\n", "\n", "\n", "# Give a slight constant boost to 'Revenue Total' after the pivot date\n", "boost_percentage = 0.1  # Adjust this value for the desired boost (e.g., 1.5%)\n", "mask = new_broker_combined_df['Origin Appt Close'] > pivot_date\n", "new_broker_combined_df.loc[mask, 'Revenue Total'] *= (1 + boost_percentage)\n", "\n", "# Break up cogs the same way we did revenue\n", "new_broker_combined_df['COGS Total Accessorial'] = new_broker_combined_df['COGS Total'] * np.random.uniform(*accessorial_range, len(new_broker_combined_df))\n", "new_broker_combined_df['COGS Fuel'] = new_broker_combined_df['COGS Total'] * np.random.uniform(*fuel_range, len(new_broker_combined_df))\n", "new_broker_combined_df['COGS Line Haul'] = new_broker_combined_df['COGS Total'] - new_broker_combined_df['COGS Total Accessorial'] - new_broker_combined_df['COGS Fuel']\n", "\n", "new_broker_combined_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Spend Breakup\n", "accessorial_range = (0, 0.20)  # 0-20% of Revenue Total\n", "fuel_range = (0.05, 0.10)      # 5-10% of Revenue Total\n", "\n", "new_broker_combined_df['Revenue Total Accessorial'] = new_broker_combined_df['Revenue Total'] * np.random.uniform(*accessorial_range, len(new_broker_combined_df))\n", "new_broker_combined_df['Revenue Fuel'] = new_broker_combined_df['Revenue Total'] * np.random.uniform(*fuel_range, len(new_broker_combined_df))\n", "new_broker_combined_df['Revenue Line Haul'] = new_broker_combined_df['Revenue Total'] - new_broker_combined_df['Revenue Total Accessorial'] - new_broker_combined_df['Revenue Fuel']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generate_metric_trends_graph(new_broker_combined_df.copy())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_broker_combined_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fake Performance Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BLT, CLT, and Prebook"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Appt Columns\n", "import random\n", "\n", "def random_timedelta(hours_min, hours_max):\n", "    return timedelta(hours=random.uniform(hours_min, hours_max))\n", "\n", "# Fill in the columns based on 'Origin Appt Close'\n", "new_broker_combined_df[\"Origin Appt Open\"] = new_broker_combined_df[\"Origin Appt Close\"].apply(\n", "    lambda x: x - random_<PERSON><PERSON>ta(1, 6) if pd.notnull(x) else np.nan\n", ")\n", "\n", "new_broker_combined_df[\"Destination Appt Open\"] = new_broker_combined_df[\"Origin Appt Close\"].apply(\n", "    lambda x: x + random_<PERSON><PERSON><PERSON>(5, 72) if pd.notnull(x) else np.nan\n", ")\n", "\n", "new_broker_combined_df[\"Destination Appt Close\"] = new_broker_combined_df[\"Destination Appt Open\"].apply(\n", "    lambda x: x + random_<PERSON><PERSON><PERSON>(4, 6) if pd.notnull(x) else np.nan\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BLT, CLT, And Prebook Reduction\n", "\n", "# Assuming df1 is your DataFrame with datetime columns\n", "initial_demo_data['Origin Appt Close'] = pd.to_datetime(initial_demo_data['Origin Appt Close'])\n", "initial_demo_data['Load Creation Date/Time'] = pd.to_datetime(initial_demo_data['Load Creation Date/Time'])\n", "initial_demo_data['Load Activation Date/Time'] = pd.to_datetime(initial_demo_data['Load Activation Date/Time'])\n", "initial_demo_data['Final Carrier Assigned Date/Time'] = pd.to_datetime(initial_demo_data['Final Carrier Assigned Date/Time'])\n", "\n", "# Calculate time differences in hours\n", "initial_demo_data['diff_loadCreation'] = (initial_demo_data['Load Creation Date/Time'] - initial_demo_data['Origin Appt Close']).dt.total_seconds() / 3600\n", "initial_demo_data['diff_loadActivation'] = (initial_demo_data['Load Activation Date/Time'] - initial_demo_data['Origin Appt Close']).dt.total_seconds() / 3600\n", "initial_demo_data['diff_carrierAssigned'] = (initial_demo_data['Final Carrier Assigned Date/Time'] - initial_demo_data['Origin Appt Close']).dt.total_seconds() / 3600\n", "\n", "# Calculate the average difference for each column\n", "average_diffs = {\n", "    'loadCreationTime': initial_demo_data['diff_loadCreation'].mean(),\n", "    'loadActivationTime': initial_demo_data['diff_loadActivation'].mean(),\n", "    'carrierAssignedTime': initial_demo_data['diff_carrierAssigned'].mean(),\n", "}\n", "average_diffs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df2 has 'Origin Appt Close' and we want to fill in the other columns\n", "new_broker_combined_df['Origin Appt Close'] = pd.to_datetime(new_broker_combined_df['Origin Appt Close'])\n", "\n", "# Adjusted averages (80% of the original average)\n", "adjusted_averages = {k: v * 1.1 for k, v in average_diffs.items()}\n", "\n", "broker_lt_adjustments = {\n", "    \"CargoCare\": -10, # GOOD LTU\n", "    \"Reliant Freight Services\": -30, # GREAT LTU\n", "    \"On-Time Logistics\": -10, # GOOD LTU\n", "    \"Transport Dynamics\": -15, # BAD LTU\n", "    \"Rapid Transport\": -15 # BAD LTU\n", "}\n", "\n", "# Function to add random noise within ±10 hours\n", "def add_noise(hours):\n", "    return hours + np.random.uniform(-20, 20)\n", "\n", "# CLT\n", "new_broker_combined_df['Load Creation Date/Time'] = new_broker_combined_df['Origin Appt Close'] + pd.to_timedelta(new_broker_combined_df.apply(lambda x: add_noise(adjusted_averages['loadCreationTime']), axis=1), unit='h')\n", "\n", "# BLT\n", "new_broker_combined_df['Load Activation Date/Time'] = new_broker_combined_df['Origin Appt Close'] + pd.to_timedelta(new_broker_combined_df.apply(\n", "    lambda row: add_noise(adjusted_averages['loadActivationTime'] + (broker_lt_adjustments[row[\"Broker Name\"]] if row[\"Broker Name\"] in broker_lt_adjustments else 0)\n", "), axis=1), unit='h')\n", "\n", "# Prebook\n", "new_broker_combined_df['Final Carrier Assigned Date/Time'] = new_broker_combined_df['Origin Appt Close'] + pd.to_timedelta(new_broker_combined_df.apply(\n", "    lambda row: add_noise(adjusted_averages['carrierAssignedTime'] + (broker_lt_adjustments[row[\"Broker Name\"]] if row[\"Broker Name\"] in broker_lt_adjustments else 0)\n", "), axis=1), unit='h')\n", "\n", "new_broker_combined_df[['Origin Appt Close', 'Load Creation Date/Time', 'Load Activation Date/Time', 'Final Carrier Assigned Date/Time']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Arrival and Departure Times"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df1 has the relevant columns with datetime values\n", "initial_demo_data['Destination Carrier Arrival'] = pd.to_datetime(initial_demo_data['Destination Carrier Arrival'])\n", "initial_demo_data['Destination Appt Close'] = pd.to_datetime(initial_demo_data['Destination Appt Close'])\n", "initial_demo_data['Destination Carrier Departure'] = pd.to_datetime(initial_demo_data['Destination Carrier Departure'])\n", "\n", "# Calculate the time differences in hours\n", "initial_demo_data['diff_destination_arrival'] = (initial_demo_data['Destination Carrier Arrival'] - initial_demo_data['Destination Appt Close']).dt.total_seconds() / 3600\n", "initial_demo_data['diff_arrival_departure'] = (initial_demo_data['Destination Carrier Departure'] - initial_demo_data['Destination Carrier Arrival']).dt.total_seconds() / 3600\n", "\n", "# Calculate the average differences\n", "average_diff_destination_arrival = initial_demo_data['diff_destination_arrival'].mean()\n", "average_diff_arrival_departure = initial_demo_data['diff_arrival_departure'].mean()\n", "\n", "average_diff_destination_arrival, average_diff_arrival_departure\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to add random noise within ±10 hours\n", "def add_noise(hours=0, lower=0, upper=1):\n", "    return hours + np.random.uniform(lower, upper)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df2 has 'Destination Appt Close' and we want to fill in 'Destination Carrier Arrival'\n", "new_broker_combined_df['Destination Appt Close'] = pd.to_datetime(new_broker_combined_df['Destination Appt Close'])\n", "\n", "broker_otd_adjustments = {\n", "    \"CargoCare\": -0.01, # OK SERVICE\n", "    \"Reliant Freight Services\": -0.025, # GOOD SERVICE\n", "    \"On-Time Logistics\": -0.025, # GOOD SERVICE\n", "    \"Transport Dynamics\": -0.015, # SHIT SERVICE\n", "    \"Rapid Transport\": -0.015\n", "}\n", "\n", "# Fill in 'Destination Carrier Arrival' based on 'Destination Appt Close' with adjusted average and noise\n", "# OTD Percent\n", "new_broker_combined_df['Destination Carrier Arrival'] = new_broker_combined_df['Destination Appt Close'] + pd.to_timedelta(new_broker_combined_df.apply(\n", "    lambda row: add_noise(lower=0.2, upper=0.55) + (broker_otd_adjustments[row[\"Broker Name\"]] if row[\"Broker Name\"] in broker_otd_adjustments else 0), \n", "axis=1), unit='h')\n", "new_broker_combined_df['Destination Carrier Departure'] = new_broker_combined_df['Destination Carrier Arrival'] + pd.to_timedelta(new_broker_combined_df.apply(lambda x: add_noise(hours=average_diff_arrival_departure, lower=-2, upper=2), axis=1), unit='h')\n", "\n", "\n", "new_broker_combined_df[['Destination Appt Close', 'Destination Carrier Arrival', 'Destination Carrier Departure']]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Recompute Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generate_key_metrics(new_broker_combined_df.copy())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generate_data_table(new_broker_combined_df.copy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fill in Origin Arrival Times"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df1 has the relevant columns with datetime values\n", "initial_demo_data['Origin Carrier Arrival'] = pd.to_datetime(initial_demo_data['Origin Carrier Arrival'])\n", "initial_demo_data['Origin Appt Close'] = pd.to_datetime(initial_demo_data['Origin Appt Close'])\n", "initial_demo_data['Origin Carrier Departure'] = pd.to_datetime(initial_demo_data['Origin Carrier Departure'])\n", "\n", "# Calculate the time differences in hours\n", "initial_demo_data['diff_destination_arrival'] = (initial_demo_data['Origin Carrier Arrival'] - initial_demo_data['Origin Appt Close']).dt.total_seconds() / 3600\n", "initial_demo_data['diff_arrival_departure'] = (initial_demo_data['Origin Carrier Departure'] - initial_demo_data['Origin Carrier Arrival']).dt.total_seconds() / 3600\n", "\n", "# Calculate the average differences\n", "average_diff_destination_arrival = initial_demo_data['diff_destination_arrival'].mean()\n", "average_diff_arrival_departure = initial_demo_data['diff_arrival_departure'].mean()\n", "\n", "average_diff_destination_arrival, average_diff_arrival_departure\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming df2 has 'Destination Appt Close' and we want to fill in 'Destination Carrier Arrival'\n", "new_broker_combined_df['Origin Appt Close'] = pd.to_datetime(new_broker_combined_df['Origin Appt Close'])\n", "\n", "# Fill in 'Destination Carrier Arrival' based on 'Destination Appt Close' with adjusted average and noise\n", "new_broker_combined_df['Origin Carrier Arrival'] = new_broker_combined_df['Origin Appt Close'] + pd.to_timedelta(new_broker_combined_df.apply(lambda x: add_noise(hours=average_diff_destination_arrival, lower=0, upper=0), axis=1), unit='h')\n", "new_broker_combined_df['Origin Carrier Departure'] = new_broker_combined_df['Origin Carrier Arrival'] + pd.to_timedelta(new_broker_combined_df.apply(lambda x: add_noise(hours=average_diff_arrival_departure, lower=0, upper=0), axis=1), unit='h')\n", "\n", "\n", "new_broker_combined_df[['Origin Appt Close', 'Origin Carrier Arrival', 'Origin Carrier Departure']]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# new_broker_combined_df = new_broker_combined_df.drop(columns=['diff_loadCreation', 'diff_loadActivation', 'diff_carrierAssigned', 'diff_destination', 'diff_destination_arrival', 'diff_destination_departure', 'diff_arrival_departure'])\n", "new_broker_combined_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_broker_combined_df.to_excel(\"./new_demo_data.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_broker_combined_df[new_broker_combined_df[\"Shipper Primary Reference\"] == \"151300223;0018578305\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["3101.963219 + 321.214312 + 93.48379"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["3346.270536 + 213.832578 + 490.190849"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "e3bab1b93d258dbfa4dccbeb3f02abbdbb0f314bf46cf351dcc60ff2a957c79e"}, "kernelspec": {"display_name": "Python 3.11.7 ('vs-notebook')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}