import datetime
import os
from collections import defaultdict

import plotly.graph_objects as go
import tqdm
from freightgpt_cognito_user_analysis import fetch_cognito_users
from freightgpt_cognito_user_analysis import load_data
from freightgpt_cognito_user_analysis import save_to_csv
from langsmith import Client

from common import init_main

PROJECT_ROOT = init_main.initialize()


# Initialize the client with environment variables
client = Client(
    api_key=os.getenv("LANGCHAIN_API_KEY"),
)

# Assuming `client` is already initialized and authenticated elsewhere in the script
# as it interacts with an external API to fetch run data.


# Fetch runs for the last N days
def fetch_runs(days=5):
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Filter and categorize runs by user and date
def categorize_runs(runs, recent_users, deny_list=None, extraneous_tags=None):
    if deny_list is None:
        deny_list = {
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "public",
            "<PERSON> <PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON> <PERSON>rovich",
            "Vivek <PERSON>",
            "Ashish Kumar <PERSON>",
        }
    if extraneous_tags is None:
        extraneous_tags = {"openai-functions", "desktop", "mobile"}

    runs_by_date_user = defaultdict(lambda: defaultdict(int))

    for run in tqdm.tqdm(runs):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            if not tags or deny_list & tags:
                continue
            user = (tags - extraneous_tags).pop()
            date = run.start_time.date()

            # Exclude recent users
            if user in recent_users:
                continue

            runs_by_date_user[date.strftime("%Y-%m-%d")][user] += 1
        except Exception as e:
            print(f"Failed to process run: {e}")

    import json

    # Save dictionary to a JSON file
    with open("my_dict.json", "w") as f:
        json.dump(runs_by_date_user, f)

    return runs_by_date_user


def plot_graphs(runs_by_date_user):
    # Initialize dictionaries
    active_users_per_day = defaultdict(int)
    queries_per_day = defaultdict(int)
    active_users_details_per_day = defaultdict(str)

    # Process data
    for date, users in runs_by_date_user.items():
        active_users_per_day[date] = len(users)
        queries_per_day[date] = sum(users.values())
        active_users_details_per_day[date] = "<br>".join(users.keys())

    # Prepare data for Plotly
    dates = sorted(active_users_per_day.keys())

    active_users = [active_users_per_day[date] for date in dates]
    queries = [queries_per_day[date] for date in dates]
    active_users_details = [active_users_details_per_day[date] for date in dates]

    # Create figure
    fig = go.Figure()

    # Add active users trace
    fig.add_trace(
        go.Scatter(
            x=dates,
            y=active_users,
            mode="lines+markers",
            name="Active Users",
            text=[
                f"Active Users: {count}<br>{details}"
                for count, details in zip(active_users, active_users_details)
            ],
            hoverinfo="text",
        )
    )

    # Add queries trace
    fig.add_trace(
        go.Scatter(
            x=dates,
            y=queries,
            mode="lines+markers",
            name="Queries",
            text=[f"No. of Queries: {count}" for count in queries],
            hoverinfo="text",
            marker=dict(color="red"),
        )
    )

    # Update layout
    fig.update_layout(
        title="Active users and no of queries per day",
        xaxis_title="Date",
        yaxis_title="Count",
        hovermode="closest",
    )

    # Show plot
    fig.show()


# Main execution function
def main():
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    save_to_csv(users, "cognito_users.csv")

    data = load_data("cognito_users.csv")
    recent_users = data[
        data["Sign Up Date"] == datetime.datetime.now().strftime("%Y-%m-%d")
    ]["Name"].tolist()
    runs = fetch_runs(5)
    runs_by_date_user = categorize_runs(runs, recent_users)
    plot_graphs(runs_by_date_user)


if __name__ == "__main__":
    main()
