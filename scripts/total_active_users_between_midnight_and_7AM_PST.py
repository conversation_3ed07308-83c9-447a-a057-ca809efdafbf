import datetime
import json  # For saving to JSON
import os
from collections import defaultdict

import pytz  # For timezone conversion
import tqdm
from freightgpt_cognito_user_analysis import fetch_cognito_users
from freightgpt_cognito_user_analysis import load_data
from freightgpt_cognito_user_analysis import save_to_csv
from langsmith import Client

from common import init_main

PROJECT_ROOT = init_main.initialize()

# Initialize the client with environment variables
client = Client(api_key=os.getenv("LANGCHAIN_API_KEY"))


# Fetch runs for the last N days
def fetch_runs(days):
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Convert UTC time to Pacific time and filter based on midnight to 7 AM PST
def is_active_between_hours(run, pacific_start_hour=0, pacific_end_hour=7):
    pacific_tz = pytz.timezone("America/Los_Angeles")
    run_time_pacific = run.start_time.astimezone(pacific_tz)
    return pacific_start_hour <= run_time_pacific.hour < pacific_end_hour


# Categorize runs by user and date between midnight and 7 AM PST
def categorize_runs(
    runs, user_email_map, signup_dates, deny_list=None, extraneous_tags=None
):
    if deny_list is None:
        deny_list = {
            "Viv Kumar",
            "Joe Petrovich",
            "Roop Pal",
            "Joey Kim",
            "public",
            "Ho Jun Yi",
            "Jr",
            "Jr Martinez",
            "Joseph Petrovich",
            "Vivek Kumar",
            "Ashish Kumar Singh",
        }
    if extraneous_tags is None:
        extraneous_tags = {"openai-functions", "desktop", "mobile"}

    active_users = defaultdict(list)  # Store details of active users

    for run in tqdm.tqdm(runs):
        try:
            tags = set(client.read_run(run.trace_id).tags)
            if not tags or deny_list & tags:
                continue
            user = (tags - extraneous_tags).pop()

            if user in user_email_map and user in signup_dates:
                signup_date = signup_dates[user].date()  # User's signup date

                # Only consider runs made after the signup date
                run_date = run.start_time.date()
                if run_date > signup_date:
                    if is_active_between_hours(run):
                        active_users[user].append(
                            {"date": str(run_date), "email": user_email_map[user]}
                        )
        except Exception as e:
            print(f"Failed to process run: {e}")

    return active_users


# Main execution function
def main():
    # Fetch Cognito users
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    save_to_csv(users, "cognito_users.csv")

    # Load Cognito data
    data = load_data("cognito_users.csv")
    user_email_map = dict(zip(data["Name"], data["Email"]))  # Map user names to emails
    signup_dates = {
        row["Name"]: datetime.datetime.strptime(
            row["Sign Up Date"], "%Y-%m-%d %H:%M:%S"
        )
        for _, row in data.iterrows()
    }  # Map signup dates

    # Fetch runs for the last month (30 days)
    runs = fetch_runs(2)

    # Categorize runs based on active hours (midnight to 7 AM PST) and filter by post-signup queries
    active_users = categorize_runs(runs, user_email_map, signup_dates)

    # Calculate total number of active users
    total_active_users = len(active_users)

    print(f"Total active users between midnight and 7 AM PST: {total_active_users}")

    # Save user details to JSON
    output_data = {
        "total_active_users_between_midnight_and_7AM_PST": total_active_users,
        "active_users": active_users,
    }

    with open("active_users_between_midnight_and_7AM_PST.json", "w") as json_file:
        json.dump(output_data, json_file, indent=4)

    print("Output saved to active_users_between_midnight_and_7AM_PST.json")


if __name__ == "__main__":
    main()
