# User by Exact Query count
import datetime
import os
from collections import defaultdict

import plotly.graph_objects as go
import tqdm
from freightgpt_cognito_user_analysis import fetch_cognito_users
from freightgpt_cognito_user_analysis import load_data
from freightgpt_cognito_user_analysis import save_to_csv
from langsmith import Client

from common import init_main

PROJECT_ROOT = init_main.initialize()

# Initialize the client with environment variables
client = Client(
    api_key=os.getenv("LANGCHAIN_API_KEY"),
)


# Fetch runs for the last N days
def fetch_runs(days=5):
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Filter and categorize runs by user and date
def categorize_runs(runs, recent_users, deny_list=None, extraneous_tags=None):
    if deny_list is None:
        deny_list = {
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "public",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "Vivek <PERSON>",
            "<PERSON>ish <PERSON>",
            "<PERSON>ish Singh",
        }
    if extraneous_tags is None:
        extraneous_tags = {"openai-functions", "desktop", "mobile"}

    runs_by_date_user = defaultdict(lambda: defaultdict(int))
    user_query_counts = defaultdict(int)

    for run in tqdm.tqdm(runs):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            if not tags or deny_list & tags:
                continue
            user = (tags - extraneous_tags).pop()
            date = run.start_time.date()

            # Exclude recent users
            if user in recent_users:
                continue

            runs_by_date_user[date.strftime("%Y-%m-%d")][user] += 1
            user_query_counts[user] += 1
        except Exception as e:
            print(f"Failed to process run: {e}")

    import json

    # Save dictionary to a JSON file
    with open("my_dict.json", "w") as f:
        json.dump(runs_by_date_user, f)

    return runs_by_date_user, user_query_counts


def plot_graphs(user_query_counts):
    # Calculate user lists for each exact query count
    max_queries = max(user_query_counts.values())
    users_with_exact_queries = {i: [] for i in range(1, max_queries + 1)}

    for user, count in user_query_counts.items():
        if count <= max_queries:
            users_with_exact_queries[count].append(user)

    query_counts = list(users_with_exact_queries.keys())
    user_counts = [len(users) for users in users_with_exact_queries.values()]
    user_details = ["<br>".join(users) for users in users_with_exact_queries.values()]

    # Create figure
    fig = go.Figure()

    # Add trace for exact query counts
    fig.add_trace(
        go.Scatter(
            x=query_counts,
            y=user_counts,
            mode="lines+markers",
            name="User Query Counts",
            text=[
                f"Users with {count} Queries:<br>{details}"
                for count, details in zip(query_counts, user_details)
            ],
            hoverinfo="text",
        )
    )

    # Update layout
    fig.update_layout(
        title="Users by Exact Query Count",
        xaxis_title="Query Count",
        yaxis_title="User Count",
        hovermode="closest",
    )

    # Show plot
    fig.show()


# Main execution function
def main():
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    save_to_csv(users, "cognito_users.csv")

    data = load_data("cognito_users.csv")
    recent_users = data[
        data["Sign Up Date"] == datetime.datetime.now().strftime("%Y-%m-%d")
    ]["Name"].tolist()
    runs = fetch_runs(5)
    runs_by_date_user, user_query_counts = categorize_runs(runs, recent_users)
    plot_graphs(user_query_counts)


if __name__ == "__main__":
    main()
