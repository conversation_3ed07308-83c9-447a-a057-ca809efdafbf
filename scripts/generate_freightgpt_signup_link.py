import base64
import os
import time

from colorama import Fore
from colorama import Style
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import algorithms
from cryptography.hazmat.primitives.ciphers import Cipher
from cryptography.hazmat.primitives.ciphers import modes
from InquirerPy import inquirer

ENCRYPTION_KEY = b"H\xb6\xd0\x04\x94\xfe\x0b\x0c\xc7\x86\t\x8e\xd5e/\x8b\x1aI\x8a\xbc\xfa\x06e\xe9\xd49{V\xca\x95&\xfd"
BASE_URL = "https://www.freightgpt.com/signup/"
EMAIL_REGISTER = "https://docs.google.com/spreadsheets/d/1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4/edit#gid=0"


def generate_signup_link(email: str) -> str:
    """
    Creates signup link with encrypted email

    Args:
        email (str): email to encrypt

    Returns:
        str: signup link
    """
    # Generate random string
    random_str = os.urandom(16)

    # Define and initialize encryptor
    encryptor = Cipher(
        algorithms.AES(ENCRYPTION_KEY), modes.CBC(random_str)
    ).encryptor()

    # Pad email
    padder = padding.PKCS7(128).padder()
    padded_email = padder.update(email.encode()) + padder.finalize()

    # Encrypt padded email
    ct = encryptor.update(padded_email) + encryptor.finalize()
    encrypted_email = base64.urlsafe_b64encode(random_str + ct).decode()

    return BASE_URL + encrypted_email


def generate_email_template(
    email: str, first: str, executor: str, signup_link: str, body_only: bool = False
) -> str:
    """
    Returns email template with parameters included

    Args:
        email (str): email of new user
        first (str): first name of new user
        executor (str): name of script executor
        signup_link (str): link for new user to signup with

    Returns:
        str: template with parameters substituted
    """
    template = f"""
To: {email}
CC: <EMAIL>, <EMAIL>, <EMAIL>
Subject: You've Been Invited to the FreightGPT Beta

Body:

"""
    body = f"""Hi {first},

Thank you for your patience on the waitlist! You now have access to FreightGPT, a tool that helps you with pricing your freight and finding new carriers.

You can sign up at www.freightgpt.com.

Here are some things you can do on FreightGPT:

1. Price Your Freight: Get a current price on any Dry Van or Reefer lane.
For example, try "Chicago to Phoenix Dry Van rate" or "Reefer rate LA to Tempe".

2. Discover New Carriers: Find carriers by lanes, location, and equipment type.
For example, try "Dallas to LA with Dry Van carriers" or "15 truck carriers in Chicago that haul meat".

3. Historical Price Trends: Get lane-specific price trend information for the past two years.
For example, try "What is the price of a Dry Van over the past 90 days for Chicago to Miami?"

4. Check Weather en Route: Check if your lane has weather issues during transit.
For example, try "What is the weather along the route from New York to Grand Rapids, MI?"

5. Estimate Transit Time: Understand how long transit time is, with FMCSA compliance in mind.
For example, try "How long is the transit time from Denver, CO to New York, NY?"

We highly value your feedback and encourage you to reach out to us via email. You can also use the thumbs ups and downs to share your thoughts.

Thanks,

{executor}"""

    if body_only:
        return body

    return template + body


def user_signup() -> None:
    """
    Collects new user information and generates signup link
    """
    print(
        f"{Fore.GREEN}============ SIGNUP LINK CREATION ============{Style.RESET_ALL}"
    )

    # Get new user email
    email = inquirer.text(message="What is the email of the new user:").execute()
    cleaned_email = email.strip().lower()

    # Create signup link
    signup_link = generate_signup_link(cleaned_email)
    print(f"\nSignup Link: {Fore.BLUE}{signup_link}{Style.RESET_ALL}")

    print(
        f"\n{Fore.GREEN}============ EMAIL TEMPLATE GENERATION ============{Style.RESET_ALL}"
    )

    # Get user first name
    first_name = inquirer.text(message="What is the first name of the user:").execute()
    cleaned_first_name = first_name.strip().capitalize()

    # Get user last name
    last_name = inquirer.text(message="What is the last name of the user:").execute()
    cleaned_last_name = last_name.strip().capitalize()

    # Get script executor name
    executor_name = inquirer.text(message="What is the your name:").execute()
    cleaned_executor_name = executor_name.strip().capitalize()

    # Get template
    email_template = generate_email_template(
        cleaned_email, cleaned_first_name, cleaned_executor_name, signup_link
    )
    print(email_template)

    print(f"\n{Fore.GREEN}============ OTHER STEPS ============{Style.RESET_ALL}")

    # Reminder to add to register
    print(
        f"1) Add user to the FreightGPT Email Register: {Fore.BLUE}{EMAIL_REGISTER}{Style.RESET_ALL}"
    )
    print(
        f"\nEmail: {Fore.BLUE}{cleaned_email}{Style.RESET_ALL}\nFull Name: {Fore.BLUE}{cleaned_first_name} {cleaned_last_name}{Style.RESET_ALL}\nCurrent Date: {Fore.BLUE}{time.strftime('%x')}{Style.RESET_ALL}"
    )

    # Reminder to add to label
    print(
        f"\n2) Add user to {Fore.BLUE}freightgpt-beta-users{Style.RESET_ALL} label in Contacts"
    )


if __name__ == "__main__":
    user_signup()
