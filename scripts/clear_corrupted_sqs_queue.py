# Deletes all messages from AWS SQS queue, in the event of a corrupted message that has expired.
# SQS: https://us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues
import time

import boto3

from common import credentials

CREDENTIALS = credentials.Credentials()

session = boto3.Session(
    aws_access_key_id=CREDENTIALS.aws_access_key_id,
    aws_secret_access_key=CREDENTIALS.aws_secret_access_key,
    region_name="us-east-1",
)

# Create an SQS client
sqs = session.client("sqs")


def clear_sqs_messages(queue_url):
    while True:
        # Receive messages from SQS queue
        response = sqs.receive_message(
            QueueUrl=queue_url,
            AttributeNames=["All"],
            MaxNumberOfMessages=10,
            WaitTimeSeconds=20,  # Long polling
        )

        messages = response.get("Messages", [])
        import pprint

        if messages:
            for message in messages:
                pprint.pprint(f"Clearing Message: {message['Body']}")

                # Delete the message from the queue to prevent it from being read again
                sqs.delete_message(
                    QueueUrl=queue_url, ReceiptHandle=message["ReceiptHandle"]
                )
        else:
            print("No messages available.")
            break

        time.sleep(1)  # Pause for 1 second before next poll


if __name__ == "__main__":
    # Sign up register queue
    # TODO (P0): make sys arg
    clear_sqs_messages(
        "https://sqs.us-east-1.amazonaws.com/903881895532/SignupRegisterQueue.fifo"
    )
