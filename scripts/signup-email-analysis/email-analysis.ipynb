{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Signup Email Analysis\n", "This notebook explores queries for users which have signed up within a certain time frame. The intention is to validate the alignment with a target audience during US off peak hours."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from common import init_main\n", "from langsmith import Client\n", "import datetime\n", "import os\n", "import tqdm\n", "\n", "PROJECT_ROOT = init_main.initialize()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Email registry should contain the sheet with user signup information\n", "# Download file from drive and place in same folder to analyze\n", "original_df = pd.read_excel(\"email-registry.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# View contents of email registry\n", "original_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pull out important columns and rename for easier lookup\n", "columns = ['Signup Date', 'Email', 'Name', 'How Did You Hear About Us?', 'Source URL']\n", "filtered_df = original_df[columns].copy()\n", "filtered_df = filtered_df.rename(columns={'How Did You Hear About Us?': 'Note'})\n", "filtered_df['Signup Date'] = pd.to_datetime(filtered_df['Signup Date']).copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# View data in filtered list\n", "filtered_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove internal users from list\n", "values_to_drop = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']\n", "filtered_df = filtered_df[~filtered_df['Email'].isin(values_to_drop)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract data between expected low peak times 6-12 GMT\n", "sus_signups_df = filtered_df.set_index('Signup Date').between_time('06:00', '12:00')\n", "sus_signups_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save low peak signups to file\n", "sus_signups_df.to_csv(\"sus_signups.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set API key env variable if fetch_run function fails\n", "# os.environ['LANGCHAIN_API_KEY'] = ''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the client with environment variables\n", "client = Client(\n", "    api_key=os.getenv(\"LANGCHAIN_API_KEY\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> runs for the last N days\n", "def fetch_runs(days=5):\n", "    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(\n", "        days=days\n", "    )\n", "    runs = list(\n", "        client.list_runs(\n", "            project_name=\"freightgpt-prod\",\n", "            is_root=True,\n", "            start_time=start_time,\n", "            filter=\"and(eq(name, 'AgentExecutor'))\",\n", "        )\n", "    )\n", "    return runs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get runs for last X days. Expected to take ~10 mins depending on number of days fetched\n", "runs = fetch_runs(71)\n", "len(runs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Loop through runs and extract queries submitted by users\n", "run_details = []\n", "for run in tqdm.tqdm(runs):\n", "    try:\n", "        for element in run.tags:\n", "            if any(str(element).lower().replace(\" \", \"\") == str(item).lower().replace(\" \", \"\") for item in sus_signups_df['Name'].tolist()):\n", "                run_details.append({\n", "                        'name': element,\n", "                        'input': run.inputs['input']\n", "                    })\n", "                # There are multiple tags per query so as soon as one matches (i.e. the name) then break to avoid extra loops\n", "                break\n", "    except Exception as e:\n", "        print(f\"Failed to process run: {e}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert runs list to Dataframe\n", "sus_runs_df = pd.DataFrame(run_details)\n", "sus_runs_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save list of runs to file\n", "sus_runs_df.to_csv(\"sus_user_inputs.csv\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}