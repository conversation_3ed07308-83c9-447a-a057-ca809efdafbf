import datetime
import json
import os

import boto3
import pandas as pd
from langsmith import Client
from tqdm import tqdm

from common import init_main

PROJECT_ROOT = init_main.initialize()
DEV_COGNITO_USER_POOL_ID = "us-east-1_OtlXNpE6h"
PROD_COGNITO_USER_POOL_ID = "us-east-1_azsO85PeM"

# Initialize Cognito client
cognito_client = boto3.client("cognito-idp", region_name="us-east-1")

# Initialize the LangSmith client
client = Client(
    api_key=os.getenv("LANGCHAIN_API_KEY"),
)


# Fetch all Cognito users from Cognito User Pool
def fetch_all_cognito_users(user_pool_id):
    users = []
    paginator = cognito_client.get_paginator("list_users")

    for page in paginator.paginate(UserPoolId=user_pool_id):
        for user in page["Users"]:
            user_data = {attr["Name"]: attr["Value"] for attr in user["Attributes"]}
            users.append(
                {
                    "user_id": user["Username"],
                    "name": user_data.get("name", ""),
                    "cognito_user_data": user_data,
                    "message_timestamps": [],
                    "lastUpdated": datetime.datetime.now().timestamp(),
                }
            )
    return users


# Fetch LangSmith runs for the last N days or all time
def fetch_runs(days=None):
    if days:
        start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            days=days
        )
    else:
        start_time = None

    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Initialize user data based on Cognito users
def initialize_user_data(cognito_users):
    user_data = {}
    for user in cognito_users:
        user_data[user["name"]] = user
    return user_data


# Process LangSmith runs and update the user data
def process_runs(runs, user_data):
    for run in tqdm(runs, desc="Processing Runs"):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            user_name = (tags - {"openai-functions", "desktop", "mobile"}).pop()
            if user_name in user_data:
                user_data[user_name]["message_timestamps"].append(
                    run.start_time.timestamp()
                )
        except Exception as e:
            print(f"Failed to process run: {e}")


# Main execution flow
def main(user_pool_id, days=None):
    # Step 1: Fetch all Cognito users
    cognito_users = fetch_all_cognito_users(user_pool_id)

    # Step 2: Fetch all runs for the given period
    runs = fetch_runs(days)

    # Step 3: Initialize the user data structure
    user_data = initialize_user_data(cognito_users)

    # Step 4: Process the runs and update user data
    process_runs(runs, user_data)

    # Convert to JSON or DataFrame as required
    user_data_list = list(user_data.values())
    user_data_df = pd.DataFrame(user_data_list)

    # Save to JSON file
    with open("user_data.json", "w") as f:
        json.dump(user_data_list, f, indent=4)

    return user_data_df, user_data_list


# Run the script

user_pool_id = PROD_COGNITO_USER_POOL_ID
user_data_df, user_data_list = main(
    user_pool_id, days=1
)  # Set days=None to fetch all-time runs

# Print output (optional)
print(user_data_df)
