import os

import eralchemy2

from common import init_main
from common.constants import AURORA_DB_NAME
from common.rds_api import RDS

PROJECT_ROOT = init_main.initialize()

# Non-ingestion dependency: graphviz / eralchemy2
#
# Install graphviz for current operating system and architecture:
# - Redhat: yum install graphviz-devel.x86_64
# - Debian: apt install graphviz-dev
# - MacOS: brew install graphviz
# - Windows: download installer from https://graphviz.org/download/ OR
#            winget install graphviz
#
# Then: pip install eralchemy2


def generate_schema_diagram(metadata, filename):
    """Generates png of database schema."""
    eralchemy2.render_er(metadata, filename)


def main():
    rds = RDS(AURORA_DB_NAME, create_tables=False)
    meta = rds.get_schema_metadata()
    filename = os.path.join(PROJECT_ROOT, "scripts", "schema_diagram.png")
    generate_schema_diagram(meta, filename)
    print(f"Schema diagram saved to {filename}")


if __name__ == "__main__":
    main()
