import datetime
import os

import plotly.graph_objects as go
import tqdm
from freightgpt_cognito_user_analysis import fetch_cognito_users
from freightgpt_cognito_user_analysis import load_data
from freightgpt_cognito_user_analysis import save_to_csv
from langsmith import Client

from common import init_main

PROJECT_ROOT = init_main.initialize()


# Initialize the client with environment variables
client = Client(api_key=os.getenv("LANGCHAIN_API_KEY"))


# Fetch runs for the last N days
def fetch_runs(days=30):
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Categorize runs by user and date of first query
def categorize_runs(runs, deny_list=None, extraneous_tags=None):
    if deny_list is None:
        deny_list = {
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "public",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
        }
    if extraneous_tags is None:
        extraneous_tags = {"openai-functions", "desktop", "mobile"}

    users_with_queries = {}

    for run in tqdm.tqdm(runs):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            if not tags or deny_list & tags:
                continue
            user = (tags - extraneous_tags).pop()
            date = run.start_time.date()
            if user not in users_with_queries:
                users_with_queries[user] = date
        except Exception as e:
            print(f"Failed to process run: {e}")

    return users_with_queries


# Plot the graph
def plot_graphs(cumulative_signups, cumulative_users_with_queries, dates):
    fig = go.Figure()

    fig.add_trace(
        go.Scatter(
            x=dates,
            y=cumulative_signups,
            mode="lines+markers",
            name="Cumulative Sign-Ups",
            text=[f"Cumulative Sign-Ups: {count}" for count in cumulative_signups],
            hoverinfo="text",
            marker=dict(color="blue"),
        )
    )

    fig.add_trace(
        go.Scatter(
            x=dates,
            y=cumulative_users_with_queries,
            mode="lines+markers",
            name="Cumulative Users with Queries",
            text=[
                f"Cumulative Users with Queries: {count}"
                for count in cumulative_users_with_queries
            ],
            hoverinfo="text",
            marker=dict(color="green"),
        )
    )

    fig.update_layout(
        title="Cumulative Signups vs Users Sending Messages",
        xaxis_title="Date",
        yaxis_title="Cumulative Count",
        hovermode="closest",
    )

    fig.show()


# Calculate cumulative counts for signups and users with queries
def calculate_cumulative_counts(signup_dates, users_with_queries, all_dates):
    cumulative_signups = []
    cumulative_users_with_queries = []

    signup_count = 0
    users_count = 0

    for date in all_dates:
        signup_count += signup_dates.count(date)
        users_count += sum(
            1 for user_date in users_with_queries.values() if user_date == date
        )

        cumulative_signups.append(signup_count)
        cumulative_users_with_queries.append(users_count)

    return cumulative_signups, cumulative_users_with_queries


# Main execution function
def main():
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    save_to_csv(users, "cognito_users.csv")

    data = load_data("cognito_users.csv")
    # Convert signup_dates to datetime.date objects
    signup_dates = sorted(
        [
            datetime.datetime.strptime(date, "%Y-%m-%d").date()
            for date in data["Sign Up Date"].tolist()
        ]
    )
    runs = fetch_runs(15)
    users_with_queries = categorize_runs(runs)

    # Combine and sort the dates
    all_dates = sorted(set(signup_dates + list(users_with_queries.values())))
    cumulative_signups, cumulative_users_with_queries = calculate_cumulative_counts(
        signup_dates, users_with_queries, all_dates
    )

    plot_graphs(cumulative_signups, cumulative_users_with_queries, all_dates)


if __name__ == "__main__":
    main()
