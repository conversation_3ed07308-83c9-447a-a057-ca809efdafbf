{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common import init_main\n", "\n", "PROJECT_ROOT = init_main.initialize()\n", "\n", "from common import google_sheets_utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4\"\n", "to_add_sheet_name = \"TO-ADD\"\n", "generated_emails_sheet_name = \"Generated Emails\"\n", "added_sheet_name = \"Added\"\n", "invited_sheet_name = \"Sheet1\"\n", "\n", "# Initialize Google Sheets utility\n", "gs_utils = google_sheets_utils.GoogleSheetsUtils()\n", "\n", "# Read the 'TO-ADD' sheet into a DataFrame\n", "to_add_df = gs_utils.load_sheet_into_dataframe(sheet_id, to_add_sheet_name)\n", "to_add_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invited_df = gs_utils.load_sheet_into_dataframe(sheet_id, invited_sheet_name)\n", "invited_emails = set(invited_df[\"Approved Emails\"].values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invited_emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove any emails that have already been invited\n", "def is_in_invited_emails(email):\n", "    email = email.lower().strip()\n", "    return email in invited_emails\n", "\n", "\n", "# Remove from to_add_df any emails that have already been invited\n", "to_add_df[\"Already Invited\"] = to_add_df[\"Email\"].apply(is_in_invited_emails)\n", "to_add_df = to_add_df[to_add_df[\"Already Invited\"] == False]\n", "to_add_df = to_add_df.drop(columns=[\"Already Invited\"])\n", "to_add_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from generate_freightgpt_signup_link import (\n", "    generate_signup_link,\n", "    generate_email_template,\n", ")\n", "\n", "# Start sending emails at 6:30 AM\n", "\n", "# Prepare data for 'Generated Emails' and 'Added'\n", "generated_emails_data = []\n", "added_data = []\n", "subject = \"You've Been Invited to the FreightGPT Beta\"\n", "# Process each entry\n", "for index, row in to_add_df.iterrows():\n", "    email = row[\"Email\"].strip().lower()\n", "    first_name = row[\"First Name\"].strip().capitalize()\n", "    last_name = row[\"Last Name\"].strip().capitalize()\n", "    company = row[\"Company\"].strip()\n", "    role = row[\"Role\"].strip() if row[\"Role\"] else \"\"\n", "\n", "    # Generate the signup link and email template\n", "    signup_link = generate_signup_link(email)\n", "    email_body = generate_email_template(\n", "        email, first_name, \"<PERSON><PERSON>\", signup_link, body_only=True\n", "    )\n", "\n", "    # Append to 'Generated Emails'\n", "    generated_emails_data.append([email, subject, email_body])\n", "\n", "    # Prepare data for 'Added'\n", "    sent_message = \"No\"\n", "    signed_up = \"No\"  # This could be updated later based on user interaction\n", "    sent_date = pd.Timestamp(\"today\").strftime(\n", "        \"%m/%d/%Y\"\n", "    )  # Current date in MM/DD/YYYY format\n", "    added_data.append(\n", "        [\n", "            email,\n", "            f\"{first_name} {last_name}\",\n", "            sent_date,\n", "            signed_up,\n", "            sent_message,\n", "            company,\n", "            role,\n", "        ]\n", "    )\n", "\n", "# Convert lists to DataFrames\n", "generated_emails_df = pd.DataFrame(\n", "    generated_emails_data, columns=[\"Email\", \"Subject\", \"Email Body\"]\n", ")\n", "added_df = pd.DataFrame(\n", "    added_data,\n", "    columns=[\n", "        \"Approved Emails\",\n", "        \"Name\",\n", "        \"Sent\",\n", "        \"Signed Up\",\n", "        \"Sent Message\",\n", "        \"Company\",\n", "        \"Role\",\n", "    ],\n", ")\n", "\n", "# Write the DataFrames back to the sheets\n", "gs_utils.write_df_to_sheet(\n", "    generated_emails_df, sheet_id, generated_emails_sheet_name, skiprows=0\n", ")\n", "gs_utils.write_df_to_sheet(added_df, sheet_id, added_sheet_name, skiprows=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Update Sheet1 based on cognito and langsmith"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sheet_id = \"1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4\"\n", "to_add_sheet_name = \"Sheet1\"\n", "generated_emails_sheet_name = \"Generated Emails\"\n", "added_sheet_name = \"Added\"\n", "\n", "# Initialize Google Sheets utility\n", "gs_utils = google_sheets_utils.GoogleSheetsUtils()\n", "\n", "# Read the 'TO-ADD' sheet into a DataFrame\n", "users_df = gs_utils.load_sheet_into_dataframe(sheet_id, to_add_sheet_name)\n", "users_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from common.cognito_utils import FREIGHTGPT_PROD_COGNITO_USER_POOL_ID, CognitoUserData\n", "\n", "\n", "COGNITO_USER_DATA = CognitoUserData(\n", "    user_pool_id=FREIGHTGPT_PROD_COGNITO_USER_POOL_ID, is_dev_env=False\n", ")\n", "users = COGNITO_USER_DATA.get_all_users()\n", "users"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Assuming users_df is already loaded as shown above\n", "\n", "# Extract emails from the users dictionary\n", "email_list = [\n", "    attr[\"Value\"]\n", "    for user in users[\"Users\"]\n", "    for attr in user[\"Attributes\"]\n", "    if attr[\"Name\"] == \"email\"\n", "]\n", "\n", "# Update the 'Signed Up' column based on the presence of the email in the email_list\n", "users_df[\"Signed Up\"] = users_df[\"Approved Emails\"].apply(\n", "    lambda email: \"Yes\" if email in email_list else \"No\"\n", ")\n", "\n", "# Display the updated DataFrame\n", "users_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Write the updated users_df to 'Sheet2' in the Google Sheets\n", "gs_utils.write_df_to_sheet(users_df, sheet_id, \"Sheet2\", skiprows=0)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}