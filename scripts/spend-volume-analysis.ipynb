{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Shipper spend and volume over time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "import pandas as pd\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# AWS RDS Data client\n", "client = boto3.client('rds-data')\n", "\n", "# Database connection parameters\n", "DATABASE_NAME = 'mvp_db'\n", "DB_CLUSTER_ARN = '<REPLACE_WITH_ARN_FROM_CRED_FILE>'\n", "DB_CREDS_SECRET_ARN = '<REPLACE_WITH_ARN_FROM_CRED_FILE>'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute_query(query):\n", "    try:\n", "        # Execute the SQL statement\n", "        response = client.execute_statement(\n", "            resourceArn=DB_CLUSTER_ARN,\n", "            secretArn=DB_CREDS_SECRET_ARN,\n", "            database=DATABASE_NAME,\n", "            sql=query\n", "        )\n", "        \n", "        # Process the response and store results in a list of dictionaries\n", "        records = response['records']\n", "        result_data = []\n", "        \n", "        for record in records:\n", "            name = record[0]['longValue'] if 'longValue' in record[0] else record[0]['stringValue']\n", "            month = record[1]['stringValue']\n", "            total_spend = record[2]['doubleValue']\n", "            volume = record[3]['longValue']\n", "            \n", "            # Append a dictionary for each record\n", "            result_data.append({\n", "                'name': name,\n", "                'month': month,\n", "                'total_spend': total_spend,\n", "                'volume': volume,\n", "            })\n", "        \n", "        # Create a DataFrame from the result data\n", "        df = pd.DataFrame(result_data)\n", "        \n", "        # Print the DataFrame\n", "        return df\n", "\n", "    except Exception as e:\n", "        print(\"Error executing query:\", e)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_graph(df):\n", "    # Create the stacked bar chart for total amounts\n", "    fig_amounts = go.Figure()\n", "\n", "    # Add bars for each user_id\n", "    for user in df['name'].unique():\n", "        user_data = df[df['name'] == user]\n", "        fig_amounts.add_trace(go.Bar(\n", "            x=user_data['month'],\n", "            y=user_data['total_spend'],\n", "            name=f'{user}',\n", "            hovertemplate='<b>Shipper:</b> %{customdata[0]}<br>' +\n", "                        '<b>Month:</b> %{customdata[1]}<br>' +\n", "                        '<b>Total Spend:</b> $%{y:,.2f}<extra></extra>',\n", "            customdata=user_data[['name', 'month']].values\n", "        ))\n", "\n", "    # Update layout for total amounts\n", "    fig_amounts.update_layout(\n", "        title='Total Spend per Shipper per Month',\n", "        barmode='stack',\n", "        yaxis_title='Total Spend (Millions)',\n", "        xaxis_title='Month'\n", "    )\n", "\n", "    # Show the total amounts figure\n", "    fig_amounts.show()\n", "\n", "    # Create the stacked bar chart for transaction volumes\n", "    fig_volumes = go.Figure()\n", "\n", "    # Add bars for each name\n", "    for user in df['name'].unique():\n", "        user_data = df[df['name'] == user]\n", "        fig_volumes.add_trace(go.Bar(\n", "            x=user_data['month'],\n", "            y=user_data['volume'],\n", "            name=f'{user}',\n", "            hoverinfo='text',\n", "            hovertemplate='<b>Shipper:</b> %{customdata[0]}<br>' +\n", "                        '<b>Month:</b> %{customdata[1]}<br>' +\n", "                        '<b>Total Volume:</b> %{y}<extra></extra>',\n", "            customdata=user_data[['name', 'month']].values,\n", "        ))\n", "\n", "    # Update layout for transaction volumes\n", "    fig_volumes.update_layout(\n", "        title='Total Volume per Shipper per Month',\n", "        barmode='stack',\n", "        yaxis_title='Total Volume',\n", "        xaxis_title='Month'\n", "    )\n", "\n", "    # Show the transaction volumes figure\n", "    fig_volumes.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL query to calculate total amount grouped by month per user\n", "query = \"\"\"\n", "    SELECT \n", "        sh.name,\n", "        DATE_FORMAT(s.originCloseTime, '%Y-%m') AS month,\n", "        SUM(s.revenueTotal) AS total_spend,\n", "        COUNT(s.id) as volume\n", "    FROM shipments s\n", "    JOIN shippers sh ON s.shipperId = sh.id\n", "    WHERE DATE(s.originCloseTime) >= \"2021-01-01\"\n", "    GROUP BY \n", "        s.shipperId, DATE_FORMAT(s.originCloseTime, '%Y-%m')\n", "    ORDER BY \n", "        s.shipperId, month;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pull details from DB\n", "df = execute_query(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot results\n", "plot_graph(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# YOY"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute_query_all(query):\n", "    try:\n", "        # Execute the SQL statement\n", "        response = client.execute_statement(\n", "            resourceArn=DB_CLUSTER_ARN,\n", "            secretArn=DB_CREDS_SECRET_ARN,\n", "            database=DATABASE_NAME,\n", "            sql=query\n", "        )\n", "        \n", "        # Process the response and store results in a list of dictionaries\n", "        records = response['records']\n", "        result_data = []\n", "        \n", "        for record in records:\n", "            name = record[0]['longValue'] if 'longValue' in record[0] else record[0]['stringValue']\n", "            year = record[1]['stringValue'] if 'stringValue' in record[1] else record[1]['isNull']\n", "            total_spend = record[2]['doubleValue'] if 'doubleValue' in record[2] else record[2]['isNull']\n", "            total_volume = record[3]['longValue'] if 'longValue' in record[3] else record[3]['isNull']\n", "            avg_margin = record[4]['doubleValue'] if 'doubleValue' in record[4] else record[4]['isNull']\n", "            avg_margin_dollars = record[5]['doubleValue'] if 'doubleValue' in record[5] else record[5]['isNull']\n", "            total_margin = record[6]['doubleValue'] if 'doubleValue' in record[6] else record[6]['isNull']\n", "            avg_otd = record[7]['stringValue'] if 'stringValue' in record[8] else record[8]['isNull']\n", "            avg_otp = record[8]['stringValue'] if 'stringValue' in record[8] else record[8]['isNull']\n", "            avg_ltu_hours = record[9]['doubleValue'] if 'doubleValue' in record[9] else record[9]['isNull']\n", "            \n", "            # Append a dictionary for each record\n", "            result_data.append({\n", "                'name': name,\n", "                'year': year,\n", "                'total_spend': total_spend,\n", "                'total_volume': total_volume,\n", "                'avg_margin': avg_margin,\n", "                'avg_margin_dollars': avg_margin_dollars,\n", "                'total_margin': total_margin,\n", "                'avg_otd': avg_otd,\n", "                'avg_otp': avg_otp,\n", "                'avg_ltu_hours': avg_ltu_hours\n", "            })\n", "        \n", "        # Create a DataFrame from the result data\n", "        df = pd.DataFrame(result_data)\n", "        \n", "        # Print the DataFrame\n", "        return df\n", "\n", "    except Exception as e:\n", "        print(\"Error executing query:\", e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQL query to calculate total amount grouped by month per user\n", "full_query = \"\"\"\n", "    SELECT \n", "        sh.name,\n", "        DATE_FORMAT(s.originCloseTime, '%Y') AS calendar_year,\n", "        SUM(s.revenueTotal) AS total_spend,\n", "        COUNT(s.id) as total_volume,\n", "        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END)) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END))) / (SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END)) AS avg_margin,\n", "        ((SUM(CASE WHEN shipmentClass = 'canonical' THEN revenueTotal ELSE NULL END)) - (SUM(CASE WHEN shipmentClass = 'canonical' THEN cogsTotal ELSE NULL END))) / COUNT(CASE WHEN shipmentClass = 'canonical' THEN 1 ELSE NULL END) AS avg_margin_dollars,\n", "        ((SUM(revenueTotal)) - (SUM(cogsTotal))) AS total_margin,\n", "        AVG(CASE\n", "                WHEN destinationDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1\n", "                WHEN destinationDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0\n", "                ELSE NULL\n", "            END\n", "        ) as avg_otd,\n", "        AVG(CASE\n", "                WHEN originDelayMinutes <= 30 AND shipmentClass = 'canonical' THEN 1\n", "                WHEN originDelayMinutes > 30 AND shipmentClass = 'canonical' THEN 0\n", "                ELSE NULL\n", "            END\n", "        ) as avg_otp,\n", "        AVG(clt - blt) as avg_ltu_hours\n", "    FROM shipments s\n", "    JOIN shippers sh ON s.shipperId = sh.id\n", "    WHERE DATE(s.originCloseTime) >= \"2021-01-01\"\n", "    GROUP BY \n", "        s.shipperId, DATE_FORMAT(s.originCloseTime, '%Y')\n", "    ORDER BY \n", "        s.shipperId, calendar_year;\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pull details from DB\n", "df = execute_query_all(full_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"yoy_data.csv\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}