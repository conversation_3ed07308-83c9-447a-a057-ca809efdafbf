# Adds new user
import pprint

import boto3
from botocore.exceptions import ClientError
from InquirerPy import inquirer

from common.query_utils import format_rds_response
from common.query_utils import query

TRUCE_USER_POOL = "us-east-1_PJgJ5RUP8"


def create_dynamo_entry(
    email: str, company: str, business_unit_id: str, use_dev: bool
) -> str:
    """
    Creates new dynamo entry with user id.

    Args:
        email (str): email of user
        name (str): company of user
        business_unit_id (str): company id of user
        use_dev (bool): flag for dev/prod environment

    Raises:
        ClientError: error thrown during user creation failure

    Returns:
        str: response of user creation
    """
    dynamodb = boto3.resource("dynamodb")
    pp = pprint.PrettyPrinter(indent=4)
    table_name = "GetShipperBusinessUnitDev" if use_dev else "GetShipperBusinessUnit"
    SBU_TABLE = dynamodb.Table(table_name)

    try:
        response = SBU_TABLE.put_item(
            Item={
                "email": email,
                "company": company,
                "shipper_business_unit": business_unit_id,
            }
        )
    except ClientError:
        raise ClientError
    else:
        pp.pprint(response)
        assert (
            response["ResponseMetadata"]["HTTPStatusCode"] == 200
        ), "Failed to create entry."
        return response


def create_cognito_user(email: str, company_name: str, role: str) -> str:
    """
    Creates new user in cognito and sends invitation email.

    Args:
        email (str): email of user
        company_name (str): company of user
        role (str): role of user

    Raises:
        ClientError: error thrown during user creation failure

    Returns:
        str: response of user creation
    """
    cognito = boto3.client("cognito-idp")
    pp = pprint.PrettyPrinter(indent=4)

    try:
        response = cognito.admin_create_user(
            UserPoolId=TRUCE_USER_POOL,
            Username=email,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "email_verified", "Value": "true"},
                {"Name": "custom:company", "Value": company_name},
                {"Name": "custom:role", "Value": role},
            ],
            DesiredDeliveryMediums=[
                "EMAIL",
            ],
        )
    except ClientError:
        raise ClientError
    else:
        pp.pprint(response)
        assert (
            response["ResponseMetadata"]["HTTPStatusCode"] == 200
        ), "Failed to create user."
        return response


def get_company_list(env: str, role: str) -> list:
    """
    Get list of companies based on parameters provided

    Args:
        env (str): dev or prod
        role (str): shipper or broker

    Returns:
        list: company objects
    """
    db_name = "mvp_db_dev" if env == "dev" else "mvp_db"
    table_name = "shippers" if role == "shipper" else "brokers"

    query_sql = f"SELECT id, name FROM {db_name}.{table_name}"

    return format_rds_response(query(query_sql, db_name))


def create_user() -> None:
    """
    Collect new user information using InquirerPy and create user based on input

    Docs: https://inquirerpy.readthedocs.io/en/latest/
    """
    email = inquirer.text(message="What is the email of the new user:").execute()
    cleaned_email = email.strip().lower()

    env = inquirer.select(
        message="Select environment:", choices=["dev", "prod"]
    ).execute()
    use_dev = env == "dev"

    role = inquirer.select(
        message="Select role:", choices=["shipper", "broker"]
    ).execute()

    company_list = get_company_list(env=env, role=role)
    company_names = [company["name"] for company in company_list]
    company_name = inquirer.fuzzy(
        message=f"Select {role}", choices=company_names
    ).execute()
    business_unit_id = [
        company for company in company_list if company["name"] == company_name
    ][0]["id"]

    # Print user details
    print("New User Details:")

    user_details = {
        "email": cleaned_email,
        "env": env,
        "role": role,
        "company": company_name,
        "business_id": business_unit_id,
    }

    pp = pprint.PrettyPrinter(indent=4)
    pp.pprint(user_details)

    confirm_dynamo = inquirer.confirm(message="Confirm DynamoDB creation:").execute()
    if confirm_dynamo:
        create_dynamo_entry(cleaned_email, company_name, business_unit_id, use_dev)
        confirm_cognito = inquirer.confirm(
            message="Confirm Cognito creation:"
        ).execute()
        if confirm_cognito:
            create_cognito_user(cleaned_email, company_name, role)
            print("User Created Successfully")


if __name__ == "__main__":
    create_user()
