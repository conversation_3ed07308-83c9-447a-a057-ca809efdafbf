import datetime
import os
from collections import defaultdict

import pandas as pd
import plotly.express as px
from langsmith import Client
from tqdm import tqdm

from common import google_sheets_utils


def fetch_runs(client, days=1):
    """
    Fetch LangSmith runs from the past 'days' days.

    Args:
        client (Client): LangSmith client instance.
        days (int): Number of days to look back for runs.

    Returns:
        list: List of fetched runs.
    """
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


def categorize_runs_by_user_and_source(client, runs, users_by_source):
    """
    Categorize runs by user and source URL.

    Args:
        client (Client): LangSmith client instance.
        runs (list): List of runs to categorize.
        users_by_source (dict): Dictionary mapping source URLs to user lists.

    Returns:
        defaultdict: Nested dictionary with counts of queries per user per source.
    """
    queries_by_user_and_source = defaultdict(lambda: defaultdict(int))
    for run in tqdm(runs, desc="Processing Runs"):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            # Exclude specific tags to identify the user
            user_tags = tags - {"openai-functions", "desktop", "mobile"}
            if not user_tags:
                continue  # Skip if no user tag is found
            user = user_tags.pop()

            # Find the user's source URL from the sheet data
            for source, users in users_by_source.items():
                if user in users:
                    queries_by_user_and_source[source][user] += 1
                    break
        except Exception as e:
            print(f"Failed to process run {run.trace_id}: {e}")
    return queries_by_user_and_source


def main():
    # Initialize the client with environment variables
    client = Client(
        api_key=os.getenv("LANGCHAIN_API_KEY"),
    )

    # Load data from Google Sheets
    GSHEETS_UTILS = google_sheets_utils.GoogleSheetsUtils()
    prod_signup_tracker = GSHEETS_UTILS.load_sheet_into_dataframe(
        spreadsheet_id="1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4",
        sheet_name="Prod Sign Up Tracker",
    )

    # Ensure 'Source URL' column does not contain None or NaN
    prod_signup_tracker = prod_signup_tracker[
        pd.notna(prod_signup_tracker["Source URL"])
    ]

    # Apply the filters to remove unwanted URLs
    prod_signup_tracker = prod_signup_tracker[
        ~prod_signup_tracker["Source URL"].str.contains("localhost")
        & ~prod_signup_tracker["Source URL"].str.contains("-dev")
        & ~prod_signup_tracker["Source URL"].str.contains("8080")
    ]

    # Define substrings to search for
    substrings = [
        "about/mc-lookup",
        "about/carrier-search",
        "about/reefer-rates",
        "about/dot-lookup",
        "about/dry-van-rates",
        "freight-trucking-rates",
        "?gad_source",  # match any query parameter with gad_source
    ]

    # Initialize a dictionary to hold user names categorized by Source URL
    users_by_source = {substring: [] for substring in substrings}

    # Debugging: Initialize a list to hold unmatched rows
    unmatched_rows = []

    # Iterate through the DataFrame with a progress bar
    for _, row in tqdm(
        prod_signup_tracker.iterrows(),
        total=prod_signup_tracker.shape[0],
        desc="Processing Rows",
    ):
        source_url = row["Source URL"]
        user_name = row["Name"]  # Use the 'Name' column as the user identifier
        matched = False
        for substring in substrings:
            if substring in source_url:
                users_by_source[substring].append(user_name)
                matched = True
                break
        if not matched:
            users_by_source.setdefault("direct_nav", []).append(user_name)
            unmatched_rows.append(source_url)

    # Fetch LangSmith runs for the last 60 days
    runs = fetch_runs(client, days=1)

    # Categorize runs by user and source URL
    queries_by_user_and_source = categorize_runs_by_user_and_source(
        client, runs, users_by_source
    )

    # Convert the counts to a DataFrame
    data = {"Source URL": [], "User": [], "No of Queries": []}
    for source, users in queries_by_user_and_source.items():
        for user, count in users.items():
            data["Source URL"].append(source)
            data["User"].append(user)
            data["No of Queries"].append(count)

    queries_df = pd.DataFrame(data)

    # Create the bar chart
    fig = px.bar(
        queries_df,
        x="User",
        y="No of Queries",
        color="Source URL",
        title="Number of Queries per User per Source URL",
        hover_data={"Source URL": True},
    )

    # Show the plot
    fig.show()


if __name__ == "__main__":
    main()
