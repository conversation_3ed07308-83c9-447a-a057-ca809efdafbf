{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from common.cognito_utils import FREIGHTGPT_PROD_COGNITO_USER_POOL_ID, CognitoUserData\n", "\n", "\n", "# Define a function to process each user's data into the required CSV format\n", "def process_user_data(users_data):\n", "    # Define the placeholder data for missing fields\n", "    placeholder_data = {\n", "        \"Title\": \"\",\n", "        \"Person Linkedin Url\": \"\",\n", "    }\n", "\n", "    # Prepare the CSV data\n", "    processed_data = []\n", "    for user in users_data:\n", "        # Extract user attributes into a dictionary\n", "        user_attrs = {attr[\"Name\"]: attr[\"Value\"] for attr in user[\"Attributes\"]}\n", "\n", "        # Prepare user data dictionary\n", "        user_data = {\n", "            \"First Name\": (\n", "                user_attrs.get(\"name\", \"\").split()[0]\n", "                if \"name\" in user_attrs and len(user_attrs[\"name\"].split()) > 0\n", "                else \"Unknown\"\n", "            ),\n", "            \"Last Name\": (\n", "                user_attrs.get(\"name\", \"\").split()[-1]\n", "                if \"name\" in user_attrs and len(user_attrs[\"name\"].split()) > 1\n", "                else \"\"\n", "            ),\n", "            \"Customer Type\": user_attrs.get(\n", "                \"custom:customer_type\", placeholder_data[\"Title\"]\n", "            ),\n", "            \"Company Name\": user_attrs.get(\"custom:company\", \"\"),\n", "            \"Email\": user_attrs.get(\"email\", \"\"),\n", "        }\n", "\n", "        processed_data.append(user_data)\n", "\n", "    return processed_data\n", "\n", "\n", "user_data_obj = CognitoUserData(\n", "    user_pool_id=FREIGHTGPT_PROD_COGNITO_USER_POOL_ID, is_dev_env=False\n", ")\n", "users_data = user_data_obj.get_all_users()\n", "\n", "# Process the user data for CSV\n", "processed_user_data = process_user_data(users_data)\n", "\n", "# Convert processed data to DataFrame\n", "df_users = pd.DataFrame(processed_user_data)\n", "df_users.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import titlecase\n", "\n", "\n", "# Load the data\n", "invited_df = pd.read_csv(\"data/prod_sheet1.csv\")\n", "\n", "# Prepare the data\n", "invited_df[\"Approved Emails\"] = invited_df[\"Approved Emails\"].str.lower()\n", "df_users[\"Email\"] = df_users[\"Email\"].str.lower()\n", "df_users[\"First Name\"] = df_users[\"First Name\"].apply(titlecase.titlecase)\n", "df_users[\"Last Name\"] = df_users[\"Last Name\"].apply(titlecase.titlecase)\n", "\n", "# Remove the users who have are already signed up\n", "invited_df = invited_df[~invited_df[\"Approved Emails\"].isin(df_users[\"Email\"])]\n", "invited_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_users.to_csv(\"data/registered_users.csv\", index=False)\n", "invited_df.to_csv(\"data/invited_users.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}