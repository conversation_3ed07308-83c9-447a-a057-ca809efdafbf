{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# User Usage\n", "\n", "This notebook when executed will provide grapsh for DAU, DAU exluding signups, WAU, and WAU excluding signups. These graphs will allow us to monitor recurring activity within the FreightGPT platform"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["import boto3\n", "from botocore.exceptions import NoCredentialsError, PartialCredentialsError\n", "from botocore.config import Config\n", "from datetime import datetime, timezone\n", "from decimal import Decimal\n", "import time\n", "import pandas as pd\n", "import plotly.express as px"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["# Define constants\n", "TABLE_NAME = \"ChatStoreProd\"\n", "REGION = \"us-east-1\"\n", "COGNITO_USER_POOL_ID = \"us-east-1_azsO85PeM\""]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["\n", "# Define a boto3 config to handle retries and exponential backoff\n", "config = Config(\n", "    retries={\n", "        'max_attempts': 10,  # Number of retry attempts\n", "        'mode': 'standard'  # Use standard retry mode with exponential backoff\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["def fetch_dynamodb_items(table_name, region_name):\n", "    \"\"\"\n", "\n", "    Fetch items from dynamodb table\n", "\n", "    Args:\n", "        table_name (str): table to scan\n", "        region_name (str): region where table is stored\n", "\n", "    Returns:\n", "        list: list of items from scanned table\n", "    \"\"\"\n", "    try:\n", "        dynamodb = boto3.resource('dynamodb', region_name=region_name, config=config)\n", "        table = dynamodb.Table(table_name)\n", "\n", "        all_items = []\n", "        response = table.scan()\n", "\n", "        # Retrieve items in paginated batches with exponential backoff\n", "        while 'LastEvaluated<PERSON>ey' in response:\n", "            all_items.extend(response['Items'])  # Append the current batch of items\n", "            time.sleep(2)  # Add a small delay to reduce load\n", "\n", "            # Retry logic using exponential backoff\n", "            try:\n", "                response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])\n", "            except Exception as e:\n", "                print(f\"<PERSON><PERSON> failed, retrying... {e}\")\n", "                time.sleep(5)  # Backoff before retrying\n", "\n", "        # Include the final page of items\n", "        all_items.extend(response['Items'])\n", "\n", "        print(f\"Successfully retrieved {len(all_items)} items from the table '{table_name}'\")\n", "        return all_items\n", "    \n", "    except NoCredentialsError:\n", "        print(\"Credentials not available\")\n", "        return []\n", "    except PartialCredentialsError:\n", "        print(\"Incomplete credentials provided\")\n", "        return []\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["def track_distinct_users_per_day(items):\n", "    \"\"\"\n", "\n", "    Loop through items from dynamo and store user id and created date details in dataframe\n", "\n", "    Args:\n", "        items (list): list of items from dynamo\n", "\n", "    Returns:\n", "        Dataframe: data frame of user id and date details from items\n", "    \"\"\"\n", "    \n", "    df = pd.DataFrame(columns=['User Id', 'Created Date'])\n", "\n", "    for item in items:\n", "        user_id = item['user_id']\n", "        timestamp_epoch = item['CreationTimestamp']\n", "\n", "        # Convert decimal.Decimal to float if necessary\n", "        if isinstance(timestamp_epoch, Decimal):\n", "            timestamp_epoch = float(timestamp_epoch)\n", "\n", "        # Convert the epoch timestamp to a datetime object\n", "        timestamp = datetime.fromtimestamp(timestamp_epoch).astimezone(timezone.utc)\n", "\n", "        # Get the date (year-month-day)\n", "        date = timestamp.date()\n", "\n", "        # Add user_id and timestamp to df\n", "        new_row = pd.DataFrame({\"User Id\": [user_id], \"Created Date\": [date]})\n", "\n", "        # Concatenate the new row to the existing DataFrame\n", "        df = pd.concat([df, new_row], ignore_index=True)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["def fetch_cognito_users(user_pool_id, region):\n", "    \"\"\"\n", "    Fetch users from user pool specified.\n", "\n", "    Args:\n", "        user_pool_id (str): Cognito user pool to fetch frmo\n", "        region (str): Region of user pool\n", "\n", "    Returns:\n", "        Dataframe: dataframe of users\n", "    \"\"\"\n", "\n", "    cognito_client = boto3.client('cognito-idp', region_name=region)\n", "    users = []\n", "    df = pd.DataFrame(columns=['User Id', 'Name', 'Created Date'])\n", "    \n", "    try:\n", "        response = cognito_client.list_users(UserPoolId=user_pool_id)\n", "\n", "        # Pagination to fetch all users if there are multiple pages\n", "        while 'PaginationToken' in response:\n", "            users.extend(response['Users'])\n", "            response = cognito_client.list_users(UserPoolId=user_pool_id, PaginationToken=response['PaginationToken'])\n", "        \n", "        # Add the final batch of users\n", "        users.extend(response['Users'])\n", "\n", "        for user in users:\n", "            user_details = \\\n", "            {\n", "                \"User Id\": [attr[\"Value\"] for attr in user[\"Attributes\"] if attr[\"Name\"] == \"sub\" ],\n", "                \"Name\": [attr[\"Value\"] for attr in user[\"Attributes\"] if attr[\"Name\"] == \"name\" ],\n", "                \"Created Date\": [user[\"UserCreateDate\"].astimezone(timezone.utc).date()]\n", "            }\n", "\n", "            # Add user_id and timestamp to df\n", "            new_row = pd.DataFrame(user_details)\n", "\n", "            # Concatenate the new row to the existing DataFrame\n", "            df = pd.concat([df, new_row], ignore_index=True)\n", "        \n", "        return df\n", "\n", "    except NoCredentialsError:\n", "        print(\"Cognito credentials not available\")\n", "        return []\n", "    except PartialCredentialsError:\n", "        print(\"Incomplete Cognito credentials provided\")\n", "        return []\n", "    except Exception as e:\n", "        print(f\"An error occurred fetching Cognito users: {e}\")\n", "        return []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fetch chat history and user details"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully retrieved 5358 items from the table 'ChatStoreProd'\n"]}], "source": ["\n", "# Fetch items from DynamoDB\n", "items = fetch_dynamodb_items(TABLE_NAME, REGION)\n"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["# Track the number of distinct users per day\n", "dynamo_users_df = track_distinct_users_per_day(items)\n", "dynamo_users_df['Created Date'] = pd.to_datetime(dynamo_users_df['Created Date'])"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["# Fetch users from cognito\n", "cognito_users_df = fetch_cognito_users(COGNITO_USER_POOL_ID, REGION)\n", "cognito_users_df['Created Date'] = pd.to_datetime(cognito_users_df['Created Date'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate DAU"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "Date=%{x}<br>Unique User Count=%{y}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines+markers", "name": "", "orientation": "v", "showlegend": false, "type": "scatter", "x": ["2024-01-08T00:00:00", "2024-01-09T00:00:00", "2024-01-10T00:00:00", "2024-01-11T00:00:00", "2024-01-12T00:00:00", "2024-01-15T00:00:00", "2024-01-16T00:00:00", "2024-01-17T00:00:00", "2024-01-18T00:00:00", "2024-01-19T00:00:00", "2024-01-22T00:00:00", "2024-01-23T00:00:00", "2024-01-24T00:00:00", "2024-01-25T00:00:00", "2024-01-26T00:00:00", "2024-01-29T00:00:00", "2024-01-30T00:00:00", "2024-01-31T00:00:00", "2024-02-01T00:00:00", "2024-02-02T00:00:00", "2024-02-05T00:00:00", "2024-02-06T00:00:00", "2024-02-07T00:00:00", "2024-02-08T00:00:00", "2024-02-09T00:00:00", "2024-02-12T00:00:00", "2024-02-13T00:00:00", "2024-02-14T00:00:00", "2024-02-15T00:00:00", "2024-02-19T00:00:00", "2024-02-20T00:00:00", "2024-02-21T00:00:00", "2024-02-22T00:00:00", "2024-02-23T00:00:00", "2024-02-27T00:00:00", "2024-02-28T00:00:00", "2024-02-29T00:00:00", "2024-03-01T00:00:00", "2024-03-04T00:00:00", "2024-03-05T00:00:00", "2024-03-07T00:00:00", "2024-03-11T00:00:00", "2024-03-12T00:00:00", "2024-03-13T00:00:00", "2024-03-14T00:00:00", "2024-03-15T00:00:00", "2024-03-18T00:00:00", "2024-03-19T00:00:00", "2024-03-20T00:00:00", "2024-03-21T00:00:00", "2024-03-22T00:00:00", "2024-03-25T00:00:00", "2024-03-28T00:00:00", "2024-03-29T00:00:00", "2024-04-01T00:00:00", "2024-04-03T00:00:00", "2024-04-04T00:00:00", "2024-04-05T00:00:00", "2024-04-08T00:00:00", "2024-04-09T00:00:00", "2024-04-11T00:00:00", "2024-04-12T00:00:00", "2024-04-15T00:00:00", "2024-04-16T00:00:00", "2024-04-17T00:00:00", "2024-04-18T00:00:00", "2024-04-22T00:00:00", "2024-04-23T00:00:00", "2024-04-24T00:00:00", "2024-04-25T00:00:00", "2024-04-26T00:00:00", "2024-04-30T00:00:00", "2024-05-01T00:00:00", "2024-05-02T00:00:00", "2024-05-03T00:00:00", "2024-05-06T00:00:00", "2024-05-07T00:00:00", "2024-05-08T00:00:00", "2024-05-13T00:00:00", "2024-05-14T00:00:00", "2024-05-15T00:00:00", "2024-05-16T00:00:00", "2024-05-17T00:00:00", "2024-05-21T00:00:00", "2024-05-22T00:00:00", "2024-05-24T00:00:00", "2024-05-27T00:00:00", "2024-05-28T00:00:00", "2024-05-29T00:00:00", "2024-05-30T00:00:00", "2024-05-31T00:00:00", "2024-06-03T00:00:00", "2024-06-04T00:00:00", "2024-06-05T00:00:00", "2024-06-06T00:00:00", "2024-06-07T00:00:00", "2024-06-10T00:00:00", "2024-06-11T00:00:00", "2024-06-12T00:00:00", "2024-06-13T00:00:00", "2024-06-14T00:00:00", "2024-06-17T00:00:00", "2024-06-18T00:00:00", "2024-06-19T00:00:00", "2024-06-20T00:00:00", "2024-06-21T00:00:00", "2024-06-24T00:00:00", "2024-06-25T00:00:00", "2024-06-26T00:00:00", "2024-06-27T00:00:00", "2024-06-28T00:00:00", "2024-07-01T00:00:00", "2024-07-02T00:00:00", "2024-07-03T00:00:00", "2024-07-04T00:00:00", "2024-07-05T00:00:00", "2024-07-08T00:00:00", "2024-07-09T00:00:00", "2024-07-10T00:00:00", "2024-07-11T00:00:00", "2024-07-12T00:00:00", "2024-07-15T00:00:00", "2024-07-16T00:00:00", "2024-07-17T00:00:00", "2024-07-18T00:00:00", "2024-07-19T00:00:00", "2024-07-22T00:00:00", "2024-07-23T00:00:00", "2024-07-24T00:00:00", "2024-07-25T00:00:00", "2024-07-26T00:00:00", "2024-07-29T00:00:00", "2024-07-30T00:00:00", "2024-07-31T00:00:00", "2024-08-01T00:00:00", "2024-08-02T00:00:00", "2024-08-05T00:00:00", "2024-08-06T00:00:00", "2024-08-07T00:00:00", "2024-08-08T00:00:00", "2024-08-09T00:00:00", "2024-08-12T00:00:00", "2024-08-13T00:00:00", "2024-08-14T00:00:00", "2024-08-15T00:00:00", "2024-08-16T00:00:00", "2024-08-19T00:00:00", "2024-08-20T00:00:00", "2024-08-21T00:00:00", "2024-08-22T00:00:00", "2024-08-23T00:00:00", "2024-08-26T00:00:00", "2024-08-27T00:00:00", "2024-08-28T00:00:00", "2024-08-29T00:00:00", "2024-08-30T00:00:00", "2024-09-02T00:00:00", "2024-09-03T00:00:00", "2024-09-04T00:00:00", "2024-09-05T00:00:00", "2024-09-06T00:00:00", "2024-09-09T00:00:00", "2024-09-10T00:00:00", "2024-09-11T00:00:00", "2024-09-12T00:00:00", "2024-09-13T00:00:00", "2024-09-16T00:00:00", "2024-09-17T00:00:00", "2024-09-18T00:00:00", "2024-09-19T00:00:00", "2024-09-20T00:00:00", "2024-09-23T00:00:00", "2024-09-24T00:00:00", "2024-09-25T00:00:00", "2024-09-26T00:00:00", "2024-09-27T00:00:00", "2024-09-30T00:00:00", "2024-10-01T00:00:00", "2024-10-02T00:00:00", "2024-10-03T00:00:00", "2024-10-04T00:00:00", "2024-10-07T00:00:00", "2024-10-08T00:00:00", "2024-10-09T00:00:00", "2024-10-10T00:00:00", "2024-10-11T00:00:00", "2024-10-14T00:00:00", "2024-10-15T00:00:00", "2024-10-16T00:00:00", "2024-10-17T00:00:00", "2024-10-18T00:00:00", "2024-10-21T00:00:00", "2024-10-22T00:00:00", "2024-10-23T00:00:00", "2024-10-24T00:00:00", "2024-10-25T00:00:00", "2024-10-28T00:00:00", "2024-10-29T00:00:00", "2024-10-30T00:00:00"], "xaxis": "x", "y": [2, 5, 6, 7, 5, 4, 5, 7, 9, 6, 4, 7, 4, 8, 3, 3, 3, 4, 3, 3, 4, 3, 3, 3, 1, 3, 1, 3, 2, 5, 3, 2, 4, 3, 3, 4, 7, 2, 2, 1, 1, 2, 2, 1, 1, 1, 3, 4, 3, 1, 1, 1, 2, 2, 1, 2, 4, 11, 3, 5, 1, 2, 1, 1, 2, 2, 3, 4, 2, 3, 3, 3, 1, 2, 3, 1, 1, 5, 1, 1, 3, 2, 2, 2, 2, 12, 1, 3, 4, 3, 21, 7, 6, 1, 3, 4, 10, 9, 13, 28, 11, 9, 8, 11, 6, 9, 8, 6, 8, 22, 12, 14, 22, 34, 9, 17, 19, 19, 15, 28, 21, 22, 29, 34, 14, 21, 10, 20, 10, 19, 11, 11, 15, 20, 14, 14, 9, 11, 15, 15, 16, 9, 14, 9, 13, 17, 6, 11, 13, 10, 11, 5, 13, 11, 8, 8, 4, 16, 29, 17, 17, 21, 26, 38, 28, 32, 39, 32, 40, 41, 27, 29, 27, 30, 54, 36, 41, 25, 41, 53, 44, 48, 43, 49, 50, 42, 37, 53, 51, 84, 68, 63, 52, 72, 51, 56, 65, 78, 5], "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "DAU"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "Date"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Unique User Count"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Exclude Weekends (Saturday=5, Sunday=6)\n", "dynamo_users_df_sans_weekends = dynamo_users_df[dynamo_users_df['Created Date'].dt.weekday < 5].copy()\n", "\n", "# Group by date and count unique IDs per day\n", "daily_counts = dynamo_users_df_sans_weekends.groupby('Created Date')['User Id'].nunique().reset_index()\n", "daily_counts.columns = ['date', 'unique_id_count']\n", "\n", "# Plot with Plotly\n", "fig = px.line(daily_counts, x='date', y='unique_id_count', markers=True,\n", "                labels={'date': 'Date', 'unique_id_count': 'Unique User Count'},\n", "                title='DAU')\n", "\n", "# Show the plot\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate DAU excluding signups"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "Week=%{x}<br>Unique User Count=%{y}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines+markers", "name": "", "orientation": "v", "showlegend": false, "type": "scatter", "x": ["2024-01-08T00:00:00", "2024-01-09T00:00:00", "2024-01-10T00:00:00", "2024-01-11T00:00:00", "2024-01-12T00:00:00", "2024-01-15T00:00:00", "2024-01-16T00:00:00", "2024-01-17T00:00:00", "2024-01-18T00:00:00", "2024-01-19T00:00:00", "2024-01-22T00:00:00", "2024-01-23T00:00:00", "2024-01-24T00:00:00", "2024-01-25T00:00:00", "2024-01-26T00:00:00", "2024-01-29T00:00:00", "2024-01-30T00:00:00", "2024-01-31T00:00:00", "2024-02-01T00:00:00", "2024-02-02T00:00:00", "2024-02-05T00:00:00", "2024-02-06T00:00:00", "2024-02-07T00:00:00", "2024-02-08T00:00:00", "2024-02-09T00:00:00", "2024-02-12T00:00:00", "2024-02-13T00:00:00", "2024-02-14T00:00:00", "2024-02-15T00:00:00", "2024-02-19T00:00:00", "2024-02-20T00:00:00", "2024-02-21T00:00:00", "2024-02-22T00:00:00", "2024-02-23T00:00:00", "2024-02-27T00:00:00", "2024-02-28T00:00:00", "2024-02-29T00:00:00", "2024-03-01T00:00:00", "2024-03-04T00:00:00", "2024-03-05T00:00:00", "2024-03-07T00:00:00", "2024-03-11T00:00:00", "2024-03-12T00:00:00", "2024-03-13T00:00:00", "2024-03-14T00:00:00", "2024-03-15T00:00:00", "2024-03-18T00:00:00", "2024-03-19T00:00:00", "2024-03-20T00:00:00", "2024-03-21T00:00:00", "2024-03-22T00:00:00", "2024-03-25T00:00:00", "2024-03-28T00:00:00", "2024-03-29T00:00:00", "2024-04-01T00:00:00", "2024-04-03T00:00:00", "2024-04-04T00:00:00", "2024-04-05T00:00:00", "2024-04-08T00:00:00", "2024-04-09T00:00:00", "2024-04-11T00:00:00", "2024-04-12T00:00:00", "2024-04-15T00:00:00", "2024-04-16T00:00:00", "2024-04-17T00:00:00", "2024-04-18T00:00:00", "2024-04-22T00:00:00", "2024-04-23T00:00:00", "2024-04-24T00:00:00", "2024-04-25T00:00:00", "2024-04-26T00:00:00", "2024-04-30T00:00:00", "2024-05-01T00:00:00", "2024-05-02T00:00:00", "2024-05-03T00:00:00", "2024-05-06T00:00:00", "2024-05-07T00:00:00", "2024-05-08T00:00:00", "2024-05-13T00:00:00", "2024-05-14T00:00:00", "2024-05-15T00:00:00", "2024-05-16T00:00:00", "2024-05-17T00:00:00", "2024-05-21T00:00:00", "2024-05-22T00:00:00", "2024-05-24T00:00:00", "2024-05-27T00:00:00", "2024-05-28T00:00:00", "2024-05-29T00:00:00", "2024-05-30T00:00:00", "2024-05-31T00:00:00", "2024-06-03T00:00:00", "2024-06-04T00:00:00", "2024-06-05T00:00:00", "2024-06-06T00:00:00", "2024-06-07T00:00:00", "2024-06-10T00:00:00", "2024-06-11T00:00:00", "2024-06-12T00:00:00", "2024-06-13T00:00:00", "2024-06-14T00:00:00", "2024-06-17T00:00:00", "2024-06-18T00:00:00", "2024-06-19T00:00:00", "2024-06-20T00:00:00", "2024-06-21T00:00:00", "2024-06-24T00:00:00", "2024-06-25T00:00:00", "2024-06-26T00:00:00", "2024-06-27T00:00:00", "2024-06-28T00:00:00", "2024-07-01T00:00:00", "2024-07-02T00:00:00", "2024-07-03T00:00:00", "2024-07-04T00:00:00", "2024-07-05T00:00:00", "2024-07-08T00:00:00", "2024-07-09T00:00:00", "2024-07-10T00:00:00", "2024-07-11T00:00:00", "2024-07-12T00:00:00", "2024-07-15T00:00:00", "2024-07-16T00:00:00", "2024-07-17T00:00:00", "2024-07-18T00:00:00", "2024-07-19T00:00:00", "2024-07-22T00:00:00", "2024-07-23T00:00:00", "2024-07-24T00:00:00", "2024-07-25T00:00:00", "2024-07-26T00:00:00", "2024-07-29T00:00:00", "2024-07-30T00:00:00", "2024-07-31T00:00:00", "2024-08-01T00:00:00", "2024-08-02T00:00:00", "2024-08-05T00:00:00", "2024-08-06T00:00:00", "2024-08-07T00:00:00", "2024-08-08T00:00:00", "2024-08-09T00:00:00", "2024-08-12T00:00:00", "2024-08-13T00:00:00", "2024-08-14T00:00:00", "2024-08-15T00:00:00", "2024-08-16T00:00:00", "2024-08-19T00:00:00", "2024-08-20T00:00:00", "2024-08-21T00:00:00", "2024-08-22T00:00:00", "2024-08-23T00:00:00", "2024-08-26T00:00:00", "2024-08-27T00:00:00", "2024-08-28T00:00:00", "2024-08-29T00:00:00", "2024-08-30T00:00:00", "2024-09-02T00:00:00", "2024-09-03T00:00:00", "2024-09-04T00:00:00", "2024-09-05T00:00:00", "2024-09-06T00:00:00", "2024-09-09T00:00:00", "2024-09-10T00:00:00", "2024-09-11T00:00:00", "2024-09-12T00:00:00", "2024-09-13T00:00:00", "2024-09-16T00:00:00", "2024-09-17T00:00:00", "2024-09-18T00:00:00", "2024-09-19T00:00:00", "2024-09-20T00:00:00", "2024-09-23T00:00:00", "2024-09-24T00:00:00", "2024-09-25T00:00:00", "2024-09-26T00:00:00", "2024-09-27T00:00:00", "2024-09-30T00:00:00", "2024-10-01T00:00:00", "2024-10-02T00:00:00", "2024-10-03T00:00:00", "2024-10-04T00:00:00", "2024-10-07T00:00:00", "2024-10-08T00:00:00", "2024-10-09T00:00:00", "2024-10-10T00:00:00", "2024-10-11T00:00:00", "2024-10-14T00:00:00", "2024-10-15T00:00:00", "2024-10-16T00:00:00", "2024-10-17T00:00:00", "2024-10-18T00:00:00", "2024-10-21T00:00:00", "2024-10-22T00:00:00", "2024-10-23T00:00:00", "2024-10-24T00:00:00", "2024-10-25T00:00:00", "2024-10-28T00:00:00", "2024-10-29T00:00:00", "2024-10-30T00:00:00"], "xaxis": "x", "y": [2, 3, 3, 5, 5, 4, 5, 7, 8, 6, 4, 6, 3, 6, 3, 3, 3, 4, 3, 2, 4, 3, 3, 3, 1, 3, 1, 3, 2, 4, 3, 2, 4, 3, 3, 3, 7, 2, 2, 1, 1, 2, 2, 1, 1, 1, 3, 4, 3, 1, 1, 1, 2, 2, 1, 2, 4, 2, 3, 1, 1, 2, 1, 1, 2, 2, 3, 4, 2, 3, 3, 3, 1, 2, 3, 1, 1, 5, 1, 1, 3, 2, 2, 2, 2, 9, 1, 3, 4, 3, 6, 7, 6, 1, 3, 4, 6, 6, 6, 5, 8, 8, 6, 7, 5, 7, 7, 4, 6, 9, 5, 6, 7, 11, 2, 11, 11, 13, 10, 15, 19, 7, 15, 22, 12, 14, 9, 16, 7, 11, 11, 8, 10, 16, 12, 13, 8, 9, 11, 14, 12, 9, 13, 8, 10, 13, 5, 9, 10, 9, 8, 5, 11, 8, 6, 7, 2, 10, 13, 11, 12, 16, 15, 19, 15, 20, 20, 17, 25, 25, 18, 23, 21, 19, 22, 21, 32, 19, 25, 32, 19, 29, 31, 29, 37, 29, 26, 36, 39, 36, 37, 46, 36, 42, 43, 33, 46, 47, 1], "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "DAU Excluding Signups"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "Week"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Unique User Count"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Exclude Weekends (Saturday=5, Sunday=6)\n", "dynamo_users_df_sans_weekends = dynamo_users_df[dynamo_users_df['Created Date'].dt.weekday < 5].copy()\n", "cognito_users_df_sans_weekends = cognito_users_df[cognito_users_df['Created Date'].dt.weekday < 5].copy()\n", "\n", "dynamo_users_df_sans_weekends['Created Date'] = pd.to_datetime(dynamo_users_df_sans_weekends['Created Date'])\n", "cognito_users_df_sans_weekends['Created Date'] = pd.to_datetime(cognito_users_df_sans_weekends['Created Date'])\n", "\n", "# Identify overlapping rows (id, date) that are in both dynamo_users_df_sans_weekends and cognito_users_df_sans_weekends\n", "overlaps = pd.merge(dynamo_users_df_sans_weekends, cognito_users_df_sans_weekends, on=['User Id', 'Created Date'], how='inner')\n", "\n", "# Exclude overlapping rows from dynamo_users_df_sans_weekends\n", "users_df_filtered = dynamo_users_df_sans_weekends[~dynamo_users_df_sans_weekends.set_index(['User Id', 'Created Date']).index.isin(overlaps.set_index(['User Id', 'Created Date']).index)]\n", "\n", "# Now proceed with weekly grouping\n", "users_df_filtered = users_df_filtered.set_index('Created Date')\n", "\n", "# Group by week and count unique IDs per week\n", "weekly_counts = users_df_filtered.groupby('Created Date')['User Id'].nunique().reset_index()\n", "weekly_counts.columns = ['week', 'unique_id_count']\n", "\n", "# Plot with Plotly\n", "fig = px.line(weekly_counts, x='week', y='unique_id_count', markers=True,\n", "                labels={'week': 'Week', 'unique_id_count': 'Unique User Count'},\n", "                title='DAU Excluding Signups')\n", "\n", "# Show the plot\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate WAU"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "Week=%{x}<br>Unique User Count=%{y}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines+markers", "name": "", "orientation": "v", "showlegend": false, "type": "scatter", "x": ["2024-01-14T00:00:00", "2024-01-21T00:00:00", "2024-01-28T00:00:00", "2024-02-04T00:00:00", "2024-02-11T00:00:00", "2024-02-18T00:00:00", "2024-02-25T00:00:00", "2024-03-03T00:00:00", "2024-03-10T00:00:00", "2024-03-17T00:00:00", "2024-03-24T00:00:00", "2024-03-31T00:00:00", "2024-04-07T00:00:00", "2024-04-14T00:00:00", "2024-04-21T00:00:00", "2024-04-28T00:00:00", "2024-05-05T00:00:00", "2024-05-12T00:00:00", "2024-05-19T00:00:00", "2024-05-26T00:00:00", "2024-06-02T00:00:00", "2024-06-09T00:00:00", "2024-06-16T00:00:00", "2024-06-23T00:00:00", "2024-06-30T00:00:00", "2024-07-07T00:00:00", "2024-07-14T00:00:00", "2024-07-21T00:00:00", "2024-07-28T00:00:00", "2024-08-04T00:00:00", "2024-08-11T00:00:00", "2024-08-18T00:00:00", "2024-08-25T00:00:00", "2024-09-01T00:00:00", "2024-09-08T00:00:00", "2024-09-15T00:00:00", "2024-09-22T00:00:00", "2024-09-29T00:00:00", "2024-10-06T00:00:00", "2024-10-13T00:00:00", "2024-10-20T00:00:00", "2024-10-27T00:00:00", "2024-11-03T00:00:00"], "xaxis": "x", "y": [11, 15, 14, 10, 7, 4, 8, 8, 3, 4, 6, 3, 15, 9, 5, 8, 7, 5, 5, 14, 25, 11, 54, 30, 41, 73, 68, 84, 43, 48, 44, 40, 31, 28, 55, 98, 120, 123, 142, 149, 210, 197, 126], "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "WAU"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "Week"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Unique User Count"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Exclude Weekends (Saturday=5, Sunday=6)\n", "dynamo_users_df_sans_weekends = dynamo_users_df[dynamo_users_df['Created Date'].dt.weekday < 5].copy()\n", "\n", "# Set date as index for weekly grouping\n", "df = dynamo_users_df_sans_weekends.set_index('Created Date')\n", "\n", "# Group by week and count unique IDs per week\n", "weekly_counts = df['User Id'].resample('W').nunique().reset_index()\n", "weekly_counts.columns = ['week', 'unique_id_count']\n", "\n", "# Plot with Plotly\n", "fig = px.line(weekly_counts, x='week', y='unique_id_count', markers=True,\n", "                labels={'week': 'Week', 'unique_id_count': 'Unique User Count'},\n", "                title='WAU')\n", "\n", "# Show the plot\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate WAU excluding signups"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "Week=%{x}<br>Unique User Count=%{y}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines+markers", "name": "", "orientation": "v", "showlegend": false, "type": "scatter", "x": ["2024-01-14T00:00:00", "2024-01-21T00:00:00", "2024-01-28T00:00:00", "2024-02-04T00:00:00", "2024-02-11T00:00:00", "2024-02-18T00:00:00", "2024-02-25T00:00:00", "2024-03-03T00:00:00", "2024-03-10T00:00:00", "2024-03-17T00:00:00", "2024-03-24T00:00:00", "2024-03-31T00:00:00", "2024-04-07T00:00:00", "2024-04-14T00:00:00", "2024-04-21T00:00:00", "2024-04-28T00:00:00", "2024-05-05T00:00:00", "2024-05-12T00:00:00", "2024-05-19T00:00:00", "2024-05-26T00:00:00", "2024-06-02T00:00:00", "2024-06-09T00:00:00", "2024-06-16T00:00:00", "2024-06-23T00:00:00", "2024-06-30T00:00:00", "2024-07-07T00:00:00", "2024-07-14T00:00:00", "2024-07-21T00:00:00", "2024-07-28T00:00:00", "2024-08-04T00:00:00", "2024-08-11T00:00:00", "2024-08-18T00:00:00", "2024-08-25T00:00:00", "2024-09-01T00:00:00", "2024-09-08T00:00:00", "2024-09-15T00:00:00", "2024-09-22T00:00:00", "2024-09-29T00:00:00", "2024-10-06T00:00:00", "2024-10-13T00:00:00", "2024-10-20T00:00:00", "2024-10-27T00:00:00", "2024-11-03T00:00:00"], "xaxis": "x", "y": [8, 14, 10, 9, 7, 4, 8, 8, 3, 4, 6, 3, 6, 6, 5, 8, 7, 5, 5, 11, 10, 11, 22, 22, 19, 31, 42, 41, 30, 37, 35, 31, 23, 21, 29, 48, 56, 64, 79, 77, 106, 122, 73], "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "WAU Excluding Signups"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "Week"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Unique User Count"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Exclude Weekends (Saturday=5, Sunday=6)\n", "dynamo_users_df_sans_weekends = dynamo_users_df[dynamo_users_df['Created Date'].dt.weekday < 5].copy()\n", "cognito_users_df_sans_weekends = cognito_users_df[cognito_users_df['Created Date'].dt.weekday < 5].copy()\n", "\n", "# Identify overlapping rows (id, date) that are in both dynamo_users_df_sans_weekends and cognito_users_df_sans_weekends\n", "overlaps = pd.merge(dynamo_users_df_sans_weekends, cognito_users_df_sans_weekends, on=['User Id', 'Created Date'], how='inner')\n", "\n", "# Exclude overlapping rows from dynamo_users_df_sans_weekends\n", "users_df_filtered = dynamo_users_df_sans_weekends[~dynamo_users_df_sans_weekends.set_index(['User Id', 'Created Date']).index.isin(overlaps.set_index(['User Id', 'Created Date']).index)]\n", "\n", "# Now proceed with weekly grouping\n", "users_df_filtered = users_df_filtered.set_index('Created Date')\n", "\n", "# Group by week and count unique IDs per week\n", "weekly_counts = users_df_filtered['User Id'].resample('W').nunique().reset_index()\n", "weekly_counts.columns = ['week', 'unique_id_count']\n", "\n", "# Plot with Plotly\n", "fig = px.line(weekly_counts, x='week', y='unique_id_count', markers=True,\n", "                labels={'week': 'Week', 'unique_id_count': 'Unique User Count'},\n", "                title='WAU Excluding Signups')\n", "\n", "# Show the plot\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}