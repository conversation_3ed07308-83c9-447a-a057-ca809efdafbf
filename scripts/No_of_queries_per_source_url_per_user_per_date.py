import datetime
import os
from collections import defaultdict

import pandas as pd
import plotly.express as px
from langsmith import Client
from tqdm import tqdm

from common import google_sheets_utils


def fetch_runs(client, days=5):
    """
    Fetches LangSmith runs for the last `days` days.

    Args:
        client (Client): An instance of the LangSmith Client.
        days (int, optional): Number of days to look back. Defaults to 5.

    Returns:
        list: A list of runs.
    """
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


def categorize_runs_by_date_user(client, runs, users_by_source):
    """
    Categorizes runs by date, user, and source URL.

    Args:
        client (Client): An instance of the LangSmith Client.
        runs (list): A list of runs to process.
        users_by_source (defaultdict): A dictionary categorizing users by source URL.

    Returns:
        pd.DataFrame: A DataFrame containing categorized run data.
    """
    data = {"Date": [], "User": [], "Source URL": [], "No of Queries": []}
    for run in tqdm(runs, desc="Processing Runs"):
        try:
            trace = client.read_run(run.trace_id)
            tags = set(trace.tags)
            # Extract user by removing known tags
            user_tags = tags - {"openai-functions", "desktop", "mobile"}
            if not user_tags:
                continue  # Skip if no user tag is found
            user = user_tags.pop()
            date = run.start_time.date()

            # Find the user's source URL from the sheet data
            for source, users in users_by_source.items():
                if user in users:
                    data["Date"].append(date)
                    data["User"].append(user)
                    data["Source URL"].append(source)
                    data["No of Queries"].append(1)
                    break
        except Exception as e:
            print(f"Failed to process run {run.trace_id}: {e}")
    return pd.DataFrame(data)


def main():
    # Initialize the LangSmith client with environment variables
    client = Client(
        api_key=os.getenv("LANGCHAIN_API_KEY"),
    )

    # Load data from Google Sheets
    GSHEETS_UTILS = google_sheets_utils.GoogleSheetsUtils()
    try:
        prod_signup_tracker = GSHEETS_UTILS.load_sheet_into_dataframe(
            spreadsheet_id="1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4",
            sheet_name="Prod Sign Up Tracker",
        )
    except Exception as e:
        print(f"Error loading Google Sheets data: {e}")
        return

    # Ensure 'Source URL' column does not contain None or NaN
    prod_signup_tracker = prod_signup_tracker[
        pd.notna(prod_signup_tracker["Source URL"])
    ]

    # Apply the filters to remove unwanted URLs
    unwanted_patterns = ["localhost", "-dev", "8080"]
    pattern = "|".join(unwanted_patterns)
    prod_signup_tracker = prod_signup_tracker[
        ~prod_signup_tracker["Source URL"].str.contains(pattern)
    ]

    # Define substrings to search for
    substrings = [
        "about/mc-lookup",
        "about/carrier-search",
        "about/reefer-rates",
        "about/dot-lookup",
        "about/dry-van-rates",
        "freight-trucking-rates",
        "?gad_source",  # match any query parameter with gad_source
    ]

    # Initialize a dictionary to hold users categorized by Source URL
    users_by_source = defaultdict(list)

    # Iterate through the DataFrame and categorize users by Source URL
    for _, row in tqdm(
        prod_signup_tracker.iterrows(),
        total=prod_signup_tracker.shape[0],
        desc="Processing Rows",
    ):
        source_url = row["Source URL"]
        user_name = row["Name"]
        matched = False
        for substring in substrings:
            if substring in source_url:
                users_by_source[substring].append(user_name)
                matched = True
                break
        if not matched:
            users_by_source["direct_nav"].append(user_name)

    # Fetch runs and categorize
    runs = fetch_runs(client, days=1)
    if not runs:
        print("No runs fetched. Exiting.")
        return

    queries_by_date_user = categorize_runs_by_date_user(client, runs, users_by_source)

    if queries_by_date_user.empty:
        print("No query data to aggregate. Exiting.")
        return

    # Aggregate the data by counting the number of queries per user per date per source URL
    aggregated_data = (
        queries_by_date_user.groupby(["Source URL", "User", "Date"])
        .size()
        .reset_index(name="No of Queries")
    )

    # Determine the number of unique users to adjust the plot height accordingly
    num_users = aggregated_data["User"].nunique()
    base_height = 400  # Base height for the plot
    additional_height_per_user = 20  # Additional height per facet row
    fig_height = base_height + (additional_height_per_user * num_users)

    # Create the bar chart with adjusted facet_row_spacing and height
    fig = px.bar(
        aggregated_data,
        x="Date",
        y="No of Queries",
        color="Source URL",
        facet_row="User",
        title="Number of Queries per Source URL per User per Date",
        height=fig_height,
        facet_row_spacing=0.005,  # Adjusted spacing
    )

    # Show the plot
    fig.show()


if __name__ == "__main__":
    main()
