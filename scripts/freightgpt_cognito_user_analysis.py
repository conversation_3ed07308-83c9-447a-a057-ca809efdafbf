import boto3
import matplotlib.pyplot as plt
import pandas as pd
from botocore.exceptions import NoCredentialsError


def fetch_cognito_users(user_pool_id):
    try:
        # Initialize a Cognito Identity Provider client
        client = boto3.client("cognito-idp")

        # List to hold all users
        users = []

        # Pagination parameters
        pagination_token = None

        while True:
            if pagination_token:
                response = client.list_users(
                    UserPoolId=user_pool_id, PaginationToken=pagination_token
                )
            else:
                response = client.list_users(UserPoolId=user_pool_id)

            # Extract users
            for user in response.get("Users", []):
                user_dict = {
                    "Email": None,
                    "Sign Up Date": user["UserCreateDate"].strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                }
                # Extract email from attributes
                for attr in user["Attributes"]:
                    if attr["Name"] == "name":
                        user_dict["Name"] = attr["Value"]
                    if attr["Name"] == "email":
                        user_dict["Email"] = attr["Value"]
                users.append(user_dict)

            # Check for pagination token
            pagination_token = response.get("PaginationToken")
            if not pagination_token:
                break

        return users
    except NoCredentialsError:
        print("Credentials not available")
        return []
    except Exception as e:
        print(f"An error occurred: {e}")
        return []


def save_to_csv(users, filename):
    df = pd.DataFrame(users)
    df.to_csv(filename, index=False)
    print(f"Data written to {filename}")


def load_data(filepath):
    """Load data from a CSV file."""
    return pd.read_csv(filepath)


def filter_users(data, keywords):
    """Filter out users whose names contain any of the specified keywords."""
    pattern = "|".join(keywords)
    return data[~data["Name"].str.lower().str.contains(pattern)]


def remove_duplicates(data):
    """Remove duplicate users based on email, keeping the first occurrence."""
    return data.drop_duplicates(subset="Email", keep="first")


def plot_cumulative_count(data):
    """Plot the cumulative count of users over time."""
    data["Sign Up Date"] = pd.to_datetime(data["Sign Up Date"])
    data_sorted = data.sort_values("Sign Up Date")
    data_sorted["Cumulative Users"] = data_sorted["Sign Up Date"].expanding().count()

    plt.figure(figsize=(10, 5))
    plt.plot(
        data_sorted["Sign Up Date"],
        data_sorted["Cumulative Users"],
        marker="o",
        linestyle="-",
    )
    plt.title("Cumulative User Count Over Time")
    plt.xlabel("Date")
    plt.ylabel("Cumulative Users")
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()


def main():
    # Define the file path and keywords to filter
    filepath = "cognito_users.csv"
    keywords = ["jr", "ruturaj", "roop", "pie9361", "viv", "joey", "ashish"]

    # Load the data
    data = load_data(filepath)

    # Filter users
    filtered_data = filter_users(data, keywords)

    # Remove duplicates
    clean_data = remove_duplicates(filtered_data)

    # Plot the cumulative user count
    plot_cumulative_count(clean_data)


if __name__ == "__main__":
    # Use your Cognito User Pool ID here
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    if users:
        save_to_csv(users, "cognito_users.csv")
    main()
