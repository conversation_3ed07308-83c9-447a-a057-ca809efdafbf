import datetime
import json  # For saving to JSON
import os
from collections import defaultdict

import pytz  # For timezone conversion
import tqdm
from freightgpt_cognito_user_analysis import fetch_cognito_users
from freightgpt_cognito_user_analysis import load_data
from freightgpt_cognito_user_analysis import save_to_csv
from langsmith import Client

from common import init_main

PROJECT_ROOT = init_main.initialize()


# Initialize the client with environment variables
client = Client(api_key=os.getenv("LANGCHAIN_API_KEY"))


# Fetch runs for the last N days
def fetch_runs(days):
    start_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
        days=days
    )
    runs = list(
        client.list_runs(
            project_name="freightgpt-prod",
            is_root=True,
            start_time=start_time,
            filter="and(eq(name, 'AgentExecutor'))",
        )
    )
    return runs


# Convert UTC time to Pacific time and filter based on midnight to 7 AM PST
def is_active_between_hours(run, pacific_start_hour=0, pacific_end_hour=7):
    pacific_tz = pytz.timezone("America/Los_Angeles")
    run_time_pacific = run.start_time.astimezone(pacific_tz)
    return pacific_start_hour <= run_time_pacific.hour < pacific_end_hour


# Categorize runs by user and date
def categorize_runs(runs, user_email_map, deny_list=None, extraneous_tags=None):
    if deny_list is None:
        deny_list = {
            "Viv Kumar",
            "Joe Petrovich",
            "Roop Pal",
            "Joey Kim",
            "public",
            "Ho Jun Yi",
            "Jr",
            "Jr Martinez",
            "Joseph Petrovich",
            "Vivek Kumar",
            "Ashish Kumar Singh",
        }
    if extraneous_tags is None:
        extraneous_tags = {"openai-functions", "desktop", "mobile"}

    runs_by_user = defaultdict(list)  # Store details of user activity

    for run in tqdm.tqdm(runs):
        try:
            tags = set(client.read_run(run.trace_id).tags)
            if not tags or deny_list & tags:
                continue
            user = (tags - extraneous_tags).pop()

            if user in user_email_map:
                run_time = run.start_time.date()  # Store the date of the run
                runs_by_user[user].append(run_time)
        except Exception as e:
            print(f"Failed to process run: {e}")

    return runs_by_user


# Find users who were inactive after signup date
def find_inactive_users_after_signup(users_df, runs_by_user):
    inactive_users = []

    for index, user in users_df.iterrows():  # Iterating over DataFrame rows
        try:
            signup_date_str = user["Sign Up Date"]  # Accessing 'Sign Up Date' column
            signup_date = datetime.datetime.strptime(
                signup_date_str, "%Y-%m-%d %H:%M:%S"
            ).date()  # Parse signup date
            username = user["Name"]
            email = user["Email"]

            # Check if the user has any activity after their signup date
            if username in runs_by_user:
                post_signup_activity = any(
                    date > signup_date for date in runs_by_user[username]
                )
                if not post_signup_activity:
                    inactive_users.append({"name": username, "email": email})
            else:
                # No activity at all
                inactive_users.append({"name": username, "email": email})

        except KeyError as e:
            print(f"KeyError: {e}, check if the column names are correct!")
        except Exception as e:
            print(f"Error processing user {user}: {e}")

    return inactive_users


# Main execution function
def main():
    # Fetch Cognito users
    user_pool_id = "us-east-1_azsO85PeM"
    users = fetch_cognito_users(user_pool_id)
    save_to_csv(users, "cognito_users.csv")

    # Load Cognito data
    data = load_data("cognito_users.csv")
    user_email_map = dict(zip(data["Name"], data["Email"]))  # Map user names to emails

    # Fetch runs for the last month (30 days)
    runs = fetch_runs(2)

    # Categorize runs by user and date
    runs_by_user = categorize_runs(runs, user_email_map)

    # Find inactive users after their signup date
    inactive_users = find_inactive_users_after_signup(data, runs_by_user)

    # Save inactive user details to JSON
    output_data = {
        "total_inactive_users_after_signup": len(inactive_users),
        "inactive_users_after_signup": inactive_users,
    }

    with open("inactive_users_after_signup.json", "w") as json_file:
        json.dump(output_data, json_file, indent=4)

    print(f"Total inactive users after signup: {len(inactive_users)}")
    print("Output saved to inactive_users_after_signup.json")


if __name__ == "__main__":
    main()
