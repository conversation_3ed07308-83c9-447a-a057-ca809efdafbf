from setuptools import find_packages
from setuptools import setup

setup(
    name="common",
    packages=find_packages(exclude=["tests"]),
    package_data={
        "common": [
            "credentials",
            "s3.py",
            "query_utils.py",
            "google_sheets.py",
            "dynamodb_utils.py",
            "polyline.py",
            "cloudwatch_log_util.py",
            "ecs_task_util.py",
            "opensearch_utils.py",
        ]
    },
    version="2024.8.0",
    description="Truce Common Packages",
    author="Truce Tech, Inc.",
    setup_requires=["pytest-runner"],
    tests_require=["pytest==7.1.2"],
    test_suite="tests",
)
