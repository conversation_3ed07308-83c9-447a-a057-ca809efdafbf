import copy

from common import google_sheets_utils


def error_exception(response_template, e, status_code):
    response = copy.deepcopy(response_template)
    response["statusCode"] = status_code
    response["body"] = str(e)
    return response


def lambda_handler(event: dict, context) -> dict:
    """The "main" function of the lambda. This is where the frontend query is handled.

    :param event: Frontend parameter with notifications query_params (dict) fields.
    :param context: Ignored for now.
    :return: Object with status code and notification data under ['body'].
    """
    print(event)
    email = event.get("email")
    gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
    required_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST,PUT,GET",
    }

    response = {
        "statusCode": 200,
        "headers": required_headers,
        "isvalid": False,
    }
    try:
        print("checking waitlist for ", email)
        is_present = gsheets_utils.search_sheet_for_value(
            email, "1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4", "Sheet1"
        )
    except Exception as e:
        print(error_exception(response, e, 404))
        return error_exception(response, e, 404)
    response["isvalid"] = is_present
    print(response)
    return response


def main():
    e = {"email": "<EMAIL>"}

    lambda_handler(e, None)


if __name__ == "__main__":
    main()
