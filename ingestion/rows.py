import datetime
import logging
import typing
from abc import ABC<PERSON>eta
from abc import abstractmethod

import gitinfo
import pandas as pd
from constants import BROKER_COLUMN_HANDLERS
from constants import COUNTRY_CODE_DICT
from constants import MAX_SHIPMENT_MARGIN
from constants import MAX_SHIPMENT_WEIGHT
from constants import MIN_SHIPMENT_WEIGHT
from constants import NA_STATES_PROVINCES
from constants import SHIPMENT_MODE_DICT
from constants import SHIPMENT_RANK_DICT
from constants import SHIPMENT_STATUS_DICT
from constants import ShipmentStatus
from constants import TIME_NORMALIZATION_EXEMPT_BROKERS
from rows_util import check_if_valid_timestamp
from rows_util import classify_shipment
from rows_util import compute_cogs_rev_err
from rows_util import detect_customer_direct
from rows_util import is_cogs_rev_na
from rows_util import normalize_times

from common import google_maps_utils
from common.constants import BrokerNames
from common.constants import EQUIPMENT_TYPE_DICT
from common.rds_api import RDS


class Row(metaclass=ABCMeta):
    def __init__(self, **kwargs):
        self.id = None
        self.key = ()
        self.values = {}
        self._validate(**kwargs)

    @abstractmethod
    def _validate(self, **kwargs):
        pass

    # Remove all NA values from dictionary.
    def to_dict(self):
        return {k: v for k, v in self.values.items() if not pd.isna(v)}


class CityRow(Row):
    # TODO(P1): Write unit tests for this.
    def _validate(self, city: str, state: str, country: str):
        self.city = pd.NA if pd.isna(city) or not city else city.title()
        if pd.isna(state):
            logging.error("State is missing.")
            raise ValueError("State is missing.")
        if pd.isna(city):
            logging.error("City is missing.")
            raise ValueError("City is missing.")
        if not isinstance(state, str):
            logging.error(f"State {state} is not a string.")
            raise TypeError("State is not a string.")
        if not isinstance(city, str):
            logging.error(f"City {city} is not a string.")
            raise TypeError("City is not a string.")
        if len(state) != 2:
            logging.error(f"Expected 2-character state/province code, given {state}.")
            raise ValueError("Expected 2-character state/province code.")
        self.state = state.upper()
        expected_country = NA_STATES_PROVINCES.df.loc[state].values[0]
        if pd.isna(country):
            self.country = expected_country
            logging.info(f"Country not given, using {self.country} from {self.state}.")
        elif not isinstance(country, str):
            logging.error(f"Country {country} is not a string.")
            raise TypeError("Country is not a string.")
        elif country.strip().lower() in COUNTRY_CODE_DICT:
            self.country = COUNTRY_CODE_DICT[country.strip().lower()]
            if self.country != expected_country:
                logging.error(
                    "Expected country {}, was given {}.".format(
                        expected_country, self.country
                    )
                )
                raise ValueError(
                    "Given country does not match expected country based on state/province code."
                )
        else:
            logging.error(f"Country code {country} is not in country code dict.")
            raise ValueError("Country code is not in country code dict.")
        if self.country not in ["US", "CA", "MX"]:
            logging.error(f"Country {self.country} is not of set US, CA, MX.")
            raise ValueError("Country is not of set US, CA, MX.")
        # TODO(P2): Add city, state, country verification based on zip (using google maps?).
        self.key = (self.city, self.state, self.country)
        self.values = {"city": self.city, "state": self.state, "country": self.country}


class ZipRow(Row):
    # TODO(P1): Write unit tests for this.
    def _validate(
        self,
        zipcode,
        country: str,
        city: str,
        state: str,
        return_dict: dict,
        rds: RDS,
        GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
    ):
        if pd.isna(zipcode) or not zipcode:
            zipcode = GMAPS_UTILS.get_zip_from_city_state(city, state, country)
            return_dict["derived_cols"].add(str(rds.zips.columns["zip"]))
        zip_len = 6 if country == "CA" else 5
        if type(zipcode) == int:
            self.zip = str(zipcode).replace(" ", "")
        else:
            if type(zipcode) != str:
                logging.error(
                    "Zipcode {} has type {}, which is not str or int".format(
                        zipcode, type(zipcode)
                    )
                )
                raise ValueError("Type of zipcode is not str or int.")
            self.zip = zipcode.replace(" ", "")

        if "-" in self.zip:
            if country != "US":
                logging.error(f"Zipcode {self.zip} has hyphen, but country is not US.")
                raise ValueError("Zipcode contains hyphen, but country is not US.")
            self.zip = self.zip.split("-")[0]

        # Consider plus4 for zip codes in the US.
        if country == "US" and len(self.zip) > 5:
            # Split on last 4 digits of string.
            self.zip = self.zip[:-4]

        if len(self.zip) < zip_len:
            logging.warning(
                "Zip {} does not have correct zip length of {} for {}".format(
                    self.zip, zip_len, country
                )
            )
            self.zip = "0" * (zip_len - len(self.zip)) + self.zip
        elif len(self.zip) > zip_len:
            logging.error(
                "Zip {} does not have correct zip length of {} for {}".format(
                    self.zip, zip_len, country
                )
            )
            raise ValueError("Zipcode does not have correct zip length.")
        # TODO(P2): Add city, state, country verification based on zip (using google maps?).
        self.key = self.zip
        self.values = {"zip": self.zip}


# Junction table
class CityZipRow(Row):
    def _validate(self, city_id: str, zip_id: str):
        self.key = (city_id, zip_id)
        self.values = {"cityId": city_id, "zipId": zip_id}


# Change lane row to identify based on origin city, origin state, origin country, destination city, destination state, destination country
class LaneRow(Row):
    def _validate(
        self,
        origin_city_id: str,
        destination_city_id: str,
        shipment_mode: str,
        equipment_type: str,
    ):
        if pd.isna(shipment_mode) or not shipment_mode:
            logging.warning("Shipment mode is empty.")
        elif shipment_mode.strip().lower() in SHIPMENT_MODE_DICT:
            shipment_mode = SHIPMENT_MODE_DICT[shipment_mode.strip().lower()]
        else:
            logging.error(f"Shipment mode {shipment_mode} is invalid.")
            raise ValueError("Shipment mode is invalid.")

        if pd.isna(equipment_type) or not equipment_type:
            self.equipment_type = pd.NA
            logging.warning("Equipment type is empty.")
        elif equipment_type.strip().lower() in EQUIPMENT_TYPE_DICT:
            self.equipment_type = EQUIPMENT_TYPE_DICT[equipment_type.strip().lower()]
        else:
            logging.error(f"Equipment type {equipment_type} is invalid.")
            raise ValueError("Equipment type is invalid.")

        self.key = (origin_city_id, destination_city_id)
        self.values = {
            "originCityId": origin_city_id,
            "destinationCityId": destination_city_id,
            "shipmentMode": shipment_mode,
            "equipmentType": self.equipment_type,
        }


class BrokerRow(Row):
    def _validate(self, name: str):
        self.key = (name,)
        self.name = name.strip().replace(",", "")
        self.values = {"name": self.name}


class ShipperRow(Row):
    def _validate(self, name: str, business_unit=None):
        self.key = (name, business_unit)
        self.values = {"name": name, "businessUnit": business_unit}


class ShipmentRow(Row):
    def _validate(
        self,
        broker_id: str,
        broker_name: str,
        lane_id: int,
        shipper_id: str,
        broker_primary_reference: int,
        shipper_primary_reference: int,
        stop_count: int,
        distance_miles: float,
        cogs_line_haul: float,
        cogs_fuel: float,
        cogs_accessorial: float,
        cogs_total: float,
        revenue_line_haul: float,
        revenue_fuel: float,
        revenue_accessorial: float,
        revenue_total: float,
        origin_warehouse: str,
        destination_warehouse: str,
        shipment_status: str,
        shipment_rank: str,
        weight: float,
        origin_open_time: datetime.datetime,
        origin_close_time: datetime.datetime,
        origin_arrival_time: datetime.datetime,
        origin_departure_time: datetime.datetime,
        origin_zip_id: str,
        destination_open_time: datetime.datetime,
        destination_close_time: datetime.datetime,
        destination_arrival_time: datetime.datetime,
        destination_departure_time: datetime.datetime,
        destination_zip_id: str,
        load_creation_time_utc: datetime.datetime,
        load_activation_time_utc: datetime.datetime,
        carrier_assigned_time_utc: datetime.datetime,
        origin_city: CityRow,
        destination_city: CityRow,
        origin_zip: ZipRow,
        destination_zip: ZipRow,
        custom_timezones: dict,
        return_dict: dict,
        rds: RDS,
        GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
    ):
        """Construct shipment row

        TODO(P1): Add all params to docstring
        :param custom_timezones: Dictionary of timezones to localize to normalize from specified timezone.
         Key is column name, value is pytz.timezone to normalize from.
        """
        # Distance miles
        if pd.isna(distance_miles) or distance_miles < 0:
            logging.warning(f"Distance {distance_miles} is empty or negative.")
            origin_str = f"{origin_city.city}, {origin_city.state} {origin_zip.zip}"
            destination_str = f"{destination_city.city}, {destination_city.state} {destination_zip.zip}"
            try:
                distance_miles = GMAPS_UTILS.get_distance_miles(
                    origin_str, destination_str
                )
            except Exception as e:
                logging.error(f"Error getting distance miles: {e}")
                distance_miles = pd.NA
            return_dict["derived_cols"].add(str(rds.shipments.columns["distanceMiles"]))

        # COGS
        cogs_error = compute_cogs_rev_err(
            cogs_line_haul, cogs_fuel, cogs_accessorial, cogs_total
        )
        cogs_na = is_cogs_rev_na(
            cogs_line_haul, cogs_fuel, cogs_accessorial, cogs_total
        )
        if not cogs_na and cogs_error >= 0.01:
            logging.error(
                "Cogs not adding up. {}: cogs_line_haul: {}, cogs_fuel: {}, cogs_accesorial: {}, cogs_total: {}".format(
                    shipper_primary_reference,
                    cogs_line_haul,
                    cogs_fuel,
                    cogs_accessorial,
                    cogs_total,
                )
            )
            raise ValueError("COGS not adding up.")

        # Revenue
        revenue_error = compute_cogs_rev_err(
            revenue_line_haul, revenue_fuel, revenue_accessorial, revenue_total
        )
        revenue_na = is_cogs_rev_na(
            revenue_line_haul, revenue_fuel, revenue_accessorial, revenue_total
        )
        if not revenue_na and revenue_error >= 0.01:
            logging.error(
                "Revenue not adding up. {}: revenue_line_haul: {}, revenue_fuel: {}, revenue_accessorial: {}, revenue_total: {}".format(
                    shipper_primary_reference,
                    revenue_line_haul,
                    revenue_fuel,
                    revenue_accessorial,
                    revenue_total,
                )
            )
            raise ValueError("Revenue not adding up.")

        # Margin
        if revenue_total - cogs_total > MAX_SHIPMENT_MARGIN:
            logging.error(
                "Shipment {} has a margin too large of {}.".format(
                    shipper_primary_reference, revenue_total - cogs_total
                )
            )
            raise ValueError("Shipment has a margin that is too large.")

        # Shipment class
        shipment_class = classify_shipment(
            cogs_line_haul,
            cogs_fuel,
            cogs_accessorial,
            cogs_total,
            revenue_line_haul,
            revenue_fuel,
            revenue_accessorial,
            revenue_total,
            distance_miles,
        )

        # Shipment status
        if pd.isna(shipment_status) or not shipment_status:
            logging.warning("Shipment status is empty.")
        elif shipment_status.strip().lower() in SHIPMENT_STATUS_DICT:
            shipment_status = SHIPMENT_STATUS_DICT[shipment_status.strip().lower()]
        else:
            logging.error(f"Shipment status {shipment_status} is invalid.")
            raise ValueError("Shipment status is invalid.")
        ingested_shipment_statuses = [
            ShipmentStatus.delivered,
            ShipmentStatus.in_transit,
            ShipmentStatus.unloading,
        ]
        if shipment_status not in ingested_shipment_statuses:
            logging.error(f"Shipment status {shipment_status} will not be ingested.")
            raise ValueError("Shipment status will not be ingested.")

        # Weight
        if not pd.isna(weight) and (
            weight < MIN_SHIPMENT_WEIGHT or weight > MAX_SHIPMENT_WEIGHT
        ):
            logging.warning(f"Weight {weight} is not in range.")
            weight = pd.NA

        # Shipment rank
        if pd.isna(shipment_rank) or not shipment_rank:
            logging.warning("Shipment rank is empty.")
        elif shipment_rank.strip().lower() in SHIPMENT_RANK_DICT:
            shipment_rank = SHIPMENT_RANK_DICT[shipment_rank.strip().lower()]
        else:
            logging.warning(f"Shipment rank {shipment_rank} is not ingested.")

        # Detect customer direct loads
        if pd.isna(destination_warehouse) or not destination_warehouse:
            logging.warning(
                "Null destination warehouse. Not computing customer-direct."
            )
            customer_direct = pd.NA
        else:
            customer_direct = detect_customer_direct(str(destination_warehouse))

        broker_column_dict = {
            "shipmentStatus": shipment_status,
            "originOpenTime": origin_open_time,
            "originCloseTime": origin_close_time,
            "originArrivalTime": origin_arrival_time,
            "originDepartureTime": origin_departure_time,
            "destinationOpenTime": destination_open_time,
            "destinationCloseTime": destination_close_time,
            "destinationArrivalTime": destination_arrival_time,
            "destinationDepartureTime": destination_departure_time,
            "loadCreationTime": load_creation_time_utc,
            "loadActivationTime": load_activation_time_utc,
            "carrierAssignedTime": carrier_assigned_time_utc,
        }

        # TODO: Recode BrokerColumnHandlers more generic
        if broker_name == BrokerNames.capstone_logistics and pd.isna(
            broker_column_dict["originCloseTime"]
        ):
            broker_column_dict["originCloseTime"] = broker_column_dict["originOpenTime"]

        if broker_name == BrokerNames.capstone_logistics and pd.isna(
            broker_column_dict["destinationCloseTime"]
        ):
            broker_column_dict["destinationCloseTime"] = broker_column_dict[
                "destinationOpenTime"
            ]

        broker_col_handlers = BROKER_COLUMN_HANDLERS.get(broker_name, {})
        # Handle Broker columns that need specific modification
        for col, handler in broker_col_handlers.items():
            if col in broker_column_dict:
                broker_column_dict[col] = handler(broker_column_dict[col])
            else:
                raise ValueError(
                    f"Invalid column '{col}' specified for broker '{broker_name}'."
                )

        broker_time_column_dict = {
            key: value for key, value in broker_column_dict.items() if "Time" in key
        }

        # Check if timestamps are valid.
        for time in broker_time_column_dict.keys():
            broker_time_column_dict[time] = check_if_valid_timestamp(
                broker_time_column_dict[time]
            )

        # Normalize timestamps
        if broker_col_handlers not in TIME_NORMALIZATION_EXEMPT_BROKERS:
            normalized_times = normalize_times(
                broker_time_column_dict,
                custom_timezones,
                origin_city,
                origin_zip,
                destination_city,
                destination_zip,
                GMAPS_UTILS,
            )

        broker_column_dict.update(normalized_times)

        self.key = (
            broker_id,
            shipper_id,
            broker_primary_reference,
            shipper_primary_reference,
        )
        self.values = {
            "brokerId": broker_id,
            "laneId": lane_id,
            "shipperId": shipper_id,
            "brokerPrimaryReference": broker_primary_reference,
            "shipperPrimaryReference": shipper_primary_reference,
            "stopCount": stop_count,
            "distanceMiles": distance_miles,
            "cogsLineHaul": cogs_line_haul,
            "cogsAccessorial": (
                cogs_accessorial if not pd.isna(cogs_accessorial) else pd.NA
            ),
            "cogsFuel": cogs_fuel,
            "cogsTotal": cogs_total,
            "revenueLineHaul": revenue_line_haul,
            "revenueFuel": revenue_fuel,
            "revenueAccessorial": (
                revenue_accessorial if not pd.isna(revenue_accessorial) else pd.NA
            ),
            "revenueTotal": revenue_total,
            "originWarehouse": origin_warehouse,
            "destinationWarehouse": destination_warehouse,
            "originZipId": origin_zip_id,
            "destinationZipId": destination_zip_id,
            "shipmentRank": shipment_rank,
            "weight": weight,
            "shipmentClass": shipment_class,
            "customerDirect": customer_direct,
        }

        for col_name, col_value in broker_column_dict.items():
            self.values[col_name] = col_value


class ShipmentMetadataRow(Row):
    def _validate(
        self,
        shipment_id: str,
        source_file: str,
        derived_cols: typing.List[str],
        shipment_diffs: dict,
    ):
        # The hash is searchable in GitHub to find code status.
        git_info = gitinfo.get_git_info()
        self.key = (shipment_id,)
        self.values = {
            "shipmentId": shipment_id,
            "sourceFile": source_file,
            "lastModifiedTime": datetime.datetime.utcnow().replace(microsecond=0),
            "derivedColumns": ",".join(derived_cols),
            "gitCommitHash": git_info["commit"],
            "shipmentDiffs": shipment_diffs,
            "gitAuthor": git_info["author"].split("<")[0].strip(),
        }
