import datetime
import logging
import typing

import numpy as np
import pandas as pd
import pytz

BUSINESS_DAY_HOURS = 10
# Datetime starts with MON = 0.
MON, TUE, WED, THU, FRI, SAT, SUN = range(7)
# All working days should be 10 hours.
# TODO(P2): Consider consolidating into 4 or 2 variables.
_CLT_WORKING_DAY_START_HOUR = 7
_CLT_WORKING_DAY_END_HOUR = 17
_BLT_WORKING_DAY_START_HOUR = 7
_BLT_WORKING_DAY_END_HOUR = 17
_PREBOOK_WORKING_DAY_START_HOUR = 7
_PREBOOK_WORKING_DAY_END_HOUR = 17


def sigmoid(x):
    # https://www.desmos.com/calculator/sestwriuji
    return 1 / (1 + np.exp(-x))


def linear_quadratic_piecewise(x, threshold, boundary=20 / 3, quadratic_slope=100):
    # https://www.desmos.com/calculator/xltl2xr3cl
    if x < boundary:
        return x**2 / quadratic_slope
    else:
        return 1 - (threshold - x) / threshold


def make_linear_fn(x_intercept: float, y_intercept: float = 1.0) -> float:
    """Return a linear function intercepting given intercepts."""
    assert x_intercept != 0
    return (
        lambda x: -y_intercept / x_intercept * x + y_intercept
        if not pd.isna(x)
        else pd.NA
    )


def bound_score(scr: float, min_score: float = 0.0, max_score: float = 1.0) -> float:
    """Bound score between min and max scores."""
    if pd.isna(scr):
        return pd.NA
    return max(min_score, min(scr, max_score))


def format_score(score: float) -> int:
    if pd.isna(score):
        return pd.NA
    return round(score * 100)


def weighted_score(scores: tuple, weights: tuple = None) -> float:
    """Calculated weighted scores.

    All scores are between 0 and 1. If they are NA, they are ignored.
    Weights cannot be NA.
    """
    # If weights are not provided, use equal weights.
    if weights is None:
        weights = (1,) * len(scores)
    assert len(scores) == len(weights)
    weighted_avg = 0.0
    denominator = 0.0
    for scr, weight in zip(scores, weights):
        if not pd.isna(scr):
            weighted_avg += weight * scr
            denominator += weight
    if denominator == 0.0:
        return pd.NA
    return weighted_avg / denominator


def parse_time_string(time_str: str) -> datetime.datetime:
    """Convert a time string from expected format to datetime object.

    :param time_str: String representation of time (ex: 2019-03-19T13:48:00.0).
    :return: datetime.datetime object.
    """
    if type(time_str) != str or not time_str:
        return pd.NaT
    try:
        return datetime.datetime.strptime(
            time_str.replace("T", " "), "%Y-%m-%d %H:%M:%S.%f"
        )
    except ValueError:
        # TODO(P0): Figure out how to parse both ways without try/except block.
        return datetime.datetime.strptime(
            time_str.replace("T", " "), "%Y-%m-%d %H:%M:%S"
        )


def localize_utc_to_central(dt: datetime.datetime) -> datetime.datetime:
    """Localize UTC timestmap to central time.

    :param datetime.datetime: UTC timestamp.
    :return: CDT or CST timestamp.
    """
    if pd.isna(dt):
        return pd.NA
    return (
        pytz.utc.localize(dt)
        .astimezone(pytz.timezone("US/Central"))
        .replace(tzinfo=None)
    )


# TODO(P0): Write unit tests for these functions.
def clip(
    day: datetime.datetime, working_day_start_hour: int, working_day_end_hour: int
) -> datetime.datetime:
    """Clip time to within working hours.

    Excepted to always have correct input (i.e, no NA inputs).

    :param day: Input datetime object, must be a weekday.
    :param working_day_start_hour: int representing Central Time hour when working day starts.
    :param working_day_end_hour: int representing Central Time hour when working day ends.
    :return: datetime object clipped to within working start and end hours.
    """
    hour = max(working_day_start_hour, min(day.hour, working_day_end_hour))

    # If hour is clipped, chop off rest of time.
    if hour != day.hour or (
        hour == working_day_end_hour
        and (day - datetime.timedelta(hours=day.hour)).time()
    ):
        return datetime.datetime(day.year, day.month, day.day, hour)
    return day


def working_hours_between(
    start: datetime.datetime,
    end: datetime.datetime,
    working_day_start_hour: int,
    working_day_end_hour: int,
) -> float:
    """Calculate working hours between start and end datetimes.

    This is done assuming a MON - FRI workweek, with a working day's start and end hours set as constants. weekday_start
    and weekday_end are calculated by finding the nearest weekday and working hours. Then, the number of days in between
    them are calculated and added to the number of work hours left in weekday_start and weekday_end.

    start timestamp must always be earlier than end timestamp.
    working_day_start_hour must always be earlier than working_day_end_hour.

    :param start: Start datetime object.
    :param end: End datetime object.
    :param working_day_start_hour: int representing Central Time hour when working day starts.
    :param working_day_end_hour: int representing Central Time hour when working day ends.
    :return: # of working hours between start and end, as a float.
    """
    if start > end:
        logging.error("Start time is after end time.")
        raise ValueError(f"Start {start} is not less than end {end}.")
    this_fri = start.date() + datetime.timedelta(FRI - start.weekday())
    end_of_workweek = datetime.datetime(
        this_fri.year, this_fri.month, this_fri.day, working_day_end_hour
    )
    # If the start date is after end of workweek, set to friday this week at 5pm.
    if start > end_of_workweek:
        weekday_start = end_of_workweek
    else:
        weekday_start = clip(start, working_day_start_hour, working_day_end_hour)

    this_fri = end.date() + datetime.timedelta(FRI - end.weekday())
    end_of_workweek = datetime.datetime(
        this_fri.year, this_fri.month, this_fri.day, working_day_end_hour
    )
    # If end date is before start of workweek, set to monday next week at 7am.
    if end > end_of_workweek:
        next_mon = end + datetime.timedelta(days=MON - end.weekday() + 7)
        weekday_end = datetime.datetime(
            next_mon.year, next_mon.month, next_mon.day, working_day_start_hour
        )
    else:
        weekday_end = clip(end, working_day_start_hour, working_day_end_hour)

    assert weekday_start <= weekday_end
    if weekday_start.date() == weekday_end.date():
        return (weekday_end - weekday_start).total_seconds() / 3600
    # Counts business days between weekday_start and weekday_end.
    working_days = (
        np.busday_count(
            weekday_start.strftime("%Y-%m-%d"), weekday_end.strftime("%Y-%m-%d")
        )
        - 1
    )
    # Calculate seconds between weekday_start and the end of the weekday_start work day.
    start_partial = (
        datetime.datetime(
            weekday_start.year,
            weekday_start.month,
            weekday_start.day,
            working_day_end_hour,
        )
        - weekday_start
    ).total_seconds()
    # Calculate seconds between weekday_end and the beginning of the weekday_start work day.
    end_partial = (
        weekday_end
        - datetime.datetime(
            weekday_end.year, weekday_end.month, weekday_end.day, working_day_start_hour
        )
    ).total_seconds()
    assert start_partial >= 0 and end_partial >= 0
    return (
        working_days * (working_day_end_hour - working_day_start_hour)
        + (start_partial + end_partial) / 3600
    )


# CLT (Customer Lead Time)
def calculate_clt(shipment: dict) -> float:
    """Calculate external lead time.

    How much time the customer gives to the broker.

    :param shipment: Shipment object with all properties.
    :return: Working hours from load creation to origin close.
    """
    if "loadCreationTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    load_creation_time = localize_utc_to_central(shipment["loadCreationTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(load_creation_time) or pd.isna(origin_close_time):
        return pd.NA
    if load_creation_time > origin_close_time:
        return 0
    return working_hours_between(
        load_creation_time,
        origin_close_time,
        _CLT_WORKING_DAY_START_HOUR,
        _CLT_WORKING_DAY_END_HOUR,
    )


# BLT (Broker Lead Time)
def calculate_blt(shipment: dict) -> float:
    """Calculate internal lead time.

    How much time the broker sales team gives their internal team.

    :param shipment: Shipment object with all properties.
    :return: Working hours from load activation to origin close.
    """
    if "loadActivationTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    load_activation_time = localize_utc_to_central(shipment["loadActivationTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(load_activation_time) or pd.isna(origin_close_time):
        return pd.NA
    if load_activation_time > origin_close_time:
        return 0
    return working_hours_between(
        load_activation_time,
        origin_close_time,
        _BLT_WORKING_DAY_START_HOUR,
        _BLT_WORKING_DAY_END_HOUR,
    )


def calculate_prebook(shipment: dict) -> float:
    """Calculate pre-book time (in hours).

    How many hours in advance the broker books the carrier.

    :param shipment: Shipment object with all properties.
    :return: Working hours from carrier assigned to origin close.
    """
    if "carrierAssignedTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    carrier_assigned_time = localize_utc_to_central(shipment["carrierAssignedTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(carrier_assigned_time) or pd.isna(origin_close_time):
        return pd.NA
    if carrier_assigned_time > origin_close_time:
        return 0
    return working_hours_between(
        carrier_assigned_time,
        origin_close_time,
        _PREBOOK_WORKING_DAY_START_HOUR,
        _PREBOOK_WORKING_DAY_END_HOUR,
    )


def turn_all_shipment_keys_to_lower(shipment: dict) -> dict:
    """
    Keys in object are mixed bag of camel case and all lowercase causing above functions to fail.
    Workaround until more long-term or sustainable fix.

    "param shipment: Shipment object with all properties.
    "return" Shipment object with all keys lowercase.
    """
    return {k.lower(): v for k, v in shipment.items()}


def normalize_datetime(dt: datetime.datetime) -> datetime.datetime:
    return dt if not pd.isna(dt) else pd.NaT


def calculate_delay(
    shipment: dict,
    pending_update: dict,
    arrival_col: str,
    close_col: str,
    delay_col: str,
) -> typing.Tuple[int, dict]:
    """Calculate if shipment is picked up on time.

    If there is an existing value in delay and it is greater than newly calculated delay, create
    a pending update with the values and do not update the shipment delay.
    """
    arrival_time = normalize_datetime(shipment[arrival_col])
    close_time = normalize_datetime(shipment[close_col])
    if pd.isna(arrival_time) or pd.isna(close_time):
        return shipment[delay_col], pending_update
    # Minutes between arrival and close.
    proposed_delay = int((arrival_time - close_time).total_seconds()) // 60
    # If shipment delay was already calculated and the proposed delay is less,
    # do not update the shipment delay and create a pending update.
    if not pd.isna(shipment[delay_col]) and shipment[delay_col] > proposed_delay:
        pending_update[delay_col] = proposed_delay
        return int(shipment[delay_col]), pending_update
    return proposed_delay, pending_update


if __name__ == "__main__":
    test_load_creation_time = datetime.datetime(2022, 3, 10, 18, 55)
    test_load_activation_time = datetime.datetime(2022, 3, 10, 19, 28)
    test_origin_close_time = datetime.datetime(2022, 3, 10, 19, 0)

    test_load_creation_time = localize_utc_to_central(test_load_creation_time)
    test_load_activation_time = localize_utc_to_central(test_load_activation_time)
    test_origin_close_time = localize_utc_to_central(test_origin_close_time)

    print(test_load_creation_time, test_load_activation_time, test_origin_close_time)
    print(
        "blt",
        working_hours_between(
            test_load_activation_time,
            test_origin_close_time,
            _PREBOOK_WORKING_DAY_START_HOUR,
            _PREBOOK_WORKING_DAY_END_HOUR,
        ),
    )
    print(
        "clt",
        working_hours_between(
            test_load_creation_time,
            test_origin_close_time,
            _PREBOOK_WORKING_DAY_START_HOUR,
            _PREBOOK_WORKING_DAY_END_HOUR,
        ),
    )
