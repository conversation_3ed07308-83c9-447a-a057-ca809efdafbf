import logging

import pandas as pd
from scorer.util import bound_score
from scorer.util import sigmoid


def cost_score(cost_value: float, carrier_score: float) -> float:
    if pd.isna(cost_value):
        return pd.NA
    return bound_score(-2.5 * cost_value + 2.25)
    if pd.isna(carrier_score) or carrier_score > 0.9:
        # https://www.desmos.com/calculator/9xfywvwibn
        # Punish shipments with cost value above the mean.
        # Temp experiment
        # Just green line in https://www.desmos.com/calculator/m19acsi81o
        return bound_score(-2.5 * cost_value + 2.25)
        # return bound_score(-2.5 * cost_value + 2.25)
    else:
        # https://www.desmos.com/calculator/m19acsi81o
        # Punish cost values that are further from mean.
        # std | score
        # 0   | 100
        # 0.5 |  85
        # 1   |  61
        # 2   |  27
        # if abs(cost_value - 0.5) < 0.1:
        #     return 1.0
        if cost_value > 0.5:
            return bound_score(-2.25 * cost_value + 2.25)
            # return bound_score(-2.5 * cost_value + 2.25)
        else:
            return bound_score(2.25 * cost_value)
            # return bound_score(2.5 * cost_value - 0.25)
        # return bound_score(-4.75 * cost_value * cost_value + 4.75 * cost_value)


def cost_value(df_row: dict) -> float:
    # Calculate the number of standard deviations the shipment's cogs is from the mean.
    # Apply a sigmoid to bound the value between 0 and 1.
    # std > mean | cost value
    #    -1      |    0.269
    #     0      |    0.500
    #     0.5    |    0.622
    #     1      |    0.731
    #     2      |    0.882
    if (
        pd.isna(df_row["cogsStd"])
        or pd.isna(df_row["cogsMean"])
        or pd.isna(df_row["cogs"])
    ):
        return pd.NA
    if df_row["cogsStd"] == 0:
        return 0.5
    x = (df_row["cogs"] - df_row["cogsMean"]) / df_row["cogsStd"]
    return sigmoid(x)


def calculate_cost_value(
    score_df: pd.DataFrame, min_sample_size: int = 3
) -> pd.DataFrame:
    """Calculate cost value of all shipments in dataframe.

    Compare a shipment's cogs to the distribution of cogs on the same lane in 30d rolling window. This is done by
    subtracting the revenue total from the distribution mean and dividing by standard deviation, then applying a sigmoid
    in order to bound the score between 0 and 1.
    """
    # TODO(P1): Do value counting instead of looping for efficiency.
    if len(score_df) < min_sample_size:
        score_df["costValue"] = pd.NA
        return score_df

    logging.info("Score df has %d rows", score_df.shape[0])

    filtered_df = score_df[score_df["cogsTotal"].notnull()]
    filtered_df = filtered_df[filtered_df["originCloseTime"].notnull()]
    # TODO(P0): Once imports are fixed, uncomment these line.
    # filtered_df = filtered_df[filtered_df["shipmentClass"] == ShipmentClass.canonical]
    # filtered_df = filtered_df[filtered_df["shipmentMode"] != ShipmentMode.imdl]
    logging.info(
        "Filtered df has %d rows, after filtering canonical shipments.",
        filtered_df.shape[0],
    )
    # filtered_df = filtered_df[filtered_df["clt"] > 1.5]
    # logging.info(
    #     "Filtered df has %d rows, after filtering CLT > 1.5.", filtered_df.shape[0]
    # )
    # In order to add carrier score filtering, the distribution of shipments must be separated from
    # the shipments we give a cost value to. This can only be done by writing a manual rolling window
    # calculation, which is not worth the effort at this time.
    # distribution_filters = []
    # cost_score_filters = []

    # Efficient mean is mu_2 = mu_1 - x_1/n_1 + x_2/n_2 I think
    # Efficient std is tricker, but can be done with a rolling window.
    # https://math.stackexchange.com/questions/106700/incremental-computation-of-standard-deviation
    # for index, row in score_df.iterrows():
    # while right - current <= 15:
    #      right += 1
    # while current - left > 15:
    #      left += 1

    # filtered_df = filtered_df[filtered_df["carrierScore"] >= 0.5]
    # logging.info(
    #     "Filtered df has %d rows, after filtering carrier score >= 0.5.",
    #     filtered_df.shape[0],
    # )

    if len(filtered_df) < min_sample_size:
        logging.info(
            "Filtered df has %d rows, which is less than min_sample_size %d. Returning NA.",
            len(filtered_df),
            min_sample_size,
        )
        score_df["costValue"] = pd.NA
        return score_df

    # Compare shipments in a 60 day rolling window.
    filtered_df.sort_values(by=["originCloseTime"], inplace=True)
    window = filtered_df.rolling(
        "60d", center=True, on="originCloseTime", min_periods=min_sample_size
    )

    # Only consider cogs at book, ignoring accessorials for cost value comparison.
    filtered_df["cogsAccessorial"] = filtered_df["cogsAccessorial"].fillna(0.0)
    filtered_df["cogs"] = filtered_df["cogsTotal"] - filtered_df["cogsAccessorial"]

    # Precompute the mean and std for each window.
    filtered_df["cogsMean"] = window["cogs"].mean()
    filtered_df["cogsStd"] = window["cogs"].std()
    filtered_df.loc[:, "costValue"] = filtered_df.apply(
        lambda s: cost_value(s.to_dict()), axis="columns"
    )

    score_df.loc[:, "costValue"] = filtered_df["costValue"]
    return score_df
