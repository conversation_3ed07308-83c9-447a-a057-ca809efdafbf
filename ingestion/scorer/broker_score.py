import pandas as pd
from scorer.util import BUSINESS_DAY_HOURS
from scorer.util import linear_quadratic_piecewise
from scorer.util import sigmoid
from scorer.util import weighted_score

CLT_THRESHOLD = int(1.5 * BUSINESS_DAY_HOURS)


def prebook_threshold_score(shipment: dict) -> float:
    """Calculate prebook score based on a constant threshold value.

    Punish score if it's less than the threshold value.

    :param shipment: Shipment object with all properties, including prebook, blt and clt.
    :return: Prebook score between 0 and 1.
    """
    if (
        pd.isna(shipment["preBook"])
        or pd.isna(shipment["clt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # A larger pre-book yields a higher score.
    if shipment["preBook"] > CLT_THRESHOLD:
        return 1
    return linear_quadratic_piecewise(shipment["preBook"], CLT_THRESHOLD)


def prebook_piecewise_linear_score(shipment: dict) -> float:
    """Calculate prebook score on various linear scales.

    Punish score more severely at higher prebook and more gradually at lower prebook.
    https://www.desmos.com/calculator/cprmt2kbq2

    :param shipment: Shipment object with all properties, including prebook, blt and clt.
    :return: Prebook score between 0 and 1.
    """
    if (
        pd.isna(shipment["preBook"])
        or pd.isna(shipment["clt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # A larger pre-book yields a higher score.
    if shipment["preBook"] > CLT_THRESHOLD:
        return 1.0
    if shipment["preBook"] > BUSINESS_DAY_HOURS:
        # scale to 0.9 - 1.0
        return (shipment["preBook"] + 35) / 50
    if shipment["preBook"] > int(0.5 * BUSINESS_DAY_HOURS):
        # scale to 0.6 - 0.9
        return (3 * shipment["preBook"] + 15) / 50
    # scale to 0.0 - 0.6
    return 3 / 25 * shipment["preBook"]


def ltu_score(shipment: dict, epsilon=0.000001) -> float:
    """Calculate ltu score based on a constant threshold value.

    LTU is lead time utilization. Is calculated as blt / clt or clt - blt. The latter is used here.
    Punish score if it's greater than the threshold value. If blt = 0, give score of 0.

    :param shipment: Shipment object with all properties, including blt and clt.
    :return: LTU score between 0 and 1.
    """
    if (
        pd.isna(shipment["clt"])
        or pd.isna(shipment["blt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # Lead time difference between external lead and internal lead. Punish higher values.
    ltd = shipment["clt"] - shipment["blt"]
    if ltd < BUSINESS_DAY_HOURS:
        return 1
    # This only occurs if blt is negative.
    if ltd > shipment["clt"]:
        return pd.NA
    # https://www.desmos.com/calculator/hpahhlbiya
    return shipment["blt"] / (shipment["clt"] - BUSINESS_DAY_HOURS + epsilon)


def prebook_score(shipment: dict, distributions: dict) -> float:
    """Calculate pre-book score of a shipment.

    Compare a shipment's pre-book hours to the distribution of pre-book hours on the same lane. This is done by
    subtracting the pre-book total from the distribution mean and dividing by standard deviation, then applying a
    sigmoid in order to bound the score between 0 and 1.

    :param shipment: Shipment object with all properties, including prebook.
    :param distributions: Distribution of pre-book hour totals, indexed by lane id.
    :return: Pre-book score between 0 and 1.
    """
    if pd.isna(shipment["preBook"]) or shipment["laneId"] not in distributions:
        return pd.NA
    d = distributions[shipment["laneId"]]["preBook"]
    assert len(d) > 0
    if d.std() == 0:
        return 0.5
    x = (d.mean() - shipment["preBook"]) / d.std()
    return sigmoid(x)


def broker_score(
    shipment: dict, prebook_weight: float = None, ltu_weight: float = None
) -> float:
    """Calculate broker score of a shipment.

    Measures the broker's ability to give carriers time and
    their use of the time given to them by the customer."""
    prebookscore = prebook_piecewise_linear_score(shipment)
    ltuscore = ltu_score(shipment)
    if prebook_weight is None or ltu_weight is None:
        return weighted_score((prebookscore, ltuscore)), prebookscore, ltuscore
    return (
        weighted_score((prebookscore, ltuscore), (prebook_weight, ltu_weight)),
        prebookscore,
        ltuscore,
    )
