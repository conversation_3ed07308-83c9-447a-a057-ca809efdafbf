import datetime

from scorer.util import bound_score
from scorer.util import make_linear_fn
from scorer.util import weighted_score


def carrier_score(shipment: dict, weights: tuple = (1, 3)) -> float:
    # OTD is three times as important as OTP.
    # Linear function from 1 to 0 per minute late.
    # For pickup function, 30 minutes is 0.5 and 60 is 0.
    pickup_fn = make_linear_fn(60)
    delivery_fn = make_linear_fn(30)
    otp_score = bound_score(pickup_fn(shipment["originDelayMinutes"]))
    otd_score = bound_score(delivery_fn(shipment["destinationDelayMinutes"]))
    return weighted_score((otp_score, otd_score), weights), otp_score, otd_score


if __name__ == "__main__":
    shipment = {
        "brokerId": "af9b8398-d8c9-4f13-9ab8-7307a3c74207",
        "laneId": "4ed8390f-5f39-4859-8d12-164fbed547c7",
        "shipperId": "504b47c8-0657-4eee-a243-6ba3cee4809f",
        "brokerPrimaryReference": "5244509",
        "shipperPrimaryReference": "*********",
        "stopCount": 2,
        "distanceMiles": 908.0,
        "cogsLineHaul": 3400.0,
        "cogsAccessorial": 0.0,
        "cogsFuel": None,
        "cogsTotal": 3400.0,
        "revenueLineHaul": 3144.0,
        "revenueFuel": 462.77,
        "revenueAccessorial": 0.0,
        "revenueTotal": 3606.77,
        "originWarehouse": "CAMPBELL SOUP SUPPLY COMPANY",
        "destinationWarehouse": "COSTCO WEST FARGO ND",
        "shipmentStatus": "Delivered",
        "shipmentRank": "Spot",
        "weight": 44081.0,
        "originOpenTime": datetime.datetime.strptime(
            "2022-03-02 01:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originCloseTime": datetime.datetime.strptime(
            "2022-03-01 19:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originArrivalTime": datetime.datetime.strptime(
            "2022-03-01 18:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originDepartureTime": datetime.datetime.strptime(
            "2022-03-02 02:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationOpenTime": datetime.datetime.strptime(
            "2022-03-03 13:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationCloseTime": datetime.datetime.strptime(
            "2022-03-03 07:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationArrivalTime": datetime.datetime.strptime(
            "2022-03-03 06:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationDepartureTime": datetime.datetime.strptime(
            "2022-03-03 14:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "loadCreationTime": datetime.datetime.strptime(
            "2022-02-25 08:56:57", "%Y-%m-%d %H:%M:%S"
        ),
        "loadActivationTime": datetime.datetime.strptime(
            "2022-02-28 10:20:56", "%Y-%m-%d %H:%M:%S"
        ),
        "carrierAssignedTime": datetime.datetime.strptime(
            "2022-03-01 10:16:50", "%Y-%m-%d %H:%M:%S"
        ),
        "humanModified": False,
        "score": 81.2811629035,
        "blt": 16.65111111111111,
        "clt": 28.050833333333333,
        "preBook": 6.719444444444444,
        "shipmentClass": "canonical",
        "originDelayMinutes": 120,
        "destinationDelayMinutes": -34,
        "pendingUpdate": "{}",
        "marginDollars": 206.76999999999998,
        "marginPercent": 0.05732830205419253,
    }
    print(carrier_score(shipment))
