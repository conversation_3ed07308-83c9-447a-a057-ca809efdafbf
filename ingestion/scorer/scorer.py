import logging

import pandas as pd
import scorer.composite_score as composite_score
from scorer.query_aurora import query_chunked
from tqdm import tqdm

from common import query_utils


class Scorer:
    def __init__(self, aurora_db: str, scorer_log_filename: str) -> None:
        self.__aurora_db = aurora_db
        self.scorer_log_filename = self.set_up_logging(scorer_log_filename)

    def get_lane_shipments(self, lane_id: str) -> pd.DataFrame:
        """Gets all shipments for a lane.

        :param lane_id: The lane to get shipments for.
        :return: Dataframe of shipments in the lane.
        """
        query_response = query_chunked(
            f'SELECT * FROM shipments WHERE laneid ="{lane_id}"', self.__aurora_db
        )
        return query_utils.append_records_to_df(query_response)

    def set_up_logging(self, scorer_log_filename: str) -> None:
        """Sets up logging for the lambda.

        :param scorer_log_filename: The filename to log to.
        """

        # TODO(P0): Configure this properly.
        # logging.basicConfig(filename=scorer_log_filename,
        #                     level=logging.INFO,
        #                     format='%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s',
        #                     datefmt='%H:%M:%S')`
        root_logger = logging.getLogger()
        logging.info("Scoring started.")

        # log_formatter = logging.Formatter("%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s")
        file_handler = logging.FileHandler(scorer_log_filename)
        root_logger.setLevel(logging.WARN)

        # file_handler.setFormatter(log_formatter)
        root_logger.addHandler(file_handler)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s",
            datefmt="%H:%M:%S",
        )
        logging.getLogger("sqlalchemy").setLevel(logging.ERROR)

        logging.info("Scoring started.")
        return

    def score_and_update_df(self, shipments_df: pd.DataFrame) -> pd.DataFrame:
        """Scores a shipment dataframe.

        :param shipment_df: The shipment dataframe to score.
        :return: The scored shipment dataframe.
        """

        if len(shipments_df) == 0:
            return
        logging.info(f"{len(shipments_df)} shipments read.")
        # Dictionary with values as formatstring for the query parameter.
        computed_cols = {
            "score": "{} = {}, ",
            "carrierScore": "{} = {}, ",
            "brokerScore": "{} = {}, ",
            "performanceScore": "{} = {}, ",
            "costScore": "{} = {}, ",
            "ltuScore": "{} = {}, ",
            "prebookScore": "{} = {}, ",
            "otpScore": "{} = {}, ",
            "otdScore": "{} = {}, ",
            "blt": "{} = {}, ",
            "clt": "{} = {}, ",
            "preBook": "{} = {}, ",
            "originDelayMinutes": "{} = {}, ",
            "destinationDelayMinutes": "{} = {}, ",
            "pendingUpdate": "{} = '{}', ",  # string column, add quotes
        }

        score_df = composite_score.score_shipments_df(shipments_df)

        for index, row in score_df.iterrows():
            # TODO(P1): Consider adding a last scored time field.
            # last_modified_time = datetime.datetime.utcnow().replace(microsecond=0)
            query_str = "UPDATE shipments SET "
            for col in computed_cols:
                if not pd.isna(row[col]):
                    query_str += computed_cols[col].format(col, row[col])
            # query_str += f'lastModifiedTime = "{last_modified_time}" WHERE id = "{index}";'
            query_str += f'id = "{index}" WHERE id = "{index}";'
            # Example query:
            # UPDATE shipments SET score = 35.58571428571429, blt = 101.24138888888889, clt = 103.0,
            # preBook = 0.8183333333333334, originDelayMinutes = 60.0, destinationDelayMinutes = -60.0, pendingUpdate = '{"originDelayMinutes": 0.0}',
            # id = "261ac7cf-03d9-416f-b659-26ce03f54d31" WHERE id = "261ac7cf-03d9-416f-b659-26ce03f54d31";
            logging.info(query_str)
            query_utils.query(query_str, self.__aurora_db)

    def score_all_shipments_by_lane(self) -> None:
        """Scores all shipments for a list of lanes.

        :param lane_ids: The list of lanes to score.
        """
        # Gets list of lanes to score.
        query_response = query_utils.query("SELECT id FROM lanes;", self.__aurora_db)
        lane_ids = []

        for record in query_response["records"]:
            lane_ids.append(next(iter(record[0].values())))

        for lane_id in tqdm(lane_ids):
            logging.info(f"Scoring lane {lane_id}")
            shipments_df = self.get_lane_shipments(lane_id)
            self.score_and_update_df(shipments_df)

    def score_all_shipments_by_3zip(self) -> None:
        """The "main" function of the lambda. This is where the scoring queries are handled.

        Gets list of lanes, and scores shipments lane by lane.
        """

        # Gets list of lanes to score, grouped by 3-zip.
        query_response = query_utils.query(
            """SELECT GROUP_CONCAT(l.id) AS laneIds, oz.originZipN, dz.destinationZipN FROM lanes l
                JOIN cities oc ON l.originCityId = oc.id
                JOIN cityzips ocz ON oc.id = ocz.cityId
                JOIN (SELECT id, SUBSTRING(zip, 1, {ziplen}) AS originZipN FROM zips) AS oz ON ocz.zipId = oz.id
                JOIN cities dc ON l.destinationCityId = dc.id
                JOIN cityzips dcz ON dc.id = dcz.cityId
                JOIN (SELECT id, SUBSTRING(zip, 1, {ziplen}) AS destinationZipN FROM zips) AS dz ON dcz.zipId = dz.id
                GROUP BY oz.originZipN, dz.destinationZipN;""".format(
                ziplen=3
            ),
            self.__aurora_db,
        )

        grouped_lane_ids = []
        for record in query_response["records"]:
            values = [next(iter(i.values())) for i in record]
            lane_ids_str, origin_3zip, destination_3zip = values
            grouped_lane_ids.append(
                (lane_ids_str.split(","), origin_3zip, destination_3zip)
            )

        for grouped_lane_id in tqdm(grouped_lane_ids):
            lane_ids, origin_3zip, destination_3zip = grouped_lane_id
            logging.info(f"Scoring lanes from {origin_3zip}* to {destination_3zip}*.")
            shipments_df = pd.DataFrame()
            for lane_id in lane_ids:
                shipments_df = pd.concat(
                    [shipments_df, self.get_lane_shipments(lane_id)]
                )

            self.score_and_update_df(shipments_df)


def main():
    scorer = Scorer("mvp_db_dev", "test_scorer.log")
    scorer.score_all_shipments_by_3zip()


if __name__ == "__main__":
    # main()
    qry_rspns = query_utils.query("SELECT * FROM shipments;", "mvp_db_dev")
