import json
import logging

import pandas as pd
from scorer.broker_score import broker_score
from scorer.carrier_score import carrier_score
from scorer.cost_score import calculate_cost_value
from scorer.cost_score import cost_score
from scorer.util import calculate_blt
from scorer.util import calculate_clt
from scorer.util import calculate_delay
from scorer.util import calculate_prebook
from scorer.util import format_score
from scorer.util import localize_utc_to_central
from scorer.util import weighted_score


def performance_score(shipment: dict) -> float:
    """Calculates broker, carrier, and composited performance score.

    :param shipment: Shipment object with all relevant computed properties.
    :return: Composite score between 0 and 100.
    """
    brokerscore, prebookscore, ltuscore = broker_score(shipment)
    carrierscore, otpscore, otdscore = carrier_score(shipment)

    if pd.isna(carrierscore):
        performancescore = brokerscore
    elif pd.isna(brokerscore):
        performancescore = carrierscore
    else:
        if carrierscore >= 0.5:
            performancescore = 0.75 * carrierscore + 0.25 * brokerscore
        else:
            performancescore = 0.5 * carrierscore + 0.5 * brokerscore

    return (
        brokerscore,
        carrierscore,
        performancescore,
        prebookscore,
        ltuscore,
        otpscore,
        otdscore,
    )


def final_score(shipment: dict) -> float:
    """Calculate cost score and final composited score.

    :param shipment: Shipment object with all properties, including constituent scores.
    :param weights: Tuple of weights for each constituent score.
    :return: Composite score between 0 and 100.
    """

    # Weights if carrier score > 75:
    # cost 40, broker 40, carrier 20
    # Weights if carrier score < 75:
    # cost 25, broker 25, carrier 50

    carrierscore = shipment["carrierScore"]
    brokerscore = shipment["brokerScore"]
    # performancescore = shipment["performanceScore"]

    costscore = cost_score(shipment["costValue"], carrierscore)
    if pd.isna(carrierscore):
        finalscore = weighted_score((brokerscore, costscore))
    elif carrierscore > 0.75:
        finalscore = weighted_score(
            (costscore, brokerscore, carrierscore), (30, 40, 30)
        )
    else:
        finalscore = weighted_score(
            (costscore, brokerscore, carrierscore), (20, 20, 60)
        )

    # if pd.isna(costscore):
    #     finalscore = performancescore
    # elif pd.isna(performancescore):
    #     finalscore = costscore
    # else:
    #     finalscore = 0.5 * performancescore + 0.5 * costscore
    return costscore, finalscore


def calculate_margin_percent(shipment: dict) -> float:
    if shipment["revenueTotal"] == 0:
        return pd.NA
    return shipment["marginDollars"] / shipment["revenueTotal"]


def score_shipments_df(shipments_df: pd.DataFrame) -> dict:
    """Score shipments based on revenue, pre-book, and delay times.

    :param shipments_df: DataFrame with all shipment data.
    :return: Score dictionary indexed by shipment id.
    """
    scored_times = [
        "originArrivalTime",
        "originCloseTime",
        "destinationArrivalTime",
        "destinationCloseTime",
        "carrierAssignedTime",
        "loadCreationTime",
        "loadActivationTime",
    ]
    # Change all dataframe columns to lowercase.
    score_df = shipments_df.set_index("id")

    for i, old_row in score_df.iterrows():
        if pd.isna(old_row["pendingUpdate"]):
            pending_update = {}
        else:
            pending_update = json.loads(old_row["pendingUpdate"])
        origin_delay_minutes, pending_update = calculate_delay(
            old_row,
            pending_update,
            "originArrivalTime",
            "originCloseTime",
            "originDelayMinutes",
        )
        # Update new row.
        score_df.at[i, "originDelayMinutes"] = origin_delay_minutes
        score_df.at[i, "pendingUpdate"] = json.dumps(pending_update).replace("'", '"')

        destination_delay_minutes, pending_update = calculate_delay(
            old_row,
            pending_update,
            "destinationArrivalTime",
            "destinationCloseTime",
            "destinationDelayMinutes",
        )
        # Update new row.
        score_df.at[i, "destinationDelayMinutes"] = destination_delay_minutes
        score_df.at[i, "pendingUpdate"] = json.dumps(pending_update).replace("'", '"')

    def error_handling_wrapper(f, shipment: dict):
        try:
            return f(shipment)
        except ValueError as e:
            logging.error(
                "(brokerPrimaryReference: %s), %s",
                shipment["brokerPrimaryReference"],
                e,
            )
            return pd.NA

    # Convert shipment (s) Series to a dict before passing to helper functions.
    score_df.loc[:, "blt"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_blt, s.to_dict()), axis="columns"
    )
    score_df.loc[:, "clt"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_clt, s.to_dict()), axis="columns"
    )
    score_df.loc[:, "preBook"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_prebook, s.to_dict()), axis="columns"
    )
    score_df.loc[:, scored_times] = score_df[scored_times].applymap(
        localize_utc_to_central
    )

    score_df.loc[:, "marginDollars"] = score_df["revenueTotal"] - score_df["cogsTotal"]
    score_df.loc[:, "marginPercent"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_margin_percent, s.to_dict()),
        axis="columns",
    )

    for i, row in score_df.iterrows():
        (
            score_df.at[i, "brokerScore"],
            score_df.at[i, "carrierScore"],
            score_df.at[i, "performanceScore"],
            score_df.at[i, "prebookScore"],
            score_df.at[i, "ltuScore"],
            score_df.at[i, "otpScore"],
            score_df.at[i, "otdScore"],
        ) = performance_score(row.to_dict())
    score_df = calculate_cost_value(score_df)
    for i, row in score_df.iterrows():
        (
            score_df.at[i, "costScore"],
            score_df.at[i, "score"],
        ) = final_score(row.to_dict())

    score_cols = [
        "brokerScore",
        "carrierScore",
        "performanceScore",
        "costScore",
        "score",
        "prebookScore",
        "ltuScore",
        "otpScore",
        "otdScore",
    ]
    # Convert scores from floats [0, 1] to ints [0, 100] for display.
    score_df.loc[:, score_cols] = score_df[score_cols].applymap(format_score)

    score_df.drop(
        columns=["marginDollars", "marginPercent"],
        inplace=True,
    )

    return score_df
