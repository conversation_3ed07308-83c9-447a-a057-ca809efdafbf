import collections
import datetime
import logging
import os
import re
import threading
import traceback
import typing
import zipfile

import numpy as np
import pandas as pd
from constants import BROKER_IGNORE_COLS
from constants import BROKER_NAME_DICT
from constants import CUSTOM_TIMEZONES_DICT
from constants import <PERSON><PERSON><PERSON><PERSON><PERSON>_COLUMNS
from constants import GC
from constants import INGESTION_DATA_DIR
from constants import NUM_THREADS
from constants import NUMERIC_COLUMNS
from constants import SHIPPER_NAME_DICT
from constants import STRING_COLUMNS
from mapping.column_mapping import map_column
from rows import BrokerRow
from rows import CityRow
from rows import CityZipRow
from rows import LaneRow
from rows import ShipmentMetadataRow
from rows import ShipmentRow
from rows import ShipperRow
from rows import ZipRow
from tqdm import tqdm

from common import google_maps_utils
from common.constants import ShipmentIngestionStatus
from common.rds_api import RDS
import google.generativeai as genai
import os

logger = logging.getLogger(__name__)

# TODO(P0): Document this fucking code.

# Configure Gemini API key from environment variable or use the default
api_key = os.environ.get('GEMINI_API_KEY', 'AIzaSyAZpQ7WDXiX0Get6582FpUjb-FL8o3rhxg')
genai.configure(api_key=api_key)
logger.info(f"Configured Gemini API with {'environment variable' if 'GEMINI_API_KEY' in os.environ else 'default key'}")

def build_df_type_dictionary(column_mapping: dict) -> tuple:
    """Build a dictionary of column names and their data types.

    The keys of the type dictionary are the keys of the column_mapping dictionary.

    :param column_mapping: A dictionary of input column names and corresponding golden column.
    :return: Partial dictionary of columns and their types.
    :return: List of datetime columns.
    """
    inverted_column_mapping = {}
    # Invert dictionary.
    for key, value in column_mapping.items():
        inverted_column_mapping[value] = key

    type_dictionary = {}
    date_columns = []
    for gold_col in STRING_COLUMNS:
        if gold_col.value in inverted_column_mapping:
            type_dictionary[inverted_column_mapping[gold_col.value]] = str
    for gold_col in DATETIME_COLUMNS:
        if gold_col.value in inverted_column_mapping:
            type_dictionary[inverted_column_mapping[gold_col.value]] = str
            date_columns.append(inverted_column_mapping[gold_col.value])
    return type_dictionary, date_columns


def round_time(dt: pd.Timestamp) -> pd.Timestamp:
    second = 0 if pd.isna(dt.second) else dt.second
    microsecond = 0 if pd.isna(dt.microsecond) else dt.microsecond
    # Calculate rounded seconds
    rounded_seconds = round(second + microsecond / 1000000)

    # Adjust the datetime
    rounded_dt = dt.replace(second=0, microsecond=0) + datetime.timedelta(
        seconds=rounded_seconds
    )

    return rounded_dt


def parse_time(datetime_str: str) -> pd.Timestamp:
    if pd.isna(datetime_str) or not datetime_str:
        return pd.NaT

    cleaned_datetime_str = re.sub(r"[^0-9/: -APMapmT]", "", datetime_str)

    try:
        parsed_time = pd.to_datetime(
            cleaned_datetime_str, format="mixed", errors="coerce"
        )

        if not pd.isna(parsed_time):
            return parsed_time

        # If "mixed" format parsing failed, try parsing with "AM/PM" format
        parsed_time_am_pm = pd.to_datetime(
            cleaned_datetime_str, format="%I:%M:%S %p", errors="coerce"
        )

        return parsed_time_am_pm
    except Exception:
        return pd.NaT

def convert_date_value(value):
    """
    Convert a single value to datetime, handling various formats including Excel serial dates.

    Args:
        value: The value to convert (could be string, number, or already datetime)

    Returns:
        pd.Timestamp or pd.NaT: The converted datetime or NaT if conversion fails
    """
    if pd.isna(value):
        return pd.NaT

    # If already a datetime object, just return it
    if isinstance(value, (pd.Timestamp, datetime.datetime)):
        return value

    # Try to handle Excel serial date (numeric value)
    if isinstance(value, (int, float)) or (isinstance(value, str) and value.replace(',', '').replace('.', '').isdigit()):
        # Convert string numbers with commas to float
        if isinstance(value, str):
            try:
                value = float(value.replace(',', ''))
            except ValueError:
                pass

        # Check if in reasonable Excel date range
        if isinstance(value, (int, float)) and 1 <= value <= 80000:
            try:
                # Try with 1899-12-30 origin (most common)
                dt = pd.to_datetime(value, unit='D', origin='1899-12-30')
                if 1900 <= dt.year <= 2100:  # Sanity check for reasonable date
                    return dt

                # If date seems unreasonable, try with 1904 date system (Mac Excel)
                dt = pd.to_datetime(value, unit='D', origin='1904-01-01')
                if 1900 <= dt.year <= 2100:
                    return dt
            except:
                pass

    # For string values, try various parsing approaches
    if isinstance(value, str):
        # Clean the string (remove non-standard characters)
        cleaned_value = re.sub(r'[^0-9/: -APMapmT.+-]', '', value)

        try:
            # Try pandas' flexible datetime parser
            return pd.to_datetime(cleaned_value, format="mixed", errors="raise")
        except:
            pass

        try:
            # Try with dateutil parser which is very flexible
            from dateutil import parser
            dt = parser.parse(cleaned_value)
            return pd.Timestamp(dt)
        except:
            pass

    # If all conversion attempts failed
    logging.warning(f"Failed to convert date value: {value} (type: {type(value)})")
    return pd.NaT

def convert_types(
    shipments_df: pd.DataFrame, added_empty_cols: typing.List[str]
) -> pd.DataFrame:
    for column in STRING_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue
        shipments_df[column.value] = shipments_df[column.value].str.strip()
        shipments_df[column.value].replace("nan", pd.NA, inplace=True)

    for column in DATETIME_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue

        # Log sample values before conversion for debugging
        sample_values = shipments_df[column.value].dropna().head(3).tolist()
        logging.info(f"Converting datetime column {column.value}, sample values: {sample_values}")

        # Convert NA to NaT
        shipments_df[column.value].replace(pd.NA, pd.NaT, inplace=True)

        # Apply row-by-row conversion using our enhanced function
        shipments_df[column.value] = shipments_df[column.value].apply(convert_date_value)

        # Round the times
        shipments_df[column.value] = shipments_df[column.value].apply(round_time)

        # Log conversion results
        success_rate = shipments_df[column.value].notna().mean() * 100
        logging.info(f"Conversion of {column.value} complete. Success rate: {success_rate:.1f}%")

    for column in NUMERIC_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue
        shipments_df[column.value] = shipments_df[column.value].apply(
            lambda s: s.replace(",", "").replace("$", "") if type(s) == str else s
        )
        shipments_df[column.value] = pd.to_numeric(
            shipments_df[column.value], errors="coerce"
        )
    return shipments_df


# # TODO(P1): Use converters https://stackoverflow.com/questions/42958217/pandas-read-excel-datetime-converter
# def convert_types(
#     shipments_df: pd.DataFrame, added_empty_cols: typing.List[str]
# ) -> pd.DataFrame:
#     for column in STRING_COLUMNS:
#         if column.value not in shipments_df.columns or column.value in added_empty_cols:
#             continue
#         shipments_df[column.value] = shipments_df[column.value].str.strip()
#         shipments_df[column.value].replace("nan", pd.NA, inplace=True)

#     for column in DATETIME_COLUMNS:
#         if column.value not in shipments_df.columns or column.value in added_empty_cols:
#             continue
#         # Convert NA to NaT.
#         shipments_df[column.value].replace(pd.NA, pd.NaT, inplace=True)

#         # Check if the column might contain Excel serial dates (numeric type)
#         if pd.api.types.is_numeric_dtype(shipments_df[column.value]):
#             # Convert Excel serial dates to datetime
#             shipments_df[column.value] = pd.to_datetime(
#                 shipments_df[column.value], unit='D', origin='1899-12-30', errors='coerce' # or '1900-01-01'
#             )
#         else: # If not numeric, assume it's a string and try parsing as datetime string
#             shipments_df[column.value] = shipments_df[column.value].apply(parse_time)


#         shipments_df[column.value] = shipments_df[column.value].apply(round_time)

#     for column in NUMERIC_COLUMNS:
#         if column.value not in shipments_df.columns or column.value in added_empty_cols:
#             continue
#         shipments_df[column.value] = shipments_df[column.value].apply(
#             lambda s: s.replace(",", "").replace("$", "") if type(s) == str else s
#         )
#         shipments_df[column.value] = pd.to_numeric(
#             shipments_df[column.value], errors="coerce"
#         )
#     return shipments_df


def read_input_file(
    input_filename: str, type_dict: dict = {}, date_cols: list = []
) -> pd.DataFrame:
    """Read an input file and return a pandas dataframe.

    The file can be a zip archive, an excel file, or a csv file.

    :param input_filename: The name of the file to read.
    :param type_dict: A dictionary of column names and their types.
    :param date_cols: A list of column names that should be parsed as dates.
    :return: A pandas dataframe."""
    if input_filename[-3:] == "zip":
        with zipfile.ZipFile(input_filename) as z:
            if len(z.namelist()) > 1:
                raise NotImplementedError(
                    "There is more than one file in the zip archive"
                )
            first_file = z.namelist()[0]
            z.extract(first_file, path=INGESTION_DATA_DIR)
            extracted_file = os.path.join(INGESTION_DATA_DIR, first_file)
            return read_input_file(extracted_file, type_dict, date_cols)
    elif "xlsx" in input_filename:
        df = pd.read_excel(input_filename, dtype=type_dict)
        skip_rows = 0
        # TODO(P1): Consider checking for empty columns.
        while all(col.startswith("Unnamed") for col in df.columns):
            skip_rows += 1
            df = pd.read_excel(input_filename, dtype=type_dict, skiprows=skip_rows)
        # Only parse date_cols when we reach the column row.
        df = pd.read_excel(input_filename, dtype=type_dict, skiprows=skip_rows)
    elif "csv" in input_filename:
        df = pd.read_csv(
            input_filename,
            dtype=type_dict,
            skip_blank_lines=True,
        )
    else:
        logging.error(f"File not supported {input_filename}")
        raise ValueError("Unsupported file extension.")
    return df


def read_and_map_file(input_filename: str, broker: str) -> pd.DataFrame:
    """
    Read and map an input file to the standardized format.

    Args:
        input_filename: Path to the input file
        broker: Name of the broker

    Returns:
        pd.DataFrame: Mapped and processed DataFrame

    Raises:
        ValueError: If required columns are missing or mapping fails
        AssertionError: If multiple columns map to the same golden column
    """
    # Read the input file
    df = read_input_file(input_filename)
    original_headers = list(df.columns)
    template_headers = [g.value for g in GC]  # Get all golden column values
    print("This is the original and template headers: ",original_headers, template_headers)
    # Get sample data for better mapping
    sample_data = df.head(5).to_dict(orient='records')

    # Initialize column mappings
    column_mappings = {}
    mapping_issues = {}

    # First attempt: Try AI-based mapping
    try:
        from .mapping.ai_column_mapping import ai_column_mapping
        logger.info("Attempting AI-based column mapping...")
        column_mappings = ai_column_mapping(
            original_headers=original_headers,
            template_headers=template_headers,
            sample_data=sample_data,
            issues=mapping_issues
        )
        # The detailed logging is now done in the ai_column_mapping function
    except Exception as e:
        logger.warning(f"AI mapping failed: {e}. Falling back to traditional mapping.")
        # Fallback to traditional mapping
        for col in original_headers:
            try:
                column_mappings[col] = map_column(col, 0).value
                logger.info(f"Traditional mapping: '{col}' -> '{column_mappings[col]}'")
            except ValueError as e:
                logger.warning(f"Failed to map column '{col}': {e}")
                mapping_issues[col] = str(e)

    # Validate the mapping
    if len(column_mappings.values()) != len(set(column_mappings.values())):
        raise AssertionError(f"Multiple columns map to single GC: {column_mappings}")

    # Re-read the file with proper types
    df = read_input_file(input_filename, *build_df_type_dictionary(column_mappings))
    print("Printing column mapping", column_mappings)
    df = df.rename(columns=column_mappings)

    # Handle broker-specific column drops
    if broker in BROKER_IGNORE_COLS:
        drop_cols = BROKER_IGNORE_COLS[broker]
        for col in drop_cols:
            if col.value in df.columns:
                df.drop(col.value, axis=1, inplace=True)
                logger.info(f"Dropped broker-specific column: {col.value}")

    # Ensure all required columns are present
    required_cols = {
        GC.origin_zip,
        GC.destination_zip,
        GC.origin_city,
        GC.destination_city,
        GC.origin_state,
        GC.destination_state,
        GC.shipment_mode,
        GC.equipment_type,
        GC.customer_name,
        GC.broker_name,
        GC.broker_primary_reference,
        GC.cogs_total,
        GC.revenue_total,
        GC.origin_close,
    }

    added_empty_cols = []
    for g in GC:
        # print("Printing df values",df.columns)
        if g.value not in df.columns:
            if g in required_cols:
                raise AssertionError(f'Missing required column "{g.value}".')
            df[g.value] = pd.NA
            added_empty_cols.append(g.value)
            logger.info(f"Added empty column: {g.value}")

    # Convert data types
    convert_types(df, added_empty_cols)
    logger.info(f"Successfully processed file: {input_filename}")
    return df


def ingest_row(
    r: pd.Series,
    rds: RDS,
    input_filename: str,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
):
    return_dict = {}
    return_dict["derived_cols"] = set()
    return_dict["shipment_diffs"] = {}

    origin_city = CityRow(
        city=r[GC.origin_city.value],
        state=r[GC.origin_state.value],
        country=r[GC.origin_country.value],
    )
    origin_city.id = rds.upsert(rds.cities, origin_city.to_dict())
    destination_city = CityRow(
        city=r[GC.destination_city.value],
        state=r[GC.destination_state.value],
        country=r[GC.destination_country.value],
    )
    destination_city.id = rds.upsert(rds.cities, destination_city.to_dict())

    origin_zip = ZipRow(
        zipcode=r[GC.origin_zip.value],
        country=origin_city.country,
        city=origin_city.city,
        state=origin_city.state,
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )
    origin_zip.id = rds.upsert(rds.zips, origin_zip.to_dict())
    destination_zip = ZipRow(
        zipcode=r[GC.destination_zip.value],
        country=destination_city.country,
        city=destination_city.city,
        state=destination_city.state,
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )
    destination_zip.id = rds.upsert(rds.zips, destination_zip.to_dict())

    origin_city_zip = CityZipRow(city_id=origin_city.id, zip_id=origin_zip.id)
    origin_city_zip.id = rds.upsert(rds.cityzips, origin_city_zip.to_dict())
    destination_city_zip = CityZipRow(
        city_id=destination_city.id, zip_id=destination_zip.id
    )
    destination_city_zip.id = rds.upsert(
        rds.cityzips,
        destination_city_zip.to_dict(),
    )

    lane = LaneRow(
        origin_city_id=origin_city.id,
        destination_city_id=destination_city.id,
        shipment_mode=r[GC.shipment_mode.value],
        equipment_type=r[GC.equipment_type.value],
    )
    lane.id = rds.upsert(rds.lanes, lane.to_dict())

    if pd.isna(r[GC.broker_name.value]):
        raise AssertionError(
            "Broker name column not found and broker name not specified."
        )
    else:
        broker_name = r[GC.broker_name.value]
    if broker_name.lower().strip() in BROKER_NAME_DICT:
        broker_name = BROKER_NAME_DICT[broker_name.lower().strip()]
    broker = BrokerRow(name=broker_name)
    broker.id = rds.upsert(rds.brokers, broker.to_dict())

    shipper_name = r[GC.customer_name.value]
    if shipper_name.lower() in SHIPPER_NAME_DICT:
        shipper_name = SHIPPER_NAME_DICT[shipper_name.lower().strip()]
    shipper = ShipperRow(name=shipper_name)
    shipper.id = rds.upsert(rds.shippers, shipper.to_dict())

    shipment = ShipmentRow(
        broker_id=broker.id,
        broker_name=broker.name,
        lane_id=lane.id,
        shipper_id=shipper.id,
        broker_primary_reference=r[GC.broker_primary_reference.value],
        shipper_primary_reference=r[GC.shipper_primary_reference.value],
        stop_count=r[GC.stop_count.value],
        distance_miles=r[GC.distance_miles.value],
        cogs_line_haul=r[GC.cogs_line_haul.value],
        cogs_fuel=r[GC.cogs_fuel.value],
        cogs_accessorial=r[GC.cogs_accessorial.value],
        cogs_total=r[GC.cogs_total.value],
        revenue_line_haul=r[GC.revenue_line_haul.value],
        revenue_fuel=r[GC.revenue_fuel.value],
        revenue_accessorial=r[GC.revenue_accessorial.value],
        revenue_total=r[GC.revenue_total.value],
        origin_warehouse=r[GC.origin_name.value],
        destination_warehouse=r[GC.destination_name.value],
        shipment_status=r[GC.shipment_status.value],
        shipment_rank=r[GC.shipment_rank.value],
        weight=r[GC.weight.value],
        origin_open_time=r[GC.origin_open.value],
        origin_close_time=r[GC.origin_close.value],
        origin_arrival_time=r[GC.origin_arrival.value],
        origin_departure_time=r[GC.origin_departure.value],
        origin_zip_id=origin_zip.id,
        destination_open_time=r[GC.destination_open.value],
        destination_close_time=r[GC.destination_close.value],
        destination_arrival_time=r[GC.destination_arrival.value],
        destination_departure_time=r[GC.destination_departure.value],
        destination_zip_id=destination_zip.id,
        load_creation_time_utc=r[GC.load_creation.value],
        load_activation_time_utc=r[GC.load_activation.value],
        carrier_assigned_time_utc=r[GC.carrier_assigned.value],
        origin_city=origin_city,
        destination_city=destination_city,
        origin_zip=origin_zip,
        destination_zip=destination_zip,
        custom_timezones=CUSTOM_TIMEZONES_DICT[broker.name.lower()],
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )

    shipment.id, shipment_ingestion_status, modified_data = rds.shipment_upsert(
        rds.shipments, shipment.to_dict()
    )

    if shipment_ingestion_status == ShipmentIngestionStatus.updated:
        now = datetime.datetime.utcnow().replace(microsecond=0)
        date_string = now.strftime("%x %X")
        return_dict["shipment_diffs"][date_string] = modified_data

    shipmentmetadata = ShipmentMetadataRow(
        shipment_id=shipment.id,
        source_file=input_filename,
        derived_cols=return_dict["derived_cols"],
        shipment_diffs=return_dict["shipment_diffs"],
    )
    shipmentmetadata.id = rds.upsert(rds.shipmentmetadata, shipmentmetadata.to_dict())

    return shipment_ingestion_status


def ingest_rows(
    df: pd.DataFrame,
    rds: RDS,
    input_filename: str,
    return_dict: typing.Dict[str, object],
    use_tqdm=True,
):
    GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()
    return_dict["successful_rows"] = 0
    return_dict["new_shipments"] = 0
    return_dict["updated_shipments"] = 0
    return_dict["value_errors"] = collections.defaultdict(lambda: 0)
    # TODO(P1): Enable/disable tqdm with command line argument.
    iterator = tqdm(df.iterrows(), total=len(df)) if use_tqdm else df.iterrows()

    for _, r in iterator:
        logging.debug("-" * 20 + str(_ + 2) + "-" * 20)
        try:
            status = ingest_row(r, rds, input_filename, GMAPS_UTILS)
            if status == ShipmentIngestionStatus.new:
                return_dict["new_shipments"] += 1
            elif status == ShipmentIngestionStatus.updated:
                logging.info(f"Updated row number: ({_})")
                return_dict["updated_shipments"] += 1
            return_dict["successful_rows"] += 1
        except ValueError as verror:
            logging.error(f"VALUE ERROR ({_}): {verror}")
            return_dict["value_errors"][str(verror)] += 1
        except Exception as error:
            # raise error
            logging.error(f"\n\nERROR ({_}): {error}")
            logging.error(traceback.format_exc())
            logging.debug(return_dict["value_errors"])
            return_dict["value_errors"][str(error)] += 1
    if len(return_dict["value_errors"]) > 0:
        logging.error("Value errors: %s" % dict(return_dict["value_errors"]))

    # Update cache
    logging.info("Syncing and updating cache.")
    # Reload internal df and pull new cache prior to push.
    GMAPS_UTILS.save_and_upload_cache_files()


def ingest_file(input_filename: str, rds: RDS, broker: str, use_tqdm: bool = True):
    logging.info(f"Reading from {input_filename}.")
    df = read_and_map_file(input_filename, broker)
    logging.info(f"Read {len(df)} rows.")

    return_dicts = [{} for _ in range(NUM_THREADS)]
    # Split dataframe into NUM_THREADS equal chunks.
    chunks = np.array_split(df, NUM_THREADS)
    threads = []
    for i, chunk in enumerate(chunks):
        threads.append(
            threading.Thread(
                target=ingest_rows,
                args=(chunk, rds, input_filename, return_dicts[i], use_tqdm),
            )
        )
        threads[-1].start()

    value_errors = collections.defaultdict(lambda: 0)
    successful_rows = 0
    updated_shipments = 0
    new_shipments = 0
    for i, thread in enumerate(threads):
        thread.join()
        logging.info(f"Thread {i + 1} joined.")
        if "error" in return_dicts[i]:
            raise return_dicts[i]["error"]
        for key, value in return_dicts[i]["value_errors"].items():
            value_errors[key] += value
        successful_rows += return_dicts[i]["successful_rows"]
        new_shipments += return_dicts[i]["new_shipments"]
        updated_shipments += return_dicts[i]["updated_shipments"]

    return (
        dict(value_errors),
        successful_rows,
        len(df),
        new_shipments,
        updated_shipments,
    )
