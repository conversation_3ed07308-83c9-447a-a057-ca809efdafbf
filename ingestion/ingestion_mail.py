import collections
import logging
import os
import typing

import boto3
import numpy as np
from constants import INGESTION_DATA_DIR
from constants import WHITELISTED_FILTERS

from common.mail import MailAPI

# Gmail API utils
# for encoding/decoding messages in base64
# for dealing with attachement MIME types


def clean(text):
    # clean text for creating a folder
    return "".join(c if c.isalnum() else "_" for c in text)


def remove_chars(s: str, c: typing.List[str]) -> str:
    """Removes all characters in c from s."""
    for char in c:
        s = s.replace(char, "")
    return s


def check_valid_lowercase_brokername(broker_name: str) -> str:
    """
    Check if the broker name from CLI is valid by comparing it with the keys in WHITELISTED_FILTERS and return the matching broker if found, otherwise return None.
    """
    for broker in WHITELISTED_FILTERS.keys():
        if broker.lower() == broker_name.lower():
            return broker


def find_ingestion_data(
    mail_api: MailAPI, broker_name=""
) -> typing.Dict[str, typing.Dict[str, typing.Dict[str, str]]]:
    """Searches email for ingestion data, saves all data, returns directory names.

    Searches based on filters specified in constants.WHITELISTED_FILTERS.
    returns:
        A dict of dirnames indexed by broker then email filter string (from constants.py) then date.
    """
    logging.info("Searching for emails...")
    # Create a dict in a dict in a dict.
    # Example usage: dirnames_dict[broker][filter_string][date] = dirname
    dirnames_dict = collections.defaultdict(lambda: collections.defaultdict(dict))
    broker_name = check_valid_lowercase_brokername(broker_name)
    if not broker_name:
        logging.error(f"Broker name not in whitelist: {broker_name}")
        raise ValueError(f"Broker name not in whitelist: {broker_name}")

    whitelisted_filters = (
        {broker_name: WHITELISTED_FILTERS[broker_name]}
        if broker_name
        else WHITELISTED_FILTERS
    )
    for broker in whitelisted_filters:
        for filter_str in WHITELISTED_FILTERS[broker]:
            # Only consider emails from the last week.
            output_dir_prefix = os.path.join(
                INGESTION_DATA_DIR, broker, remove_chars(filter_str, "\":@. []'-*_")
            )
            filter_str = filter_str + " newer_than:7d"
            os.makedirs(output_dir_prefix, exist_ok=True)
            results = mail_api.search_messages(filter_str)
            for msg in results:
                try:
                    date, dirname = mail_api.read_message(
                        msg, output_dir_prefix=output_dir_prefix
                    )
                except FileNotFoundError:
                    # Error handled in mail.read_message function.
                    continue
                mail_api.mark_as_read_and_archive(msg)
                dirnames_dict[broker][filter_str][date] = dirname

    logging.info("Finished finding all emails.")
    return dirnames_dict


def find_all_broker_emails(service) -> typing.List[str]:
    dirnames = []
    dirnames_dict = find_ingestion_data(service)
    logging.info(f"All datafile dirnames: {dict(dirnames_dict)}")
    for broker in dirnames_dict:
        for filter_str in dirnames_dict[broker]:
            dirnames.extend(
                np.array(list(dirnames_dict[broker][filter_str].values())).flatten()
            )

    return dirnames


def find_latest_broker_emails(service, broker_name) -> typing.List[str]:
    dirnames = []
    dirnames_dict = find_ingestion_data(service, broker_name)

    for broker in dirnames_dict:
        for filter_str in dirnames_dict[broker]:
            latest_date = sorted(dirnames_dict[broker][filter_str].keys())[-1]
            dirnames.append(dirnames_dict[broker][filter_str][latest_date])

    logging.info(f"Latest datafile dirnames: {dirnames}")
    return dirnames


# Verified <EMAIL>, <EMAIL>, <EMAIL> in us-east-1.
# Verification must only be run once.
def verify_email_identity(address: str):
    ses_client = boto3.client("ses", region_name="us-east-1")
    response = ses_client.verify_email_identity(EmailAddress=address)
    logging.info(response)
