import argparse
import datetime
import logging
import multiprocessing
import os
import sys
import time
import traceback
import typing
from os import path
from os.path import dirname as pdir

import dateutil.tz
import ingestion_mail
import numpy as np
import pandas as pd
from constants import INGESTION_DATA_DIR
from constants import JOB_TIMEOUT_HOURS
from constants import LOW_INGESTION_SUCCESS_RATE_THRESHOLD
from constants import OUR_EMAIL
from scorer import scorer
from tqdm import tqdm

from common import init_main

PROJECT_ROOT = init_main.initialize()

import ingestion
from common import mail
from common import s3_cache_file
from common.constants import AURORA_DB_DEV_NAME
from common.constants import AURORA_DB_NAME

# Ensure imports work from root level directory.

sys.path.append(pdir(pdir(path.abspath(__file__))))

parser = argparse.ArgumentParser()


def is_valid_file(f, parser):
    if not os.path.exists(f):
        parser.error(f"The file {f} does not exist!")
    else:
        return f


def percentage(numerator: int, denominator: int) -> float:
    return round(numerator / denominator * 100)


parser.add_argument(
    "-i",
    "--input_filenames",
    nargs="+",
    help="Input broker data file to ingest.",
    metavar="FILE",
    type=lambda f: is_valid_file(f, parser),
)

parser.add_argument(
    "-v",
    "--verbose",
    help="Level of verbosity to log from set DEBUG|INFO|WARN|ERROR.",
    default="INFO",
)
parser.add_argument(
    "-l",
    "--log_to_stdout",
    action="store_true",
    help="Whether to log to console output (stdout).",
)
parser.add_argument(
    "-s", "--scorer_only", action="store_true", help="Whether to only run the scorer."
)
parser.add_argument(
    "--ingestion_only", action="store_true", help="Whether to only run the ingestion."
)
parser.add_argument(
    "--dev_db",
    action="store_true",
    help="Whether to ingest to the dev db. DO NOT REMOVE WHEN MANUALLY RUNNING INGESTION.",
)
parser.add_argument("--use_tqdm", action="store_true", help="Whether to use tqdm.")
parser.add_argument(
    "--ingest_all_emails",
    action="store_true",
    help="Whether to ingest all emails from brokers, not just last.",
)
# Argument for timeout time 5 hours default value.
parser.add_argument(
    "--timeout", type=int, default=JOB_TIMEOUT_HOURS, help="Job timeout time in hours."
)
parser.add_argument(
    "--broker_name", default=None, help="Specifies which broker to run ingestion for"
)
parser.add_argument(
    "--create_tables",
    action="store_true",
    help="Whether to create tables using sqlalchemy.",
)
parser.add_argument(
    "--dry_mode", action="store_true", help="Read only mode, does not update to RDS"
)

args = parser.parse_args()

# TODO(P2): Consider using multi-processing in order to speed up ingestion.


def fmt_timedelta(d: datetime.timedelta) -> str:
    """Returns formatted timedelta."""
    if type(d) is not datetime.timedelta:
        return d
    if d.days > 0:
        return "{}d{}h{}m{}s".format(
            d.days, d.seconds // 3600, (d.seconds // 60) % 60, d.seconds % 60
        )
    elif d.seconds > 3600:
        return "{}h{}m{}s".format(
            d.seconds // 3600, (d.seconds // 60) % 60, d.seconds % 60
        )
    elif d.seconds > 60:
        return f"{(d.seconds // 60) % 60}m{d.seconds % 60}s"
    else:
        return f"{d.seconds}s"


def set_up_logging() -> typing.Tuple[str, str]:
    # Set up logging. Create logs directory if it doesn't exist.
    if not os.path.isdir("logs"):
        os.makedirs("logs")

    numeric_level = getattr(logging, args.verbose.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError("Invalid log level: %s" % args.verbose)
    now_str = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H-%M-%S")
    ingestion_log_filename = "{}/ingestion_{}.log".format("logs", now_str)
    # TODO(P1): Remove scorer log.
    scorer_log_filename = "{}/scorer_{}.log".format("logs", now_str)
    log_fmt_str = "%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s"

    logging.basicConfig(
        filename=ingestion_log_filename,
        format=log_fmt_str,
        level=numeric_level,
        datefmt="%H:%M:%S",
    )

    logging.info("info")
    logging.debug("debug")
    logging.warning("warning")
    logging.error("error")

    root_logger = logging.getLogger()
    if args.log_to_stdout:
        console_handler = logging.StreamHandler()
        root_logger.addHandler(console_handler)

    # Set sqlalchemy logging to ERROR.
    # logging.getLogger('sqlalchemy.engine').setLevel(logging.ERROR)

    return ingestion_log_filename, scorer_log_filename


def consolidate_all_files():
    mail_api = mail.MailAPI()
    dirnames_dict = ingestion_mail.find_ingestion_data(mail_api)
    for broker in dirnames_dict:
        df = pd.DataFrame()
        dirnames = np.array(list(dirnames_dict[broker].values())).flatten()
        for input_dirname in tqdm(dirnames):
            files = os.listdir(input_dirname)
            if len(files) > 1:
                logging.error(f"More than one file in {input_dirname}")
                raise AssertionError(f"More than one file in {input_dirname}")
            if len(files) == 0:
                logging.error(f"No files in {input_dirname}")
                raise AssertionError(f"No files in {input_dirname}")
            input_filename = os.path.join(input_dirname, files[0])
            try:
                data_to_append = ingestion.read_and_map_file(input_filename, broker)
                df = pd.concat([df, data_to_append], ignore_index=True)

            except Exception as e:
                print(f"\n\n\n{e}")

        df.drop_duplicates(subset="Broker Primary Reference", keep="last").to_csv(
            "data\\analytics\\"
            + dirnames[0].split("\\")[1].replace(" ", "_").lower()
            + "_all.csv"
        )


def upload_file_to_s3(
    s3_instance: s3_cache_file.S3, local_file_path: str, source_directory: str
):
    """
    Uploads broker file in the source_directory and its nested subdirectories
    to the S3 bucket associated with the provided S3 instance, overwriting
    the existing file.

    :param s3_instance: An instance of the S3 class, initialized with the desired bucket.
    :param local_file_path: The local file path to be uploaded to S3.
    :param source_directory: The local directory containing the broker file to be uploaded to S3.
    """
    s3_key = os.path.relpath(local_file_path, source_directory)
    try:
        s3_instance.upload(local_file_path, s3_key)
        logging.info("File %s uploaded.", s3_key)
    except Exception as upload_error:
        logging.error("Failed to upload %s due to %s.", local_file_path, upload_error)


def upload_directory_to_s3(s3_instance: s3_cache_file.S3, source_directory: str):
    """
    Uploads all files in the source_directory and its nested subdirectories
    to the S3 bucket associated with the provided S3 instance, overwriting
    existing files.

    :param s3_instance: An instance of the S3 class, initialized with the desired bucket.
    :param source_directory: The local directory containing files to be uploaded to S3.
    """
    for root, _, files in os.walk(source_directory):
        for file in files:
            local_file_path = os.path.join(root, file)
            s3_key = os.path.relpath(local_file_path, source_directory)

            try:
                s3_instance.upload(local_file_path, s3_key)
                logging.info("File %s uploaded.", s3_key)
            except Exception as upload_error:
                logging.error(
                    "Failed to upload %s due to %s.", local_file_path, upload_error
                )


# TODO(P1): Separate out the scoring logic into a separate file.
def main():
    job_start_time = datetime.datetime.now(datetime.timezone.utc)
    # Check if current date is in daylight savings time for central time.
    if datetime.datetime.now(
        dateutil.tz.gettz("America/Chicago")
    ).dst() != datetime.timedelta(0):
        central_tz = "CDT"
    else:
        central_tz = "CST"

    job_start_time_central = (
        datetime.datetime.now(tz=dateutil.tz.gettz("US/Central")).strftime("%x %H:%M ")
        + central_tz
    )

    if not args.dev_db:
        print(
            "WARNING: Are you sure you want to run ingestion on the production database?\n"
            + "If not, please CTRL+C then run the ingestion script with the --dev_db flag. Waiting 60 seconds..."
        )
        # Wait for 60 seconds for user to consider.
        time.sleep(60)

    dev_str = "[Dev]" if args.dev_db else ""
    broker_str = f"[{args.broker_name}]" if args.broker_name else ""
    email_header = "[Auto Ingestion Report]{}{} {}.".format(
        broker_str, dev_str, job_start_time_central
    )
    ingestion_log_filename, scorer_log_filename = set_up_logging()

    mail_report_attachments = [os.path.join(PROJECT_ROOT, "ingestion", "constants.py")]
    aurora_db = AURORA_DB_DEV_NAME if args.dev_db else AURORA_DB_NAME
    rds = ingestion.RDS(
        aurora_db, create_tables=args.create_tables, dry_mode=args.dry_mode
    )

    logging.info(f"Args: {args}")

    # TODO(P0): Add this flow to the below bit, not separate.
    if args.input_filenames:
        for input_filename in args.input_filenames:
            logging.info(input_filename)
            ingestion.ingest_file(
                input_filename, rds, args.broker_name, use_tqdm=args.use_tqdm
            )
        sys.exit(0)

    logging.info("No input files specified. Checking mail...")
    # Authenticate gmail account.
    mail_api = mail.MailAPI()
    all_value_errors = {}

    try:
        # TODO(P2): Consider using multi-processing in order to speed up ingestion.
        # Count how many successful rows and total rows were ingested per input file.
        mail_report_attachments.append(ingestion_log_filename)
        if not args.scorer_only:
            if args.ingest_all_emails:
                dirnames = ingestion_mail.find_all_broker_emails(mail_api)
            else:
                dirnames = ingestion_mail.find_latest_broker_emails(
                    mail_api, args.broker_name
                )

            total_success_count = 0
            total_row_count = 0
            total_new_shipments = 0
            total_updated_shipments = 0
            success_counts_by_file = {}
            row_counts_by_file = {}
            new_loads_by_file = {}
            updated_loads_by_file = {}
            ingestion_times_by_file = {}
            logging.info("Ingesting...")
            start_total_ingestion_time = datetime.datetime.now(datetime.timezone.utc)

            for input_dirname in dirnames:
                files = os.listdir(input_dirname)
                if len(files) > 1:
                    logging.error(f"More than one file in {input_dirname}")
                    raise AssertionError(f"More than one file in {input_dirname}")
                if len(files) == 0:
                    logging.error(f"No files in {input_dirname}")
                    raise AssertionError(f"No files in {input_dirname}")

                input_filename = os.path.join(
                    input_dirname,
                    files[0].decode() if isinstance(files[0], bytes) else files[0],
                )
                # upload latest broker file to s3
                upload_file_to_s3(
                    s3_cache_file.S3("raw-broker-data"),
                    input_filename,
                    INGESTION_DATA_DIR,
                )

                # TODO(P1): Upload input filename to S3.
                start_ingestion_time = datetime.datetime.now(datetime.timezone.utc)
                (
                    all_value_errors[input_filename],
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    new_loads_by_file[input_filename],
                    updated_loads_by_file[input_filename],
                ) = ingestion.ingest_file(
                    input_filename, rds, args.broker_name, use_tqdm=args.use_tqdm
                )
                total_success_count += success_counts_by_file[input_filename]
                total_row_count += row_counts_by_file[input_filename]
                total_new_shipments += new_loads_by_file[input_filename]
                total_updated_shipments += updated_loads_by_file[input_filename]
                ingestion_times_by_file[input_filename] = (
                    datetime.datetime.now(datetime.timezone.utc) - start_ingestion_time
                )

                logging.info(
                    "Successfully ingested %d/%d rows from %s in %d seconds."
                    % (
                        success_counts_by_file[input_filename],
                        row_counts_by_file[input_filename],
                        input_filename,
                        ingestion_times_by_file[input_filename].total_seconds(),
                    )
                )
            total_ingestion_time = (
                datetime.datetime.now(datetime.timezone.utc)
                - start_total_ingestion_time
            )
            logging.info("Finished ingestions.")

            if total_row_count == 0:
                logging.error("No rows were ingested.")
                raise ValueError("No rows were ingested.")

        scoring_time = "NA (not run)"
        if not args.ingestion_only:
            logging.info("Scoring...")
            start_scoring_time = datetime.datetime.now(datetime.timezone.utc)
            # mail_report_attachments.append(scorer_log_filename)
            scorer_object = scorer.Scorer(aurora_db, scorer_log_filename)
            # Choose how to group shipments for cost score.
            scorer_object.score_all_shipments_by_lane()
            scoring_time = (
                datetime.datetime.now(datetime.timezone.utc) - start_scoring_time
            )
            logging.info("Finished scoring.")
        job_time = datetime.datetime.now(datetime.timezone.utc) - job_start_time

        logging.info("Mailing...")
        low_success_rate_alert_flag = False
        mail_body = f"Ingestion{dev_str} ran successfully in {job_time}. "
        if not args.scorer_only:
            mail_body += f"Ingestion took {total_ingestion_time}. \n"
            mail_body += f"Total new loads/shipments: {total_new_shipments}.\n"
            mail_body += "Total updated loads/shipments: {}.\n".format(
                total_updated_shipments
            )
            mail_body += "Invalid rows and runtimes are listed below by file.\n\n"
        if not args.ingestion_only:
            mail_body += f"Scoring took {scoring_time}. \n"

        for input_filename in all_value_errors:
            ingestion_success_rate = percentage(
                success_counts_by_file[input_filename],
                row_counts_by_file[input_filename],
            )
            mail_body += f"{input_filename}:\n"
            if all_value_errors[input_filename]:
                for k, v in all_value_errors[input_filename].items():
                    mail_body += f"\t{k}: {v}\n"
            if ingestion_success_rate < LOW_INGESTION_SUCCESS_RATE_THRESHOLD:
                low_success_rate_alert_flag = True
                mail_body += "\tIngested {}/{} rows ({}%), resulting low ingestion percentage.\n".format(
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    ingestion_success_rate,
                )
            else:
                mail_body += "\tSuccessfully ingested {}/{} rows ({}%).\n".format(
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    ingestion_success_rate,
                )
            mail_body += "\tIngestion time: {}.\n\n".format(
                fmt_timedelta(ingestion_times_by_file[input_filename])
            )

        if not args.scorer_only and total_success_count == total_row_count:
            mail_body += "No invalid rows! 🙂\n"
        if args.scorer_only:
            mail_api.send_email_aws(
                OUR_EMAIL,
                ["<EMAIL>"],
                f"[Scorer]{email_header}",
                mail_body,
                mail_report_attachments,  # Scorer log files are typically too big to send.
            )
        else:
            if low_success_rate_alert_flag:
                email_header = "[Auto Ingestion Failure Report]{}{} {}.".format(
                    broker_str, dev_str, job_start_time_central
                )
                failure_mail_body = (
                    "Alert email due to low ingestion percentage of files.\n"
                    + mail_body
                )
                mail_api.send_email_aws(
                    OUR_EMAIL,
                    ["<EMAIL>"],
                    email_header,
                    failure_mail_body,
                    mail_report_attachments,
                )
            else:
                mail_api.send_email_aws(
                    OUR_EMAIL,
                    ["<EMAIL>"],
                    "{} {}/{} ({}%)".format(
                        email_header,
                        total_success_count,
                        total_row_count,
                        percentage(total_success_count, total_row_count),
                    ),
                    mail_body,
                    mail_report_attachments,
                )
        logging.info("Finished mailing.")
    except Exception as e:
        logging.error(f"{type(e).__name__}: {e}")
        email_header = "[Auto Ingestion Failure Report]{}{}{} {}.".format(
            "[Scorer]" if args.scorer_only else "",
            broker_str,
            dev_str,
            job_start_time_central,
        )
        mail_api.send_email_aws(
            OUR_EMAIL,
            ["<EMAIL>"],
            f"{email_header}",
            '{} failed with severe error "{}": {}\n\nError:\n\ttype: "{}"\n\tvalue: "{}"\n\ttraceback:\n{}\nCheck attached log.'.format(
                email_header,
                type(e).__name__,
                e,
                sys.exc_info()[0],
                sys.exc_info()[1],
                traceback.format_exc(),
            ),
            mail_report_attachments,
        )
        logging.error("Mailed error report.")


if __name__ == "__main__":
    mail_api = mail.MailAPI()
    # Start bar as a process
    email_header = "Auto Ingestion Dev" if args.dev_db else "Auto Ingestion"
    p = multiprocessing.Process(target=main)
    p.start()

    # Wait for args.timeout hours or until process finishes
    p.join(args.timeout * 60 * 60)

    # If thread is still active
    if p.is_alive():
        timeout_str = fmt_timedelta(datetime.timedelta(hours=args.timeout))
        logging.error(f"Job timed out after {timeout_str} seconds.")
        mail_api.send_email_aws(
            ["<EMAIL>"],
            f"[{email_header} Failure Report]",
            f"Ingestion timed out after {timeout_str}.",
            os.listdir("logs"),
        )

        # Terminate - may not work if process is stuck for good
        p.terminate()
        # OR Kill - will work for sure, no chance for process to finish nicely however
        # p.kill()

        p.join()

# Sample commands for running locally:
# python main.py --dev_db --ingestion_only --broker_name "Sage Freight" --use_tqdm --dry_mode
# python main.py --dev_db --scorer_only --use_tqdm
