## Evaluation
`python evaluate.py` evaluates all models. In the future the file will be configurable, but for now please edit the source code.

## Adding Models
Models are kept under the `model/` directory. The models must be in an object
which trains when instantiated. They must have a `get_model()`. function
which returns a _trained_ model. All other helper functions can be anything.

The `get_model()` function must return a function which takes in two words and
outputs their similarity. For example:

```
class DumbModel:
    def __init__(self):
        pass

    def get_model(self):
        def model(word1, word2):
            return word1 == word2
        return model
```

Note that the such a simple model can be defined in the `get_model()`
function itself. With machine learning approaches, this is much less likely and
will likely be closer to `return model.predict`.

## Results
Evaluated on `evaluation_data.csv`.
- edit distance: 69% accuracy
- tfidf cos: 46% accuracy
