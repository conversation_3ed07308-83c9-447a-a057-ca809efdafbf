import datetime
import typing

import pandas as pd
import pytz
import uszipcode


def normalize_time(zipcode: str, time: datetime.datetime) -> datetime.datetime:
    """Normalize a time to UTC.

    :param zipcode: Zipcode of desired timezone.
    :param time: Time normalize to UTC.
    :return: Time normalized to UTC.
    """
    zip_full = search.by_zipcode(zipcode)
    tz = pytz.timezone("US/" + zip_full.timezone)
    return tz.normalize(tz.localize(time)).astimezone(pytz.utc).replace(tzinfo=None)


def process_row(row: pd.Series) -> typing.Tuple[dict, dict]:
    """Processes raw broker data into our golden columns.

    :param row: Raw broker data row as pandas Series.
    :return: Dict of golden column values, ID. Timedelta values are
    in seconds, percentage values are floats 0-1. For performance to index, positive means more expensive. For
    OTP/OTD, positive means late.
    """
    cleaned_data = {
        "Performance to Index": (row["COGS Total"] - DAT) / DAT,
        "Gross Profit Margin": (row["Revenue Total"] - row["COGS Total"])
        / row["Revenue Total"],
        "External Lead Time": (
            normalize_time(row["Origin Zip"], row["Origin Close D/T"])
            - row["Load Creation D/T UTC"]
        ).total_seconds(),
        "Internal Lead Time": (
            row["Load Activation D/T UTC"] - row["Load Creation D/T UTC"]
        ).total_seconds(),
        "Pre-book Time": (
            normalize_time(row["Origin Zip"], row["Origin Close D/T"])
            - row["Carrier Assigned D/T UTC"]
        ).total_seconds(),
        "OTP": (row["Origin Arrival D/T"] - row["Origin Close D/T"]).total_seconds(),
        "OTD": (
            row["Destination Arrival D/T"] - row["Destination Close D/T"]
        ).total_seconds(),
    }
    identifiers = {
        "Load ID": row["Customer Primary Refernce"],
        "Customer Name": row["Customer Name"],
        "Business Unit": row["Business Unit"],
    }
    return identifiers, cleaned_data


if __name__ == "__main__":
    # Setup code.
    search = uszipcode.SearchEngine(simple_zipcode=True)
    GOLDEN_COLUMNS = (
        "Performance to Index",
        "Gross Profit Margin",
        "External Lead Time",
        "Internal Lead Time",
        "Pre-book Time",
        "OTP",
        "OTD",
    )
    DAT = 500  # Arbitrary constant.
    df = pd.read_excel("../data/Synthetic Data.xlsx")

    for _, r in df.iterrows():
        ids, cleaned = process_row(r)
        # TODO(vivek, jr): Store cleaned identifiers, data.
