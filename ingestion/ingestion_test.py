import os
import unittest

import pandas as pd
from pandas.testing import assert_frame_equal
from parameterized import parameterized
from ingestion.ingestion import read_input_file

import ingestion


def get_relative_path(filename):
    current_dir = os.path.dirname(os.path.realpath(__file__))
    relative_path = os.path.join(current_dir, "ingestion_unittest_data", filename)
    return relative_path


class TestPandasDataFrames(unittest.TestCase):
    type_dict = {
        "Broker Name": str,
        "Customer Name": str,
        "Broker Primary Reference": str,
        "Shipper Primary Reference": str,
        "Shipment Mode": str,
        "Equipment Type": str,
        "Origin Facility Name": str,
        "Destination Facility Name": str,
        "Origin City": str,
        "Destination City": str,
        "Origin State": str,
        "Destination State": str,
        "Origin Zip": str,
        "Destination Zip": str,
        "Origin Country": str,
        "Destination Country": str,
        "Shipment Status": str,
        "Shipment Rank": str,
        "Origin Appt Open": str,
        "Destination Appt Open": str,
        "Origin Appt Close": str,
        "Destination Appt Close": str,
        "Origin Carrier Arrival": str,
        "Destination Carrier Arrival": str,
        "Origin Carrier Departure": str,
        "Destination Carrier Departure": str,
        "Load Creation Date/Time": str,
        "Load Activation Date/Time": str,
        "Final Carrier Assigned Date/Time": str,
    }

    @parameterized.expand(
        [
            [
                get_relative_path("first_row_empty_line.xlsx"),
                type_dict,
                pd.read_excel(
                    get_relative_path("first_row_empty_line_corrected.xlsx"),
                    dtype=type_dict,
                ),
            ],
        ],
    )
    def test_read_input_file(
        self, input_filename: str, type_dict: dict, df: pd.DataFrame
    ):
        assert_frame_equal(read_input_file(input_filename, type_dict), df)


class TestDatetimeConversion(unittest.TestCase):
    def setUp(self):
        self.datetime_strings = [
            "2023-07-17 18:00:00-05:00",
            "2023-05-11 18:00:00-05:00",
            "2023-06-08 11:30:00-05:00",
            "2023-07-10 18:00:00-05:00",
            "2023-07-31 18:00:00-05:00",
            "2023-06-21 18:00:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-06-21 18:30:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-07-26 18:00:00-07:00",
            "2023-04-28 20:00:00-07:00",
            "2023-07-26 18:00:00-07:00",
        ]

    def test_datetime_conversion(self):
        # Apply pd.to_datetime() to the datetime_strings
        series = pd.Series(self.datetime_strings)
        converted_datetimes = pd.to_datetime(series, format="mixed", errors="coerce")

        # Define the expected output (example values)
        expected_output = [
            "2023-07-17 18:00:00-05:00",
            "2023-05-11 18:00:00-05:00",
            "2023-06-08 11:30:00-05:00",
            "2023-07-10 18:00:00-05:00",
            "2023-07-31 18:00:00-05:00",
            "2023-06-21 18:00:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-06-21 18:30:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-07-26 18:00:00-07:00",
            "2023-04-28 20:00:00-07:00",
            "2023-07-26 18:00:00-07:00",
        ]

        # Convert expected_output to Timestamps
        expected_output = pd.to_datetime(pd.Series(expected_output))

        # Compare the converted_datetimes with the expected_output
        self.assertTrue(converted_datetimes.equals(expected_output))


if __name__ == "__main__":
    unittest.main()
