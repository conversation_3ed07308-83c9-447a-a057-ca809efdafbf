import collections
import os

import pandas as pd
import pytz
from mapping.column_mapping import GoldenColumns

from common import credentials
from common import s3_cache_file
from common.constants import BrokerNames
from common.constants import construct_named_tuple
from common.constants import PROJECT_ROOT

GC = GoldenColumns
CREDENTIALS = credentials.Credentials()


# ROWS CONSTANTS
# TODO(P0): Invoke the pull manually, since it triggers on import which can be dangerous.
# It also affects the unit test.
NA_STATES_PROVINCES = s3_cache_file.S3CacheFile(
    "ingestion-api-cache", "data/na_states_provinces.csv", idx=["State/Province"]
)
NA_STATES_PROVINCES.pull()

MIN_SHIPMENT_WEIGHT = 1000
MAX_SHIPMENT_WEIGHT = 55000
MAX_SHIPMENT_MARGIN = 15000

CountryCode = construct_named_tuple("CountryCode", ["US", "CA", "MX"])
COUNTRY_CODE_DICT = {
    "us": CountryCode.us,
    "ca": CountryCode.ca,
    "canada": CountryCode.ca,
    "mx": CountryCode.mx,
    "usa": CountryCode.us,
    "can": CountryCode.ca,
    "mex": CountryCode.mx,
    "united states": CountryCode.us,
}
ShipmentMode = construct_named_tuple(
    "ShipmentMode",
    ["TL", "LTL", "PARTIAL", "SMALL PARCEL", "IMDL", "AIR", "OCEAN", "DRAYAGE"],
)
SHIPMENT_MODE_DICT = {
    "tl": ShipmentMode.tl,
    "ltl": ShipmentMode.ltl,
    "partial": ShipmentMode.partial,
    "small parcel": ShipmentMode.small_parcel,
    "imdl": ShipmentMode.imdl,
    "air": ShipmentMode.air,
    "ocean": ShipmentMode.ocean,
    "drayage": ShipmentMode.drayage,
    "truckload": ShipmentMode.tl,
    # MoLo
    "truck": ShipmentMode.tl,
    # Echo
    "partial load": ShipmentMode.partial,
    # MX Solutions
    "vr": ShipmentMode.tl,
    "r": ShipmentMode.tl,
    "v": ShipmentMode.tl,
    "tord": ShipmentMode.tl,
    # Forsla
    "ftl": ShipmentMode.tl,
    # Loadsmart
    "ftl": ShipmentMode.tl,
    "ptl": ShipmentMode.partial,
    "vltl": ShipmentMode.tl,
}

# TODO(P0): Keep RawShipmentStatus in DB.
ShipmentStatus = construct_named_tuple(
    "ShipmentStatus",
    [
        "Delivered",
        "In Transit",
        "Loading",
        "Unloading",
        "Uncovered",
        "Covered",
        "Dispatched",
        "Tendered",
        "Canceled",
    ],
)
SHIPMENT_STATUS_DICT = {
    "delivered": ShipmentStatus.delivered,
    "invoiced": ShipmentStatus.delivered,
    "in transit": ShipmentStatus.in_transit,
    "unloading": ShipmentStatus.unloading,
    "uncovered": ShipmentStatus.uncovered,
    "loading": ShipmentStatus.loading,
    "covered": ShipmentStatus.covered,
    "dispatched": ShipmentStatus.dispatched,
    # BlueGrace
    "booked": ShipmentStatus.covered,
    # Nolan
    "booked": ShipmentStatus.covered,
    "onsite-shipper": ShipmentStatus.loading,
    "out for delivery": ShipmentStatus.in_transit,
    "onsite-consignee": ShipmentStatus.unloading,
    "delivered final": ShipmentStatus.delivered,
    "picked-up": ShipmentStatus.in_transit,
    "out for delivery": ShipmentStatus.in_transit,
    "paid": ShipmentStatus.delivered,
    "pending payment": ShipmentStatus.delivered,
    "picked-up": ShipmentStatus.in_transit,
    # Uber
    "driver_arrived_at_pickup": ShipmentStatus.loading,
    "delivery_completed_and_unpaid": ShipmentStatus.delivered,
    "delivery_completed_and_paid": ShipmentStatus.delivered,
    "load_enroute": ShipmentStatus.in_transit,
    "driver_arrived_at_dropoff": ShipmentStatus.unloading,
    # Coyote
    "available": ShipmentStatus.uncovered,
    "loading begin": ShipmentStatus.loading,
    "picked up": ShipmentStatus.in_transit,
    "unloading begin": ShipmentStatus.unloading,
    "terminated": ShipmentStatus.in_transit,
    "arrdest": ShipmentStatus.in_transit,
    "traindepart": ShipmentStatus.in_transit,
    # MoLo Solutions
    "open": ShipmentStatus.uncovered,
    "departed stop": ShipmentStatus.in_transit,
    "complete": ShipmentStatus.delivered,
    "driver assigned": ShipmentStatus.covered,
    # PartnerShip
    "enroute": ShipmentStatus.in_transit,
    "on site": ShipmentStatus.unloading,
    # Sage Freight
    "appt": ShipmentStatus.uncovered,
    "avl": ShipmentStatus.uncovered,
    "comit": ShipmentStatus.covered,
    "assgn": ShipmentStatus.covered,
    "disp": ShipmentStatus.dispatched,
    "osp": ShipmentStatus.loading,
    "pckup": ShipmentStatus.in_transit,
    "enrt": ShipmentStatus.in_transit,
    "osd": ShipmentStatus.unloading,
    "dlvrd": ShipmentStatus.delivered,
    "invau": ShipmentStatus.delivered,
    "tonu": ShipmentStatus.delivered,
    "paid": ShipmentStatus.delivered,
    "comp": ShipmentStatus.delivered,
    "invcd": ShipmentStatus.delivered,
    "completed": ShipmentStatus.delivered,
    "billing": ShipmentStatus.delivered,
    "tracking": ShipmentStatus.in_transit,
    # Arrive Logistics
    "pickedup": ShipmentStatus.in_transit,
    "finished": ShipmentStatus.delivered,
    "at delivery": ShipmentStatus.unloading,
    # Pathmark Transportation
    "finshed": ShipmentStatus.delivered,
    "pickup - a": ShipmentStatus.in_transit,
    # Echo Global Logistics
    "delivered": ShipmentStatus.delivered,
    "unloading": ShipmentStatus.unloading,
    # MX Solutions
    "delivered": ShipmentStatus.delivered,
    "loaded": ShipmentStatus.in_transit,
    "consignee": ShipmentStatus.in_transit,
    "tord": ShipmentStatus.delivered,
    # Forsla
    "invoice: ready": ShipmentStatus.delivered,
    "assigned": ShipmentStatus.covered,
    "departed pickup location": ShipmentStatus.in_transit,
    # Transfix
    "tendered": ShipmentStatus.tendered,
    "assigned": ShipmentStatus.covered,
    "invoice_sent": ShipmentStatus.delivered,
    "canceled": ShipmentStatus.canceled,
    # Loadsmart
    "accounting-review": ShipmentStatus.delivered,
    "at-delivery": ShipmentStatus.unloading,
    # Capstone
    "load": ShipmentStatus.in_transit,
    "del": ShipmentStatus.unloading,
    "delv": ShipmentStatus.delivered,
}

ShipmentRank = construct_named_tuple(
    "ShipmentRank", ["Primary", "Spot", "Backup", "Cost Plus"]
)
SHIPMENT_RANK_DICT = {
    # MoLo Solutions
    "primary": ShipmentRank.primary,
    "spot": ShipmentRank.spot,
    "backup": ShipmentRank.backup,
    "cost plus": ShipmentRank.cost_plus,
    "committed": ShipmentRank.primary,
    #
    "contract - secondary": ShipmentRank.backup,
    "rateonfile": ShipmentRank.backup,
    "brokerage": ShipmentRank.backup,
    # Echo Global Logistics
    "award": ShipmentRank.primary,
    "contract": ShipmentRank.primary,
    "cost_plus": ShipmentRank.cost_plus,
    "routing guide": ShipmentRank.backup,
    "back up": ShipmentRank.backup,
    # Transfix
    "backup/published": ShipmentRank.backup,
    # Arrive Logistics
    "contract - primary": ShipmentRank.primary,
    # Uber Freight
    "rateonfile": ShipmentRank.backup,
    # Nolan Transportation Group
    "secondary": ShipmentRank.backup,
    # Loadsmart
    "contracted": ShipmentRank.primary,
    # Capstone
    "p": ShipmentRank.primary,
}

# ROWS UTIL CONSTANTS
TONU_LCD = 25
TONU_MIN = 100
TONU_MAX = 250
USD_PER_MI_MIN = 0.5

ShipmentClass = construct_named_tuple(
    "ShipmentClass", ["accessorial_tonu", "not_shipment", "accessorial", "canonical"]
)


def set_open_time_range(t: pd.Timestamp) -> pd.Timestamp:
    """Set originOpenTime and destinationOpenTime to 0:00:00 when FCFS is part of time string"""
    return (
        t.replace(hour=0, minute=0, second=0, microsecond=0)
        if t.hour == t.minute == t.second == t.microsecond == 0
        else t
    )


def set_close_time_range(t: pd.Timestamp) -> pd.Timestamp:
    """Set and originCloseTime and destinationCloseTime to 23:59:59 when FCFS is part of time string"""
    return (
        t.replace(hour=23, minute=59, second=59, microsecond=0)
        if t.hour == t.minute == t.second == t.microsecond == 0
        else t
    )


def ignore_time(t: pd.Timestamp) -> pd.Timestamp:
    """Replace time to 0:00:00 and tzinfo with None value"""
    return t.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)


def time_without_tzinfo(t: pd.Timestamp) -> pd.Timestamp:
    """Replace tzinfo with None value"""
    return t.replace(tzinfo=None) if not pd.isna(t) else pd.NA


# Mapping of Brokers to column handler functions for data transformations.
# Link to Google Doc for specific information on Broker Status:
# https://docs.google.com/document/d/1EF7w5vUpXbh_UA5e_CBatVqclh8oqzkHv9EdkZBaxB0/edit#heading=h.2lsdv4o2agdm
BROKER_COLUMN_HANDLERS = {
    # Arrive Logistics Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.arrive_logistics: {
        "originOpenTime": time_without_tzinfo,
        "originCloseTime": time_without_tzinfo,
        "originArrivalTime": time_without_tzinfo,
        "originDepartureTime": time_without_tzinfo,
        "destinationOpenTime": time_without_tzinfo,
        "destinationCloseTime": time_without_tzinfo,
        "destinationArrivalTime": time_without_tzinfo,
        "destinationDepartureTime": time_without_tzinfo,
        "loadCreationTime": time_without_tzinfo,
        "loadActivationTime": time_without_tzinfo,
        "carrierAssignedTime": time_without_tzinfo,
    },
    # Integrity Express Logistics Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.integrity_express_logistics: {
        "originCloseTime": ignore_time,
        "destinationCloseTime": ignore_time,
    },
    # MX Solutions Broker: set originOpenTime and destinationOpenTime to 0:00:00 and originCloseTime and destinationCloseTime to 23:59:59 when FCFS is part of time string
    BrokerNames.mx_solutions: {
        "originOpenTime": set_open_time_range,
        "originCloseTime": set_close_time_range,
        "destinationOpenTime": set_open_time_range,
        "destinationCloseTime": set_close_time_range,
    },
    # Loadsmart Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.loadsmart: {
        "originOpenTime": time_without_tzinfo,
        "originCloseTime": time_without_tzinfo,
        "originArrivalTime": time_without_tzinfo,
        "originDepartureTime": time_without_tzinfo,
        "destinationOpenTime": time_without_tzinfo,
        "destinationCloseTime": time_without_tzinfo,
        "destinationArrivalTime": time_without_tzinfo,
        "destinationDepartureTime": time_without_tzinfo,
    },
}

# List of Brokers with time normalization exemptions
TIME_NORMALIZATION_EXEMPT_BROKERS = [BrokerNames.arrive_logistics]

# INGESTION CONSTANTS
STRING_COLUMNS = [
    GC.broker_name,
    GC.customer_name,
    GC.business_unit,
    GC.broker_primary_reference,
    GC.shipper_primary_reference,
    GC.shipment_mode,
    GC.equipment_type,
    GC.origin_name,
    GC.destination_name,
    GC.origin_city,
    GC.destination_city,
    GC.origin_state,
    GC.destination_state,
    GC.origin_zip,
    GC.destination_zip,
    GC.origin_country,
    GC.destination_country,
    GC.shipment_status,
    GC.shipment_rank,
]
DATETIME_COLUMNS = [
    GC.origin_open,
    GC.destination_open,
    GC.origin_close,
    GC.destination_close,
    GC.origin_arrival,
    GC.destination_arrival,
    GC.origin_departure,
    GC.destination_departure,
    GC.load_creation,
    GC.load_activation,
    GC.carrier_assigned,
]
NUMERIC_COLUMNS = [
    GC.stop_count,
    GC.distance_miles,
    GC.cogs_line_haul,
    GC.revenue_line_haul,
    GC.cogs_fuel,
    GC.revenue_fuel,
    GC.cogs_accessorial,
    GC.revenue_accessorial,
    GC.cogs_total,
    GC.revenue_total,
    GC.weight,
]

ShipperNames = construct_named_tuple(
    "ShipperNames",
    [
        "Swire Coca Cola",
        "SC Johnson",
        "Reyes Holdings",
        "Campbell Soup Company",
        "Barilla America",
        "Utz Quality Foods",
        "Monster Beverage Corporation",
        "Coca Cola Southwest Beverages",
    ],
)
# TODO(P2): Use column mapping edit distance model for this.
SHIPPER_NAME_DICT = {
    # BlueGrace Logistics
    "s. c. johnson & son, inc. - tl": ShipperNames.sc_johnson,
    # Uber Freight
    "swire coca-cola - market access": ShipperNames.swire_coca_cola,
    "swire coca-cola": ShipperNames.swire_coca_cola,
    "transplace - campbell soup company - outbound": ShipperNames.campbell_soup_company,
    "transplace - campbell soup company - inbound": ShipperNames.campbell_soup_company,
    "reyes beverages, l.l.c.": ShipperNames.reyes_holdings,
    "utz quality foods llc": ShipperNames.utz_quality_foods,
    "barilla america": ShipperNames.barilla_america,
    # Sheer Transport
    "swire coca-cola usa": ShipperNames.swire_coca_cola,
    # Nolan Transportation Group
    "reyes holdings - great lakes coca-cola": ShipperNames.reyes_holdings,
    "reyes holdings - reyes beer division": ShipperNames.reyes_holdings,
    "coca-cola - swire": ShipperNames.swire_coca_cola,
    "campbell meals & beverages (outbound)": ShipperNames.campbell_soup_company,
    "campbell meals & beverages (inbound)": ShipperNames.campbell_soup_company,
    "barilla america, inc.": ShipperNames.barilla_america,
    # Coyote Logistics
    "s. c. johnson & son, inc. - blujay": ShipperNames.sc_johnson,
    "reyes - enpl": ShipperNames.reyes_holdings,
    "reyes beer division": ShipperNames.reyes_holdings,
    "reyes enpl - manual": ShipperNames.reyes_holdings,
    "reyes great lakes coca-cola distribution, l.l.c.": ShipperNames.reyes_holdings,
    "swire coca cola, usa": ShipperNames.swire_coca_cola,
    "campbell's soup – outbound": ShipperNames.campbell_soup_company,
    "utz quality foods, llc c/o lean logistics": ShipperNames.utz_quality_foods,
    "monster beverage corp / hansen beverage": ShipperNames.monster_beverage_corporation,
    # BlueGrace
    "swire coca-cola, usa tl": ShipperNames.swire_coca_cola,
    "sc johnson & son, inc. tl": ShipperNames.sc_johnson,
    # Dupre,
    "reyes coca cola bottling": ShipperNames.reyes_holdings,
    # MoLo Solutions,
    "reyes holdings": ShipperNames.reyes_holdings,
    "swire coca cola": ShipperNames.swire_coca_cola,
    "sc johnson": ShipperNames.sc_johnson,
    "campbell soup company": ShipperNames.campbell_soup_company,
    # PartnerShip LLC,
    "glcc": ShipperNames.reyes_holdings,
    # Sage Freight
    "scjohnson": ShipperNames.sc_johnson,
    # Arrive Logistics
    "campbell soup company": ShipperNames.campbell_soup_company,
    "s. c. johnson & son, inc.": ShipperNames.sc_johnson,
    "reyes holdings, l.l.c.": ShipperNames.reyes_holdings,
    "barilla america inc.": ShipperNames.barilla_america,
    # Echo Global Logistics
    "campbells meals and beverage  - tp tms": ShipperNames.campbell_soup_company,
    "great lakes coca-cola distribution, llc c/o mvfs": ShipperNames.reyes_holdings,
    "great lakes coca-cola distribution  llc c/o mvfs": ShipperNames.reyes_holdings,
    "s.c. johnson & son": ShipperNames.sc_johnson,
    "monster energy company": ShipperNames.monster_beverage_corporation,
    # MX Solutions
    "reyes / great lakes coca cola": ShipperNames.reyes_holdings,
    "coca cola southwest - ani": ShipperNames.coca_cola_southwest_beverages,
    # Ryan Transportation
    "reyes holdings": ShipperNames.reyes_holdings,
    "swire coca cola": ShipperNames.swire_coca_cola,
    "utz quality foods inc.": ShipperNames.utz_quality_foods,
    # Integrity Express Logistics
    "reyes logistic solutions llc": ShipperNames.reyes_holdings,
    # Capstone Logistics
    "reyes coca cola": ShipperNames.reyes_holdings,
    # Trailer Bridge
    "ccb swire pacific holdings": ShipperNames.swire_coca_cola,
    # Axle Logistics
    "swire pacific": ShipperNames.swire_coca_cola,
}


BROKER_NAME_DICT = {
    "mx solutions": BrokerNames.mx_solutions,
    "bluegrace logistics": BrokerNames.bluegrace_logistics,
    "arrive": BrokerNames.arrive_logistics,
    "trailer bridge, inc.": BrokerNames.trailer_bridge,
    "child logistics inc": BrokerNames.child_logistics,
}


# Columns to ignore, indexed by broker. Typically a hack to ignore faked columns.
BROKER_IGNORE_COLS = {
    BrokerNames.nolan_transportation_group: [
        GC.origin_arrival,
        GC.destination_arrival,
        GC.origin_departure,
        GC.destination_departure,
    ],
    BrokerNames.integrity_express_logistics: [
        GC.load_creation,
        GC.carrier_assigned,
    ],
    BrokerNames.forsla: [
        GC.load_activation,
        GC.carrier_assigned,
        GC.origin_open,
        GC.destination_open,
    ],
    BrokerNames.sheer_transport: [
        GC.origin_arrival,
        GC.destination_arrival,
        GC.origin_departure,
        GC.destination_departure,
    ],
}

# Key is string column name in rows.py around line 281.
# From docs.google.com/spreadsheets/d/1gLugBW_e1PqbyQFKpD3HNgcZ0g6P6OyhMzQMDVs2-Io
# If broker not specified, all timezones are assumed to be UTC.
CUSTOM_TIMEZONES_DICT = collections.defaultdict(
    lambda: {},
    {
        BrokerNames.bluegrace_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.sheer_transport.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.nolan_transportation_group.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.dupre_logistics_llc.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.partnership_llc.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.arrive_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/Chicago"),
            "loadActivationTime": pytz.timezone("America/Chicago"),
            "carrierAssignedTime": pytz.timezone("America/Chicago"),
        },
        BrokerNames.echo_global_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/Chicago"),
            "loadActivationTime": pytz.timezone("America/Chicago"),
            "carrierAssignedTime": pytz.timezone("America/Chicago"),
        },
        BrokerNames.mx_solutions.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.integrity_express_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.transfix.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.loadsmart.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.vp_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.its_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.hill_bros.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
           BrokerNames.widespread_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.openroad.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        }
    },
)

# Ensure all columns are covered exactly once.
ALL_COLUMNS = STRING_COLUMNS + DATETIME_COLUMNS + NUMERIC_COLUMNS
assert len(ALL_COLUMNS) == len(set(ALL_COLUMNS))
assert set(ALL_COLUMNS) == set(GC.__members__.values())
NUM_THREADS = 1

# RDS API CONSTANTS
NUM_UPSERT_RETRIES = 10

# MAIL CONSTANTS
# Request all access (permission to read/send/receive emails, manage the inbox, and more)
# From email
OUR_EMAIL = "<EMAIL>"
INGESTION_DATA_DIR = os.path.join(PROJECT_ROOT, "ingestion", "incoming_email_data")

# CUSTOMER DIRECT COMPANY PATTERNS
# TODO (P3): Make regex more specific after data analysis
COMPANY_REGEX_PATTERNS = {
    "albertson[']*s": "Albertsons",
    "amazon": "Amazon",
    "big.*lots": "Big Lots",
    "bj[']*s": "BJ's Wholesale Club",
    "c.*&.*s": "C&S Wholesale Grocers",
    "costco": "Costco",
    "cvs": "CVS",
    "demoulas": "Market Basket",
    "dollar.*general": "Dollar General",
    "dollar.*tree": "Dollar Tree",
    "family.*dollar": "Family Dollar",
    "jetro": "Restaurant Depot",
    "kroger": "Kroger",
    "mclane": "McLane",
    "meijer": "Meijer",
    "publix": "Publix",
    "restaurant.*depot": "Restaurant Depot",
    "rite.*aid": "Rite Aid",
    "safeway": "Safeway",
    "sam[']*s.*club": "Sam's Club",
    "shamrock": "Shamrock Foods",
    "sysco": "Sysco",
    "target": "Target",
    "unfi": "UNFI",
    "us.*foods": "US Foods",
    "wal.*mart": "Walmart",
    "winco.*foods": "WinCo Foods",
    "woodman[']*s": "Woodman's Market",
}

# MAIN CONSTANTS
JOB_TIMEOUT_HOURS = 12

LOW_INGESTION_SUCCESS_RATE_THRESHOLD = 30

WHITELISTED_FILTERS = {
    BrokerNames.bluegrace_logistics: [
        "to:<EMAIL> from:<EMAIL> subject:***AUTOMATED MESSAGE***"
    ],
    BrokerNames.uber_freight: [
        'to:<EMAIL> replyto:<EMAIL> subject:"[Auto Email] Uber Freight Rates for Truce"',
    ],
    BrokerNames.sheer_transport: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Sheer Transport]"'
    ],
    BrokerNames.nolan_transportation_group: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Nolan_Truce"'
    ],
    BrokerNames.coyote_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Coyote Logistics]"'
    ],
    BrokerNames.linehaul_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: LineHaul]"'
    ],
    BrokerNames.dupre_logistics_llc: [
        'to:<EMAIL> from:<EMAIL> subject:"Dupre Logistics LLC_REYES"'
    ],
    BrokerNames.molo_solutions: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: MoLo Solutions]"'
    ],
    BrokerNames.partnership_llc: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: PartnerShip]"'
    ],
    BrokerNames.sage_freight: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Sage Freight]"'
    ],
    BrokerNames.arrive_logistics: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Arrive Logistics"'
    ],
    BrokerNames.pathmark_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Pathmark Transportation]"'
    ],
    BrokerNames.echo_global_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Echo Global Logistics]"'
    ],
    BrokerNames.mx_solutions: [
        'to:<EMAIL> from:<EMAIL> subject:"Automated Report MRKT Truce Export Job 40279"',
        'to:<EMAIL> from:<EMAIL> subject:"Automated Report MRKT Truce Export Job 41458"',
    ],
    BrokerNames.ryan_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Ryan Transportation]"'
    ],
    BrokerNames.integrity_express_logistics: [
        'to:<EMAIL> replyto:<EMAIL> subject:"FW: [EXTERNAL] Pulse: Reyes Logistics Solutions LLC Load Data"'
    ],
    BrokerNames.forsla: ['to:<EMAIL> from:<EMAIL> subject:"Forsla"'],
    BrokerNames.transfix: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Transfix]"'
    ],
    BrokerNames.loadsmart: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Loadsmart Truce Daily Report"'
    ],
    BrokerNames.child_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Child Logistics Inc]"'
    ],
    BrokerNames.royal_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Royal Transportation]"'
    ],
    BrokerNames.schneider_brokerage: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Schneider Brokerage]"'
    ],
    BrokerNames.capstone_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Capstone]"'
    ],
    BrokerNames.trailer_bridge: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Trailer Bridge, Inc.]"'
    ],
    BrokerNames.ardent: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Ardent"'
    ],
    BrokerNames.axle_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"AXLL"'
    ],
    BrokerNames.demo_data: [
        'to:<EMAIL> from:<EMAIL> subject:"[Auto Ingestion][Demo Data]"'
    ],
    BrokerNames.vp_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: VP Logistics]"'
    ],
    BrokerNames.its_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: ITS Logistics]"'
    ],
    BrokerNames.hill_bros: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Hill Bros]"'
    ],
    BrokerNames.widespread_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Widespread Logistics]"'
    ],
    BrokerNames.openroad: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: OpenRoad]"'
    ],

}
