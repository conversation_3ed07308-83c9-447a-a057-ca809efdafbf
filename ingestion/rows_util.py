import datetime
import logging
import re

import numpy as np
import pandas as pd
import pytz
from constants import COMPANY_REGEX_PATTERNS
from constants import ShipmentClass
from constants import TONU_LCD
from constants import TONU_MAX
from constants import TONU_MIN
from constants import USD_PER_MI_MIN

from common import google_maps_utils

dt = datetime.datetime


def check_if_valid_timestamp(
    time: pd.Timestamp,
) -> pd.Timestamp:
    if pd.isna(time) or type(time) == str and time.isspace():
        return pd.NA
    if type(time) != dt and type(time) != pd.Timestamp:
        logging.error(f"Time {time} has type {type(time)}, which is not datetime.")
        raise ValueError("Type of time is not datetime.")
    return time


def is_tz_aware(time: pd.Timestamp):
    """Checks if a datetime object is timezone aware."""
    return time.tzinfo is not None and time.tzinfo.utcoffset(time) is not None


def normalize_time_given_tz(time: pd.Timestamp, tz: pytz.timezone) -> pd.Timestamp:
    """Normalize a time to UTC.

    First, localize the naive datetime object time with given timezone tz. Then, convert to UTC.
    """
    if pd.isna(time):
        return pd.NaT

    if not is_tz_aware(time):
        time = tz.localize(time)
    else:
        # Verify that time is in the correct timezone, based on given tz which comes from zip.
        if type(time.tzinfo) != type(tz):
            logging.error(f"Time {time} is not in timezone {tz}.")
            raise ValueError("Time is not in correct timezone.")

    # If timezone is localized, normalize to UTC.
    return pytz.utc.normalize(time).replace(tzinfo=None)


def normalize_time_given_zip(
    city_row,
    zip_row,
    time: pd.Timestamp,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
) -> dt:
    tz = GMAPS_UTILS.get_tz_from_zip(zip_row.zip, city_row.country)
    return normalize_time_given_tz(time, tz)


def compute_cogs_rev_err(
    line_haul: float, fuel: float, accessorial: float, total: float
) -> float:
    """Computes the COGS or revenue error for each row."""
    # If each field is pd.NA, convert to np.nan.
    line_haul = np.nan if pd.isna(line_haul) else line_haul
    fuel = np.nan if pd.isna(fuel) else fuel
    accessorial = np.nan if pd.isna(accessorial) else accessorial
    total = np.nan if pd.isna(total) else total
    # np.nansum to treat nan as 0.
    error = abs(np.nansum([line_haul, fuel, accessorial, -total]))
    return error


def is_cogs_rev_na(
    line_haul: float, fuel: float, accessorial: float, total: float
) -> bool:
    """Checks if all constituent fields or the total field is pd.NA."""
    if pd.isna(line_haul) and pd.isna(fuel) and pd.isna(accessorial):
        return True
    if pd.isna(total):
        return True
    return False


def classify_shipment(
    cogs_line_haul,
    cogs_fuel,
    cogs_accessorial,
    cogs_total,
    revenue_line_haul,
    revenue_fuel,
    revenue_accessorial,
    revenue_total,
    distance_miles,
) -> ShipmentClass:
    def is_empty(v):
        return pd.isna(v) or v == 0

    if is_empty(revenue_total) and is_empty(cogs_total):
        return ShipmentClass.not_shipment

    def is_tonu(total):
        return (
            not is_empty(total)
            and total >= TONU_MIN
            and total <= TONU_MAX
            and total % TONU_LCD == 0
        )

    if (
        (is_tonu(cogs_total) and is_empty(revenue_total))
        or (is_tonu(revenue_total) and is_empty(cogs_total))
        or (is_tonu(cogs_total) and is_tonu(revenue_total))
    ):
        return ShipmentClass.accessorial_tonu

    if is_empty(cogs_total) or is_empty(revenue_total):
        return ShipmentClass.accessorial

    if (
        is_empty(cogs_line_haul)
        and is_empty(cogs_fuel)
        and not is_empty(cogs_accessorial)
    ):
        return ShipmentClass.accessorial
    if (
        is_empty(revenue_line_haul)
        and is_empty(revenue_fuel)
        and not is_empty(revenue_accessorial)
    ):
        return ShipmentClass.accessorial

    # Cost per mile is way too low. Must be junk.
    if distance_miles == 0:
        return ShipmentClass.canonical

    if distance_miles < 0:
        raise ValueError("Distance in miles is negative.")

    if cogs_total / distance_miles < USD_PER_MI_MIN:
        return ShipmentClass.accessorial

    if revenue_total / distance_miles < USD_PER_MI_MIN:
        return ShipmentClass.accessorial

    return ShipmentClass.canonical


def detect_customer_direct(destination_name: str) -> str:
    """
    Companies that charge a fee on late delivery are designated
    customer direct, or "white glove."
    Determines if destination is a customer direct warehouse.
    """
    customer_direct_match = pd.NA

    for expr in COMPANY_REGEX_PATTERNS:
        matches = re.findall(expr, destination_name.lower())

        if len(matches) != 0:
            if not pd.isna(customer_direct_match):
                logging.error(
                    f"""Discovered multiple matches for expr {expr}:
                            {customer_direct_match}, {matches}"""
                )
                raise AssertionError("Multiple customer direct destinations found.")
            customer_direct_match = COMPANY_REGEX_PATTERNS[expr]

    return customer_direct_match


def normalize_times(
    time_columns: dict,
    custom_timezones: dict,
    origin_city_row,
    origin_zip_row,
    destination_city_row,
    destination_zip_row,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
):
    """
    Normalization on times, whether with custom_timezones or with city_row, zip_row and GMAP_UTILS
    """
    for time in time_columns.keys():
        if time in custom_timezones:
            assert "Time" in time, f"Invalid time column name: {time}"
            time_columns[time] = normalize_time_given_tz(
                time_columns[time], custom_timezones[time]
            )
        # Add times normalized to UTC based on origin/destination zip.
        elif "origin" in time:
            time_columns[time] = normalize_time_given_zip(
                origin_city_row, origin_zip_row, time_columns[time], GMAPS_UTILS
            )
        elif "destination" in time:
            time_columns[time] = normalize_time_given_zip(
                destination_city_row,
                destination_zip_row,
                time_columns[time],
                GMAPS_UTILS,
            )
    return time_columns
