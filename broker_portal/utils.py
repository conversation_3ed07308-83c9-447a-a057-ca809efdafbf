"""
Utility functions for the broker portal backend.
"""

import os
import shutil
import tempfile
from typing import Dict, List, Optional, Tuple, Union

import pandas as pd
from fastapi import UploadFile

from .config import UPLOAD_DIR
from .logger import portal_logger

# Import ingestion modules
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from ingestion import ingestion
from ingestion.mapping.column_mapping import GoldenColumns
from ingestion.mapping.ai_column_mapping import ai_column_mapping
from common.rds_api import RDS


def get_broker_upload_dir(broker_name: str) -> str:
    """
    Get the upload directory for a specific broker.

    Args:
        broker_name: Name of the broker

    Returns:
        Path to the broker-specific upload directory
    """
    broker_dir = os.path.join(UPLOAD_DIR, broker_name)
    os.makedirs(broker_dir, exist_ok=True)
    return broker_dir


async def save_upload_file(file: UploadFile, job_id: str, broker_name: str) -> str:
    """
    Save an uploaded file to the broker-specific upload directory.

    Args:
        file: The uploaded file
        job_id: Unique identifier for the job
        broker_name: Name of the broker

    Returns:
        Path to the saved file
    """
    # Get broker-specific directory
    broker_dir = get_broker_upload_dir(broker_name)

    # Create a unique filename
    filename = f"{job_id}_{file.filename}"
    file_path = os.path.join(broker_dir, filename)

    # Save the file
    with open(file_path, "wb") as f:
        shutil.copyfileobj(file.file, f)

    portal_logger.root_logger.info(f"File saved to {file_path}")
    return file_path


async def analyze_columns(file_path: str, job_id: str, broker_name: Optional[str] = None) -> Tuple[List[str], List[str], List[Dict[str, str]], Optional[Dict[str, str]]]:
    """
    Analyze columns in the uploaded file.

    Args:
        file_path: Path to the uploaded file
        job_id: Unique identifier for the job
        broker_name: Name of the broker (optional)

    Returns:
        Tuple containing:
        - Original column headers
        - Template headers (golden columns)
        - Sample data
        - AI-suggested column mapping (if available)
    """
    if broker_name:
        portal_logger.root_logger.info(f"Analyzing columns for broker: {broker_name}")
    # Read the input file
    df = ingestion.read_input_file(file_path)

    # Get original headers and template headers
    original_headers = list(df.columns)
    template_headers = [g.value for g in GoldenColumns]

    # Get sample data and convert all values to strings to avoid Pydantic validation issues
    sample_data_raw = df.head(5).to_dict(orient='records')

    # Convert all values to strings to avoid Pydantic validation issues
    sample_data = []
    for row in sample_data_raw:
        string_row = {}
        for key, value in row.items():
            if pd.isna(value):
                string_row[key] = None
            else:
                string_row[key] = str(value)
        sample_data.append(string_row)

    # Try AI-based column mapping
    ai_mapping = None
    try:
        ai_mapping = ai_column_mapping(
            original_headers=original_headers,
            template_headers=template_headers,
            sample_data=sample_data
        )
    except Exception as e:
        portal_logger.root_logger.warning(f"AI mapping failed: {e}")

    return original_headers, template_headers, sample_data, ai_mapping


async def apply_column_mapping(
    file_path: str,
    column_mapping: Dict[str, str],
    broker_name: str,
    job_id: str
) -> pd.DataFrame:
    """
    Apply column mapping to the uploaded file.

    Args:
        file_path: Path to the uploaded file
        column_mapping: Mapping from original columns to golden columns
        broker_name: Name of the broker
        job_id: Unique identifier for the job

    Returns:
        DataFrame with applied column mapping
    """
    # Read the input file
    df = ingestion.read_input_file(file_path)

    # Apply column mapping
    df = df.rename(columns=column_mapping)

    # Build type dictionary and convert types
    type_dict, date_cols = ingestion.build_df_type_dictionary(column_mapping)
    df = ingestion.read_input_file(file_path, type_dict, date_cols)
    df = df.rename(columns=column_mapping)

    # Handle broker-specific column drops
    if broker_name in ingestion.BROKER_IGNORE_COLS:
        drop_cols = ingestion.BROKER_IGNORE_COLS[broker_name]
        for col in drop_cols:
            if col.value in df.columns:
                df.drop(col.value, axis=1, inplace=True)
                portal_logger.root_logger.info(f"Dropped broker-specific column: {col.value}")

    # Ensure all required columns are present
    required_cols = {
        GoldenColumns.origin_zip,
        GoldenColumns.destination_zip,
        GoldenColumns.origin_city,
        GoldenColumns.destination_city,
        GoldenColumns.origin_state,
        GoldenColumns.destination_state,
        GoldenColumns.shipment_mode,
        GoldenColumns.equipment_type,
        GoldenColumns.customer_name,
        GoldenColumns.broker_name,
        GoldenColumns.broker_primary_reference,
        GoldenColumns.cogs_total,
        GoldenColumns.revenue_total,
        GoldenColumns.origin_close,
    }

    added_empty_cols = []
    for g in GoldenColumns:
        if g.value not in df.columns:
            if g in required_cols:
                raise ValueError(f'Missing required column "{g.value}".')
            df[g.value] = pd.NA
            added_empty_cols.append(g.value)
            portal_logger.root_logger.info(f"Added empty column: {g.value}")

    # Convert data types
    ingestion.convert_types(df, added_empty_cols)

    return df


async def ingest_data(
    file_path: str,
    broker_name: str,
    dev_db: bool,
    dry_run: bool,
    job_id: str
) -> Dict[str, Union[int, Dict[str, int]]]:
    """
    Ingest data from the uploaded file.

    Args:
        file_path: Path to the uploaded file
        broker_name: Name of the broker
        dev_db: Whether to use the development database
        dry_run: Whether to run in dry run mode
        job_id: Unique identifier for the job

    Returns:
        Dictionary containing ingestion results
    """
    from common.constants import AURORA_DB_DEV_NAME, AURORA_DB_NAME

    # Initialize RDS connection
    aurora_db = AURORA_DB_DEV_NAME if dev_db else AURORA_DB_NAME
    rds = RDS(aurora_db, create_tables=False, dry_mode=dry_run)

    # Ingest the file
    portal_logger.root_logger.info(f"Ingesting file: {file_path}")
    portal_logger.root_logger.info(f"Broker: {broker_name}")
    portal_logger.root_logger.info(f"Dev DB: {dev_db}")
    portal_logger.root_logger.info(f"Dry Run: {dry_run}")

    value_errors, successful_rows, total_rows, new_shipments, updated_shipments = ingestion.ingest_file(
        file_path, rds, broker_name, use_tqdm=False
    )

    # Return results
    return {
        "total_rows": total_rows,
        "successful_rows": successful_rows,
        "new_shipments": new_shipments,
        "updated_shipments": updated_shipments,
        "errors": value_errors
    }
