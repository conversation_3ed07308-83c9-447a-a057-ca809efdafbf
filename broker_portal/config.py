"""
Configuration settings for the broker portal backend.
"""

import os
from pathlib import Path

# Base directory for the project
PROJECT_ROOT = Path(__file__).parent.parent.parent

# Directory for storing broker portal uploaded files
UPLOAD_DIR = os.path.join(PROJECT_ROOT, "broker_portal_uploads")

# Directory for storing broker portal logs
LOG_DIR = os.path.join(PROJECT_ROOT, "broker_portal_logs")

# Ensure directories exist
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# API settings
API_TITLE = "Broker Onboarding Portal API"
API_DESCRIPTION = "API for broker onboarding portal, providing file upload, column mapping, and ingestion capabilities."
API_VERSION = "0.1.0"
API_HOST = "0.0.0.0"
API_PORT = 8000

# Default settings
DEFAULT_BROKER_NAME = None  # Will be set from the request
DEFAULT_USE_TQDM = False    # No progress bar in API mode
DEFAULT_LOG_LEVEL = "INFO"
