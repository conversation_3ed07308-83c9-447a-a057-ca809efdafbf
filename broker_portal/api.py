"""
FastAPI endpoints for the broker portal backend.
"""

import logging
import os
from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware

from .config import API_DESCRIPTION, API_TITLE, API_VERSION, UPLOAD_DIR, LOG_DIR
from .logger import generate_job_id, portal_logger
from .models import (ColumnMappingRequest, ColumnMappingResponse,
                    IngestRequest, IngestResponse, JobStatus,
                    LogResponse, StatusResponse, UploadRequest, UploadResponse)
from .utils import analyze_columns, apply_column_mapping, ingest_data, save_upload_file, get_broker_upload_dir

# Create FastAPI app
app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Store job status and details
job_store: Dict[str, Dict] = {}


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Broker Portal API is running"}


@app.get("/brokers")
async def list_brokers():
    """
    List all brokers with files and logs.

    Returns:
        List of brokers with file and log counts
    """
    # Get all brokers from upload directory
    upload_brokers = []
    if os.path.exists(UPLOAD_DIR):
        upload_brokers = [d for d in os.listdir(UPLOAD_DIR) if os.path.isdir(os.path.join(UPLOAD_DIR, d))]

    # Get all brokers from log directory
    log_brokers = []
    if os.path.exists(LOG_DIR):
        log_brokers = [d for d in os.listdir(LOG_DIR) if os.path.isdir(os.path.join(LOG_DIR, d))]

    # Combine unique brokers
    all_brokers = list(set(upload_brokers + log_brokers))

    # Get file and log counts for each broker
    brokers = []
    for broker_name in all_brokers:
        broker_info = {"name": broker_name}

        # Count files
        broker_dir = os.path.join(UPLOAD_DIR, broker_name)
        if os.path.exists(broker_dir):
            broker_info["files_count"] = len([f for f in os.listdir(broker_dir) if os.path.isfile(os.path.join(broker_dir, f))])
            broker_info["upload_dir"] = broker_dir
        else:
            broker_info["files_count"] = 0

        # Count logs
        broker_log_dir = os.path.join(LOG_DIR, broker_name)
        if os.path.exists(broker_log_dir):
            broker_info["logs_count"] = len([f for f in os.listdir(broker_log_dir) if f.endswith(".log")])
            broker_info["log_dir"] = broker_log_dir
        else:
            broker_info["logs_count"] = 0

        brokers.append(broker_info)

    return {
        "brokers": brokers,
        "upload_dir": UPLOAD_DIR,
        "log_dir": LOG_DIR
    }


@app.post("/upload", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    broker_name: str = Form(...),
    broker_id: Optional[str] = Form(None),
    analyze: bool = Form(True)
):
    """
    Upload a broker file (CSV, Excel, or ZIP) and optionally analyze its columns.

    Args:
        file: The file to upload
        broker_name: Name of the broker
        broker_id: ID of the broker (optional)
        analyze: Whether to analyze columns after upload (default: True)

    Returns:
        UploadResponse: Response containing job ID, file details, and column analysis (if requested)
    """
    # Generate a job ID
    job_id = generate_job_id()

    # Set up logging for this job with broker-specific directory
    log_filepath = portal_logger.setup_logging(job_id, broker_name)

    # Save the uploaded file to broker-specific directory
    try:
        file_path = await save_upload_file(file, job_id, broker_name)
        logging.info(f"File uploaded: {file_path} for broker: {broker_name}")
    except Exception as e:
        logging.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"Error uploading file: {e}")

    # Store job details
    job_store[job_id] = {
        "status": JobStatus.PENDING,
        "broker_name": broker_name,
        "broker_id": broker_id,
        "filename": file.filename,
        "file_path": file_path,
        "log_filepath": log_filepath,
    }

    # Initialize response
    response = UploadResponse(
        job_id=job_id,
        broker_name=broker_name,
        broker_id=broker_id,
        filename=file.filename,
        file_path=file_path,
        status=JobStatus.PENDING,
    )

    # Analyze columns if requested
    if analyze:
        try:
            original_headers, template_headers, sample_data, ai_mapping = await analyze_columns(file_path, job_id, broker_name)
            logging.info(f"Columns analyzed for job {job_id} for broker: {broker_name}")

            # Store analysis results
            job_store[job_id].update({
                "original_headers": original_headers,
                "template_headers": template_headers,
                "sample_data": sample_data,
                "ai_mapping": ai_mapping,
            })

            # Add analysis results to response
            response.original_headers = original_headers
            response.template_headers = template_headers
            response.sample_data = sample_data
            response.ai_mapping = ai_mapping

            # Add hardcoded list of required columns
            response.required_columns = [
                "origin_zip", "destination_zip", "origin_city", "destination_city",
                "origin_state", "destination_state", "shipment_mode", "equipment_type",
                "customer_name", "broker_name", "broker_primary_reference",
                "cogs_total", "revenue_total", "origin_close"
            ]
        except Exception as e:
            logging.error(f"Error analyzing columns: {e}")
            job_store[job_id]["status"] = JobStatus.FAILED
            job_store[job_id]["error"] = str(e)
            response.status = JobStatus.FAILED
            # Don't raise an exception here, just return the upload part without analysis

    return response


@app.post("/map-columns", response_model=ColumnMappingResponse)
async def map_columns(request: ColumnMappingRequest):
    """
    Apply column mapping to the uploaded file.

    Args:
        request: Column mapping request

    Returns:
        ColumnMappingResponse: Response containing applied column mapping
    """
    # Check if job exists
    job_id = request.job_id
    if job_id not in job_store:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    # Get job details
    job = job_store[job_id]

    # Get column mapping
    column_mapping = request.column_mapping
    if request.use_ai_mapping and "ai_mapping" in job and job["ai_mapping"]:
        # Invert the AI mapping (template headers as keys -> original headers as keys)
        # This is needed because column_mapping is used to rename DataFrame columns,
        # which expects keys to be original column names and values to be new column names
        # Skip null values in the AI mapping
        column_mapping = {v: k for k, v in job["ai_mapping"].items() if v is not None}

    # Store column mapping and update status to processing
    job["column_mapping"] = column_mapping
    job["status"] = JobStatus.PROCESSING
    logging.info(f"Column mapping applied for job {job_id}. Status updated to processing.")

    # Return response
    return ColumnMappingResponse(
        job_id=job_id,
        column_mapping=column_mapping,
        status=JobStatus.PROCESSING,
    )


@app.post("/ingest", response_model=IngestResponse)
async def ingest(request: IngestRequest):
    """
    Ingest the uploaded file with the applied column mapping.

    Args:
        request: Ingestion request

    Returns:
        IngestResponse: Response containing ingestion results
    """
    # Check if job exists
    job_id = request.job_id
    if job_id not in job_store:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    # Get job details
    job = job_store[job_id]
    file_path = job["file_path"]

    # Check if column mapping exists
    if "column_mapping" not in job:
        raise HTTPException(status_code=400, detail="Column mapping not applied")

    # Use broker_name from job store if not provided in request
    broker_name = request.broker_name or job.get("broker_name")
    if not broker_name:
        raise HTTPException(status_code=400, detail="Broker name not provided")

    # Update job status
    job["status"] = JobStatus.PROCESSING
    job["broker_name"] = broker_name
    job["dev_db"] = request.dev_db
    job["dry_run"] = request.dry_run

    # Ingest data
    try:
        results = await ingest_data(
            file_path=file_path,
            broker_name=request.broker_name,
            dev_db=request.dev_db,
            dry_run=request.dry_run,
            job_id=job_id,
        )
        logging.info(f"Data ingested for job {job_id}")

        # Update job status
        job["status"] = JobStatus.COMPLETED
        job.update(results)

        # Return response
        return IngestResponse(
            job_id=job_id,
            broker_name=request.broker_name,
            dev_db=request.dev_db,
            dry_run=request.dry_run,
            total_rows=results["total_rows"],
            successful_rows=results["successful_rows"],
            new_shipments=results["new_shipments"],
            updated_shipments=results["updated_shipments"],
            errors=results["errors"],
            status=JobStatus.COMPLETED,
        )
    except Exception as e:
        logging.error(f"Error ingesting data: {e}")
        job["status"] = JobStatus.FAILED
        job["error"] = str(e)
        raise HTTPException(status_code=500, detail=f"Error ingesting data: {e}")


@app.get("/logs/{job_id}", response_model=LogResponse)
async def get_logs(job_id: str):
    """
    Get logs for a specific job.

    Args:
        job_id: Unique identifier for the job

    Returns:
        LogResponse: Response containing log content
    """
    # Check if job exists
    if job_id not in job_store:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    # Get log content
    log_content = portal_logger.get_log_content(job_id)
    if log_content is None:
        raise HTTPException(status_code=404, detail=f"Logs for job {job_id} not found")

    # Return response
    return LogResponse(
        job_id=job_id,
        log_content=log_content,
    )


@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_status(job_id: str):
    """
    Get status of a specific job.

    Args:
        job_id: Unique identifier for the job

    Returns:
        StatusResponse: Response containing job status
    """
    # Check if job exists
    if job_id not in job_store:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    # Get job details
    job = job_store[job_id]

    # Return response
    return StatusResponse(
        job_id=job_id,
        status=job["status"],
        details={k: v for k, v in job.items() if k != "status"},
    )


@app.get("/broker-files/{broker_name}")
async def list_broker_files(broker_name: str):
    """
    List all files for a specific broker.

    Args:
        broker_name: Name of the broker

    Returns:
        List of files for the broker
    """
    from .utils import get_broker_upload_dir

    # Get broker-specific directory
    broker_dir = get_broker_upload_dir(broker_name)

    # Check if directory exists
    if not os.path.exists(broker_dir):
        return {"broker_name": broker_name, "files": []}

    # List files in directory
    files = []
    for filename in os.listdir(broker_dir):
        file_path = os.path.join(broker_dir, filename)
        if os.path.isfile(file_path):
            file_stats = os.stat(file_path)
            files.append({
                "filename": filename,
                "path": file_path,
                "size": file_stats.st_size,
                "created": file_stats.st_ctime,
                "modified": file_stats.st_mtime,
            })

    return {
        "broker_name": broker_name,
        "directory": broker_dir,
        "files": files,
    }


@app.get("/broker-logs/{broker_name}")
async def list_broker_logs(broker_name: str):
    """
    List all logs for a specific broker.

    Args:
        broker_name: Name of the broker

    Returns:
        List of logs for the broker
    """
    # Get broker-specific log directory
    broker_log_dir = portal_logger.get_broker_log_dir(broker_name)

    # Check if directory exists
    if not os.path.exists(broker_log_dir):
        return {"broker_name": broker_name, "logs": []}

    # List logs in directory
    logs = []
    for filename in os.listdir(broker_log_dir):
        if filename.endswith(".log"):
            log_path = os.path.join(broker_log_dir, filename)
            if os.path.isfile(log_path):
                file_stats = os.stat(log_path)
                logs.append({
                    "filename": filename,
                    "path": log_path,
                    "size": file_stats.st_size,
                    "created": file_stats.st_ctime,
                    "modified": file_stats.st_mtime,
                })

    return {
        "broker_name": broker_name,
        "directory": broker_log_dir,
        "logs": logs,
    }
