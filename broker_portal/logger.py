"""
Logging setup and management for the broker portal backend.
"""

import logging
import os
import time
import uuid
from datetime import datetime
from typing import Dict, Optional

from .config import LOG_DIR


class PortalLogger:
    """
    Logger for the broker portal backend.

    This class provides methods for setting up logging and retrieving logs.
    """

    def __init__(self):
        self.log_files: Dict[str, str] = {}
        self.root_logger = logging.getLogger()

    def setup_logging(self, job_id: str, broker_name: str, log_level: str = "INFO") -> str:
        """
        Set up logging for a specific job in a broker-specific directory.

        Args:
            job_id: Unique identifier for the job
            broker_name: Name of the broker
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)

        Returns:
            Path to the log file
        """
        # Create broker-specific directory if it doesn't exist
        broker_log_dir = os.path.join(LOG_DIR, broker_name)
        os.makedirs(broker_log_dir, exist_ok=True)

        # Create a timestamp for the log filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"broker_portal_{job_id}_{timestamp}.log"
        log_filepath = os.path.join(broker_log_dir, log_filename)

        # Store the log file path for this job
        self.log_files[job_id] = log_filepath

        # Set up logging
        numeric_level = getattr(logging, log_level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError(f"Invalid log level: {log_level}")

        # Configure root logger
        self.root_logger.setLevel(numeric_level)

        # Create file handler
        file_handler = logging.FileHandler(log_filepath)
        file_handler.setLevel(numeric_level)

        # Create formatter
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s] %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        file_handler.setFormatter(formatter)

        # Add handler to root logger
        self.root_logger.addHandler(file_handler)

        logging.info(f"Logging initialized for job {job_id}")
        return log_filepath

    def get_log_content(self, job_id: str) -> Optional[str]:
        """
        Get the content of the log file for a specific job.

        Args:
            job_id: Unique identifier for the job

        Returns:
            Content of the log file, or None if the log file doesn't exist
        """
        if job_id not in self.log_files:
            return None

        log_filepath = self.log_files[job_id]
        if not os.path.exists(log_filepath):
            return None

        with open(log_filepath, "r") as f:
            return f.read()

    def get_broker_log_dir(self, broker_name: str) -> str:
        """
        Get the log directory for a specific broker.

        Args:
            broker_name: Name of the broker

        Returns:
            Path to the broker-specific log directory
        """
        broker_log_dir = os.path.join(LOG_DIR, broker_name)
        os.makedirs(broker_log_dir, exist_ok=True)
        return broker_log_dir

    def get_all_logs(self) -> Dict[str, str]:
        """
        Get all log files and their content.

        Returns:
            Dictionary mapping job IDs to log content
        """
        result = {}
        for job_id, log_filepath in self.log_files.items():
            if os.path.exists(log_filepath):
                with open(log_filepath, "r") as f:
                    result[job_id] = f.read()
        return result


# Create a singleton instance
portal_logger = PortalLogger()


def generate_job_id() -> str:
    """
    Generate a unique job ID.

    Returns:
        Unique job ID
    """
    return str(uuid.uuid4())
