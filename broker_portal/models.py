"""
Data models for the broker portal backend.
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Status of an ingestion job."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class UploadRequest(BaseModel):
    """Request model for file upload."""
    broker_name: str = Field(..., description="Name of the broker")
    broker_id: Optional[str] = Field(None, description="ID of the broker (optional)")


class UploadResponse(BaseModel):
    """Response model for file upload."""
    job_id: str = Field(..., description="Unique identifier for the job")
    broker_name: str = Field(..., description="Name of the broker")
    broker_id: Optional[str] = Field(None, description="ID of the broker (if provided)")
    filename: str = Field(..., description="Name of the uploaded file")
    file_path: str = Field(..., description="Path to the uploaded file")
    status: JobStatus = Field(JobStatus.PENDING, description="Status of the job")
    # Column analysis data (included when analyze=True)
    original_headers: Optional[List[str]] = Field(None, description="Original column headers from the file")
    template_headers: Optional[List[str]] = Field(None, description="Template headers (golden columns)")
    sample_data: Optional[List[Dict[str, Union[str, int, float, None]]]] = Field(None, description="Sample data from the file")
    ai_mapping: Optional[Dict[str, str]] = Field(None, description="AI-suggested column mapping")
    required_columns: Optional[List[str]] = Field(None, description="Required columns that must be mapped")


class ColumnAnalysisResponse(BaseModel):
    """Response model for column analysis."""
    job_id: str = Field(..., description="Unique identifier for the job")
    original_headers: List[str] = Field(..., description="Original column headers from the file")
    template_headers: List[str] = Field(..., description="Template headers (golden columns)")
    sample_data: List[Dict[str, Union[str, int, float, None]]] = Field(..., description="Sample data from the file")
    ai_mapping: Optional[Dict[str, str]] = Field(None, description="AI-suggested column mapping")
    status: JobStatus = Field(JobStatus.PROCESSING, description="Status of the job")


class ColumnMappingRequest(BaseModel):
    """Request model for column mapping."""
    job_id: str = Field(..., description="Unique identifier for the job")
    column_mapping: Dict[str, str] = Field(..., description="Mapping from original columns to golden columns")
    use_ai_mapping: bool = Field(False, description="Whether to use AI-based column mapping")


class ColumnMappingResponse(BaseModel):
    """Response model for column mapping."""
    job_id: str = Field(..., description="Unique identifier for the job")
    column_mapping: Dict[str, str] = Field(..., description="Applied column mapping")
    status: JobStatus = Field(JobStatus.PROCESSING, description="Status of the job")


class IngestRequest(BaseModel):
    """Request model for ingestion."""
    job_id: str = Field(..., description="Unique identifier for the job")
    broker_name: Optional[str] = Field(None, description="Name of the broker (optional if provided during upload)")
    dev_db: bool = Field(False, description="Whether to use the development database")
    dry_run: bool = Field(False, description="Whether to run in dry run mode")


class IngestResponse(BaseModel):
    """Response model for ingestion."""
    job_id: str = Field(..., description="Unique identifier for the job")
    broker_name: str = Field(..., description="Name of the broker")
    dev_db: bool = Field(..., description="Whether the development database was used")
    dry_run: bool = Field(..., description="Whether dry run mode was used")
    total_rows: int = Field(..., description="Total number of rows in the file")
    successful_rows: int = Field(..., description="Number of rows successfully processed")
    new_shipments: int = Field(..., description="Number of new shipments created")
    updated_shipments: int = Field(..., description="Number of shipments updated")
    errors: Dict[str, int] = Field(..., description="Errors encountered during ingestion")
    status: JobStatus = Field(JobStatus.COMPLETED, description="Status of the job")


class LogResponse(BaseModel):
    """Response model for log retrieval."""
    job_id: str = Field(..., description="Unique identifier for the job")
    log_content: str = Field(..., description="Content of the log file")


class StatusResponse(BaseModel):
    """Response model for job status."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Status of the job")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details about the job")
