# This is a basic workflow to help you get started with Actions

name: Lambda CI

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the dev branch
  push:
    branches: [ dev ]
  pull_request:
    branches: [ dev ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs a single command using the runners shell
      - name: Run a one-line script
        run: echo hello world

      # Runs a set of commands using the runners shell
      - name: Run a multi-line script
        run: |
          echo Add other actions to build,
          echo test, and deploy your project.

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: shipment_request_handler_dev
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          RDS_SECRET_ARN: ${{ secrets.RDS_SECRET_ARN }}
          GCP_API_KEY: ${{ secrets.GCP_API_KEY }}
          IMAGE_TAG: latest
        run: |
          cd aws/lambda/shipment_request_handler/
          touch credentials
          echo [default] > credentials
          echo aws_access_key_id = "$AWS_ACCESS_KEY_ID" >> credentials
          echo aws_secret_access_key = "$AWS_SECRET_ACCESS_KEY" >> credentials
          echo region = us-east-1 >> credentials
          echo output = json >> credentials
          echo RDS_SECRET_ARN="$RDS_SECRET_ARN" >> credentials
          echo GCP_API_KEY="$GCP_API_KEY" >> credentials
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Use AWS CLI
        uses: chrislennon/action-aws-cli@v1.1
        env:
          ACTIONS_ALLOW_UNSECURE_COMMANDS: 'true'

      # All commands after this point have access to the AWS CLI
      - name: Update Lambda Function Code
        run: aws lambda update-function-code --function-name shipment_request_handler_dev --image-uri 903881895532.dkr.ecr.us-east-1.amazonaws.com/shipment_request_handler_dev:latest
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
