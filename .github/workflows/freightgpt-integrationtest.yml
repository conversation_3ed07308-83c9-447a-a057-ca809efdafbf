name: ci-python-freightgpt-integration
# https://github.com/philschmid/github-actions

# Test can only be kicked off manually.
on:
  workflow_dispatch:
    inputs:
      prNumber:
        description: 'Pull Request Number (optional)'
        required: false
        default: ''

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    env:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v1
        with:
          python-version: ${{ matrix.python-version }}

      - name: Fake credentials file creation.
        run: |
          touch "./common/credentials"
          echo "[default]" >> "./common/credentials"
          echo "aws_access_key_id=${{ secrets.AWS_ACCESS_KEY_ID }}" >> "./common/credentials"
          echo "aws_secret_access_key=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> "./common/credentials"
          echo "region=${{ secrets.REGION }}" >> "./common/credentials"
          echo "output=json" >> "./common/credentials"
          echo "RDS_SECRET_ARN=${{ secrets.RDS_SECRET_ARN }}" >> "./common/credentials"
          echo "REDSHIFT_SECRET_ARN=${{ secrets.REDSHIFT_SECRET_ARN }}" >> "./common/credentials"
          echo "GCP_API_KEY=${{ secrets.GCP_API_KEY }}" >> "./common/credentials"
          echo "CLUSTER_ARN=${{ secrets.CLUSTER_ARN }}" >> "./common/credentials"
          echo "sagemaker_aws_access_key_id=${{ secrets.SAGEMAKER_AWS_ACCESS_KEY_ID }}" >> "./common/credentials"
          echo "sagemaker_aws_secret_access_key=${{ secrets.SAGEMAKER_AWS_SECRET_ACCESS_KEY }}" >> "./common/credentials"
          echo "PIRATE_WEATHER_API_KEY=${{ secrets.PIRATE_WEATHER_API_KEY}}" >> "./common/credentials"
          cat "./common/credentials"

      - name: Fake credentials.env file creation.
        run: |
          touch "./common/credentials.env"
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> "./common/credentials.env"
          echo "SERPAPI_API_KEY=${{ secrets.SERPAPI_API_KEY}}" >> "./common/credentials.env"
          echo "OPENWEATHERMAP_API_KEY=${{ secrets.OPENWEATHERMAP_API_KEY}}" >> "./common/credentials.env"
          echo "LANGCHAIN_TRACING_V2=${{ secrets.LANGCHAIN_TRACING_V2 }}" >> "./common/credentials.env"
          echo "LANGCHAIN_ENDPOINT=${{ secrets.LANGCHAIN_ENDPOINT }}" >> "./common/credentials.env"
          echo "LANGCHAIN_API_KEY=${{ secrets.LANGCHAIN_API_KEY }}" >> "./common/credentials.env"
          echo "LANGCHAIN_PROJECT=${{ secrets.LANGCHAIN_PROJECT }}" >> "./common/credentials.env"
          cat "./common/credentials.env"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install .
          pip install -r ./common/requirements.txt
          pip install -r ./machine_learning/price_recommendation/requirements.txt
          pip install -r ./machine_learning/price_recommendation/daily_cpm/requirements.txt
          pip install -r ./machine_learning/freightgpt/requirements.txt
          pip install -r ./machine_learning/handlers/integration_tests/uvicorn_tests/requirements.txt

      - name: FreightGPT Dry Tests
        run: |
          pytest -s ./machine_learning/handlers/integration_tests/uvicorn_tests/uvicorn_dry.py

      - name: FreightGPT Wet Tests
        run: |
          pytest -s ./machine_learning/handlers/integration_tests/uvicorn_tests/uvicorn_wet.py
