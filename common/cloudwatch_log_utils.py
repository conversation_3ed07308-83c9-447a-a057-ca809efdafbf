import collections
import contextvars
import logging
import os
import typing
from datetime import datetime as dt

import boto3
from botocore.exceptions import ClientError

LOG_TEMPLATE_URL = "https://console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/{log_group}/log-events/{log_stream_id}"


class CloudWatchLogger:
    """
    A class for sending all logging messages (logging.INFO, logging.WARN, logging.ERROR, etc.) to a Cloudwatch
    log group / log stream.

    Cloud watch is structured as follows:

    log_group ---> log_stream_1
                -> log_stream_2
                -> log_stream_3

    A log group must be provided and exist. Log streams will be automatically created if they do not exist.

    Args:
        log_group (str): the CloudWatch log group that contains your stream.
        verbose_level (str): logging verbosity level
        log_to_stdout (bool): whether to also log to standard out. Note that setting this to true does not prevent CloudWatch uploads
                              unless log_only is also set to True.
        log_only (bool): whether to only log and not upload to cloudwatch at all. Useful for development or solely logging to stdout.
        batch_logs (bool): whether to store logs as they come in, so that they may be batch uploaded later.
                           Useful for if there are performance concerns, so that each log does not make a cloudwatch call.
    """

    # Stores current log stream id. Designed to work with sync and async contexts.
    STREAM_ID = contextvars.ContextVar("stream_id", default="logs")

    # Store logs as they are emitted for batch upload later.
    LOGS_STORE = collections.defaultdict(list)

    # Cloudwatch boto3 client.
    CWL_CLIENT = boto3.client("logs")

    # Defining CloudWatchLogHandler as a nested class because it needs access to class scope variables such as STREAM_ID.
    class CloudWatchLogHandler(logging.Handler):
        """
        Custom logging handler that allows us to either batch logs in the log store or send immediately as a
        Cloudwatch event when logging.info, error, warn, etc. are called.

        Args:
            log_group (str): The name of the CloudWatch log group.
            log_only (bool, optional): If True, only logs the messages and does not upload them to CloudWatch Logs. Defaults to False.
            batch_logs (bool, optional): If True, batches the log events in log store for later upload. Defaults to False.
        """

        def __init__(
            self, log_group: str, log_only: bool = False, batch_logs: bool = False
        ):
            super().__init__()
            self.log_group = log_group
            self.log_only = log_only
            self.batch_logs = batch_logs

        def emit(self, record: logging.LogRecord) -> None:
            """
            Overwrite of "emit" method for logging.Handler. Either stores log in log store (if batch logs)
            or writes log to cloudwatch stream immediately.

            Args:
                record (logging.LogRecord): The log record to be emitted.
            """

            stream_id = CloudWatchLogger.STREAM_ID.get()
            log_message = self.format(record)
            timestamp = int(dt.now().timestamp() * 1000)  # in milliseconds

            # Cloudwatch event
            event = {"timestamp": timestamp, "message": log_message}

            try:
                # If batch logs, store under stream id.
                if self.batch_logs:
                    CloudWatchLogger.LOGS_STORE[stream_id].append(event)

                # Otherwise, write immediately to cloudwatch.
                if not self.batch_logs and not self.log_only:
                    self.upload_logs(stream_id, [event])

            except ClientError as e:
                print(
                    f"Failed to update logs on log stream {stream_id} log group {self.log_group} with exception {e}"
                )

        def upload_logs(
            self, stream_id: str, log_events: typing.List[typing.Dict]
        ) -> None:
            """Uploads log events to CloudWatch Logs.

            Args:
                stream_id (str): The ID of the log stream.
                log_events (list): A list of log events to be uploaded to the stream.
            """
            try:
                CloudWatchLogger.CWL_CLIENT.put_log_events(
                    logGroupName=self.log_group,
                    logStreamName=stream_id,
                    logEvents=log_events,
                )
            except ClientError as e:
                print(
                    f"Failed to update logs on log stream {stream_id} log group {self.log_group} with exception {e}"
                )

    def __init__(
        self,
        log_group: str,
        verbose_level: str = "INFO",
        log_to_stdout: bool = True,
        log_only: bool = False,
        batch_logs: bool = False,
    ):
        self.log_group = log_group
        self.log_only = log_only
        self.batch_logs = batch_logs
        self.stream_id_tokens = {}
        self.created_log_streams = set()
        self.is_test_env = os.getenv("ENV") == "TEST"
        self.cwl_handler = self.setup_logging(verbose_level, log_to_stdout)

    def set_stream(self, id: str) -> str:
        """
        Set the current log stream ID. Create a log stream if one does not exist.

        Args:
            id (str): The ID of the log stream.

        Returns:
            ContextToken: The context token for the log stream ID.

        """

        # A token references the state of the contextvar before it is set.
        token = self.STREAM_ID.set(id)
        self.stream_id_tokens[id] = token

        self.create_log_stream(id)
        self.log_url = LOG_TEMPLATE_URL.format(
            log_group=self.log_group, log_stream_id=id
        )
        return token

    def clear_stream(self, id: str, reset_logs_store: bool = True):
        """
        Clear the context variable for the specified log stream ID.

        Args:
            id (str): The ID of the log stream.
            reset_logs_store (bool, optional): Whether to clear the log store when resetting the stream id.

        """
        token = self.stream_id_tokens.get(id)
        self.STREAM_ID.reset(token)

        # Clear log store if stream id is being reset.
        if reset_logs_store and id in self.LOGS_STORE:
            del self.LOGS_STORE[id]

    def setup_logging(
        self, verbose_level: str = "INFO", log_to_stdout: bool = False
    ) -> CloudWatchLogHandler:
        """
        Setup logging configuration.

        Args:
            verbose_level (str, optional): The verbose level for logging. Defaults to "INFO".
            log_to_stdout (bool, optional): Whether to log messages to stdout. Defaults to False.

        Returns:
            CloudWatchLogHandler: The created CloudWatchLogHandler instance.

        Raises:
            ValueError: If an invalid log level is provided.

        """

        # CloudWatch handler
        log_fmt_str = (
            "%(asctime)s [%(filename)s:%(lineno)d] [%(levelname)-4.4s]  %(message)s"
        )
        formatter = logging.Formatter(log_fmt_str, datefmt="%H:%M:%S")
        handlers = []
        if not self.is_test_env:
            handler = self.CloudWatchLogHandler(
                self.log_group, log_only=self.log_only, batch_logs=self.batch_logs
            )
            handler.setFormatter(formatter)
            handlers.append(handler)
        else:
            handler = None

        # Verbosity
        numeric_level = getattr(logging, verbose_level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError("Invalid log level: %s" % verbose_level)

        # Standard out logger
        if log_to_stdout:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            handlers.append(console_handler)

        logging.getLogger().handlers = []
        logging.basicConfig(level=numeric_level, datefmt="%H:%M:%S", handlers=handlers)

        return handler

    def create_log_stream(self, log_stream_id: str) -> None:
        """
        Create a log stream in the log group. Appends to created log streams.

        Args:
            log_stream_id (str): The ID of the log stream.

        """

        try:
            if self.log_only or self.is_test_env:
                return

            # If a log stream has already been created, there is no need to re-create it.
            if log_stream_id in self.created_log_streams:
                print(
                    f"Log stream '{log_stream_id}' has already been created this instantiation in log group '{self.log_group}'"
                )
                return

            # Create log stream.
            self.CWL_CLIENT.create_log_stream(
                logGroupName=self.log_group, logStreamName=log_stream_id
            )
            print(
                f"Log stream '{log_stream_id}' created in log group '{self.log_group}'."
            )

            self.created_log_streams.add(log_stream_id)

        except ClientError as e:
            if e.response["Error"]["Code"] == "ResourceAlreadyExistsException":
                print(
                    f"Log stream '{log_stream_id}' already exists in log group '{self.log_group}'."
                )
            else:
                raise

    def upload_logs_store(self, *args):
        """
        If logs have been batched / stored, upload log events from the logs store to CloudWatch Logs.
        This function can be used as an async callback as well.
        """
        if self.log_only or self.is_test_env:
            return

        stream_id = self.STREAM_ID.get()
        if stream_id in self.LOGS_STORE and self.LOGS_STORE[stream_id]:
            events = self.LOGS_STORE[stream_id]
            if len(events) > 0:
                self.cwl_handler.upload_logs(stream_id, events)
