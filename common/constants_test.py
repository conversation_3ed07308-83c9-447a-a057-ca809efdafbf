import unittest

from common.constants import construct_named_tuple


class TestConstants(unittest.TestCase):
    def setUp(self):
        self.fields = [
            "Field One",
            "Field Two",
            "Field Three",
        ]

    def test_construct_named_tuple(self):
        fields = ["Field One", "Field Two", "Field Three"]
        MyNamedTuple = construct_named_tuple("Test", fields)
        self.assertEqual(MyNamedTuple.field_one, "Field One")
        self.assertEqual(MyNamedTuple.field_two, "Field Two")
        self.assertEqual(MyNamedTuple.field_three, "Field Three")


if __name__ == "__main__":
    unittest.main()
