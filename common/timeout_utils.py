import threading
from typing import <PERSON><PERSON>
from typing import Type


class TimeoutException(Exception):
    """Custom exception to signal a timeout in a block of code."""

    pass


class Timeout:
    def __init__(self, seconds: int):
        """
        Initialize the timeout context manager with a specific duration.

        Args:
            seconds (int): The number of seconds after which the timeout should occur.
        """
        self.seconds = seconds
        self.timer: Optional[threading.Timer] = None

    def handle_timeout(self) -> None:
        """Callback function to be called when the timer expires."""
        raise TimeoutException("Operation timed out")

    def __enter__(self) -> None:
        """Start a timer when entering the context."""
        self.timer = threading.Timer(self.seconds, self.handle_timeout)
        self.timer.start()

    def __exit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_val: Optional[BaseException],
        exc_tb: Optional[object],
    ) -> Optional[bool]:
        """Cancel the timer when exiting the context.

        Args:
            exc_type: The type of the exception (if any).
            exc_val: The exception instance raised (if any).
            exc_tb: The traceback object (if any).

        Returns:
            False if the exception is not a TimeoutException, allowing it to be propagated.
            None otherwise, suppressing any TimeoutException.
        """
        self.timer.cancel()
        if exc_type is not None and not issubclass(exc_type, TimeoutException):
            return False  # Propagate other exceptions
