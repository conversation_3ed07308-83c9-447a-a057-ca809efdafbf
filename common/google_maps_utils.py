import logging
import os
import time
from functools import lru_cache
from typing import Any
from typing import Dict
from typing import List
from typing import Tu<PERSON>
from typing import Union

import googlemaps
import numpy as np
import pandas as pd
import pytz

from common import credentials
from common import s3_cache_file


# TODO(P1): Consider periodically saving the cache files to S3.
# We need to think about how to manage caches and such if we are running in distributed fashion
# Each node should not have separate versions of the cache.
# We may wanna change this to be a data store of some kind with fast access (i.e. key value store)


class GoogleMapsUtils:
    """Implements a number of utility functions for Google Maps API.

    It caches the API calls in dataframes, which you can save locally and upload to S3.
    It loads the cached data from S3 when the object is initialized.

    Example usage:
        from common import google_maps_utils
        gmaps_utils = google_maps_utils.GoogleMapsUtils()
        gmaps_utils.get_distance_miles("New York, NY", "Los Angeles, CA")
        gmaps_utils.get_lat_lng("New York", "NY", "US", "10001")
        gmaps_utils.get_zip_from_city_state("New York", "NY", "US")
        gmaps_utils.get_tz_from_zip("10001", "US")

        # Once you have updated local cache, upload to S3.
        gmaps_utils.upload_cache_files_to_s3()
    """

    def __init__(self, mock=False):
        self.CREDENTIALS = credentials.Credentials()
        gcp_key = self.CREDENTIALS.gcp_api_key if not mock else "mock"
        self.GMAPS = googlemaps.Client(key=gcp_key)

        cache_bucket = "ingestion-api-cache" if not mock else "mock"
        self.zip_lat_lng = s3_cache_file.S3CacheFile(
            cache_bucket,
            "data/zip_lat_lng.csv",
            idx=["city", "state", "zip", "country"],
        )
        self.zip_tz = s3_cache_file.S3CacheFile(
            cache_bucket, "data/zip_tz.csv", idx=["country/zip"]
        )
        self.dist_mi = s3_cache_file.S3CacheFile(
            cache_bucket, "data/dist_mi.csv", idx=["origin/dest"]
        )
        self.zip_from_city_state = s3_cache_file.S3CacheFile(
            cache_bucket, "data/zip_from_city_state.csv", idx=["city"]
        )

        # Make data directory if it doesn't exist.
        os.makedirs("data", exist_ok=True)

        self.MILES_PER_METER = 0.000621371192
        self.cache_files = [
            self.zip_lat_lng,
            self.zip_tz,
            self.dist_mi,
            self.zip_from_city_state,
        ]
        if not mock:
            # Pull cached data from S3.
            for cache_file in self.cache_files:
                cache_file.pull()

    def _get_ttl_hash(self, seconds=3600):
        """Return the same value within `seconds` time period for cache invalidation purposes.

        This method is used in conjunction with lru_cache to create a time-based cache invalidation mechanism. By changing the hash value every `seconds`, it ensures that cached data is refreshed periodically.

        Args:
        - seconds (int): The time period in seconds. Defaults to 3600 seconds (1 hour).
        Returns:
        - int: The same value within the time period.
        """
        if seconds <= 0:
            raise ValueError("Seconds must be greater than 0.")
        return round(time.time() / seconds)

    # https://developers.google.com/maps/documentation/distance-matrix/distance-matrix#DistanceMatrixResponse
    @lru_cache(maxsize=4)
    def _distance_matrix_request(
        self,
        origin: str,
        destination: str,
        mode: str = "driving",
        departure_time: str = "",
        ttl_hash=None,
    ) -> Dict[str, Any]:
        """Helper method to call Google Maps API and return raw data.

        Uses lru_cache, with ttl set to 1 hour (3600 seconds), to cache the results.

        Args:
        - origin (str): The origin location, e.g. "New York, NY".
        - destination (str): The destination location, e.g. "New York, NY".
        - mode (str): The mode of transport, e.g. "driving", "walking"
        - departure_time (str, int): The departure time. Defaults to "now", but can be any string that pd.Timestamp can parse.
        - ttl_hash (int): A hash that changes every hour, intended for cache invalidation when used with lru_cache. It's passed to ensure the cache is refreshed periodically but is not directly used within the function.

        Returns:
        - dict: The raw data from the Google Maps API response.
        """

        logging.info(f"Ping GMAPS distance matrix for {origin} to {destination}.")
        # TODO(P1): Add traffic model parameter optimistic/pessimistic instead of default best_guess
        response = self.GMAPS.distance_matrix(
            origin,
            destination,
            mode=mode,
            departure_time=departure_time,
        )
        if response["status"] != "OK":
            logging.error("GMAPS distance matrix request failed.")
            raise ValueError("GMAPS distance matrix request failed.")

        if not (response.get("rows") and response.get("rows")[0].get("elements")):
            err_str = "GMAPS status OK but no data found for origin: {} and destination: {}.".format(
                origin, destination
            )
            logging.error(err_str)
            raise ValueError(err_str)
        return response

    def _get_gmaps_route_data(
        self,
        origin: str,
        destination: str,
        keys: List[str],
        departure_time: str = "",
    ) -> Dict[str, Any]:
        """Helper method to call Google Maps API and return raw data.

        Uses lru_cache, with ttl set to 1 hour (3600 seconds), to cache the results.
        This method leverages the cached `_distance_matrix_request` to fetch route data, ensuring efficient API usage.

        Example usage:
        >>> gmaps_utils = GoogleMapsUtils()
        >>> gmaps_utils._get_gmaps_route_data("New York, NY", "Los Angeles, CA", ["duration_in_traffic", "distance"])
        {"duration_in_traffic": 12345, "distance": 12345}

        Args:
        - origin (str): The origin location, e.g. "New York, NY".
        - destination (str): The destination location, e.g. "New York, NY".
        - key (List[str]): The key to retrieve from the response, e.g. ["duration_in_traffic", "distance"].
        - departure_time (str, int): The departure time. Defaults to "now", but can be any string that pd.Timestamp can parse.
        Returns:
        - dict: The raw data from the Google Maps API response, keyed by the keys.
        """
        if departure_time != "now" and departure_time:
            try:
                departure_time = pd.Timestamp(departure_time)
                departure_time = int(departure_time.timestamp())
            except ValueError as e:
                logging.error("Invalid departure_time: ", e)
                raise

        return_data = {}

        response = self._distance_matrix_request(
            origin=origin,
            destination=destination,
            departure_time=departure_time,
            ttl_hash=self._get_ttl_hash(),  # This is a hash that changes every hour
        )

        for key in keys:
            value = response["rows"][0]["elements"][0].get(key, {}).get("value")
            if pd.isna(value):
                err_str = "{} not found for origin: {} and destination: {}.".format(
                    key, origin, destination
                )
                logging.error(err_str)

            return_data[key] = value
        return return_data

    def save_and_upload_cache_files(self):
        """Save cached data to S3."""
        for cache_file in self.cache_files:
            cache_file.save_and_upload()

    def get_driving_time(
        self, origin: str, destination: str, departure_time: str = "now"
    ) -> Dict[str, float]:
        """Get driving time in seconds between two locations, with traffic accounted for.

        Args:
        - origin (str): The origin location.
        - destination (str): The destination location.
        - departure_time (str): The departure time. Defaults to "now", but can be any string that pd.Timestamp can parse.
        Returns:
        - Dict[str, float]: keyed by duration, duration_in_traffic. values are in seconds.
        """
        #  If departure_time is blank, this may break as there won't be any traffic data. Maybe use "duration" instead.
        if not departure_time:
            logging.warning(
                "Departure time is blank. Using 'now' as the departure time instead."
            )
            departure_time = "now"

        durations = self._get_gmaps_route_data(
            origin=origin,
            destination=destination,
            keys=["duration", "duration_in_traffic"],
            departure_time=departure_time,
        )

        return durations

    def meters_to_miles(self, meters: float, precision: int = 5) -> Union[float, int]:
        """Convert meters to miles.

        Args:
        - meters (float): The distance in meters.
        - precision (int): The number of decimal places to round to. Defaults to 5.
        Returns:
        - float: The distance in miles, if precision is > 0.
        - int: The distance in miles, if precision is <= 0.
        """
        if pd.isna(meters):
            return pd.NA
        miles = round(meters * self.MILES_PER_METER, precision)
        return miles if precision > 0 else int(miles)

    def get_distance_miles(
        self, origin: str, destination: str, use_cache=True
    ) -> float:
        """Get distance in miles between two strings.

        The origin/destination strings can be anything that Google Maps API accepts.
        """
        if pd.isna(origin) or not origin:
            logging.error(f"Origin '{origin}' not valid.")
            raise ValueError("No origin in get_distance_miles.")
        if pd.isna(destination) or not destination:
            logging.error(f"Destination '{destination}' not valid.")
            raise ValueError("No destination in get_distance_miles.")

        origin_dest = origin + "/" + destination
        # Check if origin_dest is in cached origin_dest -> miles datafile.
        if not use_cache or origin_dest not in self.dist_mi.df.index:
            # Ping GMAPS for distance in miles between input strings.
            distance_meters = self._get_gmaps_route_data(
                origin=origin,
                destination=destination,
                keys=["distance"],
            ).get("distance", pd.NA)
            if pd.isna(distance_meters):
                raise ValueError(
                    "Distance not found for origin: {} and destination: {}.".format(
                        origin, destination
                    )
                )

            distance_miles = self.meters_to_miles(distance_meters)
            logging.info(
                "Ping GMAPS to find {:.0f} miles for {} to {}.".format(
                    distance_miles, origin, destination
                )
            )
            # Only update the cache if use_cache is True and the distance is not already in the cache.
            if use_cache:
                self.dist_mi.df.loc[origin_dest] = distance_miles
                self.dist_mi.df.to_csv(self.dist_mi.filename)
        else:
            distance_miles = self.dist_mi.df.loc[origin_dest].values[0]
            # TODO: Write a function that detects duplicate rows and returns without the duplicates instead of the bottom if statement
            if not isinstance(distance_miles, np.float64):
                raise AssertionError(
                    "There is more than one origin_dist in the keys column in dist_mi.csv."
                )

        return distance_miles

    def _get_gmaps_geocode(
        self,
        query_str: str,
        components: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """Get geocode data from Google Maps API.

        https://developers.google.com/maps/documentation/geocoding/requests-geocoding#GeocodingResponses

        Args:
        - query_str (str): The query string to geocode.
        Returns:
        - dict: The raw data from the Google Maps API response.
        """
        logging.info(f"Ping GMAPS geocode for {query_str}.")
        if not (response := self.GMAPS.geocode(query_str, components=components)):
            logging.error("GMAPS geocode request failed. Query: %s", query_str)
            raise ValueError("GMAPS geocode request failed.")
        return response

    def _parse_location(self, geocode_result: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Parse the location from a geocode result."""
        if not (location := geocode_result[0].get("geometry", {}).get("location")):
            logging.error(
                "Location not found in geocode result for %s.", geocode_result
            )
            raise ValueError("Location not found in geocode result.")
        return location

    def _parse_lat_lng(
        self, geocode_result: List[Dict[str, Any]]
    ) -> Tuple[float, float]:
        """Parse the latitude and longitude from a geocode result."""
        location = self._parse_location(geocode_result)
        lat = location.get("lat")
        lng = location.get("lng")
        if lat is None or lng is None:
            logging.error(
                "Latitude or longitude not found in geocode location result for %s.",
                geocode_result,
            )
            raise ValueError(
                "Latitude or longitude not found in geocode location result."
            )
        return lat, lng

    def get_uncached_lat_lng(
        self, query_str: str, components: Dict[str, Any] = {}
    ) -> Tuple[float, float]:
        """Get latitude and longitude of a query string."""
        geocode_result = self._get_gmaps_geocode(query_str, components)
        lat, lng = self._parse_lat_lng(geocode_result)
        return lat, lng

    def get_lat_lng(
        self, city: str, state: str, country: str, zipcode: str = ""
    ) -> Tuple[float, float]:
        """Get latitude and longitude of a query string."""
        if pd.isna(city) or pd.isna(state) or pd.isna(country):
            return pd.NA, pd.NA
        idx = (city, state, zipcode, country)
        if idx not in self.zip_lat_lng.df.index:
            query_str = (
                f"{city}, {state}" + f" {zipcode}" if not pd.isna(zipcode) else ""
            )
            geocode_result = self._get_gmaps_geocode(
                query_str, components={"country": country}
            )
            lat, lng = self._parse_lat_lng(geocode_result)

            self.zip_lat_lng.df.loc[idx] = [lat, lng]
            # There is weird behavior when using .loc with a tuple in a multi-index DataFrame.
            # Pandas treates the tuple as a multi-level key, where where each element corresponds
            # to a level of the MultiIndex (city, state, zipcode).When pandas encounters a new key
            # that's not in the DataFrame's index, it creates a new row with that key. The names of
            # the index levels frustratingly are lost during  this operation. In order to preserve
            # the index names, we have to set them again.
            self.zip_lat_lng.df.index.set_names(self.zip_lat_lng.idx, inplace=True)
            self.zip_lat_lng.df.to_csv(self.zip_lat_lng.filename)

        return self.zip_lat_lng.df.loc[idx].values[:2]

    def get_zip_from_city_state(self, city: str, state: str, country_code: str) -> str:
        """Retrieve zipcode by using city, state, and country_code information"""
        for address_info in [city, state, country_code]:
            if pd.isna(address_info) or not address_info:
                logging.error("Address information not found and not specified.")
                raise ValueError("Address information not found and not specified")
            elif not isinstance(address_info, str):
                logging.error(f"Address information {address_info} is not a string")
                raise ValueError(f"Address information {address_info} is not a string")
        zip_query = self.zip_from_city_state.df.query(
            "city == @city and state == @state"
        )

        if not zip_query.empty:
            zipcode = zip_query.iloc[0]["zip"]
        else:
            geocode_result = self._get_gmaps_geocode(
                f"{city}, {state}", components={"country": country_code}
            )
            lat, lng = self._parse_lat_lng(geocode_result)
            reverse_geocode_result = self.GMAPS.reverse_geocode((lat, lng))
            if reverse_geocode_result == []:
                logging.error("GMAPS reverse geocode request failed.")
                raise ValueError("GMAPS reverse geocode request failed.")
            zipcode = None
            for result in reverse_geocode_result:
                for component in result["address_components"]:
                    if "postal_code" in component["types"]:
                        zipcode = component["long_name"]
                        break
                    if zipcode:
                        break
            if not zipcode:
                logging.error("Zipcode cannot be found with given information")
                raise ValueError("Zipcode cannot be found with given information")
            self.zip_from_city_state.df.loc[city] = [
                state,
                zipcode,
                lat,
                lng,
                country_code,
            ]
            self.zip_from_city_state.df.to_csv(self.zip_from_city_state.filename)
        return zipcode

    def get_county_from_address(self, address: str) -> Tuple[str, Tuple[float, float]]:
        """Retrieve county name using an address.
        Args:
        - address (str): The full address to get the county for.
        Returns:
        - str: The county name (can be None).
        - Tuple: The latitude and longitude of the address.
            - float: Latitude of the address (can be pd.NA).
            - float: Longitude of the address (can be pd.NA).
        """
        geocode_result = self._get_gmaps_geocode(address)
        county_name = None

        # Try to find county in the initial geocode results
        for component in geocode_result[0]["address_components"]:
            if (
                "administrative_area_level_2" in component["types"]
            ):  # This is typically the county level
                county_name = component["long_name"]
                break

        lat_lng_dict = geocode_result[0].get("geometry", {}).get("location", {})
        lat, lng = lat_lng_dict.get("lat"), lat_lng_dict.get("lng")

        if pd.isna(lat) or pd.isna(lng):
            lat, lng = self.get_uncached_lat_lng(address)

        if not pd.isna(county_name):
            return county_name, (lat, lng)

        # Fallback to using latitude and longitude if county not found
        if pd.isna(lat) or pd.isna(lng):
            logging.error(
                "County name, latitude, or longitude not found for %s.", address
            )
            return county_name, (lat, lng)

        reverse_geocode_result = self.GMAPS.reverse_geocode((lat, lng))
        for result in reverse_geocode_result:
            for component in result["address_components"]:
                if "administrative_area_level_2" in component["types"]:
                    county_name = component["long_name"]
                    break
            if not pd.isna(county_name):
                break

        return county_name, (lat, lng)

    def get_tz_from_address(self, address: str, country: str = "") -> str:
        """Get a timezone str from an address, and possibly a country.

        Args:
        - address (str): The address to get the timezone for.
        - country (str): The country of the address, such as "America" or "Germany".

        Returns:
        - str: The timezone string, e.g. "America/New_York". Can be used like pytz.timezone("America/New_York").
        """
        logging.info("Pinging Google Maps API for timezone localization.")

        geocode_result = self._get_gmaps_geocode(
            address, components={"country": country}
        )
        location = self._parse_location(geocode_result)
        timezone = self.GMAPS.timezone(location=location)

        if "timeZoneId" not in timezone:
            logging.error("timeZoneId not found in timezone result.")
            raise ValueError("timeZoneId not found in timezone result.")

        return timezone["timeZoneId"]

    def get_tz_from_zip(self, zipcode: str, country: str) -> pytz.timezone:
        """Get a timezone object from a zipcode and country code."""

        country_zip = country + "/" + zipcode
        # Check if zip is in cached zip -> tz datafile.
        if country_zip not in self.zip_tz.df.index:
            tz_str = self.get_tz_from_address(zipcode, country)
            self.zip_tz.df.loc[country_zip] = tz_str
            self.zip_tz.df.to_csv(self.zip_tz.filename)
        if not isinstance(self.zip_tz.df.loc[country_zip].values[0], str):
            raise AssertionError(
                "There is more than one zip in the keys column in zip_tz.csv."
            )
        return pytz.timezone(self.zip_tz.df.loc[country_zip].values[0])

    def get_city_state_from_lat_lng(
        self, lat: float, lng: float, short_state_name: bool = False
    ) -> str:
        """Retrieve city and state name using latitude and longitude information.

        Args:
        - lat (float): Latitude of the location.
        - lng (float): Longitude of the location.
        - short_state_name (bool): Whether to return the short state name (e.g. "NY") or the long state name (e.g. "New York"). Defaults to False.

        Returns:
        - str: The city and state name, e.g. "New York City, New York".
        """

        # Reverse geocoding using Google Maps API
        reverse_geocode_result = self.GMAPS.reverse_geocode((lat, lng))
        if reverse_geocode_result == []:
            logging.error("GMAPS reverse geocode request failed.")
            raise ValueError("GMAPS reverse geocode request failed.")

        # Parse through the results to find the city and state name
        city_name = None
        state_name = None
        for result in reverse_geocode_result:
            for component in result["address_components"]:
                if (
                    "locality" in component["types"]
                ):  # 'locality' often represents the city
                    city_name = component["long_name"]
                if (
                    "administrative_area_level_1" in component["types"]
                ):  # represents the state
                    state_key = "short_name" if short_state_name else "long_name"
                    state_name = component[state_key]

            # Break if both city and state names are found
            if city_name and state_name:
                break

        if not city_name:
            logging.error("City name cannot be found with given information")
            raise ValueError("City name cannot be found with given information")
        if not state_name:
            logging.error("State name cannot be found with given information")
            raise ValueError("State name cannot be found with given information")

        return f"{city_name}, {state_name}"

    # TODO(P0): Add return type hint.
    def get_directions(self, origin: str, destination: str) -> List[Dict[str, Any]]:
        """
        Get directions between two strings.

        The origin/destination strings can be anything that Google Maps API accepts.

        Returns:
        List[Dict[str, Any]]: A list of dictionaries containing directions information.
        Each dictionary may contain the following keys:
        - 'bounds': Dict[str, Any]: Northeast and southwest boundaries of the route.
            - 'northeast': Dict[str, float]: Lat-long of northeast boundary.
            - 'southwest': Dict[str, float]: Lat-long of southwest boundary.
        - 'copyrights': str: Copyrights notice.
        - 'legs': List[Dict[str, Any]]: Details about each leg of the route.
            - 'distance': Dict[str, Any]: Distance information of the leg.
            - 'duration': Dict[str, Any]: Duration information of the leg.
            - 'start_address': str: Starting address of the leg.
            - 'end_address': str: Ending address of the leg.
            - 'steps': List[Dict[str, Any]]: Each step of the leg.
                - 'distance': Dict[str, Any]: Distance information of the step.
                - 'duration': Dict[str, Any]: Duration information of the step.
                - 'start_location': Dict[str, float]: Start location of the step in lat-long.
                - 'end_location': Dict[str, float]: End location of the step in lat-long.
                - 'html_instructions': str: HTML instructions for the step.
                - 'polyline': Dict[str, str]: Polyline of the step.
                - 'travel_mode': str: Travel mode of the step.
            - ... (other possible fields in the 'legs' dictionary)
        - 'overview_polyline': Dict[str, str]: Overview polyline of the route.
            - 'points': str: Polyline points.
        - 'summary': str: Summary of the route.
        - 'warnings': List[str]: Warnings about the route.

        Example of usage:
        GoogleMaps.get_directions("New York, NY", "Los Angeles, CA")

        References:
        Google Maps Directions API: https://developers.google.com/maps/documentation/directions/get-directions#DirectionsResponses
        Python Client Library: https://github.com/googlemaps/google-maps-services-python
        """

        # Send the request to the Routes API
        # Based on https://github.com/googlemaps/google-maps-services-python/blob/645e07de5a27c4c858b2c0673f0dd6f23ca62d28/googlemaps/directions.py#L153
        try:
            directions_result = self.GMAPS.directions(origin, destination)
        except Exception as e:
            logging.error("GMAPS Directions Request Failed: ", e)
            raise

        # Consider returning instead of erroring, depending on client.
        if len(directions_result) == 0:
            logging.error(
                "No routes found in get_directions from %s to %s.", origin, destination
            )
            raise ValueError("No routes found in get_directions.")

        return directions_result


if __name__ == "__main__":
    # Use google maps API to get distance between two zip codes.
    gmaps_utils = GoogleMapsUtils()
    # print(gmaps_utils.get_distance_miles("10027", "08536"))
    # print(gmaps_utils.get_distance_miles("New York, NY 10027", "Plainsboro, NJ 08536"))
    # print(gmaps_utils.get_distance_miles("Sunnyvale", "San Francisco"))
    # print(gmaps_utils.get_lat_lng("Sunnyvale", "CA", "USA", "94085"))
    # print(gmaps_utils.get_tz_from_zip("94085", "USA"))

    # print(gmaps_utils.get_lat_lng("New York", "NY", "US"))

    # print(
    #     gmaps_utils.get_directions(
    #         "1024 jena ter sunnyvale ca", "1250 lakeside drive sunnyvale"
    #     )
    # )
    # print(gmaps_utils.get_lat_lng(city="York", state="", country="US"))
    # # Test UK
    # print(gmaps_utils.get_lat_lng(city="York", state="", country="UK"))
    # # city Texarkana state AR country US

    # print(
    #     gmaps_utils.get_zip_from_city_state(
    #         city="Texarkana", state="AR", country_code="US"
    #     )
    # )
    #   "input": "{'lane_hauled_origin': 'Chicago, IL', 'lane_hauled_dest': 'St. Louis, MO'}"
    # s = gmaps_utils.get_county_from_address("Chicago, IL")
    # print(s)
    e = gmaps_utils.get_county_from_address("Williamsburg, VA")
    print(e)
