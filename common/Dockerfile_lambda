# Dockerfile used for lambdas that contains the common library.
# Set a base image that includes Lambda runtime API:
# Public docker image: https://gallery.ecr.aws/lambda/python

FROM public.ecr.aws/lambda/python:3.8

# Create the truce directory to hold all files and set it as the working directory
WORKDIR /var/task/truce
ENV PYTHONPATH="${PYTHONPATH}:/var/task"

# Add git, setup, and install common library and requirements.
ADD .git .git
ADD common common
COPY setup.py .

RUN pip3 install .
RUN pip3 install -r common/requirements.txt
