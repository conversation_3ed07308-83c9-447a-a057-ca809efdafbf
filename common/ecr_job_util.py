import datetime
import logging
import os
import sys
import traceback
import typing

import dateutil

from common import mail


# TODO(P0): #1807 Have scorer and ingestion using this.
# TODO(P0): Considering making this a class.


# If someone modifies logging behavior after this is called, logs won't go to this file.
# To avoid this, call this function at the top of the file.
def set_up_logging(
    filename: str, verbose_level: str = "INFO", log_to_stdout: bool = False
) -> str:
    numeric_level = getattr(logging, verbose_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError("Invalid log level: %s" % verbose_level)

    # Create logs directory if it doesn't exist.
    if not os.path.isdir("logs"):
        os.makedirs("logs")

    now_str = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H-%M-%S")
    log_filename = f"logs/{filename}_{now_str}.log"
    log_fmt_str = "%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s"

    # Remove all handlers from the root logger.
    logging.getLogger().handlers = []

    # Configure root logger
    logging.basicConfig(
        filename=log_filename,
        format=log_fmt_str,
        level=numeric_level,
        datefmt="%H:%M:%S",
    )

    # Log to stdout if needed
    if log_to_stdout:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(log_fmt_str, datefmt="%H:%M:%S"))
        logging.getLogger().addHandler(console_handler)

    # Set sqlalchemy logging to ERROR.
    # logging.getLogger('sqlalchemy.engine').setLevel(logging.ERROR)

    return log_filename


# Call this function at the top of the file to avoid log file not created in some cases (see above).
# We started using cloudwatch logging instead. Prefer to use common/cloudwatch_log_utils.py


def set_up(
    job_name: str,
    set_up_logging_flag: bool = False,
    verbose_level: str = "INFO",
    log_to_stdout: bool = False,
) -> typing.Union[
    typing.Tuple[datetime.datetime, str, mail.MailAPI],
    typing.Tuple[datetime.datetime, str, mail.MailAPI, str],
]:
    """
    Sets up the job by creating a MailAPI object and setting up logging.

    Args:
    - job_name (str): Name of the job (such as "Foo Runner").
    - set_up_logging_flag (bool, optional): Whether to set up logging. Defaults to False.
    - verbose_level (str, optional): The verbose level for logging. Defaults to "INFO".
    - log_to_stdout (bool, optional): Whether to log messages to stdout. Defaults to False.

    Returns:
    - tuple: Tuple containing the job start time, job start time in central time, and the MailAPI object,
            and the log filename if set_up_logging_flag is True.
    """
    job_start_time = datetime.datetime.now(datetime.timezone.utc)
    # Check if current date is in daylight savings time for central time.
    if datetime.datetime.now(
        dateutil.tz.gettz("America/Chicago")
    ).dst() != datetime.timedelta(0):
        central_tz = "CDT"
    else:
        central_tz = "CST"
    job_start_timestr_central = (
        datetime.datetime.now(tz=dateutil.tz.gettz("US/Central")).strftime("%x %H:%M ")
        + central_tz
    )

    mail_api = mail.MailAPI(gmail=False)

    return_vals = (job_start_time, job_start_timestr_central, mail_api)

    if set_up_logging_flag:
        log_filename = set_up_logging(
            "_".join(job_name.lower().split(" ")), verbose_level, log_to_stdout
        )
        return return_vals + (log_filename,)

    return return_vals


def success_email(
    mail_api: mail.MailAPI,
    dev: bool,
    job_name: str,
    job_start_time: datetime.datetime,
    job_start_timestr_central: str,
    mail_header: str = "",
    mail_body: str = "",
    mail_report_attachments: typing.List[str] = [],
):
    dev_str = "[Dev]" if dev else ""
    job_time = datetime.datetime.now(datetime.timezone.utc) - job_start_time

    logging.info(f"{job_name}{dev_str} took {job_time}.")
    logging.info("Mailing success report.")
    mail_api.send_email_aws(
        frm="<EMAIL>",
        to=["<EMAIL>"],
        subject=f"[{job_name} Report]{dev_str}{mail_header} {job_start_timestr_central}",
        body=f"{job_name}{dev_str} took {job_time}. \n" + mail_body,
        attachments=mail_report_attachments,
    )


def format_exception(
    e: Exception,
) -> str:
    """Constructs the body of a failure email."""
    failure_mail_body = (
        f'Severe Error "{type(e).__name__}": {e}\n\n'
        f"Error:\n"
        f'\ttype: "{sys.exc_info()[0]}"\n'
        f'\tvalue: "{sys.exc_info()[1]}"\n'
        f"\ttraceback:\n{traceback.format_exc()}"
    )
    return failure_mail_body


def failure_email(
    e: Exception,
    mail_api: mail.MailAPI,
    dev: bool,
    job_name: str,
    job_start_time: datetime.datetime = None,  # UTC
    job_start_timestr_central: str = "",  # Central
    mail_header: str = "",
    mail_body: str = "",
    mail_report_attachments: typing.List[str] = [],
) -> None:
    """
    Sends a failure email based on exception stacktrace.

    Args:
        e: Exception that was raised.
        mail_api: MailAPI object to send email with.
        dev: Whether this is a dev run or in the dev environment.
        job_name: Name of the job.
        job_start_time: Time the job started.
        job_start_timestr_central: Time the job started in central time.
        mail_header: Additional header for the email.
        mail_body: Additional body for the email.
        mail_report_attachments: List of paths to files to attach to the email.
    """
    dev_str = "[Dev]" if dev else ""
    current_time = datetime.datetime.now(datetime.timezone.utc)
    job_time = current_time - job_start_time if job_start_time else None

    failure_mail_body = ""
    if job_time:
        failure_mail_body += f"{job_name}{dev_str} took {job_time}. \n"

    failure_mail_body += mail_body + "\n" + format_exception(e)

    logging.error(f"{type(e).__name__}: {e}")
    # Either use the job start time in central, or the current time in UTC.
    job_timestr = job_start_timestr_central or current_time.strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    mail_api.send_email_aws(
        frm="<EMAIL>",
        to=["<EMAIL>"],
        subject=f"[{job_name} Failure Report]{dev_str}{mail_header} {job_timestr}",
        body=failure_mail_body,
        attachments=mail_report_attachments,
    )
    logging.error("Mailed error report.")


if __name__ == "__main__":
    logging.info("INFO")
    logging.debug("DEBUG")
    logging.warning("WARNING")
    logging.error("ERROR")

    job_start_time, job_start_timestr_central, mail_api, log_filename = set_up(
        "Test Job 1", verbose_level="DEBUG", log_to_stdout=True
    )

    logging.info("INFO")
    logging.debug("DEBUG")
    logging.warning("WARNING")
    logging.error("ERROR")
    job_start_time, job_start_timestr_central, mail_api, log_filename = set_up(
        "Test Job 2"
    )

    logging.info("INFO")
    logging.debug("DEBUG")
    logging.warning("WARNING")
    logging.error("ERROR")

    set_up_logging("test_job_3", verbose_level="ERROR", log_to_stdout=True)

    logging.info("INFO")
    logging.debug("DEBUG")
    logging.warning("WARNING")
    logging.error("ERROR")
