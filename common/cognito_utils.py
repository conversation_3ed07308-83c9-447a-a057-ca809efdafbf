import logging
import os
import shelve
from typing import Dict
from typing import List
from typing import Optional

import boto3
import botocore

from common import ecr_job_util
from common import mail
from common import timeout_utils

FREIGHTGPT_DEV_COGNITO_USER_POOL_ID = "us-east-1_OtlXNpE6h"
FREIGHTGPT_PROD_COGNITO_USER_POOL_ID = "us-east-1_azsO85PeM"
# Initialize the boto3 Cognito IDP client
COGNITO_IDP_CLIENT = boto3.client("cognito-idp")


class CognitoUserData:
    def __init__(
        self, is_dev_env: bool, user_pool_id: str, mail_api: mail.MailAPI = None
    ):
        """
        Retrieves a list of all users from Cognito,
        Gets info for each user,
        Stores everything on disk, indexed by user_id
        """
        self.is_dev_env = is_dev_env
        self.user_pool_id = user_pool_id
        self.store_filename = f"user_info_store{'_dev' if self.is_dev_env else ''}"
        if mail_api is None:
            logging.warning(
                "Failure emails will not be sent as mail_api is not populated"
            )
        else:
            self.mail_api = mail_api

    def get_all_users(self, filter: Optional[str] = None) -> List[Dict[str, str]]:
        """
        Retrieves all users from the specified Cognito User Pool,
        which is determined based on the environment (development or production).
        An optional filter can be applied to narrow down the results based on
        specific attributes.

        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/cognito-idp/client/list_users.html

        Parameters:
            filter (Optional[str]): An optional filter string to apply to the
                                    user list. The filter should follow the format specified in the
                                    Cognito API documentation.

        Returns:
            List[Dict[str, str]]: A full list of users retrieved from the Cognito User Pool. Each user is represented as a
                                        dictionary of user attributes.

        Example:
            >>> users = get_all_users(filter="name='Roop Pal'") # Filters as "attribute_name=attribute_value"
            [{'username': 'user123', 'sub': 'a7151349-d506-459c-b4a9-157bd38fe148',
            'email_verified': 'true', 'name': 'John Doe', 'email': '<EMAIL>'}]
        """
        list_users_params = {"UserPoolId": self.user_pool_id}
        if filter:
            list_users_params["Filter"] = filter

        all_users = []
        pagination_token = None

        try:
            with timeout_utils.Timeout(seconds=120):
                while True:
                    if pagination_token:
                        list_users_params["PaginationToken"] = pagination_token
                    response = COGNITO_IDP_CLIENT.list_users(**list_users_params)
                    all_users.extend(response.get("Users", []))
                    pagination_token = response.get("PaginationToken")
                    if not pagination_token:
                        break
        except timeout_utils.TimeoutException as e:
            logging.error(
                f"The operation timed out: {e}. Some user data may not have been retrieved."
            )

        return all_users

    def pull_all_users(self) -> None:
        if os.getenv("DRY_MODE") or os.getenv("ENV") == "TEST":
            logging.warning("Ignoring method call as running in Dry/Test mode")
            return
        try:
            with shelve.open(self.store_filename) as store:
                user_list = self.get_all_users()
                for user in user_list:
                    store[user["Username"]] = {
                        attr["Name"]: attr["Value"] for attr in user["Attributes"]
                    }
        except Exception as e:
            logging.error("Error getting users: %s", e)
            if self.mail_api:
                ecr_job_util.failure_email(
                    e=e,
                    mail_api=self.mail_api,
                    dev=self.is_dev_env,
                    job_name="Cognito User Pull",
                    mail_body="Failed to retrieve users from cognito",
                )

    # Define a function to get user attributes using admin access
    def get_cognito_user_data(self, user_id: str) -> Dict[str, str]:
        """
        Returns a dictionary containing user attributes.
        Object from Cognito:
        [
            {'Name': 'sub', 'Value': '7e49a6d5-a472-4712-a781-edd1c216ab2a'},
            {'Name': 'email_verified', 'Value': 'true'},
            {'Name': 'custom:customer_type_int', 'Value': '3'},
            {'Name': 'name', 'Value': 'Roop Pal'},
            {'Name': 'custom:customer_type', 'Value': 'Other'},
            {'Name': 'email', 'Value': '<EMAIL>'},
            {'Name': 'custom:company', 'Value': 'Truce Tech Inc'}
        ] turns into
        {
            'sub': '7e49a6d5-a472-4712-a781-edd1c216ab2a',
            'email_verified': 'true',
            'custom:customer_type_int': '3',
            'name': 'Roop Pal',
            'custom:customer_type': 'Other',
            'email': '<EMAIL>',
            'custom:company': 'Truce Tech Inc'
        }
        """
        if os.getenv("DRY_MODE") or os.getenv("ENV") == "TEST":
            logging.warning("Skipping storage as running in Dry/Test mode")
            return self._pull_user_from_cognito(user_id)

        with shelve.open(self.store_filename) as store:
            if user_id in store:
                return store[user_id]

            user_attributes = self._pull_user_from_cognito(user_id)
            store[user_id] = user_attributes
            return store[user_id]

    def _get_user_data(self, user_id: str) -> Dict[str, str]:
        """
        Retrieve user data from AWS Cognito using the admin_get_user API.
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/cognito-idp/client/admin_get_user.html

        Args:
            user_id (str): The ID of the user to retrieve data for.

        Returns:
            Dict[str, str]: A dictionary containing the user data if found,
            or an empty dictionary if the user is not found.

        Raises:
            botocore.exceptions.ClientError: If there is an error calling the
                                             admin_get_user API, with handling for UserNotFoundException.
        """
        try:
            response = COGNITO_IDP_CLIENT.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=user_id,
            )
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "UserNotFoundException":
                logging.warning(f"User {user_id} not found with admin_get_user.")
                return {}
            raise e

        if not response:
            return {}

        return response

    def _pull_user_from_cognito(self, user_id: str):
        try:
            user_attributes = None
            user_data = self._get_user_data(user_id)

            # If users found, access user attributes
            if raw_user_attributes := user_data.get("UserAttributes", []):
                user_attributes = {
                    attr["Name"]: attr["Value"] for attr in raw_user_attributes
                }

            # Otherwise, run an expanded search on the "sub" property.
            else:
                filtered_users = self.get_all_users(filter=f'sub="{user_id}"')

                # Access user attributes
                if filtered_users:
                    user_attributes = {
                        attr["Name"]: attr["Value"]
                        for attr in filtered_users[0]["Attributes"]
                    }

            if not user_attributes:
                raise Exception("Could not access user attributes from Cognito.")

        except Exception as e:
            logging.error("Error getting user %s: %s", user_id, e)
            if self.mail_api:
                ecr_job_util.failure_email(
                    e=e,
                    mail_api=self.mail_api,
                    dev=self.is_dev_env,
                    job_name="Cognito User Pull",
                    mail_body=f"Failed to retrieve user {user_id} from cognito",
                )
                return {}

        if "name" not in user_attributes:
            logging.warning("User %s does not have a name.", user_id)

        return user_attributes

    def update_all_user_attributes(
        self, attribute_name: str, attribute_value: str
    ) -> None:
        """
        General method to update a specific attribute for all users in the Cognito User Pool.

        This is a utility used by scripts so we use print for now. Change to logging if used in a job.

        Args:
            attribute_name (str): The name of the attribute to update.
            attribute_value (str): The value to set for the attribute.

        This method will add the attribute if it does not already exist for a user.
        """
        user_list = self.get_all_users()
        for user in user_list:
            try:
                # Check if the attribute exists and update or set it
                existing_attributes = [attr["Name"] for attr in user["Attributes"]]
                action = (
                    "Adding"
                    if attribute_name not in existing_attributes
                    else "Updating"
                )
                print(
                    f"{action} '{attribute_name}' attribute for user {user['Username']}"
                )

                update_response = COGNITO_IDP_CLIENT.admin_update_user_attributes(
                    UserPoolId=self.user_pool_id,
                    Username=user["Username"],
                    UserAttributes=[{"Name": attribute_name, "Value": attribute_value}],
                )
                print(
                    f"Successfully {action.lower()} '{attribute_name}' for user {user['Username']}"
                )
                if (
                    not update_response.get("ResponseMetadata", {}).get(
                        "HTTPStatusCode"
                    )
                    == 200
                ):
                    raise Exception(
                        f"Failed to {action.lower()} '{attribute_name}' for user {user['Username']}"
                    )
            except botocore.exceptions.ClientError as e:
                print(
                    f"\n\nFailed to {action.lower()} '{attribute_name}' for user {user['Username']}: {e}\n\n"
                )


if __name__ == "__main__":
    import time

    start = time.perf_counter()
    user_data_obj = CognitoUserData(
        user_pool_id=FREIGHTGPT_PROD_COGNITO_USER_POOL_ID,
        is_dev_env=False,
        mail_api=mail.MailAPI(gmail=False),
    )
    all_users = user_data_obj.get_all_users()
    # user_data_obj = CognitoUserData(user_pool_id=FREIGHTGPT_DEV_COGNITO_USER_POOL_ID, is_dev_env=True, mail_api=mail.MailAPI(gmail=False))
    # print(user_data_obj._pull_user_from_cognito("7e49a6d5-a472-4712-a781-edd1c216ab2a"))
    # print(time.perf_counter() - start)

    # user_data_obj = CognitoUserData(
    #     user_pool_id=FREIGHTGPT_PROD_COGNITO_USER_POOL_ID,
    #     is_dev_env=False, mail_api=mail.MailAPI(gmail=False)
    # )
    # user_data_obj.update_all_user_attributes ("custom:priorSignIn", "1")
