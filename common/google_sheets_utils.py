import logging
import os
import pickle

import pandas as pd
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from common.constants import PROJECT_ROOT


# TODO(P1): Consider standardizing calss names across mail.py, google_maps_utils.py, and here.
class GoogleSheetsUtils:
    # If modifying these SCOPES, delete the file gsheets_token.pickle.
    SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]
    SHEET_URL_TEMPLATE = "https://docs.google.com/spreadsheets/d/{sheet_id}/edit#gid=0"
    SHEETS_CREDENTIALS_DIR = os.path.join(PROJECT_ROOT, "common", "credentials_files")

    def __init__(self) -> None:
        self._service = self._get_service()

    def _get_service(self):
        """
        Gets the Google Sheets API service.
        """
        creds = None
        # The file token.pickle stores the user's access and refresh tokens, and is
        # created automatically when the authorization flow completes for the first
        # time.
        token_pickle_path = os.path.join(
            self.SHEETS_CREDENTIALS_DIR, "gsheets_token.pickle"
        )
        if os.path.exists(token_pickle_path):
            with open(token_pickle_path, "rb") as token:
                creds = pickle.load(token)

        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    os.path.join(
                        self.SHEETS_CREDENTIALS_DIR, "gsheets_credentials.json"
                    ),
                    self.SCOPES,
                )
                creds = flow.run_local_server(port=0)

            # If running in Lambda, it is readonly filesystem, so do not dump pickle.
            if os.getenv("FS_TYPE") != "RO":
                with open(token_pickle_path, "wb") as token:
                    pickle.dump(creds, token)

        service = build("sheets", "v4", credentials=creds)
        return service

    def get_sheet(self, spreadsheet_id: str, sheet_name: str):
        # Call the Sheets API
        sheet = self._service.spreadsheets()
        if spreadsheet_id:
            self.spreadsheet_id = spreadsheet_id
        if sheet_name:
            self.sheet_name = sheet_name
        result = (
            sheet.values().get(spreadsheetId=spreadsheet_id, range=sheet_name).execute()
        )
        return result

    # TODO(P1): Don't assume header row, and don't force skiprows to be -1 for that case.
    def load_sheet_into_dataframe(
        self, spreadsheet_id: str, sheet_name: str, skiprows: int = 0
    ):
        """
        Loads a Google Sheet into a pandas DataFrame.

        Args:
            spreadsheet_id (str): The ID of the Google Spreadsheet.
            sheet_name (str): The name of the sheet within the spreadsheet.
            skiprows (int, optional): The number of rows to skip from the top of the sheet. Defaults to 0.

        Returns:
            pandas.DataFrame: The loaded sheet data as a DataFrame.
        """
        result = self.get_sheet(spreadsheet_id, sheet_name)
        if values := result.get("values", []):
            return pd.DataFrame(values[skiprows + 1 :], columns=values[skiprows])
        else:
            raise ValueError("No data found.")

    def search_sheet_for_value(
        self, search_value, spreadsheet_id: str, sheet_name: str
    ) -> bool:
        """
        Searches a Google Sheet for a specific value.

        Args:
            search_value (Any): The value to search for in the sheet.
            spreadsheet_id (str): The ID of the Google Spreadsheet.
            range_name (str): The range of cells to search within the spreadsheet.

        Returns:
            bool: True if the search value is found in any row of the specified range, False otherwise.
        """

        result = self.get_sheet(spreadsheet_id, sheet_name)
        values = result.get("values", [])
        return any(search_value in row for row in values)

    def clear_sheet(self, sheet_id: str, sheet_name: str, skiprows: int) -> None:
        """
        Clears a Google Sheet, skipping the specified number of rows from the top of the sheet.

        Args:
            df (pandas.DataFrame): The DataFrame to write to the sheet.
            sheet_id (str): The ID of the Google Spreadsheet.
            sheet_name (str): The name of the sheet within the spreadsheet.
            skiprows (int): The number of rows to skip from the top of the sheet.
        """

        # Specify the range to update, considering the skiprows parameter
        start_row = skiprows + 1  # Google Sheets is 1-indexed
        range_to_update = f"{sheet_name}!A{start_row}:AA1000"

        # Update the sheet
        result = (
            self._service.spreadsheets()
            .values()
            .clear(
                spreadsheetId=sheet_id,
                range=range_to_update,
            )
            .execute()
        )
        sheet_url = self.SHEET_URL_TEMPLATE.format(sheet_id=sheet_id)
        logging.info(f"{result.get('updatedCells')} cells cleared in {sheet_url}.")

    def write_df_to_sheet(
        self, df: pd.DataFrame, sheet_id: str, sheet_name: str, skiprows: int
    ) -> None:
        """
        Writes a pandas DataFrame to a Google Sheet.

        Args:
            df (pandas.DataFrame): The DataFrame to write to the sheet.
            sheet_id (str): The ID of the Google Spreadsheet.
            sheet_name (str): The name of the sheet within the spreadsheet.
            skiprows (int): The number of rows to skip from the top of the sheet.
        """
        self.clear_sheet(sheet_id, sheet_name, skiprows + 1)
        # Convert DataFrame to list of lists
        values = [df.columns.tolist()] + df.values.tolist()

        # Build the request body for updating the sheet
        body = {"values": values}

        # Specify the range to update, considering the skiprows parameter
        start_row = skiprows + 1  # Google Sheets is 1-indexed
        range_to_update = f"{sheet_name}!A{start_row}"

        # Update the sheet
        result = (
            self._service.spreadsheets()
            .values()
            .update(
                spreadsheetId=sheet_id,
                range=range_to_update,
                valueInputOption="USER_ENTERED",
                body=body,
            )
            .execute()
        )
        sheet_url = self.SHEET_URL_TEMPLATE.format(sheet_id=sheet_id)
        logging.info(f"{result.get('updatedCells')} cells updated in {sheet_url}.")

    def append_df_to_sheet(
        self, df: pd.DataFrame, sheet_id: str, sheet_name: str
    ) -> None:
        """
        Appends a pandas DataFrame to a Google Sheet.

        Args:
            df (pandas.DataFrame): The DataFrame to append to the sheet.
            sheet_id (str): The ID of the Google Spreadsheet.
            sheet_name (str): The name of the sheet within the spreadsheet.
        """
        # Convert DataFrame to list of lists
        values = df.values.tolist()

        # Build the request body for updating the sheet
        body = {"values": values}

        range_to_update = f"{sheet_name}!A1"

        # Update the sheet
        result = (
            self._service.spreadsheets()
            .values()
            .append(
                spreadsheetId=sheet_id,
                range=range_to_update,
                valueInputOption="USER_ENTERED",
                body=body,
            )
            .execute()
        )
        sheet_url = self.SHEET_URL_TEMPLATE.format(sheet_id=sheet_id)

        # TODO (P1, Roop): cells do get updated, but this comes as None in the logs.
        logging.info(f"{result.get('updatedCells')} cells updated in {sheet_url}.")

    def set_row_height(
        self,
        spreadsheet_id: str,
        sheet_id: int,
        start_index: int,
        end_index: int,
        pixel_size: int = 21,  # default row height in Google Sheets
    ):
        requests = [
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "ROWS",
                        "startIndex": start_index,
                        "endIndex": end_index,
                    },
                    "properties": {"pixelSize": pixel_size},
                    "fields": "pixelSize",
                }
            }
        ]
        body = {"requests": requests}
        response = (
            self._service.spreadsheets()
            .batchUpdate(spreadsheetId=spreadsheet_id, body=body)
            .execute()
        )
        return response
