# Utils for managing and searching an opensearch index.
# You can only access opensearch as es_user
# Opensearch dashboard: https://us-east-1.console.aws.amazon.com/aos/home?region=us-east-1#opensearch/dashboard
# Opensearch user: https://us-east-1.console.aws.amazon.com/iam/home?region=us-east-1#/users/details/es_user?section=permissions
import collections
import logging
import pprint
from typing import Any
from typing import Dict
from typing import Iterator
from typing import List
from typing import Tuple
from typing import Union

import pyspark
from opensearchpy import OpenSearch
from opensearchpy import RequestsHttpConnection
from requests_aws4auth import AWS4Auth

from common import credentials
from common import init_main

PROJECT_ROOT = init_main.initialize()

CREDENTIALS = credentials.Credentials()

SERVICE = "es"
REGION = "us-east-1"
AWSAUTH = AWS4Auth(
    CREDENTIALS.es_user_access_key_id,
    CREDENTIALS.es_user_secret_access_key,
    REGION,
    SERVICE,
)
OS_HOST = "search-freightgpt-carrier-search-v2-7qmivck77r5as2ixgvb654awim.us-east-1.es.amazonaws.com"
OS_CLIENT = OpenSearch(
    hosts=[{"host": OS_HOST, "port": 443}],
    http_auth=AWSAUTH,
    use_ssl=True,
    verify_certs=True,
    connection_class=RequestsHttpConnection,
    timeout=300,
)

####################################################################
#         Index / Ingestion Logic
####################################################################


def create_index(
    index_name: str, index_mappings: Dict[str, Dict[str, str]]
) -> Dict[str, str | bool]:
    """Creates an opensearch index."""
    response = OS_CLIENT.indices.create(index=index_name, body=index_mappings)
    if not response["acknowledged"]:
        logging.error(f"Failed to create index with response {response}")
        raise ValueError("Index creation failed")

    return response


def delete_index(index_name: str) -> Dict[str, str | bool]:
    """Deletes an opensearch index"""
    response = OS_CLIENT.indices.delete(index_name)
    if not response["acknowledged"]:
        logging.error(f"Failed to delete index with response {response}")
        raise ValueError("Index deletion failed")

    return response


def send_partition_to_opensearch(
    partition: Iterator[pyspark.sql.Row],
    index_name: str,
    id_column: str,
    batch_size: int,
) -> None:
    """
    Sends a partition of a spark dataframe to an OpenSearch index for indexing.
    It generates action dictionaries for each row in the partition, specifying how the data should be indexed,
    and then performs bulk indexing operations.

    Args:
        partition (Iterator[pyspark.sql.Row]): An iterator over a partition of rows from a Spark DataFrame.
        index_name (str): The name of the OpenSearch index where data is to be indexed.
        id_column (str): The column name in the DataFrame to use as the unique ID for each document in the index.
        batch_size (int): The number of rows to accumulate before sending a bulk index request to OpenSearch.
    """

    def bulk_index_ingest(actions: list[Dict[str, Any]]) -> None:
        response = OS_CLIENT.bulk(body=actions, index=index_name)
        if response["errors"]:
            logging.error(f"ERROR INDEXING: {response}")

    # Iterate over partition
    actions = []
    for row in partition:
        row_dict = row.asDict()
        actions.append(
            {
                "index": {
                    "_index": index_name,
                    "_id": row_dict[id_column],
                }
            }
        )
        action = {
            column: row_dict[column] for column in row_dict if column != id_column
        }
        actions.append(action)

        # Ingest
        if len(actions) % batch_size == 0:
            bulk_index_ingest(actions)
            actions = []

    # Ingest remaining rows
    if actions:
        bulk_index_ingest(actions)


def ingest_parquet_file(
    spark_session: pyspark.sql.SparkSession,
    parquet_file_path: str,
    index_name: str,
    id_column: str,
    partitions: int = 10,
    batch_size: int = 1000,
    show: bool = False,
) -> None:
    """
    Reads a Parquet file into a DataFrame, repartitions it, and sends each partition to OpenSearch for indexing.

    Args:
        spark_session (pyspark.sql.SparkSession): The Spark session to use for reading the Parquet file.
        parquet_file_path (str): The file path to the Parquet file to ingest.
        index_name (str): The name of the OpenSearch index where the data will be indexed.
        id_column (str): The column name in the DataFrame to use as the unique ID for each document in the index.
        partitions (int, optional): The number of partitions to repartition the DataFrame into. Defaults to 10.
        batch_size (int, optional): The number of rows to accumulate before sending a bulk index request to OpenSearch. Defaults to 1000.
        show (bool, optional): Whether to show the DataFrame after reading the Parquet file. Defaults to False.
    """

    # Read Parquet file
    df = spark_session.read.parquet(parquet_file_path)
    if "__index_level_0__" in df.columns:
        df = df.drop("__index_level_0__")

    # Optionally show dataframe
    if show:
        df.show()

    # Partition and ingest
    df = df.repartition(partitions)
    df.foreachPartition(
        lambda partition: send_partition_to_opensearch(
            partition,
            index_name=index_name,
            id_column=id_column,
            batch_size=batch_size,
        )
    )


####################################################################
#         Query / Filter Builders
#         https://opensearch.org/docs/latest/about/
####################################################################


class MatchQuery:
    """
    Constructs a query that looks for an exact string match against a field.
    https://opensearch.org/docs/latest/query-dsl/full-text/match/

    Args:
        query (str): The query string to match.
        column_name (str): The name of the document field to search.
        boost (int, optional): How much to relatively boost this query if running with other queries. Defaults to 3.
        suffix (str, optional): An optional suffix to append to the query result. Defaults to "".
    """

    def __init__(
        self, query: str, column_name: str, boost: int = 3, suffix: str = ""
    ) -> None:
        self.on = column_name
        self.query_type = "match_query"
        self.json = {
            "match": {
                column_name: {
                    "query": query,
                    "boost": boost,
                    "_name": "exact_match" + (f"_{suffix}" if suffix else ""),
                }
            }
        }


class FullPhraseQuery:
    """
    Constructs a query that looks for an an entire phrase match on the entire field.
    https://opensearch.org/docs/latest/query-dsl/full-text/match-phrase/

    Args:
        query (str): The query string to match.
        column_name (str): The name of the document field to search.
        boost (int, optional): How much to relatively boost this query if running with other queries. Defaults to 3.
        suffix (str, optional): An optional suffix to append to the query result. Defaults to "".
    """

    def __init__(
        self, query: str, column_name: str, boost: int = 3, suffix: str = ""
    ) -> None:
        self.on = column_name
        self.query_type = "full_phrase_query"
        self.json = {
            "match_phrase": {
                column_name: {
                    "query": query,
                    "boost": boost,
                    "_name": "exact_match" + (f"_{suffix}" if suffix else ""),
                }
            }
        }


class WildcardQuery:
    """
    Constructs a query that looks for an substring match against a field.
    https://opensearch.org/docs/latest/query-dsl/term/wildcard/

    Args:
        query (str): The query string to match.
        column_name (str): The name of the document field to search.
        boost (int, optional): How much to relatively boost this query if running with other queries. Defaults to 3.
        suffix (str, optional): An optional suffix to append to the query result. Defaults to "".
    """

    def __init__(
        self, query: str, column_name: str, boost: int = 3, suffix: str = ""
    ) -> None:
        self.on = column_name
        self.query_type = "wildcard_query"
        self.json = {
            "wildcard": {
                column_name: {
                    "value": f"*{query}*",
                    "boost": boost,
                    "_name": "wildcard_match" + (f"_{suffix}" if suffix else ""),
                }
            }
        }


class FuzzyQuery:
    """
    Constructs a query that looks for a fuzzy match against a field.
    https://opensearch.org/docs/latest/query-dsl/term/fuzzy/

    Args:
        query (str): The query string to match with fuzziness.
        column_name (str): The name of the document field to search.
        fuzziness (str, optional): The fuzziness setting. Defaults to "AUTO", can be "1" (less fuzziness) or "2" (more fuzziness).
        max_expansions (int, optional): Maximum number of terms to which the query will expand. Defaults to 1000.
        boost (int, optional): How much to relatively boost this query if running with other queries. Defaults to 3.
        suffix (str, optional): An optional suffix to append to the query name. Defaults to "".
    """

    def __init__(
        self,
        query: str,
        column_name: str,
        fuzziness: str = "AUTO",
        max_expansions: int = 1000,
        boost: int = 3,
        suffix: str = "",
    ) -> None:
        self.on = column_name
        self.query_type = "fuzzy_query"
        self.json = {
            "match": {
                column_name: {
                    "query": query,
                    "fuzziness": fuzziness,
                    "boost": boost,
                    "_name": "fuzzy_match" + (f"_{suffix}" if suffix else ""),
                    "max_expansions": max_expansions,
                }
            }
        }


class DSLQuery:
    """
    Represents a DSL (Domain Specific Language) query for Opensearch that allows for complex searches.
    https://opensearch.org/docs/latest/query-dsl/

    Args:
        query (str): The query string to match.
        column_name (str): The name of the document field to search.
        boost (int, optional): How much to relatively boost this query if running with other queries. Defaults to 3.
        suffix (str, optional): An optional suffix to append to the query result. Defaults to "".
    """

    def __init__(
        self, query: str, column_name: str, boost: int = 3, suffix: str = ""
    ) -> None:
        self.on = column_name
        self.query_type = "dsl_query"
        self.json = {
            "query_string": {
                "default_field": column_name,
                "query": query,
                "boost": boost,
                "_name": "dsl_match" + (f"_{suffix}" if suffix else ""),
            }
        }


class RangeFilter:
    """
    Represents a range filter for Opensearch that filters documents based on whether the value of a specified
    field falls within a given range.
    https://opensearch.org/docs/latest/query-dsl/term/range/

    Args:
        column_name (str): The name of the field to filter on.
        from_value (int | float): The lower bound of the range (inclusive).
        to_value (int | float): The upper bound of the range (inclusive).
    """

    def __init__(
        self, column_name: str, from_value: int | float, to_value: int | float
    ) -> None:
        self.on = column_name
        self.query_type = "range_filter"
        self.json = {"range": {column_name: {"gte": from_value, "lte": to_value}}}


class GeoPointDistanceFilter:
    """
    Represents a geospatial distance filter for Opensearch that filters documents based on their proximity to a
    given geographical point.
    https://opensearch.org/docs/latest/aggregations/bucket/geo-distance/

    Args:
        column_name (str): The name of the field containing geospatial data.
        lat (float): The latitude of the point to calculate distances from.
        lng (float): The longitude of the point to calculate distances from.
        distance_miles (int, optional): The maximum distance from the point in miles. Defaults to 25.
    """

    def __init__(
        self, column_name: str, lat: float, lng: float, distance_miles=25
    ) -> None:
        self.on = column_name
        self.query_type = "geopoint_distance_filter"
        self.json = {
            "geo_distance": {
                "distance": f"{distance_miles}miles",
                column_name: [lng, lat],
            }
        }


class OSQueryBuilder:
    """
    A class to build and execute queries against an Opensearch index. This builder
    supports adding queries and filters. The class allows for the combination
    of these queries and filters in flexible ways, supporting both "should" and "must"
    join types for queries, and also provides functionality to scramble the results or
    limit the size of the result set.

    Example usage:
        >>>  query_builder = OSQueryBuilder('my_index')
        >>> query_builder.add_query(MatchQuery(...)) # Add a query
        >>> query_builder.add_filter(GeoPointDistanceFilter(...)) # Add a filter
        >>> results = query_builder.build_and_run(show_query=True, size=10) # Build and run
        >>> query_builder.clear()
    """

    def __init__(self, index_name: str) -> None:
        self._index_name = index_name

        # Keyed by query type and column.
        # match_query -> dot_number -> object
        self._queries = collections.defaultdict(dict)
        self._filters = collections.defaultdict(dict)

    def add_query(
        self,
        query_object: Union[
            MatchQuery, FullPhraseQuery, WildcardQuery, DSLQuery, FuzzyQuery
        ],
    ) -> None:
        """
        Adds a query object to the list of queries.
        """

        # Ensure there is only one type of query for each unique column.
        self.delete_query(
            query_type=query_object.query_type, column_name=query_object.on
        )
        self._queries[query_object.query_type][query_object.on] = query_object

    def add_filter(
        self, filter_object: Union[RangeFilter, GeoPointDistanceFilter]
    ) -> None:
        """
        Adds a filter object to the list of filters.
        """

        # Ensure there is only one type of filter for each unique column.
        self.delete_filter(
            filter_type=filter_object.query_type, column_name=filter_object.on
        )
        self._filters[filter_object.query_type][filter_object.on] = filter_object

    def delete_query(self, query_type: str, column_name: str) -> None:
        """Deletes a query from the queries list"""
        if self._queries.get(query_type, {}).get(column_name):
            del self._queries[query_type][column_name]

    def delete_filter(self, filter_type: str, column_name: str) -> None:
        """Deletes a filter from the filters list"""
        if self._filters.get(filter_type, {}).get(column_name):
            del self._filters[filter_type][column_name]

    def build_and_run(
        self,
        query_join_type: str = "should",
        show_query: bool = False,
        size: int = 50,
        numerical_sort: Tuple[str, str] = (),
        minimum_should_match: int = 1,
    ) -> List[Dict[str, Any]]:
        """
        Builds and runs the query with the provided queries on filters.

        Args:
            query_join_type (str): How the queries should be joined ('should' (logical OR) or 'must'(logical AND)). Default is 'should'.
            show_query (bool): If True, prints the final query before executing. Default is True.
            size (int): The number of results to return. Default is 50.
            numerical_sort (Tuple[str, str]): A tuple specifying the field and the order ('asc' or 'desc') to sort the results numerically. Default is an empty tuple.
            minimum_should_match (int): The minimum number of 'should' clauses that must match for a document to be returned. Default is 1.


        Returns:
            List[Dict[str, Any]]: List of hits from opensearch.

        Example:
            >>> results = query_builder.build_and_run(show_query=True, size=10)
            [
                {"_id":"blah", "_score": 50, "_source":{"name":"viv"}},
                ...
            ]

        Raises:
            AttributeError: If an unrecognized query join type is provided or no queries/filters are set.
        """

        # Check if query join is should or must.
        if query_join_type.lower() not in ["should", "must"]:
            logging.info(
                f"Unrecognized type {query_join_type}. Allowed types are 'should' or 'must'."
            )
            raise AttributeError("Unrecognized query join type")

        # Ensure that you have at least a query or a filter.
        if not self._queries and not self._filters:
            raise AttributeError("Must provide either a query or a filter.")

        # Construct query and filter lists
        query_obj_list = [
            self._queries[match_type][column].json
            for match_type in self._queries
            for column in self._queries[match_type]
        ]

        filter_obj_list = [
            self._filters[match_type][column].json
            for match_type in self._filters
            for column in self._filters[match_type]
        ]

        # Construct base Opensearch query.
        query = {
            "query": {
                "bool": {
                    query_join_type: query_obj_list,
                    "filter": filter_obj_list,
                    "minimum_should_match": minimum_should_match,
                },
            },
            "size": size,
        }

        # If only filters, replace the query section with a match_all
        if not query["query"]["bool"][query_join_type]:
            query["query"]["bool"][query_join_type] = {"match_all": {}}

        # Sort results.
        # TODO (P1): We only have support for sorting by numerical fields right now.
        if numerical_sort:
            sort_params = [
                {column: {"order": order}} for column, order in numerical_sort
            ]
            query["sort"] = sort_params

        # Display query.
        if show_query:
            pprint.pprint(query, indent=4)

        try:
            response = OS_CLIENT.search(body=query, index=self._index_name)
        except Exception as e:
            logging.warning(f"Search failed with exception {e}")
            return None

        return response

    def clear(self, keep_filters: bool = False, keep_queries: bool = False):
        """Clear filters and queries"""
        if not keep_filters:
            self._filters.clear()

        if not keep_queries:
            self._queries.clear()
