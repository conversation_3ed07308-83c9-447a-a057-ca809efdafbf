"""Defines constants used throughout the project.

Includes a utility function to construct named tuples with lowered fields.

Example Usage:
from common.constants import BrokerNames
print(BrokerNames.uber_freight) # Prints "Uber Freight"
"""
import collections

from common import init_main

# Get project root path to enable absolute filepaths.
PROJECT_ROOT = init_main.initialize()

AURORA_DB_NAME = "mvp_db"
AURORA_DB_DEV_NAME = "mvp_db_dev"


def construct_named_tuple(
    name: str, fields: list, upper=False
) -> collections.namedtuple:
    """Returns instance of named tuple with given name and fields.

    Example Usage:

    >>> from common.constants import construct_named_tuple
    >>> Colors = construct_named_tuple("Colors", ["blue", "red orange"])
    >>> print(Colors.blue) # Prints "blue"
    >>> print(Colors.red_orange) # Prints "red orange"

    >>> Animals = construct_named_tuple("Animals", ["dog", "cat"], upper=True)
    >>> print(Animals.DOG) # Prints "dog"
    >>> print(Animals.CAT) # Prints "cat"
    >>> print(Animals.dog) # Raises AttributeError

    Args:
        name (str): Name of the namedtuple.
        fields (list): List of fields for the namedtuple.
        upper (bool, optional): If True, keys are uppercased. Defaults to False.

    Fields are lowered. Examples are found below."""
    str_fn = str.upper if upper else str.lower
    return collections.namedtuple(
        name, [str_fn(i).replace(" ", "_") for i in fields], defaults=fields
    )()


BrokerNames = construct_named_tuple(
    "BrokerNames",
    [
        "BlueGrace Logistics",
        "Uber Freight",
        "Sheer Transport",
        "Nolan Transportation Group",
        "Linehaul Logistics",
        "Coyote Logistics",
        "Dupre Logistics LLC",
        "MoLo Solutions",
        "PartnerShip LLC",
        "Sage Freight",
        "Arrive Logistics",
        "Echo Global Logistics",
        "Pathmark Transportation",
        "MX Solutions",
        "Ryan Transportation",
        "Integrity Express Logistics",
        "Forsla",
        "Transfix",
        "Loadsmart",
        "Child Logistics",
        "Royal Transportation",
        "Schneider Brokerage",
        "Capstone Logistics",
        "Trailer Bridge",
        "Ardent",
        "Axle Logistics",
        "Demo Data",
        "VP Logistics",
        "ITS Logistics",
        "Hill Bros",
        "Widespread Logistics",
        "OpenRoad",
        "Prosponsive Logistics",
    ],
)

ShipmentIngestionStatus = construct_named_tuple(
    "ShipmentIngestionStatus", ["new", "updated", "unchanged"]
)


# Maybe add these
# Step Deck / Drop Deck: This is similar to a flatbed but with two deck levels.
# Tanker: Used for transporting liquids.
# Lowboy: A trailer that drops down in section to allow for taller items to be loaded.
# Double / Tandem: A truck that pulls two trailers.
# Car Carrier: Designed specifically for transporting cars.
# Removable Gooseneck (RGN): Trailers where the front detaches, allowing large equipment to be driven onto the trailer.
# Dump Truck: Used for transporting loose material (e.g., sand, gravel).
# Bulk / Hopper: Open-top container suitable for bulk cargo.
# Auto Carrier: For transporting vehicles.
# Specialized trailers: For oversized or overweight loads.

EquipmentType = construct_named_tuple(
    "EquipmentType",
    [
        "Dry Van",
        "Reefer",
        "Flatbed",
        "Power Only",
        "Container",
        "Straight Truck",
        "Pup",
        "Vented Van",
        "Cargo Van",
    ],
)
EQUIPMENT_TYPE_DICT = {
    "dry van": EquipmentType.dry_van,
    "reefer": EquipmentType.reefer,
    "flatbed": EquipmentType.flatbed,
    "power only": EquipmentType.power_only,
    "container": EquipmentType.container,
    "straight truck": EquipmentType.straight_truck,
    "vented van": EquipmentType.vented_van,
    "pup": EquipmentType.pup,
    "van": EquipmentType.dry_van,
    "v": EquipmentType.dry_van,
    "dry": EquipmentType.dry_van,
    "van - van": EquipmentType.dry_van,
    # Coyote
    "r": EquipmentType.reefer,
    "p": EquipmentType.power_only,
    # Nolan
    "straight trucks": EquipmentType.straight_truck,
    # Dupre Logistics
    # TODO(P1): Check TMS used for this.
    "rc": EquipmentType.reefer,
    "tv": EquipmentType.dry_van,
    "tw": EquipmentType.reefer,
    # MoLo Solutions
    "reefer, van": EquipmentType.dry_van,
    # Pathmark Transportation
    "vr": EquipmentType.dry_van,
    "v": EquipmentType.dry_van,
    "r": EquipmentType.reefer,
    # Uber Freight
    "powerloop": EquipmentType.power_only,
    "power_only": EquipmentType.power_only,
    # Integrity Express Logistics
    "van": EquipmentType.dry_van,
    "power_only": EquipmentType.power_only,
    "reefer": EquipmentType.reefer,
    # Forsla
    'dry van 53"': EquipmentType.dry_van,
    # MX Solutions
    "tord": EquipmentType.dry_van,
    # Loadsmart
    "drv": EquipmentType.dry_van,
    # Emerge Load Board
    "ftl van": EquipmentType.dry_van,
    "ftl reefer": EquipmentType.reefer,
    "vptl van": EquipmentType.dry_van,
    "container": EquipmentType.container,
    # J.B. Hunt Board
    "refrigerated": EquipmentType.reefer,
    "flatbed ——": EquipmentType.flatbed,
    "flatbed standard": EquipmentType.flatbed,
    "ftl van 53'": EquipmentType.dry_van,
    "flatbed hot shot": EquipmentType.flatbed,
    "ftl reefer 53'": EquipmentType.reefer,
    "ftl van or reefer": EquipmentType.dry_van,
    # Arrive Load Board
    "van or reefer": EquipmentType.dry_van,
    "conestoga": EquipmentType.flatbed,
    "flatbed or conestoga": EquipmentType.flatbed,
    "van or flatbed": EquipmentType.dry_van,
    # Nolan
    "drayage": EquipmentType.container,
    "sprinters": EquipmentType.cargo_van,
    # Sage Frieght
    "str": EquipmentType.straight_truck,
    # Load Boards
    "straight box truck": EquipmentType.straight_truck,  # Assuming similar to 'straight truck'
    "sprinter/cargo van": EquipmentType.cargo_van,  # Assuming similar to 'cargo van'
    "hot shot": EquipmentType.flatbed,  # Assuming 'hot shot' refers to a type of flatbed
    "removable gooseneck": EquipmentType.flatbed,  # Often a type of flatbed trailer
    "flatbed or hooper": EquipmentType.flatbed,  # Assuming a typo of 'hopper', which is a type of flatbed
    "step deck": EquipmentType.flatbed,  # Step deck is a type of flatbed trailer
    "vptl reefer": EquipmentType.reefer,  # Assuming VPTL refers to a type of reefer
    "drop deck landoll": EquipmentType.flatbed,  # Assuming drop deck is a kind of flatbed as well.
    # Royal Transportation
    "53 v": EquipmentType.dry_van,
    # Capstone
    "v53": EquipmentType.dry_van,
    "r53": EquipmentType.reefer,
    # Trailer Bridge
    "truck": EquipmentType.dry_van,
    # Fahey Data
    # "sd": EquipmentType.step_deck,  # Assuming 'sd' stands for step deck
    # "fb": EquipmentType.flatbed,  # Assuming 'fb' stands for flatbed
    # "dray": EquipmentType.container,  # Commonly used for container transport
    # "vt": EquipmentType.vented_van,  # Assuming 'vt' stands for vented van
    # "cred": EquipmentType.cargo_van,  # Assuming 'cred' refers to cargo-ready or equivalent vans
    # "int": EquipmentType.intermodal,  # Assuming 'int' stands for intermodal equipment
    # "ltl": EquipmentType.less_than_truckload,  # Assuming 'ltl' stands for less-than-truckload
    # "mxv": EquipmentType.mixed_vehicle,  # Assuming 'mxv' stands for mixed vehicle type, specify if needed
    # "po": EquipmentType.power_only,  # Assuming 'po' stands for power only trucks
    # "chas": EquipmentType.chassis,  # Assuming 'chas' is short for chassis
    # "fb": EquipmentType.flatbed,  # Example for flatbed if not yet defined under Fahey
    # Ardent
    "dry van 53'": EquipmentType.dry_van,
    "ltl": EquipmentType.container,
}

CARGO_TYPE_DICT = {
    "beverages": "Beverages",
    "building materials": "Building Materials",
    "chemicals": "Chemicals",
    "coalcoke": "Coal/Coke",
    "commodities dry bulk": "Commodities Dry Bulk",
    "construction": "Construction",
    "driveaway towaway": "Driveaway/Towaway",
    "farm supplies": "Farm Supplies",
    "fresh produce": "Fresh Produce",
    "garbage refuse trash": "Garbage, Refuse, Trash",
    "general freight": "General Freight",
    "grain feed hay": "Grain, Feed, Hay",
    "household goods": "Household Goods",
    "intermodal containers": "Intermodal Containers",
    "liquids gases": "Liquids/Gases",
    "livestock": "Livestock",
    "logs poles beams lumber": "Logs, Poles, Beams, Lumber",
    "machinery large objects": "Machinery, Large Objects",
    "meat": "Meat",
    "metal sheets coils rolls": "Metal: Sheets, Coils, Rolls",
    "mobile homes": "Mobile Homes",
    "motor vehicles": "Motor Vehicles",
    "oilfield equipment": "Oilfield Equipment",
    "paper products": "Paper Products",
    "passengers": "Passengers",
    "refrigerated food": "Refrigerated Food",
    "us mail": "U.S. Mail",
    "utility": "Utility",
    "water well": "Water-Well",
}
