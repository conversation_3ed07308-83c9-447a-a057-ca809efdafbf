import concurrent.futures
import datetime
import logging
import threading
import time
from decimal import Decimal
from typing import Any
from typing import Dict
from typing import List
from typing import Union

import aioboto3
import boto3
import pandas as pd
from botocore.exceptions import ClientError
from tqdm import tqdm

from common import credentials

# Set the AWS environment variables by initializing
_ = credentials.Credentials()


class DynamoDBSessionFactory:
    """Factory pattern to create different DynamoDB access types."""

    def __init__(self, region_name):
        self.dynamodb_resource = boto3.resource("dynamodb", region_name=region_name)
        self.dynamodb_client = boto3.client("dynamodb")
        self.dynamodb_async_session = aioboto3.Session()

    def get_sync_resource(self):
        return self.dynamodb_resource

    def get_sync_client(self):
        return self.dynamodb_client

    def get_async_session(self):
        return self.dynamodb_async_session


# Create Dynamo access resources
dynamo_factory = DynamoDBSessionFactory(region_name="us-east-1")
async_dynamodb_session = dynamo_factory.get_async_session()
sync_dynamodb = dynamo_factory.get_sync_resource()
sync_dynamodb_client = dynamo_factory.get_sync_client()


def convert_to_dynamo_format(value: Any) -> Any:
    "Converts a value to the appropriate format for DynamoDB."
    if isinstance(value, dict):
        return {key: convert_to_dynamo_format(val) for key, val in value.items()}
    if isinstance(value, list):
        return [convert_to_dynamo_format(val) for val in value]
    if pd.isna(value):
        return None
    if isinstance(value, bool):
        return value
    if isinstance(value, (int, float, Decimal)):
        return None if abs(value) == float("inf") else Decimal(str(value))
    return str(value)


def dynamo_primitive_type(value: type) -> str:
    """
    Returns the DynamoDB format of primitive Python types
    """
    mapping = {str: "S", int: "N", float: "N", bool: "BOOL"}
    return mapping[value]


####################################################################
#         SYNC DYNAMODB FUNCTIONS
####################################################################


def insert_item(
    table_name: str,
    item: Dict[str, Any],
    timestamp: bool = True,
) -> Dict[str, Any]:
    """
    Inserts an item into the specified DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        item (dict): A dictionary containing the item to insert.
        timestamp (bool): whether to include a timestamp with the inserted item

    Returns:
        dict: The response from the DynamoDB service if the item is inserted successfully.
        None: If the item could not be inserted due to an error.
    """
    try:
        table = sync_dynamodb.Table(table_name)
        dynamo_item = convert_to_dynamo_format(item)
        if timestamp:
            dynamo_item["last_updated_timestamp"] = datetime.datetime.now(
                datetime.timezone.utc
            ).isoformat()
        return table.put_item(Item=dynamo_item)
    except ClientError as e:
        logging.error(f"An error occurred: {e.response['Error']['Message']}")
        raise


def read_item(table_name: str, key: Dict[str, Any]) -> Dict[str, Any]:
    """
    Reads an item from the specified DynamoDB table using the given primary key.

    Args:
        table_name (str): The name of the DynamoDB table.
        key (dict): A dictionary containing the primary key of the item to read.
        key = {'primaryKeyName': 'primaryKeyValue'}

    Returns:
        dict: The retrieved item if found.
        None: If the item could not be found or an error occurred.
    """
    try:
        # Assuming `dynamodb` is a boto3 resource previously defined and accessible within this function's scope.
        table = sync_dynamodb.Table(table_name)
        response = table.get_item(Key=key)
        item = response.get("Item")
        if not item:
            logging.info("Item not present.")
            return None
        return item
    except ClientError as e:
        logging.error(f"An error occurred: {e.response['Error']['Message']}")
        raise


def get_items_with_primary_key(
    table_name: str,
    primary_key_name: str,
    primary_key_value: str,
    filters: Dict = None,
) -> List:
    """Queries a DynamoDB table to retrieve items with a specific primary key value.

    Args:
        dynamodb (boto3.client): The DynamoDB client.
        primary_key_name (str): The name of the primary key attribute.
        primary_key_value (str): The value of the primary key attribute.
        filters (dict): An optional dictionary for matching or negation of primitive attributes.
            {
                'include': {<attribute name>: <value>},
                'exclude': {<attribute name>: <value>}
            }

    Returns:
        list: A list of items matching the specified primary key value.

    Example:
        >>> dynamodb = boto3.client('dynamodb')
        >>> items = get_items_with_primary_key(dynamodb, 'id', '123')
        >>> print(items)
        [{'id': '123', 'name': 'John Doe'}, {'id': '123', 'name': 'Jane Smith'}]
    """

    # Uses sync dynamodb client as .query is not available with resource.
    args = {
        "TableName": table_name,
        "KeyConditionExpression": "#pk = :pkval",
        "ExpressionAttributeNames": {"#pk": primary_key_name},
    }
    expression_attribute_values = {
        ":pkval": {"S": primary_key_value}  # Assuming the attribute is of type String
    }
    if filters:
        filter_expression = ""
        for ind, (key, val) in enumerate(filters.get("include", {}).items()):
            filter_expression += " AND " if filter_expression else ""
            filter_expression += f"{key} = :incval{ind}"
            expression_attribute_values[":incval" + str(ind)] = {
                dynamo_primitive_type(type(val)): val
            }
        for ind, (key, val) in enumerate(filters.get("exclude", {}).items()):
            filter_expression += " AND " if filter_expression else ""
            filter_expression += f"NOT {key} = :exval{ind}"
            expression_attribute_values[":exval" + str(ind)] = {
                dynamo_primitive_type(type(val)): val
            }
        args["FilterExpression"] = filter_expression
    args["ExpressionAttributeValues"] = expression_attribute_values
    response = sync_dynamodb_client.query(**args)
    return response.get("Items", [])


def thread_execute_dynamo_operations(
    operations: List,
) -> List[Any]:
    """
    Executes a list of sync DynamoDB operations in parallel using multiple threads.

    Args:
        operations (List): A list of tuples, where each tuple contains an operation and its parameters.

    Returns:
        List: A list of results from each operation.

    Example:
        operations = [
            (sync_dynamodb_func_1, {'param1': value1}),
            (sync_dynamodb_func_2, {'param2': value2}),
            ...
        ]
        results = thread_execute_dynamo_operations(operations)
    """
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for i in range(len(operations)):
            operation, params = operations[i]
            future = executor.submit(operation, **params)
            futures.append(future)

        return [future.result() for future in futures]


def delete_item(table_name: str, key: Dict) -> Dict:
    """Deletes an item from a DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        key (dict): The primary key of the item to be deleted.

    Returns:
        dict: The response from the `delete_item` operation.
    """
    try:
        # Note: we only support string keys for delete currently.
        str_typed_key = {k: {"S": key[k]} for k in key}

        response = sync_dynamodb_client.delete_item(
            TableName=table_name, Key=str_typed_key
        )
        return response
    except ClientError as e:
        logging.error(f"An error occurred: {e.response['Error']['Message']}")
        raise


def update_item_attribute(
    table_name: str,
    primary_key: Dict[str, str],
    update_dict: Dict[str, Any],
    timestamp: bool = True,
) -> Dict:
    """
    Updates an item in the specified DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        primary_key (dict): A dictionary containing the primary key of the item to update.
            e.g. {'user_id': '<id value>'}
        update_dict (dict): A dictionary containing the name of the attribute to update and the new value.
            e.g. {'attribute_name': '<new value>'}
        timestamp (bool): whether to include a timestamp with the inserted item

    Returns:
        dict: The response from the DynamoDB service if the item is inserted successfully.
        None: If the item could not be inserted due to an error.
    """
    table = sync_dynamodb.Table(table_name)
    attr_values = {}
    attr_names = {}
    update_expression = ""
    for key, val in update_dict.items():
        attr_values[f":new_{key}"] = val
        update_expression += ", " if update_expression else "SET "
        if key in ["name"]:
            update_expression += f"#{key} = :new_{key}"
            attr_names[f"#{key}"] = key
        else:
            update_expression += f"{key} = :new_{key}"

    timestamp_update_clause = ""
    if timestamp:
        timestamp_update_clause = ", SET last_updated_timestamp = :timestamp"
        attr_values[":timestamp"] = datetime.datetime.now(
            datetime.timezone.utc
        ).isoformat()
    for attempt in range(12):  # 12 attempts
        try:
            result = table.update_item(
                Key=primary_key,
                UpdateExpression=update_expression + timestamp_update_clause,
                ExpressionAttributeValues=attr_values,
                ExpressionAttributeNames=attr_names,
            )
            return result
        except ClientError as e:
            if e.response["Error"]["Code"] == "ProvisionedThroughputExceededException":
                wait_time = 2**attempt
                time.sleep(wait_time)
            else:
                logging.error(
                    f"An error occurred while updating item {key} in Dynamo table {table_name}: {e.response['Error']['Message']}"
                )
                raise
    logging.error("Failed to update item after maximum number of attempts.")
    return {"ResponseMetadata": {"HTTPStatusCode": 500}}


def update_item_attribute_wrapper(args) -> Dict:
    return update_item_attribute(*args)


def scan_table(
    table_name: str, scan_params: Dict[str, Union[str, Dict]]
) -> List[Dict[str, Union[str, int]]]:
    """
    Perform a scan operation on a DynamoDB table and retrieve all matching entries.
    https://docs.aws.amazon.com/amazondynamodb/latest/APIReference/API_Scan.html

    Args:
        table_name (str): The name of the DynamoDB table to be scanned.
        scan_params (Dict[str, Union[str, Dict]]): A dictionary containing the parameters
            for the scan operation. This includes filter expressions, attribute values,
            projection expressions, etc.

    Returns:
        List[Dict[str, Union[str, int]]]: A list of dictionaries, where each dictionary
        represents an item from the DynamoDB table that matches the scan parameters.

    Raises:
        ClientError: If an error occurs during the scan operation on the DynamoDB table.

    Example:
        scan_params = {
            "FilterExpression": Attr("year").eq(1992),
            "ProjectionExpression": "#yr, title, info.genres, info.actors[0]",
            "ExpressionAttributeNames": {"#yr": "year"}
        }
        >>> entries = scan_table("Movies", scan_params)
        >>> [{movie_name: ..., genre:...}]
    """

    table = sync_dynamodb.Table(table_name)

    def run_scan(params):
        # Run table scan with error logging.
        try:
            response = table.scan(**params)
            return response
        except ClientError as e:
            logging.error(f"A scan error occurred {e}")
            raise

    # Initial scan
    response = run_scan(scan_params)
    entries = response["Items"]

    # Continue scanning if necessary, i.e. there are many entries
    scans_run = 1
    while "LastEvaluatedKey" in response:
        scan_params["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        response = run_scan(scan_params)
        new_items = response["Items"]
        entries.extend(new_items)

        logging.info(
            f"Iteration {scans_run} complete. Items retrieved this iteration: {len(new_items)}. Total items retrieved: {len(entries)}"
        )

        scans_run += 1

    return entries


def wait_for_table_active(table_name):
    dynamodb = boto3.resource("dynamodb")
    table = dynamodb.Table(table_name)

    while True:
        if table.table_status == "ACTIVE":
            break
        print(f"Waiting for table {table_name} to be ACTIVE...")
        time.sleep(2)  # sleep for 2 seconds before checking the status again
        table.reload()  # refresh the table status


####################################################################
#         ASYNC DYNAMODB FUNCTIONS
####################################################################


async def async_insert_item(
    table_name: str, item: dict, timestamp: bool = True
) -> dict:
    """
    Asynchronously inserts an item into the specified DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        item (dict): A dictionary containing the item to insert.
        timestamp (bool): whether to include a timestamp with the inserted item

    Returns:
        dict: The response from the DynamoDB service if the item is inserted successfully.
        None: If the item could not be inserted due to an error.
    """
    try:
        async with async_dynamodb_session.resource("dynamodb") as dynamodb:
            table = await dynamodb.Table(table_name)
            dynamo_item = convert_to_dynamo_format(item)
            if timestamp:
                dynamo_item["last_updated_timestamp"] = datetime.datetime.now(
                    datetime.timezone.utc
                ).isoformat()
            return await table.put_item(Item=dynamo_item)
    except ClientError as e:
        logging.error(f"An error occurred: {e.response['Error']['Message']}")
        raise


async def async_read_item(table_name: str, key: dict) -> dict:
    """
    Asynchronously retrieves an item from the specified DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        key (dict): A dictionary representing the primary key of the item to retrieve.
        key = {'primaryKeyName': 'primaryKeyValue'}

    Returns:
        dict: The retrieved item if found.
        None: If the item could not be retrieved or does not exist.
    """
    try:
        # Use aioboto3 to create an asynchronous session with DynamoDB
        async with aioboto3.resource("dynamodb") as dynamodb:
            # Access the table
            table = await dynamodb.Table(table_name)
            # Get the item using the primary key
            response = await table.get_item(Key=key)
            # If the item is found, response will contain the 'Item' key
            return response.get("Item")
    except ClientError as e:
        # Printing the error message for the developer's debugging
        logging.error(f"An error occurred: {e.response['Error']['Message']}")
        raise


####################################################################
#         BATCH INSERT DYNAMODB FUNCTIONS
####################################################################


def batch_upload(
    table_name: str,
    items: List[Dict[str, Any]],
    gsi_keys: List[str] = [],
    timestamp: bool = True,
) -> None:
    """Synchronously upload a batch of items to a DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table.
        items (list): A list of dictionaries, where each dictionary is an item to upload.
        gsi_keys (list): The global secondary indexes key names.
        timestamp (bool): whether to include a timestamp with the inserted item

    """
    table = sync_dynamodb.Table(table_name)

    with table.batch_writer() as batch:
        for item in items:
            try:
                dynamo_item = convert_to_dynamo_format(item)
                # GSI Keys cannot point to "None", so remove the keys from item.
                for gsi_key in gsi_keys:
                    if gsi_key in dynamo_item and pd.isna(dynamo_item[gsi_key]):
                        del dynamo_item[gsi_key]
                if timestamp:
                    dynamo_item["ingested_timestamp"] = datetime.datetime.now(
                        datetime.timezone.utc
                    ).isoformat()
                _ = batch.put_item(Item=dynamo_item)
            except Exception as e:
                logging.error(f"Error: {e} caused by item: {item}")
                raise


async def async_batch_upload(table_name: str, items: List[Dict]) -> None:
    """Publish all new sessions to DynamoDB.

    Args:
        table_name (str): The name of the DynamoDB table.
        items (list): A list of dictionaries, where each dictionary is an item to upload.

    """
    async with async_dynamodb_session.resource(
        "dynamodb", region_name="us-east-1"
    ) as async_dynamodb:
        table = await async_dynamodb.Table(table_name)
        async with table.batch_writer() as batch:
            for item in items:
                try:
                    item = convert_to_dynamo_format(item)
                    await batch.put_item(Item=item)
                except Exception as e:
                    logging.error(f"Failed to update item {item}: {e}")


def multithreaded_batch_upload(
    table_name: str,
    items: List[Dict[str, Any]],
    gsi_keys: List[str] = [],
) -> None:
    """
    Uploads items to a DynamoDB table in batches, using multiple threads.

    Args:
        table_name (str): The name of the DynamoDB table.
        items (list): A list of dictionaries, where each dictionary is an item to upload.
        gsi_keys (list): The global secondary indexes key names.

    Returns:
        None
    """

    # Split items into chunks of 25 (max items per batch)
    chunks = [items[i : i + 25] for i in range(0, len(items), 25)]

    # Create and start threads
    # TODO (P2): use ThreadPoolExecutor?
    threads = []
    for chunk in chunks:
        thread = threading.Thread(
            target=batch_upload, args=(table_name, chunk, gsi_keys)
        )
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()


def thread_pool_batch_update(
    table_name: str,
    primary_key: str,
    items: List[Dict[str, Any]],
    timestamp: bool = True,
) -> List[Dict]:
    """
    Uses a thread pool executor to update items in a DynamoDB table in parallel.

    Args:
        table_name (str): The name of the DynamoDB table.
        primary_key (str): The primary key of the table being updated.
        items (list): A list of dictionaries, where each dictionary is an item to update.

    Returns:
        None
    """
    to_update = []
    results = []
    success_count = 0
    failure_count = 0
    failure_types = set()
    for entry in items:
        to_update.append(
            (
                table_name,
                {primary_key: entry[primary_key]},
                {k: v for k, v in entry.items() if k != primary_key},
                timestamp,
            )
        )
    with concurrent.futures.ThreadPoolExecutor(max_workers=24) as executor:
        pool_iterator = [
            executor.submit(update_item_attribute_wrapper, item) for item in to_update
        ]
        for future in tqdm(
            concurrent.futures.as_completed(pool_iterator),
            total=len(pool_iterator),
            desc="Updating items",
        ):
            try:
                result = future.result()
                results.append(result)
                if result["ResponseMetadata"]["HTTPStatusCode"] == 200:
                    success_count += 1
                else:
                    failure_count += 1
            except Exception as e:
                failure_count += 1
                failure_types.add(e)
    logging.info("Success count: ", success_count)
    logging.info("Failure count: ", failure_count)
    logging.info("Failure types: ", failure_types)
    return results


def upload_df_in_chunks(
    df: pd.DataFrame,
    table_name: str,
    chunk_size: int,
    gsi_keys: List[str] = [],
    use_tqdm: bool = False,
) -> None:
    """Uploads a DataFrame to DynamoDB in chunks."""

    # Calculate the number of chunks
    num_chunks = len(df) // chunk_size + (0 if len(df) % chunk_size == 0 else 1)
    rnge = range(num_chunks)
    iterator = tqdm(rnge) if use_tqdm else rnge
    for chunk_index in iterator:
        # Get the chunk of the DataFrame
        chunk = df.iloc[chunk_index * chunk_size : (chunk_index + 1) * chunk_size]
        # Write the converted items to DynamoDB in batches
        multithreaded_batch_upload(
            table_name, chunk.to_dict("records"), gsi_keys=gsi_keys
        )


def batch_delete_items(
    table_name: str,
    items_to_delete: List[Dict[str, Union[str, int]]],
    primary_key: str,
    sort_keys: List = [],
):
    """
    Deletes a batch of items from a specified DynamoDB table.

    Args:
        table_name (str): The name of the DynamoDB table from which items are to be deleted.
        items_to_delete (List[Dict[str, Union[str, int]]]): A list of dictionaries, where
            each dictionary contains the keys of an item to be deleted.
        PK (str): The name of the primary key attribute in the DynamoDB table.
        SK (str, optional): The name of the sort key attribute in the DynamoDB table, if
            the table has a composite primary key (default is None).

    Example:
        items = [{"id": "1234"}, {"id": "5678"}]
        batch_delete_items("MyTable", items, "id")
    """
    # Split the list into batches of 25
    chunks = [items_to_delete[i : i + 25] for i in range(0, len(items_to_delete), 25)]

    for chunk in chunks:
        delete_requests = []
        for item in chunk:
            key = {primary_key: {"S": item[primary_key]}}
            if sort_keys:
                for sort_key in sort_keys:
                    key[sort_key] = {"S": item[sort_key]}
            delete_requests.append({"DeleteRequest": {"Key": key}})

        # Batch write operation
        try:
            response = sync_dynamodb_client.batch_write_item(
                RequestItems={table_name: delete_requests}
            )

            if response.get("UnprocessedItems") and response["UnprocessedItems"].get(
                table_name
            ):
                logging.warning(
                    f"Unprocessed items detected: {response['UnprocessedItems'][table_name]}"
                )

        except ClientError as e:
            logging.error(
                f"An error occurred during batch write operation: {e.response['Error']['Message']}"
            )
            raise


####################################################################
#         DYNAMODB LIST ENTRY MANAGER
####################################################################


class DynamoDBListEntry:
    """A class representing a list entry in a DynamoDB table.
       PK | SK | [...items]

       Allows easy modification or addition to list entry by modifying in RAM.

    Args:
        table_name (str): The name of the DynamoDB table.
        key (dict, optional): The key used to retrieve the entry. Defaults to None.

    Attributes:
        table_name (str): The name of the DynamoDB table.
        key (dict): The key used to retrieve the entry.
        items (list): The list of items in the entry, if any exist.

    """

    def __init__(self, table_name: str, key: dict = None):
        self.table_name = table_name
        self.key = key
        self.items = self.get_items()

    def get_items(self):  # type: ignore
        """Retrieves the items from the DynamoDB table.

        Returns:
            list: The list of items in the entry.

        """
        response = read_item(self.table_name, self.key)
        items = response["Entries"] if response and "Entries" in response else []
        return items

    def add_item(self, item: Dict) -> None:
        """Adds item to current items without upload."""
        self.items.append(item)

    def add_item_upload(self, item) -> None:
        """Adds an item to the entry.

        Args:
            item: The item to be added.

        Returns:
            None

        """
        self.items.append(item)
        insert_item(self.table_name, {**self.key, "Entries": self.items})

    async def async_add_item_upload(self, item: Dict) -> None:
        """Asynchronously adds an item to the entry.

        Args:
            item: The item to be added.

        Returns:
            None

        """
        self.items.append(item)
        await async_insert_item(self.table_name, {**self.key, "Entries": self.items})

    async def async_add_items_upload(
        self, new_items: List, reset_items: bool = False
    ) -> None:
        """Asynchronously adds multiple items to the entry.

        Args:
            new_items (list): The list of items to be added.

        Returns:
            None

        """
        if reset_items:
            self.items = new_items
        else:
            self.items += new_items
        await async_insert_item(self.table_name, {**self.key, "Entries": self.items})

    def clear(self) -> None:
        """Clears the entry from the DynamoDB table.

        Returns:
            None

        """
        try:
            self.table.delete_item(Key=self.key)
        except ClientError as err:
            logging.error(err)
            raise


####################################################################
#         DYNAMODB TABLE UTILITIES
####################################################################


def describe_dynamo_table(table_name: str) -> str:
    # Get stats on the table
    dynamodb_client = boto3.client("dynamodb")
    response = dynamodb_client.describe_table(TableName=table_name)

    read_capacity = response["Table"]["ProvisionedThroughput"]["ReadCapacityUnits"]
    write_capacity = response["Table"]["ProvisionedThroughput"]["WriteCapacityUnits"]

    description_str = (
        f"Read Capacity: {read_capacity}\n"
        f"Write Capacity: {write_capacity}\n"
        f"Item Count: {response['Table']['ItemCount']}"
    )

    return description_str


def update_table_gsi_provisioned_throughput(
    table_name: str, gsi_name: str, read_capacity: int, write_capacity: int
) -> Dict:
    """
    Updates the provisioned throughput for a global secondary index on a DynamoDB table.
    """
    return sync_dynamodb_client.update_table(
        TableName=table_name,
        GlobalSecondaryIndexUpdates=[
            {
                "Update": {
                    "IndexName": gsi_name,
                    "ProvisionedThroughput": {
                        "ReadCapacityUnits": read_capacity,
                        "WriteCapacityUnits": write_capacity,
                    },
                }
            }
        ],
    )
