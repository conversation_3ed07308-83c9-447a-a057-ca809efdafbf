import inspect
import os
import re
import sys

import dotenv
import pkg_resources


def extract_version(file_path: str) -> str:
    """
    Extract the version number from setup.py file.

    Args:
        file_path (str): The path to the setup.py file.

    Returns:
        str: The version number.
    """
    version_pattern = r"version=['\"](.+?)['\"]"
    with open(file_path) as file:
        content = file.read()
        match = re.search(version_pattern, content)
        if match:
            return match.group(1)


def initialize():
    """
    Adjust sys.path to include the project root directory.
    This function finds the project root directory by traversing up the directory tree
    until it encounters a directory named 'truce'. Once the project root
    is identified, it appends the root directory to sys.path, allowing Python to find
    and import packages and modules using absolute imports.
    Usage:
        Call this function at the beginning of a 'main.py' script before any import
        statements to ensure that the imports work correctly regardless of the
        location of the 'main.py' file within the project directory structure.
    """

    if getattr(initialize, "initialized", False):
        frame = inspect.currentframe().f_back
        print(
            f"Warning: Duplicate call to initialize()."
            f" Previously initialized in {frame.f_code.co_filename} at line {frame.f_lineno}."
        )
        return initialize.project_root

    current_file_path = os.getcwd()
    project_root = current_file_path
    while (
        os.path.dirname(project_root) != project_root
        and not os.path.basename(project_root) == "truce"
    ):
        project_root = os.path.dirname(project_root)

    if not os.path.basename(project_root) == "truce":
        raise ValueError(
            "Project root not found. Please ensure that the project root directory is named 'truce'."
        )

    sys.path.append(project_root)
    assert dotenv.load_dotenv(os.path.join(project_root, "common", "credentials.env"))

    initialize.initialized = True
    initialize.project_root = project_root

    installed_version = pkg_resources.get_distribution("common").version
    setup_version = extract_version(os.path.join(project_root, "setup.py"))
    if installed_version != setup_version:
        raise ValueError(
            f"The installed version of the common package ({installed_version}) "
            f"does not match the version in setup.py ({setup_version})."
            f"Please run 'pip install .' from truce/ to install the package."
        )

    return project_root


if __name__ == "__main__":
    print(initialize())
    print(initialize())  # This should print a warning about duplicate calls
