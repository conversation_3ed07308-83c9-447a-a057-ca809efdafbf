# Helper file for managing ECS tasks and services.
import logging
import time
import urllib
from typing import Any
from typing import Dict
from typing import List
from typing import Tuple

import boto3
from botocore.exceptions import ClientError

ECS_CLIENT = boto3.client("ecs", region_name="us-east-1")


def list_tasks(cluster_name: str, service_name: str) -> Dict[str, List]:
    """
    Returns a list of tasks for a given cluster and service.

    Args:
        cluster_name (str): The name of the cluster.
        service_name (str): The name of the running service.

    Returns:
        dict: various information, including ARNs, on all tasks running.
    """
    try:
        tasks = ECS_CLIENT.list_tasks(cluster=cluster_name, serviceName=service_name)
        return tasks

    except ClientError as e:
        logging.error(
            f"Cannot access tasks on cluster {cluster_name} and service {service_name} with exception {e}"
        )
        raise


def describe_tasks(
    cluster_name: str, task_arns: List[str]
) -> List[Dict[str, List[Dict[str, List[Dict[str, str]]]]]]:
    """
    Describe the tasks on a cluster with the given ARNs for relevant information including network interface info.

    Args:
        cluster_name (str): The name of the cluster.
        task_arns (List[str]): A list of task ARNs for each task.

    Returns:
        List[Dict[str, List[Dict[str, List[Dict[str, str]]]]]]: A list of task descriptions, including important information
                                                                such as private ipv4 address.
    """
    try:
        task_descriptions = ECS_CLIENT.describe_tasks(
            cluster=cluster_name, tasks=task_arns
        )["tasks"]
        return task_descriptions

    except Exception as e:
        logging.error(
            f"Cannot describe tasks on cluster {cluster_name} and with arns {task_arns} with exception {e}"
        )
        raise


def format_private_ipv4(ip: str, port: int, endpoint: str) -> str:
    """
    Format the private IP address, port, and endpoint into a fully resolved URL.

    Args:
        ip (str): The private IP address.
        port (int): The port number.
        endpoint (str): The endpoint to be appended to the URL.

    Returns:
        str: The formatted URL string.

    Example:
        >>> format_private_ipv4("*************", 4200, "likeya_cut_g")
        >>> "http://*************:4200/likeya_cut_g"
    """
    return urllib.parse.urljoin(f"http://{ip}:{port}", endpoint)


def list_private_ipv4s(
    cluster_name: str,
    service_name: str,
    endpoint_details: Tuple[int, str] = None,
    is_local: bool = False,
) -> List[str]:
    # If local, return local server endpoint instead of node endpoints.
    # Used for development.
    """
    Get a list of private IP addresses for a given cluster and service.

    Args:
        cluster_name (str): The name of the cluster.
        service_name (str): The name of the service.
        endpoint_details (Tuple[int, str], optional): Endpoint details to format the IP address in format (PORT, ENDPOINT)
        is_local (bool, optional): Used to return 0.0.0.0 if running against local server.

    Returns:
        List[str]: A list of private IP addresses.

    Example:
        >>> list_private_ipv4s("roop-cluster", "pal-service", (8080, "/roop_pal"))
        >>> ["*************", "************", ...]
    """
    if is_local:
        return (
            [format_private_ipv4("0.0.0.0", *endpoint_details)]
            if endpoint_details
            else ["http://0.0.0.0"]
        )

    # Obtain task information
    running_tasks = list_tasks(cluster_name, service_name)
    task_descriptions = describe_tasks(cluster_name, running_tasks["taskArns"])

    # Source private IPs from task descriptions.
    private_ips = []
    for description in task_descriptions:
        for attachment in description.get("attachments", []):
            for detail in attachment.get("details", []):
                if detail.get("name") == "privateIPv4Address":  # Private IP present
                    ip = detail["value"]
                    if endpoint_details:
                        private_ips.append(format_private_ipv4(ip, *endpoint_details))
                    else:
                        private_ips.append(ip)

    return private_ips


def roll_service(cluster_name: str, service_name: str) -> Dict[str, Any]:
    """
    Forces a new deployment of a service. Keep same service configuration as currently running.

    Args:
        cluster_name (str): The name of the cluster.
        service_name (str): The name of the service.

    Returns:
        Response[Dict[str, Any]]: Dictionary containing status and deployment metadata.
    """

    response = ECS_CLIENT.update_service(
        cluster=cluster_name, service=service_name, forceNewDeployment=True
    )

    return response


def wait_for_running(
    cluster_name: str, service_name: str, timeout_seconds: int = 300
) -> bool:
    """
    Monitors a service, allowing caller to block execution until all node states are in RUNNING.
    Will block until states match desired states.

    Args:
        cluster_name (str): The name of the cluster.
        service_name (str): The name of the service.
        timeout_seconds (int): number of seconds to wait on node states.

    Returns:
        Bool (whether desired states were reached successfully)
    """

    all_nodes_running = False
    start_time = time.perf_counter()

    while not all_nodes_running:
        print("Waiting on nodes to reach RUNNING...")

        # Obtain task information
        running_tasks = list_tasks(cluster_name, service_name)
        described_tasks = describe_tasks(cluster_name, running_tasks["taskArns"])

        # Check statuses
        all_nodes_running = True
        for description in described_tasks:
            for container in description.get("containers", []):
                if container["lastStatus"] != "RUNNING":
                    all_nodes_running = False

        # Check if we have timed out.
        current_time = time.perf_counter()
        if (current_time - start_time) > timeout_seconds:
            print("Node statuses did not reach state RUNNING in time!")
            return False

        time.sleep(5)

    return True


def stop_all_service_tasks(cluster_name: str, service_name: str) -> None:
    """
    Stops all tasks for a given service within an ECS cluster.

    Args:
        cluster_name (str): The name of the cluster.
        service_name (str): The name of the service.
    """
    try:
        # Step 1: List all tasks for the service
        tasks_info = list_tasks(cluster_name, service_name)
        task_arns = tasks_info.get("taskArns", [])

        # If no tasks are found, exit early
        if not task_arns:
            print("No tasks to stop.")
            return

        # Step 2: Stop each task
        for task_arn in task_arns:
            ECS_CLIENT.stop_task(
                cluster=cluster_name,
                task=task_arn,
                reason="Stopping task as part of service update",
            )

    except Exception as e:
        print(
            f"Error stopping tasks for service {service_name} on cluster {cluster_name}: {e}"
        )
        raise


if __name__ == "__main__":
    print(list_private_ipv4s("freightgpt", "freightgpt-service-alb-dev"))
