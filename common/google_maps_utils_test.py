import random
import unittest
from unittest.mock import patch

import pandas as pd

from common.google_maps_utils import GoogleMapsUtils


class TestGoogleMapsUtils(unittest.TestCase):
    def setUp(self):
        with patch(
            "common.google_maps_utils.googlemaps.Client", autospec=True
        ) as self.mock_GMAPS:
            self.gmap_utils = GoogleMapsUtils(mock=True)
        self.random_string = "".join(
            random.choice("0123456789ABCDEF") for i in range(10)
        )

    def test_get_distance_miles(self):
        # Manually create s3 cache files for testing.
        self.gmap_utils.dist_mi.df = pd.DataFrame(
            columns=["origin/dest", "miles"]
        ).set_index("origin/dest")

        test_origin = "TEST" + self.random_string
        test_destination = "TEST" + self.random_string
        # Test case where origin_dest is not in cached origin_dest -> miles datafile and GMAPS distance_matrix request fails
        self.mock_GMAPS.return_value.distance_matrix.return_value = {
            "status": "INVALID_REQUEST"
        }
        with self.assertRaises(ValueError):
            self.gmap_utils.get_distance_miles(test_origin, test_destination)

        # Test case where origin_dest is not in cached origin_dest -> miles datafile
        self.mock_GMAPS.return_value.distance_matrix.return_value = {
            "status": "OK",
            "rows": [{"elements": [{"distance": {"value": 1000}}]}],
        }
        distance_miles = self.gmap_utils.get_distance_miles(
            test_origin, test_destination
        )
        self.assertEqual(distance_miles, 0.62137)

        self.mock_GMAPS.return_value.distance_matrix.return_value = {}
        # Test case where origin_dest is in cached origin_dest -> miles datafile
        distance_miles = self.gmap_utils.get_distance_miles(
            test_origin, test_destination
        )
        self.assertEqual(distance_miles, 0.62137)

    def test_get_lat_lng(self):
        self.gmap_utils.zip_lat_lng.df = pd.DataFrame(
            columns=["city", "state", "zip", "lat", "lng", "country"]
        ).set_index(self.gmap_utils.zip_lat_lng.idx)
        test_city = "TEST" + self.random_string
        test_state = "TEST" + self.random_string
        test_country = "TEST" + self.random_string
        test_zipcode = "TEST" + self.random_string

        # Test case where geocode request fails
        self.mock_GMAPS.return_value.geocode.return_value = []
        with self.assertRaises(ValueError):
            self.gmap_utils.get_lat_lng(
                test_city, test_state, test_country, test_zipcode
            )

        # Test case where idx is not in cached zip_lat_lng datafile
        self.mock_GMAPS.return_value.geocode.return_value = [
            {"geometry": {"location": {"lat": 12.3, "lng": -45.6}}}
        ]
        lat, lng = self.gmap_utils.get_lat_lng(
            test_city, test_state, test_country, test_zipcode
        )
        self.assertEqual(lat, 12.3)
        self.assertEqual(lng, -45.6)

        # Test case where idx is in cached zip_lat_lng datafile
        # Assuming GMaps is no longer called in this case, hence setting return value to []
        self.mock_GMAPS.return_value.geocode.return_value = []
        lat, lng = self.gmap_utils.get_lat_lng(
            test_city, test_state, test_country, test_zipcode
        )
        self.assertEqual(lat, 12.3)
        self.assertEqual(lng, -45.6)

    def test_get_zip_from_city_state(self):
        self.gmap_utils.zip_from_city_state.df = pd.DataFrame(
            columns=["city", "state", "zip", "lat", "lng", "country"]
        ).set_index(["city"])
        test_city = "TEST" + self.random_string
        test_state = "TEST" + self.random_string
        test_country = "TEST" + self.random_string

        # Test case where GMAPS geocode request fails
        self.mock_GMAPS.return_value.geocode.return_value = []
        with self.assertRaises(ValueError):
            self.gmap_utils.get_zip_from_city_state(test_city, test_state, test_country)

        # Test case where GMAPS reverse_geocode request fails
        self.mock_GMAPS.return_value.geocode.return_value = [
            {"geometry": {"location": {"lat": 40.7128, "lng": -74.0060}}}
        ]
        self.mock_GMAPS.return_value.reverse_geocode.return_value = []
        with self.assertRaises(ValueError):
            self.gmap_utils.get_zip_from_city_state(test_city, test_state, test_country)

        # Test case where GMAPS geocode and reverse_geocode requests succeed
        self.mock_GMAPS.return_value.geocode.return_value = [
            {
                "geometry": {"location": {"lat": 40.7128, "lng": -74.0060}},
            }
        ]
        self.mock_GMAPS.return_value.reverse_geocode.return_value = [
            {
                "address_components": [
                    {"types": ["postal_code"], "long_name": "10001"}
                ],
            }
        ]
        zip = self.gmap_utils.get_zip_from_city_state(
            test_city, test_state, test_country
        )
        self.assertEqual(zip, "10001")

    def test_get_tz_from_zip(self):
        self.gmap_utils.zip_tz.df = pd.DataFrame(
            columns=["countryzip", "tz"]
        ).set_index("countryzip")
        test_zip = "TEST" + self.random_string
        test_country = "TEST" + self.random_string

        # Test case where GMAPS geocode request fails
        self.mock_GMAPS.return_value.geocode.return_value = []
        with self.assertRaises(ValueError):
            self.gmap_utils.get_tz_from_zip(test_zip, test_country)

        # Test case where country_zip is not in cached zip -> tz datafile
        self.mock_GMAPS.return_value.geocode.return_value = [
            {"geometry": {"location": {"lat": 12.3, "lng": -45.6}}}
        ]
        self.mock_GMAPS.return_value.timezone.return_value = {
            "timeZoneId": "America/New_York"
        }
        tz = self.gmap_utils.get_tz_from_zip(test_zip, test_country)
        self.assertEqual(str(tz), "America/New_York")

        # Ensure mock is not called for cached case.
        self.mock_GMAPS.return_value.geocode.reset_mock()
        tz = self.gmap_utils.get_tz_from_zip(test_zip, test_country)
        self.assertEqual(str(tz), "America/New_York")
        self.mock_GMAPS.return_value.geocode.assert_not_called()


if __name__ == "__main__":
    unittest.main()
