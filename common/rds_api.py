import datetime
import logging
import typing
import uuid
from decimal import Decimal

import pandas as pd
import sqlalchemy as sqlalc
from sqlalchemy import Column
from sqlalchemy import Integer
from sqlalchemy import String
from sqlalchemy import UniqueConstraint
from sqlalchemy.engine.reflection import Inspector
from sqlalchemy.sql.sqltypes import DateTime
from sqlalchemy.sql.sqltypes import JSON
from sqlalchemy.sql.sqltypes import Numeric
from sqlalchemy.sql.sqltypes import SmallInteger

from common import credentials
from common.constants import ShipmentIngestionStatus

# TODO(P1): Consider moving this and the generate_schema_diagram.py to the common folder.
CREDENTIALS = credentials.Credentials()
NUM_UPSERT_RETRIES = 10
SHIPMENT_DECIMAL_FIELDS = [
    "distanceMiles",
    "cogsLineHaul",
    "cogsFuel",
    "cogsAccessorial",
    "cogsTotal",
    "revenueLineHaul",
    "revenueAccessorial",
    "revenueFuel",
    "revenueTotal",
    "weight",
    "blt",
    "clt",
    "preBook",
]


def json_serialize_element(data):
    """
    Convert data elements into JSON serializable elements for RDS.

    Examples:
        >>> json_serialize_element(datetime.datetime(2021, 1, 1))
        '01/01/21 00:00:00'
        >>> json_serialize_element(Decimal(1.0))
        1.0
        >>> json_serialize_element(1.0)
        1.0
        >>> json_serialize_element("test")
        'test'
    """
    if isinstance(data, datetime.datetime) or isinstance(data, pd.Timestamp):
        return data.strftime("%x %X")
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


class RDS:
    def __init__(self, db_name, create_tables=False, dry_mode=False):
        """Creates Amazon Relational Database access object.
        This object interfaces with the aurora database with queries, updates, and inserts.

        :param create_tables: Flag that instantiates the schema when true (running CREATE TABLE).
        """
        # TODO(P1): Consider using a connection pool. https://docs.sqlalchemy.org/en/14/core/pooling.html
        # TODO(P0): Ensure schema matches the one in the database.
        self.__meta = sqlalc.MetaData()
        cluster_arn = CREDENTIALS.cluster_arn
        secret_arn = CREDENTIALS.rds_secret_arn

        logging.info(f"Writing to DB: {db_name}")
        self.__engine = sqlalc.create_engine(
            "mysql+auroradataapi://:@/" + db_name,
            connect_args=dict(aurora_cluster_arn=cluster_arn, secret_arn=secret_arn),
        )

        self.dry_mode = dry_mode

        self.__insp = Inspector.from_engine(self.__engine)
        self.__instantiate_tables(create_tables)

    def __instantiate_tables(self, create_tables):
        self.cities = sqlalc.Table(
            "cities",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column("city", String(200), nullable=False),
            Column("state", String(2), nullable=False),
            Column("country", String(2), nullable=False),
            UniqueConstraint("city", "state", "country"),
        )

        # https://docs.sqlalchemy.org/en/14/orm/basic_relationships.html#many-to-many
        # Junction table
        self.cityzips = sqlalc.Table(
            "cityzips",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column(
                "cityId", String(36), sqlalc.ForeignKey("cities.id"), nullable=False
            ),
            Column("zipId", String(36), sqlalc.ForeignKey("zips.id"), nullable=False),
            UniqueConstraint("cityId", "zipId"),
        )

        self.zips = sqlalc.Table(
            "zips",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column("zip", String(6), nullable=False),
            Column("plus4", String(4)),
            UniqueConstraint("zip", "plus4"),
        )

        # TODO(P1): Consider adding sqlalc.ForeignKeyConstraint to Tables.
        # This may better represent one-to-many. https://docs.sqlalchemy.org/en/14/core/constraints.html#on-update-and-on-delete
        self.lanes = sqlalc.Table(
            "lanes",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column(
                "originCityId",
                String(36),
                sqlalc.ForeignKey("cities.id"),
                nullable=False,
            ),
            Column(
                "destinationCityId",
                String(36),
                sqlalc.ForeignKey("cities.id"),
                nullable=False,
            ),
            Column("shipmentMode", String(50), nullable=False),
            Column("equipmentType", String(200), nullable=False),
            UniqueConstraint(
                "originCityId", "destinationCityId", "shipmentMode", "equipmentType"
            ),
        )

        self.shippers = sqlalc.Table(
            "shippers",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column("name", String(200), nullable=False),
            Column("businessUnit", String(200)),
            UniqueConstraint("name", "businessUnit"),
        )

        self.brokers = sqlalc.Table(
            "brokers",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column("name", String(200), nullable=False),
            UniqueConstraint("name"),
        )

        # TODO(P0): Right now constructing with Numeric creates int fields. Ensure it creates double(20,10) fields.
        self.shipments = sqlalc.Table(
            "shipments",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column(
                "brokerId", String(36), sqlalc.ForeignKey("brokers.id"), nullable=False
            ),
            Column("laneId", String(36), sqlalc.ForeignKey("lanes.id"), nullable=False),
            Column(
                "shipperId",
                String(36),
                sqlalc.ForeignKey("shippers.id"),
                nullable=False,
            ),
            Column("brokerPrimaryReference", String(200), nullable=False),
            Column("shipperPrimaryReference", String(200)),
            Column("stopCount", SmallInteger),
            Column("distanceMiles", Numeric),
            Column("cogsLineHaul", Numeric),
            Column("cogsAccessorial", Numeric),
            Column("cogsFuel", Numeric),
            Column("cogsTotal", Numeric, nullable=False),
            Column("revenueLineHaul", Numeric),
            Column("revenueFuel", Numeric),
            Column("revenueAccessorial", Numeric),
            Column("revenueTotal", Numeric, nullable=False),
            Column("originWarehouse", String(200)),
            Column("destinationWarehouse", String(200)),
            Column("shipmentStatus", String(200)),
            Column("shipmentRank", String(200)),
            Column("weight", Numeric),
            Column("originOpenTime", DateTime),
            Column("originCloseTime", DateTime, nullable=False),
            Column("originArrivalTime", DateTime),
            Column("originDepartureTime", DateTime),
            Column(
                "originZipId",
                String(36),
                sqlalc.ForeignKey("zips.id"),
                nullable=False,
            ),
            Column("destinationOpenTime", DateTime),
            Column("destinationCloseTime", DateTime),
            Column("destinationArrivalTime", DateTime),
            Column("destinationDepartureTime", DateTime),
            Column(
                "destinationZipId",
                String(36),
                sqlalc.ForeignKey("zips.id"),
                nullable=False,
            ),
            Column("loadCreationTime", DateTime),
            Column("loadActivationTime", DateTime),
            Column("carrierAssignedTime", DateTime),
            Column("shipmentIngestionTime", DateTime),
            Column("shipmentClass", String(200)),
            Column("brokerScore", SmallInteger),
            Column("carrierScore", SmallInteger),
            Column("costScore", SmallInteger),
            Column("performanceScore", SmallInteger),
            Column("ltuScore", SmallInteger),
            Column("prebookScore", SmallInteger),
            Column("otpScore", SmallInteger),
            Column("otdScore", SmallInteger),
            Column("score", SmallInteger),
            Column("blt", Numeric),
            Column("clt", Numeric),
            Column("preBook", Numeric),
            Column("originDelayMinutes", Integer),
            Column("destinationDelayMinutes", Integer),
            Column("pendingUpdate", JSON),
            Column("customerDirect", String(50)),
            Column("mlPrice", Numeric),
            Column("mlPriceUpperBound", Numeric),
            Column("mlPriceLowerBound", Numeric),
            Column("mlPricedTime", DateTime),
            Column("modelName", String(200)),
            Column("modelSource", String(200)),
            UniqueConstraint(
                "brokerId",
                "shipperId",
                "brokerPrimaryReference",
                "shipperPrimaryReference",
            ),
            # Consider if the times should be nullable.
        )

        # Meta Tables
        self.shipmentmetadata = sqlalc.Table(
            "shipmentmetadata",
            self.__meta,
            Column("id", String(36), primary_key=True),
            Column(
                "shipmentId",
                String(36),
                sqlalc.ForeignKey("shipments.id"),
                nullable=False,
            ),
            Column("sourceFile", String(400)),
            Column("shipmentIngestionTime", DateTime),
            Column("lastModifiedTime", DateTime),
            Column("gitCommitHash", String(40)),
            Column("gitAuthor", String(200)),
            Column("shipmentDiffs", JSON),
            Column("derivedColumns", String(200)),
            UniqueConstraint("shipmentId"),
        )

        if create_tables:
            self.__meta.create_all(self.__engine)

    def get_schema_metadata(self):
        """Returns schema metadata."""
        return self.__meta

    def query(self, table: sqlalc.Table, params: dict) -> dict:
        """Queries table using given params.

        "params" dictionary has column name as key and column data as value.
        Returns column data as dictionary (column name as key).
        """
        select_statement = table.select()
        for col, param in params.items():
            # Appends "WHERE col=val" to SELECT statement.
            select_statement = select_statement.where(table.c[col] == param)
        conn = self.__engine.connect()
        result = conn.execute(select_statement)
        row = result.fetchone()
        if not row:
            return {}
        return dict(zip(row.keys(), row))

    def check_required_columns(self, table: sqlalc.Table, value: dict) -> None:
        """Check and validate that all required columns from table are present in value.
        Raises:
            ValueError: If any required column (excluding 'id') is missing in value.
        Returns None
        """
        # Check that all required columns are present.
        for col in table.columns:
            if col.name != "id" and not col.nullable and col.name not in value:
                logging.error(f"Required field {col.name} is missing during upsert.")
                raise ValueError(f"Required field {col.name} missing during upsert.")

    def query_existing_record(self, table: sqlalc.Table, value: dict) -> dict:
        """
        Queries table using unique columns.
        Returns column data as dictionary.
        """
        if not self.__insp.get_unique_constraints(table.name):
            logging.error(f"Table {table.name} has no unique constraints.")
            raise AssertionError("Table has no unique constraints.")
        # Query database using present unique columns.
        unique_cols = self.__insp.get_unique_constraints(table.name)[0]["column_names"]
        return self.query(table, {uc: value[uc] for uc in unique_cols if uc in value})

    def __upsert(self, table: sqlalc.Table, value: dict) -> str:
        """Inserts or updates value into table if unique keys match.

        If value does not already exist in table, construct new row with unique id.
        Returns the id of the existing or newly created row.
        """

        self.check_required_columns(table, value)
        existing_record = self.query_existing_record(table, value)

        # TODO(P2): Only log the diff
        logging.debug(f"Existing record: {existing_record}")
        logging.debug(f"Inserting value: {value}")
        if existing_record:
            row_id = existing_record["id"]
        else:
            row_id = uuid.uuid4()
            if table == self.shipmentmetadata:
                value["shipmentIngestionTime"] = datetime.datetime.utcnow().replace(
                    microsecond=0
                )
        value["id"] = row_id

        conn = self.__engine.connect()

        # Insert row if it does not exist, otherwise update.
        if not self.dry_mode:
            insert_stmt = sqlalc.dialects.mysql.insert(table).values(**value)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**value)
            conn.execute(on_duplicate_key_stmt)
        return row_id

    def upsert(self, table: sqlalc.Table, value: dict) -> str:
        for i in range(NUM_UPSERT_RETRIES):
            try:
                return self.__upsert(table, value)
            except ValueError as e:
                raise e
            except AssertionError as e:
                raise e
            except Exception:
                logging.error(
                    "Upsert failed with unknown exception {} times. Retrying...".format(
                        i + 1
                    )
                )
                logging.error(f"Query: {value}")
        return self.__upsert(table, value)

    def __shipment_upsert(
        self, table: sqlalc.Table, value: dict
    ) -> typing.Tuple[str, ShipmentIngestionStatus, typing.List[typing.Tuple[str]]]:
        """Upsert function for ShipmentRows.

        Need to keep track of columns and values to check for rows whether they are new, updated or unchanged.
        Returning row_id along with ShipmentIngestionStatus and data that were updated.
        """
        self.check_required_columns(table, value)
        existing_record = self.query_existing_record(table, value)

        # TODO(P2): Only log the diff
        logging.debug(f"Existing record: {existing_record}")
        logging.debug(f"Inserting value: {value}")
        modified_data = []
        status = ShipmentIngestionStatus.unchanged
        if existing_record:
            row_id = existing_record["id"]
            for col, new_data in value.items():
                if col in SHIPMENT_DECIMAL_FIELDS:
                    new_data = Decimal(str(new_data))
                if (col, new_data) not in existing_record.items():
                    status = ShipmentIngestionStatus.updated
                    logging.info(
                        f"Updated shipment id: {row_id}, old {col} : {json_serialize_element(existing_record[col])}, updated {col} : {json_serialize_element(new_data)}"
                    )
                    modified_data.append(
                        (
                            col,
                            json_serialize_element(existing_record[col]),
                            json_serialize_element(new_data),
                        )
                    )
            if status == ShipmentIngestionStatus.unchanged:
                return row_id, status, modified_data
        else:
            row_id = uuid.uuid4()
            status = ShipmentIngestionStatus.new
        value["id"] = row_id

        conn = self.__engine.connect()

        # Insert row if it does not exist, otherwise update.
        if not self.dry_mode:
            insert_stmt = sqlalc.dialects.mysql.insert(table).values(**value)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**value)
            conn.execute(on_duplicate_key_stmt)
        return row_id, status, modified_data

    def shipment_upsert(
        self, table: sqlalc.Table, value: dict
    ) -> typing.Tuple[str, ShipmentIngestionStatus, typing.List[typing.Tuple[str]]]:
        for i in range(NUM_UPSERT_RETRIES):
            try:
                return self.__shipment_upsert(table, value)
            except ValueError as e:
                raise e
            except AssertionError as e:
                raise e
            except Exception:
                logging.error(
                    "Upsert failed with unknown exception {} times. Retrying...".format(
                        i + 1
                    )
                )
                logging.error(f"Query: {value}")
        return self.__shipment_upsert(table, value)


if __name__ == "__main__":
    brokers = sqlalc.Table(
        "brokers",
        sqlalc.MetaData(),
        Column("id", String(36), primary_key=True),
        Column("name", String(200), nullable=False),
        UniqueConstraint("name"),
    )
    print(brokers.columns)

    import IPython

    IPython.embed()
    # Access name of column
    # Get string representation of table, column of "name"
    s = str(brokers.columns["name"])
