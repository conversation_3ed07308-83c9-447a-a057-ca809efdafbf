# Consolidate python lambda functions from web_backend/lambda directory
import datetime
import decimal
import math
from threading import Thread
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

import boto3
import pandas as pd
from tqdm import tqdm

from common import credentials
from common.constants import AURORA_DB_NAME

# https://docs.aws.amazon.com/dms/latest/sql-server-to-aurora-mysql-migration-playbook/chap-sql-server-aurora-mysql.sql.datatypes.html
TYPE_DICT = {
    "BIT": bool,
    "BOOLEAN": bool,
    "INT": int,
    "INTEGER": int,
    "SMALLINT": int,
    "TINYINT": int,
    "MEDIUMINT": int,
    "BIGINT": int,
    "DECIMAL": decimal.Decimal,
    "NUMERIC": decimal.Decimal,
    "FLOAT": float,
    "DOUBLE": float,
    "CHAR": str,
    "VARCHAR": str,
    "SET": str,
    "DATE": datetime.date,
    "DATETIME": datetime.datetime,
    "TIMESTAMP": datetime.datetime,
    "TIME": datetime.time,
    "YEAR": int,
    "BINARY": bytes,
    "VARBINARY": bytes,
    "BLOB": bytes,
    "TEXT": str,
    "LONGTEXT": str,
    "JSON": str,
}
CREDENTIALS = credentials.Credentials()


class ThreadQuery(Thread):
    CREDENTIALS = credentials.Credentials()
    secret_arn = CREDENTIALS.rds_secret_arn
    cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"
    database_name = "mvp_db_dev"

    def __init__(
        self,
        client: Any = None,
        group: Optional[Any] = None,
        name: Optional[str] = None,
        batch: bool = False,
        args: tuple = (),
        kwargs: Dict[str, Any] = {},
        Verbose: Optional[Any] = None,
    ) -> None:
        """Initialize the ThreadQuery class with given arguments."""
        print("Initializing ThreadQuery")
        Thread.__init__(
            self, group, (self.batch_query if batch else self.query), name, args, kwargs
        )
        self.client = client
        self._return = None

    def run(self) -> None:
        """Override the run method to execute the target query."""
        if self._target is not None:
            self._return = self._target(*self._args, **self._kwargs)

    def join(self, *args) -> Any:
        """Override the join method and return the result of the query."""
        Thread.join(self, *args)
        return self._return

    def query(self, query_text: str) -> Dict[str, Any]:
        """Execute the given SQL query and return the response."""
        include_metadata = True
        response = self.client.execute_statement(
            resourceArn=self.cluster_arn,
            database=self.database_name,
            secretArn=self.secret_arn,
            sql=query_text,
            includeResultMetadata=include_metadata,
        )
        return response

    def batch_query(self, query_text: str) -> List[Dict[str, Any]]:
        response = get_data_in_batches_raw(self.database_name, 100, query_text, False)
        return response


def format_rds_response(response: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Format the RDS response into a more readable structure.

    Args:
        response: The raw response from an RDS query.

    Returns:
        A list of dictionaries representing the query results.
    """
    metadata = response["columnMetadata"]
    records = response["records"]

    return [
        {
            metadata[j]["label"]: (
                records[i][j][next(iter(records[i][j]))]
                if next(iter(records[i][j])) != "isNull"
                else None
            )
            for j in range(len(metadata))
        }
        for i in range(len(records))
    ]


# TODO(P1): #1278 Use the format_rds_response to format the response from the query function.
def append_records_to_df(
    query_response: Dict[str, Any], df: pd.DataFrame = pd.DataFrame()
) -> pd.DataFrame:
    """Append query response records to a Pandas DataFrame.

    Args:
        query_response: The response from a query containing records and metadata.
        df: An optional existing DataFrame to append to.

    Returns:
        A Pandas DataFrame containing the query records.
    """
    rows = []
    for record in query_response["records"]:
        record_dict = {}
        for i in range(len(record)):
            metadata = query_response["columnMetadata"][i]
            if next(iter(record[i].keys())) == "isNull":
                assert next(iter(record[i].values()))
                record_dict[metadata["label"]] = None
                continue
            value = next(iter(record[i].values()))
            value_type = TYPE_DICT[metadata["typeName"]]
            if metadata["typeName"] == "DATETIME":
                value = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
            else:
                value = value_type(value)
            record_dict[metadata["label"]] = value
        rows.append(record_dict)
    return pd.concat([df, pd.DataFrame(rows)], ignore_index=True)


def query(sql_query: str, database_name: str) -> dict:
    """Query rds database, using stored credentials.

    :param sql_query: See https://docs.aws.amazon.com/rds/latest/dg/c_SQL_commands.html.
    :return: Dictionary of boto3 client response containing SQL query's results.
    :raises: ValueError whenever the query is bad.
    """
    client = boto3.client("rds-data")
    secret_arn = CREDENTIALS.rds_secret_arn
    cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"
    response = client.execute_statement(
        resourceArn=cluster_arn,
        database=database_name,
        secretArn=secret_arn,
        sql=sql_query,
        includeResultMetadata=True,
    )
    return response


def batch_query(sql_query: str, database_name: str, parameter_sets) -> dict:
    """Execute batch statements on rds database, using stored credentials.

    :param sql_query: The SQL query to be executed.
    :param database_name: The database where the query should be executed.
    :param parameter_sets: A list of tuples, where each tuple contains the parameters for a single execution of the SQL query.
    :return: Dictionary of boto3 client response containing SQL query's results.
    :raises: ValueError whenever the query is bad.
    """
    client = boto3.client("rds-data")
    secret_arn = CREDENTIALS.rds_secret_arn
    cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"
    response = client.batch_execute_statement(
        resourceArn=cluster_arn,
        database=database_name,
        secretArn=secret_arn,
        sql=sql_query,
        parameterSets=parameter_sets,
    )
    return response


# TODO(P0): #1279 Use above batch_query in this function.
# TODO(P1): Add a parameter to limit the number of records returned.
def get_data_in_batches(db_name, batch_size, query_str, use_tqdm=True):
    """
    Retrieve data in batches from the specified database.

    Use this when you get the error "Database returned more than the allowed response size limit".
    Experimentally determine the offset size that works for your query.

    :param db_name: Name of the database to query.
    :param batch_size: The size of each batch of records to retrieve.
    :param query_str: The SQL query to execute, don't include LIMIT or OFFSET.
    :return: A DataFrame containing all records retrieved.
    """

    # Add LIMIT and OFFSET to the query string, removing semicolon if present.
    query_str = query_str.strip()
    if query_str[-1] == ";":
        query_str = query_str[:-1]

    # Remove order by from query_str for count query
    count_query_str = "SELECT COUNT(*) FROM \n(" + query_str + "\n) AS count_table;"
    records_df = pd.DataFrame()

    # Get the total number of records in the database
    records_count = query(count_query_str, db_name)["records"][0][0]["longValue"]
    if records_count == 0:
        return records_df

    # Add LIMIT and OFFSET for retrieval query
    limit_query_str = query_str + " LIMIT {limit}, {offset};"

    # Retrieve records in batches
    total_batches = math.ceil(records_count / batch_size)
    rnge = range(0, total_batches * batch_size, batch_size)
    iterator = tqdm(rnge) if use_tqdm else rnge
    for i in iterator:
        records = query(
            limit_query_str.format(limit=i, offset=batch_size),
            db_name,
        )
        records_df = append_records_to_df(records, records_df)

    return records_df


def get_data_in_batches_raw(db_name, batch_size, query_str, use_tqdm=True):
    """
    Retrieve data in batches from the specified database.

    Use this when you get the error "Database returned more than the allowed response size limit".
    Experimentally determine the offset size that works for your query.

    :param db_name: Name of the database to query.
    :param batch_size: The size of each batch of records to retrieve.
    :param query_str: The SQL query to execute, don't include LIMIT or OFFSET.
    :return: A DataFrame containing all records retrieved.
    """

    # Add LIMIT and OFFSET to the query string, removing semicolon if present.
    query_str = query_str.strip()
    if query_str[-1] == ";":
        query_str = query_str[:-1]

    # Remove order by from query_str for count query
    count_query_str = f"SELECT COUNT(*) FROM ({query_str}) AS count_table;"
    records_df = []

    # Get the total number of records in the database
    records_count = query(count_query_str, db_name)["records"][0][0]["longValue"]
    if records_count == 0:
        return records_df

    # Add LIMIT and OFFSET for retrieval query
    limit_query_str = query_str + " LIMIT {limit}, {offset};"

    # Retrieve records in batches
    total_batches = math.ceil(records_count / batch_size)
    rnge = range(0, total_batches * batch_size, batch_size)
    iterator = tqdm(rnge) if use_tqdm else rnge
    for i in iterator:
        records = query(
            limit_query_str.format(limit=i, offset=batch_size),
            db_name,
        )
        records_df += format_rds_response(records)

    return records_df


if __name__ == "__main__":
    query_response = query("SELECT * FROM shipments LIMIT 10", AURORA_DB_NAME)

    query_response = query("SELECT id FROM lanes;", AURORA_DB_NAME)
    lane_ids = []
    for record in query_response["records"]:
        lane_ids.append(next(iter(record[0].values())))

    # print query result
    import IPython

    IPython.embed()
    print(query_response["records"])
