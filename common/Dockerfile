# General purpose dockerfile that contains the common library.
FROM python:3.11

# Create the truce directory to hold all files and set it as the working directory
WORKDIR /var/task/truce
ENV PYTHONPATH="${PYTHONPATH}:/var/task"

# Add git, setup, and install common library and requirements.
ADD .git .git
ADD common common
COPY setup.py .

RUN pip3 install .
RUN pip3 install -r common/requirements.txt
