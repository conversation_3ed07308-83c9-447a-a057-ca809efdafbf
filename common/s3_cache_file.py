import json
import logging
import os
import typing
from urllib.parse import urlparse

import boto3
import pandas as pd


def load_s3_json(
    bucket: str,
    filename: str,
    client: boto3.client,
) -> typing.Union[
    typing.List[typing.Dict[str, typing.Any]], typing.Dict[str, typing.Any]
]:
    """
    Loads a JSON file from S3 and returns it as a list of dictionaries.

    If the file does not exist, returns None.
    """
    try:
        response = client.get_object(Bucket=bucket, Key=filename)
        return json.loads(response["Body"].read())
    except client.exceptions.NoSuchKey:
        # We check for None in format_inspection_info and format_crash_info.
        return None
    except Exception as e:
        raise Exception(f"Error loading {filename} from {bucket}.") from e


class S3:
    """Creates Amazon S3 Object.
    This object interfaces with an S3 bucket and allows basic
    upload and download functionality.

    :param bucket: bucket name to connect with.
    """

    def __init__(self, bucket, s3_resource=None):
        if not s3_resource:
            self.__s3_resource = boto3.resource("s3")
        else:
            self.__s3_resource = s3_resource
        self.__bucket = bucket

    def __load_df(self, path: str, index: typing.List[str]) -> pd.DataFrame:
        """
        Loads df from file and sets index.

        :param path: path of file to read.
        """
        # If path doesn't exist, create csv.
        if not os.path.exists(path):
            os.makedirs(os.path.dirname(path), exist_ok=True)
            pd.DataFrame().to_csv(path)
        df = pd.read_csv(path)
        if index:
            df = df.set_index(index)
        return df

    def upload(self, source_filepath: str, s3_filepath: str = None) -> None:
        """
        Uploads file to S3 bucket.

        :param source_filepath: filepath for file to upload.
        :param s3_filepath: filepath taken by obj in s3 bucket.
        """
        if s3_filepath is None:
            s3_filepath = os.path.basename(source_filepath)

        # Replace backslashes (from Windows) with forward slashes in s3_filepath
        s3_filepath = s3_filepath.replace("\\", "/")

        try:
            self.__s3_resource.meta.client.upload_file(
                source_filepath, self.__bucket, s3_filepath
            )
        except Exception as e:
            logging.warning(f"Cache upload failed with {e}.")

    def pull(
        self,
        target_filepath: str,
        merge: bool,
        index: typing.List[str] = None,
        s3_filepath: str = None,
    ) -> None:
        """
        Downloads a file from an S3 bucket and saves it to the specified target
        filepath. Optionally merges the downloaded file with
        existing contents.

        :param target_filepath: The filepath to save the downloaded file to.
        :param merge: When True, the downloaded file will be merged with any existing
                      contents of the target file. Overrite the target when False.
        :param index: A list of column names to use as the index for the DataFrame
                      if the downloaded file is a CSV.
        :param s3_filepath: The path to the file in the S3 bucket to download.
                            If None, the basename of target_filepath will be used.
        """
        if s3_filepath is None:
            s3_filepath = os.path.basename(target_filepath)

        old_file = self.__load_df(target_filepath, index)

        # Pull file.
        try:
            self.__s3_resource.meta.client.download_file(
                self.__bucket, s3_filepath, target_filepath
            )
        except Exception as err:
            logging.error(
                "Cache file download failed with: '%s'.",
                err,
            )
            raise err

        if merge:
            new_file = self.__load_df(target_filepath, index)

            # Sync df and current file.
            unique_old_rows = old_file[~old_file.index.isin(new_file.index)]
            merged_df = pd.concat([new_file, unique_old_rows], axis=0)

            # Write out to file.
            merged_df.to_csv(target_filepath)

    @staticmethod
    def download_file_from_url(
        s3_url: str, local_file_path: str, client: boto3.client
    ) -> None:
        """Downloads a file from an S3 URL to local storage.

        Args:
            s3_url: URL of the file in the S3 bucket.
            local_file_path: Path where the file will be saved locally.

        Raises:
            Exception: If there is an error during the download process.
        """

        try:
            parsed_url = urlparse(s3_url)
            s3_bucket = parsed_url.netloc
            s3_input_file = parsed_url.path.lstrip("/")
        except Exception as err:
            logging.error(f"Error parsing S3 URL {s3_url}: {err}")
            raise err

        try:
            client.download_file(s3_bucket, s3_input_file, local_file_path)
            logging.info(
                f"File {s3_input_file} from bucket {s3_bucket} downloaded to {local_file_path} successfully."
            )
        except Exception as err:
            logging.error(
                f"Error downloading file {s3_input_file} from bucket {s3_bucket} to {local_file_path}: {err}."
            )
            raise err

    @staticmethod
    def upload_file_to_url(
        local_file_path: str, s3_url: str, client: boto3.client
    ) -> None:
        """Uploads a file to an S3 URL from local storage.

        Args:
            local_file_path: Path of the file on the local file system.
            s3_url: URL where the file will be uploaded in the S3 bucket.

        Raises:
            Exception: If there is an error during the upload process.
        """

        try:
            parsed_url = urlparse(s3_url)
            s3_bucket = parsed_url.netloc
            s3_file_key = parsed_url.path.lstrip("/")
        except Exception as err:
            logging.error(f"Error parsing S3 URL {s3_url}: {err}")
            raise err

        try:
            client.upload_file(local_file_path, s3_bucket, s3_file_key)
            logging.info(
                f"File {local_file_path} uploaded to {s3_file_key} in bucket {s3_bucket} successfully."
            )
        except Exception as err:
            logging.error(
                f"Error uploading file {local_file_path} to {s3_file_key} in bucket {s3_bucket}: {err}."
            )
            raise err


class S3CacheFile:
    """Implements a cache dataframe that is stored in S3.

    You must call pull() to pull the file from S3 before gaining access to the dataframe df.
    When mocking, set your mock dataframe to self.df manually, and do not call pull().
    Once you are done with the dataframe, call save_and_upload() to save the dataframe to the
    cache file and upload it to S3.

    TODO(P1): Consider hiding the df, and make this a generic cache file class.
    """

    def __init__(self, s3_cache_bucket: str, filename: str, idx: list) -> None:
        self.s3_cache_bucket = s3_cache_bucket
        self.s3_cache = S3(self.s3_cache_bucket)
        self.filename = filename
        self.idx = idx
        self.df = None

    def pull(self, merge=False):
        # By default, overrite whatever local file is there.
        self.s3_cache.pull(target_filepath=self.filename, merge=merge)
        self.df = pd.read_csv(self.filename)

        # Drop duplicates and set index
        self.df = self.df.drop_duplicates(subset=self.idx).set_index(
            self.idx, drop=True
        )

    def save_and_upload(self, pull=True, merge=True):
        # By default, pull the file from S3 and merge it with the local file before uploading.
        if self.df is None:
            logging.error("Cannot save_and_upload() with None df.")
            raise ValueError(".df value is None. Call pull() first.")
        self.df.to_csv(self.filename)
        if pull:
            self.s3_cache.pull(
                target_filepath=self.filename, merge=merge, index=self.idx
            )
        self.s3_cache.upload(self.filename)
