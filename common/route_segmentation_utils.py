import logging
import os
import re
import typing

import gmplot
import polyline

from common.constants import PROJECT_ROOT
from common.google_maps_utils import GoogleMapsUtils


def plot_on_us_map(lat_lngs, polylines, step_polylines):
    """
    Utility function to plot segmentations. Writes to data/map.html

    Example usage:

    in `splice_to_equal_segments`:

        plot_on_us_map(
            [segment["point"] for segment in segments],
            [segment["polyline"] for segment in segments],
            step_polylines,
        )
    """
    # Extract the bounds
    lats, lngs = zip(*lat_lngs)
    center_lat, center_lng = (min(lats) + max(lats)) / 2, (min(lngs) + max(lngs)) / 2

    # Create a map object
    gmap = gmplot.GoogleMapPlotter(
        center_lat, center_lng, zoom=10
    )  # Adjust zoom as needed
    colors = ["red", "green", "blue", "orange", "purple", "yellow"]
    for i, pline in enumerate(step_polylines):
        decoded_polyline = polyline.decode(pline)
        poly_lats, poly_lngs = zip(*decoded_polyline)
        # Cycle edge width from 20,16,12,8,4,2
        edge_width = 10
        gmap.plot(
            poly_lats,
            poly_lngs,
            color=colors[i % len(colors)],
            edge_width=edge_width,
            alpha=0.5,
        )
    # Plot the polylines
    colors = ["black", "white"]
    for i, pline in enumerate(polylines):
        decoded_polyline = polyline.decode(pline)
        poly_lats, poly_lngs = zip(*decoded_polyline)
        # Cycle edge width from 20,16,12,8,4,2
        edge_width = 5
        gmap.plot(
            poly_lats,
            poly_lngs,
            color=colors[i % len(colors)],
            edge_width=edge_width,
            alpha=0.9,
        )
    # Plot the lat/lngs as points
    gmap.scatter(lats, lngs)
    # Draw the map to an HTML file
    data_path = os.path.join(
        PROJECT_ROOT, "machine_learning", "freightgpt", "data", "map.html"
    )
    gmap.draw(data_path)


def extract_location_names(html_instructions: str) -> str:
    """
    Extracts the location names from GMAPs HTML instructions.
    https://developers.google.com/maps/documentation/directions/overview
    Examples:
        input: "Turn <b>right</b> onto <b>US-31 S</b>/<wbr><b>US-31 Scenic S</b>"
        output: "US-31 S US-31 Scenic S"

        input: "Head <b>north</b> on <b>3rd St NW</b> toward <b>Copper Ave NW</b>"
        output: "north 3rd St NW Copper Ave NW"

        input: "Turn <b>left</b> onto <b>CO-71 N</b>"
        output: "CO-71 N"
    """
    # Regular expression to match patterns like <b>location_name</b>
    pattern = re.compile(r"<b>([^<]+)</b>")

    # Remove words "right" and "left" from the instructions
    html_instructions = html_instructions.replace("right", "").replace("left", "")

    # Extract all matching patterns from the data
    locations = pattern.findall(html_instructions)

    return " ".join(locations)


def splice_to_equal_segments(
    steps: typing.List[typing.Dict],
    segment_length: int = 100000,
    plot: bool = False,
) -> typing.List[typing.Dict[str, typing.Any]]:
    """
    Breaks a list of steps into segments of a specified length.

    Includes destination but not origin in the segments.

    Args:
    - steps (List[Dict]): A list of steps where each step is a dictionary containing:
        - "distance": a dictionary with "value" key indicating step distance in some unit.
        - "duration": a dictionary with "value" key indicating step duration in some unit.
    - segment_length (int): The desired length for each segment.
    - plot (bool): Whether to plot the segments on a map for debugging.

    Returns:
    - List[Dict]: A list of segments where each segment is a dictionary containing:
        - "step_name" (str): the name of the road or step from which the segment was derived.
        - "polyline" (str): the encoded polyline of the segment.
        - "distance_in_step" (float): the position in the current step at which the segment ends, in meters.
        - "seconds_enroute" (int): the number of seconds into the route at which the segment ends.
        - "point" (Tuple[float]): the lat, lng of the end of the segment.

    Toy Example:
    If steps = [
        {"distance": {"value":  50}, "duration": {"value": 50}},
        {"distance": {"value": 100}, "duration": {"value": 100}},
        {"distance": {"value":  70}, "duration": {"value": 140}}
    ] and segment_length = 70, the function will return [
        {"distance_in_step": 20, "seconds_enroute":  70},
        {"distance_in_step": 90, "seconds_enroute": 140},
        {"distance_in_step": 60, "seconds_enroute": 260},
    ].
    """
    if steps == []:
        return []

    # Output list of segments
    segments = []
    # Initialize the remaining distance for the current segment
    remaining_seg_dist = segment_length
    last_polyline = []
    seconds_enroute = 0
    # This is for debugging using plot_on_us_map
    step_polylines = []

    for step in steps:
        # Extract distance, duration, and name from the step
        step_distance = step["distance"]["value"]
        step_duration = step["duration"]["value"]
        step_name = extract_location_names(step["html_instructions"])

        # Extract the polyline from the step.
        # https://developers.google.com/android/reference/com/google/android/gms/maps/model/Polyline
        pline = step["polyline"]["points"]
        step_polylines.append(pline)
        # Get list of (lat, lng) tuples from the polyline
        decoded_polyline = polyline.decode(pline)

        # Initialize the position within the current step
        position_in_step = remaining_seg_dist
        # Iterate through steps until we have reached the segment length
        remaining_seg_dist -= step_distance

        # Initialize outside of loop for last segment
        end_fraction = 0
        while remaining_seg_dist <= 0:
            # Calculate the fractions of the step covered by this segment
            start_fraction = max(0, position_in_step - segment_length) / step_distance
            end_fraction = position_in_step / step_distance
            middle_fraction = (start_fraction + end_fraction) / 2

            # Add remaining polyline from the last step, and update the current segment polyline
            current_polyline = (
                last_polyline
                + decoded_polyline[
                    int(start_fraction * len(decoded_polyline)) : int(
                        end_fraction * len(decoded_polyline)
                    )
                    + 1
                ]
            )
            # Reset the last polyline for the next segment
            last_polyline = []
            # Select the point at the middle of the segment
            point = decoded_polyline[int(middle_fraction * len(decoded_polyline))]
            # How many seconds into the step is the point
            seconds_at_point = middle_fraction * step_duration

            # Add the segment to the list of segments
            segments.append(
                {
                    "step_name": step_name,
                    "polyline": polyline.encode(current_polyline),
                    "distance_in_step": position_in_step,
                    "seconds_enroute": seconds_enroute + seconds_at_point,
                    "point": point,
                }
            )
            # Update remaining segment distance and position within the current step
            remaining_seg_dist += segment_length
            position_in_step += segment_length

        # Update total time enroute
        seconds_enroute += step_duration
        # Update the last_polyline for the next segment
        last_polyline.extend(
            decoded_polyline[int(end_fraction * len(decoded_polyline)) :]
        )

    # Append the remaining details for the last step
    segments.append(
        {
            "step_name": step_name,
            "polyline": polyline.encode(last_polyline),
            "distance_in_step": position_in_step,
            "seconds_enroute": seconds_enroute,
            "point": decoded_polyline[-1],
        }
    )

    if plot:
        plot_on_us_map(
            [segment["point"] for segment in segments],
            [segment["polyline"] for segment in segments],
            step_polylines,
        )

    return segments


def get_route(
    start: str, end: str, gmaps_utils: GoogleMapsUtils
) -> typing.Dict[str, typing.Any]:
    """Gets the route details from start to end.

    Args:
        start (str): The starting location, a searchable Google Maps location.
        end (str): The ending location, a searchable Google Maps location.

    Returns:
        Dict[str, Any]: A dictionary containing:
            - "encoded_polyline": the encoded polyline of the whole route.
            - "bounds": the northeast and southwest lat/lng bounds of the whole route.
            - "summary": a summary of the route chosen by Google Maps.
            - "warnings": any warnings from Google Maps as a list.
            - "steps": list of steps, each step being a dictionary of the step details.

    Raises:
        ValueError: If the encoded_polyline is missing in the response.
    """
    routes = gmaps_utils.get_directions(start, end)

    # Extract the first route and its details safely
    first_route = routes[0]
    encoded_polyline = first_route.get("overview_polyline", {}).get("points")
    bounds = first_route.get("bounds")
    summary = first_route.get("summary")
    warnings = first_route.get("warnings")

    legs = first_route.get("legs", [])
    first_leg = legs[0] if legs else {}
    steps = first_leg.get("steps", [])
    distance_miles = gmaps_utils.meters_to_miles(
        first_leg.get("distance", {}).get("value"), precision=0
    )

    if encoded_polyline is None:
        raise ValueError("Missing encoded_polyline in the response.")

    return {
        "encoded_polyline": encoded_polyline,
        "bounds": bounds,
        "summary": summary,
        "warnings": warnings,
        "steps": steps,
        "total_distance": distance_miles,
    }


# Make sure to make it traffic aware
# Try alternatives = true maybe?
def get_segmented_route(
    origin: str,
    destination: str,
    segment_meters: int,
    gmaps_utils: GoogleMapsUtils = None,
) -> typing.Dict[str, typing.Any]:
    """Gets the route from origin to destination and breaks it into segments of a specified length.

    Example response:
    {
        "encoded_polyline": "a~l~Fj",
        "bounds": {
            "northeast": {
                "lat": 38.0000000,
                "lng": -120.0000000
            },
            "southwest": {
                "lat": 37.0000000,
                "lng": -121.0000000
            }
        },
        "summary": "CA 123",
        "warnings": [],
        "segments": [
            {
                "step_name": "CA 123",
                "polyline": "a~l~Fj",
                "distance_in_step": 90,
                "seconds_enroute": 280,
                "point": (37.5000000, -120.5000000)
            },
        ]
    }

    Args:
    - origin (str): The starting location, a searchable Google Maps location.
    - destination (str): The ending location, a searchable Google Maps location.
    - segment_meters (int): The desired length for each segment.

    Returns:
    - Dict[str, Any]: A dictionary containing:
        - "encoded_polyline": the encoded polyline of the whole route.
        - "bounds": the northeast and southwest lat/lng bounds of the whole route.
            - "northeast": the northeast lat/lng bounds of the whole route.
                - "lat": the latitude of the northeast bound.
                - "lng": the longitude of the northeast bound.
            - "southwest": the southwest lat/lng bounds of the whole route.
                - "lat": the latitude of the southwest bound.
                - "lng": the longitude of the southwest bound.
        - "summary": a summary of the route chosen by Google Maps.
        - "warnings": any warnings from Google Maps as a list.
        - "segments": a list of segments where each segment is a dictionary containing:
            - "step_name": the name of the step from which the segment was derived.
            - "polyline": the encoded polyline of the segment.
            - "distance_in_step": the position in the current step at which the segment ends.
            - "seconds_enroute": the number of seconds into the route at which the segment ends.
            - "point": the lat/lng of the end of the segment.
    """
    if gmaps_utils is None:
        gmaps_utils = GoogleMapsUtils()
    route_details = get_route(origin, destination, gmaps_utils=gmaps_utils)

    # Extract the start location coordinates safely
    steps = route_details.get("steps", [])
    # Check that steps is not empty
    if not steps:
        logging.error(
            f"Unable to find steps for route between {origin} and {destination}."
        )
        raise ValueError(
            f"Unable to find steps for route between {origin} and {destination}."
        )
    start_location = steps[0].get("start_location", {})
    lat = start_location.get("lat")
    lng = start_location.get("lng")
    if lat is None or lng is None:
        logging.error("Start location is missing lat or lng.")
        raise ValueError("Start location is missing lat or lng.")
    # Construct the origin segment to match the segments format
    origin_segment = {"point": (lat, lng), "seconds_enroute": 0}

    # Extract 'steps' safely and obtain segments
    steps = route_details.get("steps", [])
    # Add the origin segment to the route, so that the llm response includes the origin and destination
    segments = [origin_segment] + splice_to_equal_segments(steps, segment_meters)

    return {
        "encoded_polyline": route_details["encoded_polyline"],
        "bounds": route_details["bounds"],
        "summary": route_details["summary"],
        "warnings": route_details["warnings"],
        "segments": segments,
        "total_distance": route_details["total_distance"],
    }
