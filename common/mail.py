import email
import logging
import os
import pickle
from base64 import urlsafe_b64decode
from base64 import urlsafe_b64encode
from email.mime.application import MIMEApplication
from email.mime.audio import MIMEAudio
from email.mime.image import MIMEImage
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from mimetypes import guess_type as guess_mime_type
from typing import Any
from typing import Dict
from typing import List

import boto3
import pytz
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.discovery import Resource

from common import credentials
from common.constants import PROJECT_ROOT

# TODO(P0): Write unit tests for this library.

# From https://www.thepythoncode.com/article/use-gmail-api-in-python
# TODO(P2): Rewrite library using email library more. Document params.

MAIL_TOKEN_PICKLE = os.path.join(PROJECT_ROOT, "common", "gmail_token.pickle")
MAIL_TOKEN_JSON = os.path.join(PROJECT_ROOT, "common", "gmail_credentials.json")
SCOPES = ["https://mail.google.com/"]
CREDENTIALS = credentials.Credentials()


class MailAPI:
    def __init__(self, gmail=True, creds_path: str = MAIL_TOKEN_PICKLE):
        self.creds_path = creds_path
        if gmail:
            self.gmail_service = self.gmail_authenticate()

    def gmail_authenticate(self) -> Resource:
        """
        Authenticates to Gmail API.

        The function first checks if there is a token stored in `MAIL_TOKEN_PICKLE`. If the token exists, it tries to refresh the token.
        If the token does not exist, it creates a new token using the `InstalledAppFlow`.

        Returns:
            gmail_service: An instance of the Gmail API client.
        """
        creds = None
        # the file token.pickle stores the user's access and refresh tokens, and is
        # created automatically when the authorization flow completes for the first time
        if os.path.exists(self.creds_path):
            with open(self.creds_path, "rb") as token:
                creds = pickle.load(token)
        else:
            raise FileNotFoundError(
                "Missing file {}.\n Please download the mail token pickle file from the Google Cloud Platform, or contact Roop.".format(
                    self.creds_path
                )
            )
        # if there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    MAIL_TOKEN_JSON, SCOPES
                )
                creds = flow.run_local_server(port=0)
            # save the credentials for the next run
            with open(self.creds_path, "wb") as token:
                pickle.dump(creds, token)
        return build("gmail", "v1", credentials=creds)

    def search_messages(self, query: str) -> List[Dict[str, str]]:
        """
        Searches for messages that match a specific query.

        Args:
            query: The search query string.

        Returns:
            List of messages that match the query.
        """
        # TODO(P1): Make this recursive.
        result = (
            self.gmail_service.users().messages().list(userId="me", q=query).execute()
        )
        messages = result["messages"] if "messages" in result else []
        while "nextPageToken" in result:
            page_token = result["nextPageToken"]
            result = (
                self.gmail_service.users()
                .messages()
                .list(userId="me", q=query, pageToken=page_token)
                .execute()
            )
            if "messages" in result:
                messages.extend(result["messages"])
        return messages

    def mark_as_read_and_archive(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Marks a specific message as read and moves it to the archive.

        Args:
            message: The message to mark as read and archive.

        Returns:
            The updated message.
        """
        return (
            self.gmail_service.users()
            .messages()
            .modify(
                userId="me",
                id=message["id"],
                body={"removeLabelIds": ["UNREAD", "INBOX"]},
            )
            .execute()
        )

    def get_size_format(self, b, factor=1024, suffix="B"):
        """
        Scale bytes to its proper byte format
        e.g:
            1253656 => '1.20MB'
            1253656678 => '1.17GB'
        """
        for unit in ["", "K", "M", "G", "T", "P", "E", "Z"]:
            if b < factor:
                return f"{b:.2f}{unit}{suffix}"
            b /= factor
        return f"{b:.2f}Y{suffix}"

    def parse_parts(self, parts, folder_name, message):
        """
        Utility function that parses the content of an email partition
        """
        file_found = False
        if parts:
            for part in parts:
                filename = part.get("filename")
                mimeType = part.get("mimeType")
                body = part.get("body")
                data = body.get("data")
                file_size = body.get("size")
                part_headers = part.get("headers")
                if part.get("parts"):
                    # recursively call this function when we see that a part
                    # has parts inside
                    file_found = (
                        self.parse_parts(part.get("parts"), folder_name, message)
                        or file_found
                    )
                if mimeType == "text/plain":
                    # if the email part is text plain
                    if data:
                        # text
                        logging.debug(
                            "ignoring text/plain data. Consider using _ = urlsafe_b64decode(data).decode()."
                        )
                elif mimeType == "text/html":
                    # if the email part is an HTML content
                    # save the HTML file and optionally open it in the browser
                    if not filename:
                        filename = "index.html"
                    # filepath = os.path.join(folder_name, filename)
                    logging.debug("parsing index.html")
                    # with open(filepath, "wb") as f:
                    #     f.write(urlsafe_b64decode(data))
                else:
                    # attachment other than a plain text or HTML
                    for part_header in part_headers:
                        part_header_name = part_header.get("name")
                        part_header_value = part_header.get("value")
                        if (
                            (
                                part_header_name == "Content-Disposition"
                                and "attachment" in part_header_value
                            )
                            or (
                                part_header_name == "Content-Type"
                                and "xlsx" in part_header_value
                            )
                            or (
                                # This is only for MX Solutions. Probably NBD.
                                ".csv" == os.path.splitext(filename)[1]
                                and part_header_name == "Content-Type"
                                and "application/octet-stream" in part_header_value
                            )
                        ):
                            # we get the attachment ID
                            # and make another request to get the attachment itself
                            logging.debug(
                                "Saving the file: {} size: {}".format(
                                    filename, self.get_size_format(file_size)
                                )
                            )
                            attachment_id = body.get("attachmentId")
                            attachment = (
                                self.gmail_service.users()
                                .messages()
                                .attachments()
                                .get(
                                    id=attachment_id,
                                    userId="me",
                                    messageId=message["id"],
                                )
                                .execute()
                            )
                            data = attachment.get("data")
                            filepath = os.path.join(folder_name, filename)
                            if data:
                                file_found = True
                                # TODO(P1): Save file to S3 instead of local disk.
                                with open(filepath, "wb") as f:
                                    f.write(urlsafe_b64decode(data))
        return file_found

    def read_message(self, message, output_dir_prefix):
        """
        This function takes Gmail API `self.gmail_service` and the given `message_id` and does the following:
            - Downloads the content of the email
            - Prints email basic information (To, From, Subject & Date) and plain/text parts
            - Creates a folder for each email based on the subject
            - Downloads text/html content (if available) and saves it under the folder created as index.html
            - Downloads any file that is attached to the email and saves it in the folder created
        """
        msg = (
            self.gmail_service.users()
            .messages()
            .get(userId="me", id=message["id"], format="full")
            .execute()
        )
        # parts can be the message body, or attachments
        payload = msg["payload"]
        headers = payload.get("headers")
        parts = payload.get("parts")
        has_subject = False
        if headers:
            # this section prints email basic info & creates a folder for the email
            for header in headers:
                name = header.get("name")
                value = header.get("value")
                if name.lower() == "from":
                    # we print the From address
                    logging.debug("From: %s" % value)
                if name.lower() == "to":
                    # we print the To address
                    logging.debug("To: %s" % value)
                if name.lower() == "subject":
                    has_subject = True
                    logging.debug("Subject: %s" % value)
                if name.lower() == "date":
                    # we print the date when the message was sent
                    date = (
                        email.utils.parsedate_to_datetime(value)
                        .astimezone(pytz.utc)
                        .replace(tzinfo=None)
                    )
                    logging.debug("Date: %s" % date)
        # TODO(P1): Investigate microsecond precision for timestamp (.%f).
        folder_name = os.path.join(
            output_dir_prefix, date.strftime("%Y-%m-%d_%H-%M-%S").replace("/", "-")
        )
        if not os.path.isdir(folder_name):
            os.mkdir(folder_name)
        if not has_subject:
            logging.warning("No subject in email.")
        file_found = self.parse_parts(parts, folder_name, message)
        if not file_found:
            logging.error(
                "No attachment found in email (date: {}, folder_name: {}).".format(
                    date, folder_name
                )
            )
            raise FileNotFoundError("No attachment found in email.")
        logging.debug("=" * 50)
        return date, folder_name

    # Adds the attachment with the given filename to the given message
    # TODO(P0): Change this to only use MIMEApplication.
    def add_attachment(self, message, filename):
        content_type, encoding = guess_mime_type(filename)
        if content_type is None or encoding is not None:
            content_type = "application/octet-stream"
        main_type, sub_type = content_type.split("/", 1)
        fp = open(filename, "rb")
        if main_type == "text":
            logging.info(f"Adding text attachment: {filename}")
            msg = MIMEText(fp.read().decode(), _subtype=sub_type)
            fp.close()
        elif main_type == "image":
            logging.info(f"Adding image attachment: {filename}")
            msg = MIMEImage(fp.read(), _subtype=sub_type)
            fp.close()
        elif main_type == "audio":
            logging.info(f"Adding audio attachment: {filename}")
            msg = MIMEAudio(fp.read(), _subtype=sub_type)
            fp.close()
        else:
            logging.info(f"Adding attachment: {filename}")
            msg = MIMEApplication(main_type, sub_type)
            msg.set_payload(fp.read())
            fp.close()
        filename = os.path.basename(filename)
        msg.add_header("Content-Disposition", "attachment", filename=filename)
        message.attach(msg)

    def build_message(
        self,
        frm: str,
        to: List[str],
        subject: str,
        body: str,
        attachments: List[str] = [],
    ):
        if not attachments:  # no attachments given
            message = MIMEText(body)
            message["to"] = ",".join(to)
            message["from"] = frm
            message["subject"] = subject
        else:
            message = MIMEMultipart()
            message["to"] = ",".join(to)
            message["from"] = frm
            message["subject"] = subject
            message.attach(MIMEText(body))
            for filename in attachments:
                self.add_attachment(message, filename)
        logging.info("Message built.")
        return message

    def send_message_gmail(
        self,
        frm: str,
        to: List[str],
        subject: str,
        body: str,
        attachments: List[str] = [],
    ):
        message = self.build_message(frm, to, subject, body, attachments)
        body = {"raw": urlsafe_b64encode(message.as_bytes()).decode()}
        return (
            self.gmail_service.users().messages().send(userId="me", body=body).execute()
        )

    # TODO(P1): Unify the code for sending emails -- figure out why <NAME_EMAIL> fails for AWS.
    def send_email_aws(
        self,
        frm: str,
        to: List[str],
        subject: str,
        body: str,
        attachments: List[str] = [],
    ):
        # TODO(P0): Upload attachments to S3.
        message = self.build_message(frm, to, subject, body, attachments)

        ses_client = boto3.client("ses", region_name=CREDENTIALS.aws_region)
        response = ses_client.send_raw_email(
            Source=frm, Destinations=to, RawMessage={"Data": message.as_string()}
        )
        logging.info(response)


# OUR_EMAIL should be a parameter passed in from ingestion
# INGESTION_DATA_DIR should not be a default arg
