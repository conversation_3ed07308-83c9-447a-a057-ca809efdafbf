{"name": "Truce", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ungap/structured-clone": "^1.0.1", "apexcharts": "^3.35.3", "aws-amplify": "^4.3.36", "aws-sdk": "^2.1582.0", "axios": "^0.21.1", "chroma-js": "^2.4.2", "core-js": "^3.6.5", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "jsvectormap": "^1.5.1", "twojs-ts": "^0.7.0", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-gtag": "^1.16.1", "vue-phone-number-input": "^1.1.10", "vue-roller": "^1.12.7", "vue-router": "^3.5.1", "vuelayers": "^0.12.6", "vuetify": "^2.7.2", "vuevectormap": "^1.1.0", "vuex": "^3.6.2", "vuex-persistedstate": "^4.0.0-beta.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.18.12/xlsx-0.18.12.tgz"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.1.0", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}