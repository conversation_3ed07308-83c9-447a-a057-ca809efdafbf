<template>
    <div style="width: 100%">
        <apexchart
            height="220"
            type="line"
            :options="options"
            :series="series"
            ref="driversChart"
        ></apexchart>
    </div>
</template>
<script>
import * as format from '../formatShipmentData';
import * as utils from '../utils';
export default {
    name: 'DriversGraph',
    props: ['driversVariables'],

    //TODO - make val an object instead of an array
    watch: {
        driversVariables(val) {
            if (val[0] == null) {
                return;
            }
            this.metricTrends = val[0];
            this.isBrokerData = val[1];
            this.metricTrendType = val[2];
            this.updateGraph();
            this.timePeriod = val[3];
        }
    },

    methods: {
        updateGraph() {
            var toDraw = [];

            switch (this.metricTrendType) {
                case 'MPL':
                    toDraw.push(this.lineData('MPL'));
                    toDraw.push(this.lineData('Average', 2));
                    this.options.colors = ['#D81B60', '#1E88E5'];
                    this.options.stroke = {
                        width: [2, 2],
                        dashArray: [0, 4]
                    };
                    break;
                case 'VOL':
                    toDraw.push(this.lineData('volume'));
                    toDraw.push(this.lineData('Average', 0));
                    this.options.colors = ['#7E57C2', '#1E88E5'];
                    this.options.stroke = {
                        width: [2, 2],
                        dashArray: [0, 4]
                    };
                    break;
                case 'CPM':
                    toDraw.push(this.lineData('SPM'));
                    toDraw.push(this.lineData('CPM'));
                    this.options.colors = ['#1E88E5', '#FF6F00'];
                    this.options.stroke = {
                        width: [2, 2],
                        dashArray: [0, 0]
                    };
                    break;
            }
            this.series = toDraw;
            this.options.series = this.series;
            this.$refs.driversChart.updateOptions(this.options);
        },

        lineData: function (type, avgDecimal = 0) {
            // returns line as object to be inserted into this.series
            const data = this.metricTrends;
            var nameVal = type.toLowerCase();
            var shaped = [];
            if (nameVal == 'average') {
                // Average line
                nameVal = 'avg';
                let average = format.formatDecimal(
                    data[0]['avg_metric'],
                    avgDecimal
                );
                data.forEach((element) =>
                    shaped.push({
                        x: new Date(element.week).getTime(),
                        y: average
                    })
                );
            } else if (nameVal in data[0]) {
                // Trend line
                data.forEach((element) =>
                    shaped.push({
                        x: new Date(element.week).getTime(),
                        y: parseFloat(element[nameVal])
                    })
                );
            } else {
                // Type not found
                console.log('Invalid graph type');
                return {};
            }
            return {
                name: nameVal,
                data: shaped.sort((a, b) => (a.x > b.x ? 1 : -1))
            };
        },

        getGraphTooltipDate(value) {
            var options = { month: 'short', day: 'numeric' };
            var day_ms = 1000 * 60 * 60 * 24;
            if (this.timePeriod > utils.DEFAULT_DAYS) {
                const week_date = new Date(value + day_ms).toLocaleDateString(
                    'en-us',
                    options
                );
                var week_offset_date = new Date(
                    value + 7 * day_ms
                ).toLocaleDateString('en-us', options);
                return week_date + ' - ' + week_offset_date;
            } else {
                const week_date = new Date(value + day_ms).toLocaleDateString(
                    'en-us',
                    options
                );
                return week_date;
            }
        }
    },

    beforeMount() {
        window.component = this;
    },

    data: function () {
        return {
            displayedData: this.displayed_data,
            isBrokerData: this.is_broker_data,
            metricTrendType: this.metric_trend_type,
            timePeriod: this.time_period,

            graphAverage: 0,
            graphAverageCount: 0,
            avgList: [],

            options: {
                chart: {
                    type: 'line',
                    toolbar: {
                        show: false,
                        tools: {
                            download: false,
                            selection: false,
                            zoom: false,
                            zoomin: true,
                            zoomout: true,
                            pan: false,
                            reset: false,
                            customIcons: []
                        }
                    },
                    animations: {
                        enabled: true
                    }
                },
                tooltip: {
                    x: {
                        show: true,
                        formatter: this.getGraphTooltipDate
                    }
                },
                stroke: {
                    lineCap: 'round',
                    curve: 'smooth'
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        datetimeFormatter: {
                            month: 'dd MMM'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            switch (window.component.metricTrendType) {
                                case 'MPL':
                                    return format.formatDollars(value);
                                case 'VOL':
                                    return value.toFixed(0);
                                case 'CPM':
                                    return format.formatDollars(value, 2);
                            }
                        },
                        tickAmount: 4,
                        min: function (min) {
                            switch (window.component.metricTrendType) {
                                case 'MPL':
                                    return min - 1;
                                case 'VOL':
                                    return min - 1;
                                case 'CPM':
                                    return min - 0.1;
                            }
                        },
                        max: function (max) {
                            switch (window.component.metricTrendType) {
                                case 'MPL':
                                    return max + 1;
                                case 'VOL':
                                    return max + 1;
                                case 'CPM':
                                    return max + 0.1;
                            }
                        }
                    }
                },
                legend: {
                    show: true,
                    position: 'top',
                    horizontalAlign: 'left'
                },
                series: []
            }
        };
    }
};
</script>
