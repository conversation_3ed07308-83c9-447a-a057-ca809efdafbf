<template>
    <div>
        <v-row justify="center" align="center">
            <v-col cols="12" align="center">
                <v-avatar size="100" class="mt-10">
                    <img alt="user" src="../assets/box.png" />
                </v-avatar>
            </v-col>
        </v-row>
        <v-row justify="center" align="center">
            <v-col cols="12" align="center">
                <p class="font-weight-bold text-h5">
                    {{ name }}
                </p>
                <v-chip color="deep-purple accent-4" outlined>
                    {{ role }}
                </v-chip>
            </v-col>
        </v-row>
        <v-row justify="center" align="center" class="mt-2">
            <v-col cols="12" align="center">
                <p class="">
                    <v-icon medium>mdi-email</v-icon>
                    : {{ email }}
                </p>
                <p class="">
                    <v-icon medium>mdi-phone</v-icon>
                    : {{ phone_number }}
                </p>
            </v-col>
        </v-row>
    </div>
</template>

<script>
import * as stateAPI from '../stateAPI';
export default {
    name: 'Profile',

    components: {},

    methods: {
        processRole(r) {
            if (r == 'admin') {
                return 'Site Administrator';
            }

            return r.charAt(0).toUpperCase() + r.slice(1);
        }
    },

    data: function () {
        return {
            name: stateAPI.getStateProperty(this, 'name'),
            role: this.processRole(stateAPI.getStateProperty(this, 'role')),
            email: stateAPI.getStateProperty(this, 'email'),
            phone_number: stateAPI.getStateProperty(this, 'phone_number'),
            company: stateAPI.getStateProperty(this, 'company')
        };
    }
};
</script>

<style></style>
