// // Count business hours between start and date. Assumes 7a-3p Central Time Mon - Fri.
//function businessHoursBetween(startDate, endDate) {
//    var start = UTCtoCentralTime(new Date(startDate.valueOf()));
//    var end = UTCtoCentralTime(new Date(endDate.valueOf()));
//    var count = 0;
//    for (var i = start.valueOf(); i < end.valueOf(); i = (start.setMinutes(start.getMinutes() + 1)).valueOf()) {
//        if (start.getDay() != 0 && start.getDay() != 6 && start.getHours() >= 7 && start.getHours() < 15) {
//            count++;
//        }
//    }
//    return count / 60;
//}
//
//function UTCtoCentralTime(time) {
//    return new Date(time.toLocaleString("en-US", { timeZone: "America/Chicago" }));
//}

function parseUTC(time) {
    const as_local_time = new Date(time);
    return new Date(as_local_time.toLocaleDateString() + ' GMT');
}

export function marginPercent(shipment) {
    const total_cogs = parseFloat(shipment.cogsdisplay);
    const total_rev = parseFloat(shipment.revenuedisplay);
    const margin_percent = (total_rev - total_cogs) / total_rev;
    return total_rev ? margin_percent : 0;
}

export function marginDollars(shipment) {
    const total_cogs = parseFloat(shipment.cogsdisplay);
    const total_rev = parseFloat(shipment.revenuedisplay);
    const margin_dollars = total_rev - total_cogs;
    return margin_dollars;
}

export function OTP(shipment) {
    const origin_arrival_time = parseUTC(shipment.originarrivaltime);
    const origin_close_time = parseUTC(shipment.originclosetime);
    return origin_arrival_time > origin_close_time ? 'N' : 'Y';
}

export function OTD(shipment) {
    const destination_arrival_time = parseUTC(shipment.destinationarrivaltime);
    const destination_close_time = parseUTC(shipment.destinationclosetime);
    return destination_arrival_time > destination_close_time ? 'N' : 'Y';
}
