# Truce

Requires [Node.js](https://nodejs.org/en/download/).

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### Directories
└── src
    ├── analytics
    ├── assets
    ├── authentication
    │   └── auth_components
    ├── components
    │   └── Icons
    ├── configs
    ├── containers
    ├── landing
    └── plugins

* `src`: website src code
* `src/analytics`: reserved for user analytics
* `src/assets`: static assets such as images
* `src/authentication`: authentication javascript and components
    * `src/auth_components`: components for login and sign in flows
* `src/components`: re-usable components for entire website
* `src/configs`: relevant configurations
* `src/containers`: relevant containers (i.e. pages)
* `src/landing`: landing page code
* `src/plugins`: vuetify directory

### JS Files

* `computeKeyMetrics.js`: computes key metrics
* `computeShipmentData.js`: computes relevant shipment metrics
* `fetchAccountDetails.js`: fetches account information from RDS
* `fetchNotifications.js`: fetches notifications for user
* `fetchScoreCarding.js`: (TEMP) used for UTZ scorecarding
* `fetchShipments.js`: fetches shipment data from database
* `formatShipmentData.js`: formats response from database
* `globalVariables.js`: global variables store
* `main.js`: vuetify main js initialization
* `stateAPI.js`: exposes state commands in API form
* `stateConstants.js`: abbreviations for states
* `store.js`: Vuex Store
* `us-map.js`: map of united states in JSVector format
* `util.js`: global utils
