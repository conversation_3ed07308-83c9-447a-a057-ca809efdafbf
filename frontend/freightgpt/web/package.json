{"name": "FreightGPT", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.614.0", "@aws-sdk/client-sqs": "^3.609.0", "@mapbox/polyline": "^1.2.0", "@mdi/js": "^7.2.96", "@vueuse/core": "^10.9.0", "apexcharts": "^3.45.2", "aws-amplify": "^5.3.11", "aws-sdk": "^2.1691.0", "aws4": "^1.12.0", "axios": "^1.5.1", "core-js": "^3.8.3", "crypto-browserify": "^3.12.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "marked": "^5.1.2", "node-polyfill-webpack-plugin": "^2.0.1", "request": "^2.88.2", "vue": "^2.6.14", "vue-apexcharts": "^1.6.2", "vue-class-component": "^7.2.3", "vue-gtag": "^1.16.1", "vue-meta": "^2.4.0", "vue-property-decorator": "^9.1.2", "vue-router": "^3.6.5", "vue-typed-js": "^0.1.2", "vue2-google-maps": "^0.10.7", "vuedraggable": "^2.24.3", "vuetify": "^2.6.0", "vuevectormap": "^1.1.0", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.22.15", "@types/aws-sdk": "^2.7.0", "@types/aws4": "^1.11.4", "@types/googlemaps": "^3.43.3", "@types/lodash": "^4.17.0", "@types/node": "^20.8.6", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "~1.32.0", "sass-loader": "^10.0.0", "typescript": "~4.5.5", "vue-cli-plugin-vuetify": "~2.5.8", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.0"}, "browser": {"crypto": false}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/typescript"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {"vue/multi-word-component-names": ["error", {"ignores": ["default"]}]}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}