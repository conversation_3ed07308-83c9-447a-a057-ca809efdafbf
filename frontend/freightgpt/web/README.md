# web

## Project setup

```bash
npm install
```

### Deploy

Follow this [google doc](https://docs.google.com/document/d/1p2TrGuNGFcwWOPf0RSLCRMAtPkdqREShUIiNgboDwIQ/edit#heading=h.rfx9dkh1yi7m), and deploy to [Amplify > FreightGPT](https://us-east-1.console.aws.amazon.com/amplify/home?region=us-east-1#/d1t0uohl08c0re).

### Compiles and hot-reloads for development

```bash
npm run serve
```

### Compiles and minifies for production

```bash
npm run build
```

### Lints and fixes files

```bash
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).


### Adding environment variables

To access environment variables in a Vue.js application, you need to follow a specific convention. Environment variables should be prefixed with VUE_APP_{variable_name} and should be defined in a .env file at the root of your project.
