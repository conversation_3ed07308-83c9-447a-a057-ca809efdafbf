export function readEmailRegister() {
    const { google } = require('googleapis');
    const sheets = google.sheets('v4');
    const key = require('./googlesheetskey.json'); // Import your service account key

    // Authenticate with the service account
    const jwtClient = new google.auth.JWT(
        key.client_email,
        null,
        key.private_key,
        ['https://www.googleapis.com/auth/spreadsheets.readonly'] // Use the right scopes as needed
    );

    jwtClient.authorize((err: any, tokens: any) => {
        if (err) {
            console.error(err);
            console.log(tokens);
            return;
        }

        // Access the sheet
        const SPREADSHEET_ID = '1hWMS3OmYByhttY0wnjT7GwoHK2bQ5yZRcJt12MCUHB4';
        const RANGE = 'Sheet1!A2:A';

        sheets.spreadsheets.values.get(
            {
                auth: jwtClient,
                spreadsheetId: SPREADSHEET_ID,
                range: RANGE
            },
            (err: any, response: any) => {
                if (err) {
                    console.error(err);
                    return;
                }

                const rows = response.data.values;
                console.log(rows);
            }
        );
    });
}
