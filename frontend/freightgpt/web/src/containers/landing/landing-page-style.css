input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
input:autofill {
    box-shadow: 0 0 0 80px rgb(21, 22, 25) inset;
    -webkit-text-fill-color: rgb(255, 255, 255);
}

.landing-app-class {
    background-image: url(../../assets/landing-page-background.png) !important;
    background-color: #07090e !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    position: relative !important;
}

.landing-header {
    font-size: 4rem;
    text-align: center;
    font-weight: 340;
    color: rgb(255, 255, 255);
    margin-top: 4rem;
    margin-bottom: 4rem;
}

.landing-spacer {
    height: 9vh;
}

.landing-sub-header {
    font-size: 2rem;
    font-weight: 400;
    color: rgb(255, 255, 255);
    margin-bottom: 1rem;
}

.landing-bold-text {
    font-size: 1.2rem;
    font-weight: 500;
    color: rgb(255, 255, 255);
    margin-bottom: 1rem;
}

.landing-text {
    font-size: 1.2rem;
    font-weight: 400;
    color: rgb(255, 255, 255);
    margin-bottom: 1rem;
}

.landing-light-text {
    font-size: 1.2rem;
    font-weight: 300;
    color: whitesmoke;
    margin-bottom: 1rem;
}

.page-container {
    height: 100%;
    width: 55%;
    padding-top: 5vh;
    overflow: hidden;
}

/* For mobile devices */
@media (max-width: 1000px) {
    .landing-header {
        font-size: 3rem;
        text-align: center;
        font-weight: 400;
        color: rgb(255, 255, 255);
    }

    .landing-sub-header {
        font-size: 1.2rem;
        font-weight: 400;
        color: rgb(255, 255, 255);
        margin-bottom: 1rem;
    }

    .landing-text {
        font-size: 0.9rem;
        font-weight: 400;
        color: rgb(255, 255, 255);
        margin-bottom: 1rem;
    }

    .page-container {
        height: 100%;
        width: 95%;
    }
}

@media screen and (max-width: 1200px) {
    .page-container {
        height: 100%;
        width: 100%;
        padding: 0;
    }
}
