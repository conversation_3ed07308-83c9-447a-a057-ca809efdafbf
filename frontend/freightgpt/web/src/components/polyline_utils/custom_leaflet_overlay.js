function createCustomOverlayClass(L) {
    return L.Layer.extend({
        options: {
            position: null, // LatLng object
            content: '' // HTML content for the overlay
        },

        initialize: function (options) {
            L.Util.setOptions(this, options);
            this._map = null;
            this._div = null;
        },

        onAdd: function (map) {
            this._map = map;
            this._div = L.DomUtil.create('div', 'leaflet-custom-overlay');
            this._div.innerHTML = this.options.content;
            this._div.style.position = 'absolute';
            map.getPanes().overlayPane.appendChild(this._div);
            this.update();
            map.on('zoomend', this.update, this);
            map.on('moveend', this.update, this);
        },

        onRemove: function () {
            L.DomUtil.remove(this._div);
            this._map.off('zoomend', this.update, this);
            this._map.off('moveend', this.update, this);
            this._map = null;
        },

        update: function () {
            if (!this._map || !this._div || !this.options.position) {
                return;
            }
            var position = this._map.latLngToLayerPoint(this.options.position);
            let offset = L.point(5, 5); // Adjust the offset as needed

            position = position.add(offset);
            L.DomUtil.setPosition(this._div, position);
        }
    });
}

export default createCustomOverlayClass;
