/* These are all overrides of default Vuetify styling */
::v-deep .v-input {
    margin-top: 12px;
}
::v-deep .v-input__append-inner {
    margin-top: 0px !important;
}
::v-deep .v-expansion-panel::before {
    display: none;
}
::v-deep .v-expansion-panel {
    margin-top: 0;
}
::v-deep .v-expansion-panel--active > .v-expansion-panel-header {
    min-height: 48px;
}
::v-deep .v-text-field--outlined .v-input__slot {
    min-height: 0 !important;
}
::v-deep .v-text-field .v-input__append-inner {
    align-self: center;
}
::v-deep .v-text-field .v-input__prepend-inner {
    align-self: center;
    margin-top: 0;
}
::v-deep .cursor-default input {
    cursor: default;
}


/* common classes between the rfp components */

.active-rfp-btn{
    padding: 2px 12px;
    font-weight: 300;
    cursor: pointer;
    color: #32C3FD;
    border-bottom: 1px solid #32C3FD;
}
.inactive-rfp-btn{
    padding: 2px 12px;
    font-weight: 300;
    cursor: pointer;
    color:#AAAAAA
}
