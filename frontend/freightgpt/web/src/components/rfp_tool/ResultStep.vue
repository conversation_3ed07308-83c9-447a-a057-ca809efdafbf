<template>
<div style="display: flex; flex-direction: column; height: 100%; width: 100%; overflow-y: auto;">
    <div style="padding: 10px 0px; flex: 1">
        <div class="result-header">
            <span>
            <svg
                width="26"
                height="26"
                viewBox="0 0 95 95"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
            >
                <rect width="95" height="95" fill="url(#pattern0_327_482)" />
                <defs>
                <pattern
                    id="pattern0_327_482"
                    patternContentUnits="objectBoundingBox"
                    width="1"
                    height="1"
                >
                    <use
                    xlink:href="#image0_327_482"
                    transform="scale(0.0111111)"
                    />
                </pattern>
                <image
                    id="image0_327_482"
                    width="90"
                    height="90"
                    xlink:href="data:image/png;base64,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"
                />
                </defs>
            </svg>
            </span>
            <span style="padding-left: 12px">Successfully Priced</span>
        </div>
        <div
            style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            "
        >
            <span class="successfull-price-container">
                <span>
                    Successfully priced: {{ successCount }} / {{ laneCount }}
                </span>
                <span>
                    <span style="
                        display: flex;
                        align-items: center;">
                        <span style="margin-right: 12px;" class="percentage">
                            {{ (successCount/laneCount * 100).toFixed(2) + '%' }}
                        </span>
                            <svg
                                width="30"
                                height="30"
                                viewBox="0 0 36 36"
                            >
                            <path
                                class="circle-bg"
                                d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                fill="none"
                                stroke="#eee"
                                stroke-width="1"
                            />
                            <path
                                class="circle"
                                :stroke-dasharray="strokeDasharray"
                                d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                                fill="none"
                                stroke="#00EAFF"
                                stroke-width="1"
                                stroke-linecap="round"
                            />
                            </svg>
                    </span>
                </span>
            </span>
            <span style="display: flex; flex-direction: row; align-items: center;">
                <span style="display: flex; align-items: center; margin: 0px 15px; font-weight: 700;">
                    <span class="xlsx-icon-wrapper" v-if="pricedFileName">
                        <!-- <svg
                            width="25"
                            height="25"
                            viewBox="0 0 83 84"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                        >
                            <rect width="83" height="84" fill="url(#pattern0_364_411)" />
                            <defs>
                                <pattern
                                    id="pattern0_364_411"
                                    patternContentUnits="objectBoundingBox"
                                    width="1"
                                    height="1"
                                >
                                    <use
                                        xlink:href="#image0_364_411"
                                        transform="matrix(0.00258176 0 0 0.00255102 -0.0060241 0)"
                                    />
                                </pattern>
                                <image
                                    id="image0_364_411"
                                    width="392"
                                    height="392"
                                    xlink:href="data:image/png;base64,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"
                                />
                            </defs>
                        </svg> -->
                        <v-icon size="22" color="rgb(124,196,1)">mdi-file-document</v-icon>
                    </span>
                    <span>
                        {{ resultFileName }}
                    </span>
                </span>
                <v-btn class="download-btn" @click="downloadPricedFile" :disabled="!enableDownload">
                    Download
                    <v-icon size="22" style="margin-left: 8px">
                        mdi-tray-arrow-down
                    </v-icon>
                </v-btn>
            </span>
        </div>
        <!-- Failed pricing rows table, disabled in case we want to bring it back -->
        <!-- <div class="partition-break"/>
        <div class="failed-price-container" v-if="totalErrorCount > 0">
            <div style="display: flex; align-items: center; margin: 5px 0px">
            <span style="padding-top: 4px;">
                <svg
                    width="26"
                    height="26"
                    viewBox="0 0 90 90"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                <rect width="90" height="90" fill="url(#pattern0_327_481)" />
                    <defs>
                        <pattern
                            id="pattern0_327_481"
                            patternContentUnits="objectBoundingBox"
                            width="1"
                            height="1"
                        >
                            <use
                                xlink:href="#image0_327_481"
                                transform="scale(0.0111111)"
                            />
                        </pattern>
                        <image
                            id="image0_327_481"
                            width="90"
                            height="90"
                            xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABaCAYAAAA4qEECAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFZklEQVR4nO2da2hcRRTHx1fFNz6oiig+EasomGbP2VipKIJ+0A/FghX9ogHFB4qC+kWjQlVQrEKpbO45m1qxYor6xccXH0htg1KqUPCBWoPShmqb3DObRJsar8zmYZBMzN7Hzr2784P/l2x2zpw/Z2fv7pk7q5TH4/F4PB6Px+PxeOIT9S9boqvYLQyfa8L9RkK4Vah8p3kswdCeGeS1zlM144BmjCzaUdtUXjr7BE/jRD0rjxSGnQuYPGt21L/6iBghPIYwwPsXYXJd5n/rT/I0RhjgKfX1eJFGa4ZhXek4rcEwHmF4dfEmT0kIN3jnGqDW23mpMBxq3Gj4a5RLVzQSq60Rxk8bNXlOVW+NInWY6xxyT43Kq+Oa/K9Kt7jOI9dE/XiMJvw5qdFC+MueSsexrvPJLZrxyeTVPHsV8oTrfHLJ2EY8SxOOpmW0EIwPV646x3VeuUMTbE6vmmer+g3XeeUKoa6yEP6dttH1Maulq13nlwuiHnW4Jvgy/WqeNpthp4mh2h1dxe6sTJ6ju1Q78zt1nSCMe7M2Wgj2DVc6TlLtiiZ8sQnVPKMXVDsSBnihMPzZLKOF8KBQ18Wq3dCE7zexmmf0nmonwgCud2DyVGUz3KjagajScZRm+M6V0Zrhh+iVG45WrY4wPOLO5Jn1Gh5WrUxtU3mpJhhxbbQmlNG+zjNUqyIEgXOTZ9dq7FWtyEgVrxTGyXgVCPeGG1acPJ/MYzGNnjRzUq2GMHwWuwIDvNs2ria8J/4SAttaqu0VcnlNopc5wYO2sYXxoSRj1whuVS3TnmIYTLSmEjxmG18zPJ5wrf51aNPlx6miIwRPJzK5bjT2WMdneCrp+GYMVWTGq8vP1oxjiY0geNYWQwifS2H88ZE+OFcVFWHoT1zNXF86XrLF0ATr0oghBG+pIqIZVqTVnpIFtnrF2TpmUy2Aa1Th2lOMO9IyQBNWbbE0Y19acYTw60Jt/010bcvzLh2bbbGE8c1UYy1wzZ4rzKc1IfgtzeSF4B1bPGF4N1WjGQ6YuwxU3tGML6ebOJql4wN7PPgw/XiwTuUZHcAlwjiRduLC+EkWO0/t8eBQjcqXqbySSXVx/eW83R5zwRuJkpj9kcojQnBzlptglD3uV5nFDfAmlbv7ABm/z85o/MYWWwi+zSquJvgxV20v86VPZslyXbvtsZPvp/4fParyQC2A0zVjmGWywrjXFl8Ih7I1GvRopeNM5Zo0P5lpe7LD1vjN6UH2KZeE1XJn7PYUN1TRE8L4vEUTTYg/aXJ1YrJpAZk7oJpQTVFONOCk7SVUuiMHyUfNlDDc3lST961febww7Gk7owmH9r9eOrFpRgvDWtdJa3dVvbYpJoe8/Hwh+MN1wtpdVR+UKlyUudHC+LajJMdMa8w0e+uaapONuTHb/rVtKtSqpWvdmAyD5pX03/mEG7suMHfJuphTjcrXZXkazC4XSYVcXmObVxjgbU6qmmGX8SR1o2tcus9NNWM00ls6zzYv8/HY1byMJ45Pg0nZ6D77nguz/dbVvEzby3iTmtFCsN5dMrjg/rik+/qSyniTismmpRPnNJiUK2fQvPHNe2eXozfD1NtepqXj1mScqZxxIdwihM9Ma4v5Wz7mhh8nMlkHsMp1ErooCmBVLJNNC0cT/uQ8AS6MdsdqezXpZviopVTF7oaNrh+46nriXDARbGu8os3JiK4nzkUTHIhjdM39xLFYIpQ4Rn/hfOJcOA00bHRI+EAOJh4VSbFO/DWXKllut9ItJuNV7NPZzVl03mxclMnGq1gmz1Z2/7Il0wdmD/g3SJxjsLlYgO3GG/87Ax6Px+PxeDwej8fj8XhUm/EPS8J7GypACIoAAAAASUVORK5CYII="
                        />
                    </defs>
                </svg>
            </span>
            <span style="padding-left: 12px"> Failed to Price </span>
            </div>
        </div>
        <div style="display: flex; align-items: center" v-else>
            <span style="display: flex">
                <svg
                    width="26"
                    height="26"
                    viewBox="0 0 95 95"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                    <rect width="95" height="95" fill="url(#pattern0_327_482)" />
                    <defs>
                    <pattern
                        id="pattern0_327_482"
                        patternContentUnits="objectBoundingBox"
                        width="1"
                        height="1"
                    >
                        <use
                        xlink:href="#image0_327_482"
                        transform="scale(0.0111111)"
                        />
                    </pattern>
                    <image
                        id="image0_327_482"
                        width="90"
                        height="90"
                        xlink:href="data:image/png;base64,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"
                    />
                    </defs>
                </svg>
            </span>
            <span style="margin-left: 12px">All rows have been priced successfully.</span>
        </div>
        <table class="custom-table">
            <tbody>
                <tr
                    v-for="[error, count] of Object.entries(pricingErrors).filter(([_, count]) => count > 0)"
                    :key="error"
                    class="custom-table-row"
                >
                    <td style="padding-left: 25px; margin-bottom: 6px; width: 30%">
                        {{ count }} rows
                    </td>
                    <td style="margin-bottom: 6px; width: min-content">
                        {{ error }}
                    </td>
                    <td class="info-tooltip" v-if="error === 'Multiple errors'">
                        <v-icon small color="#7b7b7b">mdi-help-circle-outline</v-icon>
                        <span class="info-tooltip-text">These rows have multiple issues, download the file to see them in more detail.</span>
                    </td>
                    <td v-else></td>
                </tr>
            </tbody>
        </table> -->
        <div class="partition-break" v-if="showFeedbackForm"/>
        <div v-if="showFeedbackForm" >
            Tell us about your experience
            <v-form ref="contactForm" style="display: flex; flex-direction: column;">
                <v-textarea
                    v-model="feedback"
                    dark
                    filled
                    hide-details
                    solo
                    flat
                    no-resize
                    rows="5"
                    light
                    background-color="rgba(48,56,80,0.5)"
                    style="width: 100%; margin-top: 12px"
                />
                <span style="margin-top: 12px; margin-left: auto; display: flex">
                    <span
                        v-if="feedbackSuccess"
                        style="
                            font-size: 14px;
                            color: #5ea09c;
                            margin-right: 12px;
                            display: flex;
                            align-items: center
                        ">Thank you for giving us your feedback</span>
                    <v-btn
                        light
                        color="#F8F8F8"
                        outlined
                        style="
                            text-transform: capitalize;
                            border-color: rgb(49, 129, 183);
                            font-weight: 400;
                        "
                        @click="sendFeedbackEmail"
                    >
                        Submit Feedback
                    </v-btn>
                </span>
            </v-form>
        </div>
    </div>
    <div style="display: flex; margin-top: 10px; justify-content: flex-end">
        <v-btn
            class="nav-btn"
            @click="$emit('changeCurrentState','price')();"
        >
            <v-icon size="22" style="margin-right: 8px">mdi-skip-previous-outline</v-icon>
            Back
        </v-btn>
    </div>
    <v-snackbar
        v-model="snackbar"
        :color="snackColor"
        style="position: fixed; top: 6px; z-index: 30"
        top
        content-class="snackbar-content"
        timeout="2500">
        {{ snackText }}
    </v-snackbar>
</div>
</template>

<script>
import S3Utils from '../../utils/s3-utils';
import { downloadFile } from '../../utils/file-utils';
import { refreshAWSCredentials } from '../../utils/aws-cognito-auth'
import { sendContactMessage } from '../../contact';
export default {
    name: 'ResultStep',
    props: {
        s3_utils: {
            type: S3Utils,
            required: true,
        },
        user_info: {
            type: Object,
            required: true,
        },
        pricing_response_data: {
            type: Object,
            required: false,
        },
        original_file_name: {
            type: String,
            required: true,
        },
    },
    data() {
        const miniTimestamp = Date.now().toString().slice(-7, -3)
        let fileName = this.original_file_name.split('.').join(`_priced_${miniTimestamp}.`);
        if (this.original_file_name.split('.').length > 2) {
            const dotIndex = this.original_file_name.lastIndexOf('.');
            const name = this.original_file_name.substring(0, dotIndex);
            const extension = this.original_file_name.substring(dotIndex);
            fileName = `${name}${'_priced'}_${miniTimestamp}${extension}`;
        }
        return {
            successCount: this.pricing_response_data.lanesPriced,
            laneCount: this.pricing_response_data.lanesGiven,
            totalErrorCount: Object.values(this.pricing_response_data.errors).reduce((sum, val) => sum + val, 0),
            pricingErrors: this.pricing_response_data.errors,
            pricedFile: null,
            pricedFileName: this.splitFilePath(this.pricing_response_data.pricedFilename),
            fileExtension: this.pricing_response_data.pricedFilename.split('.').pop(),
            showFeedbackForm: true,
            feedbackSuccess: false,
            feedback: '',
            snackbar: false,
            snackText: '',
            snackColor: 'green',
            enableDownload: false,
            strokeDasharray: null,
            resultFileName: fileName,
        };
    },
    watch: {
        successCount(){
            this.getStrokeDasharray()
        },
        laneCount(){
            this.getStrokeDasharray()
        }
    },
    methods: {
        downloadPricedFile() {
            downloadFile(this.pricedFile, this.resultFileName);
            this.showFeedbackForm = true;
        },
        getStrokeDasharray() {
            const percentage = (this.successCount / this.laneCount) * 100;
            const circumference = 100;
            const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
            this.strokeDasharray = strokeDasharray;
        },
        splitFilePath(filePath) {
            return filePath.split('/').pop();
        },
        sendFeedbackEmail() {
            // only send email if form validation passes
            if (this.validateForm()) {
                const body = {
                    name: '[FreightGPT][RFPFeedback] ' + this.user_info?.name,
                    company: '',
                    email: this.user_info?.email,
                    comment: this.sanitizeString(this.feedback)
                };
                try {
                    sendContactMessage(body);
                    this.snackText = 'Your feedback has been sent.';
                    this.snackColor = 'green';
                    this.snackbar = true;
                    this.feedbackSuccess = true;
                    this.helpMenu = false;
                } catch {
                    console.log('Error sending feedback email, please try again');
                }
            } else {
                this.snackText = 'Invalid message.';
                this.snackColor = 'red';
                this.snackbar = true;
            }
        },

        validateForm() {
            const contactForm = this.$refs.contactForm;
            return contactForm.validate();
        },

        sanitizeString(text) {
            let pattern = /[<>{}()"'`;/\\]/g;
            if (text) {
                return text.replace(pattern, '');
            } else {
                return text;
            }
        },

    },
    beforeMount() {
        refreshAWSCredentials(this.user_id_token).then(() => {
            this.s3_utils.readFileFromS3(process.env.VUE_APP_RFP_BUCKET_NAME, this.pricing_response_data.pricedFilename).then(
                (response) => {
                    this.pricedFile = response;
                    this.enableDownload = true;
                }
            );
        }).catch(() => {
            console.error('Error refreshing credentials');
        });
    },
    mounted() {
        this.getStrokeDasharray()
    },
};
</script>

<style scoped>

.circle-bg {
    fill: none;
    stroke: #f1f1f1;
    stroke-width: 1;
}

.circle {
    fill: none;
    stroke-width: 1;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s;
}

.percentage {
    text-anchor: middle;
    color: #C4C3C3;
    font-weight: 400;
}
.result-header {
    display: flex;
    margin-top: min(20px, 2%);
    margin-bottom: min(20px, 2%);
    font-size: 18px;
    font-weight: 700;
}
.partition-break {
    height: 1px;
    background-color: #9f9f9f;
    margin: min(30px, 2%) 0px min(30px, 2%) 0px;
}
.successfull-price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 400;
    width: 65%;
    background-color: rgba(230, 230, 230, 0.04);
    padding: 8px 20px;
    font-size: 18px;
    border-radius: 12px;
}
.failed-price-container {
    font-size: 18px !important;
    font-weight: 400 !important;
}
.download-btn {
    text-transform: none;
    background-color: transparent !important;
    border: 1px solid #1dd3c0;
    font-size: 15px;
    font-weight: 700;
    border-radius: 6px;
    padding: 20px !important;
    width: 200px;
}
.download-btn:disabled {
    border: 1px solid #4f4f4f;
    color: #4f4f4f;
}
.nav-btn{
    text-transform: none;
    background-color: transparent;
    border: 1px solid #ffc456;
    margin-right: 15px;
    border-radius: 6px;
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}
.custom-table td:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.custom-table td:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.custom-table tr:nth-child(even) {
    background-color: transparent;
}
.custom-table tr:nth-child(odd) {
    background-color: rgba(230, 230, 230, 0.04);
}
.custom-table td {
    padding: 8px;
}
.xlsx-icon-wrapper {
    margin: 8px;
}

::v-deep .snackbar-content {
    text-align: center;
}

.info-tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.info-tooltip .info-tooltip-text {
    visibility: hidden;
    width: max-content;
    background-color: #333333;
    color: #dddddd;
    text-align: center;
    border-radius: 6px;
    padding: 6px 12px;
    position: absolute;
    z-index: 7;
    bottom: 125%; /* Position the tooltip above the text */
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
}

.info-tooltip:hover .info-tooltip-text {
    visibility: visible;
    opacity: 1;
}

</style>
