import axios from 'axios';
import aws4 from 'aws4';

export default function validateEmail(email: string) {
    const request = {
        host: 'f0hf1c8sa7.execute-api.us-east-1.amazonaws.com',
        method: 'POST',
        url: 'https://f0hf1c8sa7.execute-api.us-east-1.amazonaws.com/beta/waitlist',
        data: {
            email: email
        }
    };

    const signedRequest = aws4.sign(request, {
        // assumes user has authenticated and we have called
        // AWS.config.credentials.get to retrieve keys and
        // session tokens
        secretAccessKey: '',
        accessKeyId: ''
    }) as any;

    delete signedRequest.headers['Host'];
    delete signedRequest.headers['Content-Length'];
    const response = axios(signedRequest)
        .then((result) => {
            return result.data.isvalid;
        })
        .catch((error) => {
            console.log(`[ERROR] axios: ${error}`);
        });
    return response;
}
