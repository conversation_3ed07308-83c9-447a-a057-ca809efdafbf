from common import init_main

PROJECT_ROOT = init_main.initialize()

import pprint
import subprocess

from colorama import Fore
from colorama import Style
from constants import IMAGE_METADATA, INTEGRATION_TESTS
from InquirerPy import inquirer, prompt
import configparser
import pytest
import os


from deploy.track_deployment import track_deployment
from deploy.roll_ecs_service import roll_service

AWS_ECR_LOGIN_CMD = "aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 903881895532.dkr.ecr.us-east-1.amazonaws.com"
DOCKER_BUILD_CMD = (
    "docker build{build_args} --no-cache -t {ecr_repo_name} -f {docker_file_path} ."
)
DOCKER_TAG_CMD = "docker tag {ecr_repo_name}:latest 903881895532.dkr.ecr.us-east-1.amazonaws.com/{ecr_repo_name}:latest"
DOCKER_PUSH_CMD = (
    "docker push 903881895532.dkr.ecr.us-east-1.amazonaws.com/{ecr_repo_name}:latest"
)

COMMON_IMAGE_BUILD_CMD = (
    "docker build --no-cache -t {image_name}:latest -f common/{docker_filename} ."
)


def read_marker_names_from_config(file_path: str):
    """Reads all marker names from a pytest.ini file."""
    config = configparser.ConfigParser()
    config.read(file_path)

    # Access the 'markers' from the 'pytest' section
    markers = config.get("pytest", "markers")

    # Extract marker names
    marker_list = [marker.strip() for marker in markers.strip().split("\n")]
    marker_names = [
        marker.split(":")[0].strip() for marker in marker_list if ":" in marker
    ]

    return marker_names


def execute_integration_tests() -> None:
    """Executes integration tests for deploy - allows user to select test and test groups to run."""

    # Confirm running tests
    run_tests = inquirer.confirm(
        message="Run available integration tests for this deploy?"
    ).execute()
    if not run_tests:
        print(
            f"{Fore.YELLOW}Warning: not running integration tests...{Style.RESET_ALL}"
        )
        return

    # Select integration test to run.
    integration_test = inquirer.select(
        message="Select test:",
        choices=INTEGRATION_TESTS,
    ).execute()

    # Read pytest markers and allow user to select test groups.
    available_groups = read_marker_names_from_config(
        os.path.join(integration_test["source_dir"], "pytest.ini"),
    )

    questions = [
        {
            "type": "checkbox",
            "message": "Select specific test groups to run (SPACE to select, ENTER to continue).",
            "choices": [
                {
                    "name": group,
                    "value": group,
                    "enabled": group in integration_test.get("default_groups", []),
                }
                for group in available_groups
            ],
            "validate": lambda result: len(result) > 0
            or "You must choose at least one option.",
        }
    ]

    results = prompt(questions)
    test_groups_to_run = " or ".join(results[0])

    print(
        f"{Fore.CYAN}Running {integration_test['name']} integration tests with group markers {test_groups_to_run}{Style.RESET_ALL}"
    )

    # Run integration tests.
    pytest_args = [
        "-s",
        os.path.join(integration_test["source_dir"], integration_test["test_filename"]),
        "-m",
        test_groups_to_run,
    ]
    test_result = pytest.main(pytest_args)

    # Check result. User can continue even on failed test, though it is recommended that they do not.
    if test_result != pytest.ExitCode.OK:
        print(f"{Fore.RED}Warning: integration tests failed.{Style.RESET_ALL}")
        continue_deploy = inquirer.confirm(
            message="Integration tests failed. Do you want to continue with this deploy regardless?"
        ).execute()

        if not continue_deploy:
            exit()


def execute_deployment_commands(
    build_args: str,
    ecr_repo_name: str,
    docker_file_path: str,
    env: str,
    is_lambda: bool,
) -> None:
    """
    Executed command necessary for deploying an image to the Amazon Elastic Container Registry (ECR) specified

    Args:
        build_args (str): arguments to provide in docker build command
        ecr_repo_name (str): name of ECR
        docker_file_path (str): path to docker file
        env (str): environment to deploy to
        is_lambda (bool): builds common image for lambda if set to true
    """
    # Build common image
    if is_lambda:
        common_image_dockerfile_cmd = COMMON_IMAGE_BUILD_CMD.format(
            image_name="common-lambda", docker_filename="Dockerfile_lambda"
        )
    else:
        common_image_dockerfile_cmd = COMMON_IMAGE_BUILD_CMD.format(
            image_name="common", docker_filename="Dockerfile"
        )

    print(f"\nExecuting: {Fore.BLUE}{common_image_dockerfile_cmd}{Style.RESET_ALL}")
    subprocess.run(common_image_dockerfile_cmd, shell=True, check=True)

    # Login to AWS
    print(f"\nExecuting: {Fore.BLUE}{AWS_ECR_LOGIN_CMD}{Style.RESET_ALL}")
    subprocess.run(AWS_ECR_LOGIN_CMD, shell=True, check=True)

    # Build image
    formatted_build_cmd = DOCKER_BUILD_CMD.format(
        build_args=build_args,
        ecr_repo_name=ecr_repo_name,
        docker_file_path=docker_file_path,
    )
    print(f"\nExecuting: {Fore.BLUE}{formatted_build_cmd}{Style.RESET_ALL}")
    subprocess.run(formatted_build_cmd, shell=True, check=True)

    # Tag image
    formatted_tag_cmd = DOCKER_TAG_CMD.format(ecr_repo_name=ecr_repo_name)
    print(f"\nExecuting: {Fore.BLUE}{formatted_tag_cmd}{Style.RESET_ALL}")
    subprocess.run(formatted_tag_cmd, shell=True, check=True)

    # Push image
    formatted_push_cmd = DOCKER_PUSH_CMD.format(ecr_repo_name=ecr_repo_name)
    print(f"\nExecuting: {Fore.BLUE}{formatted_push_cmd}{Style.RESET_ALL}")
    subprocess.run(formatted_push_cmd, shell=True, check=True)

    # Tracker
    is_prod = env == "prod"
    track_deployment(ecr_repo_name=ecr_repo_name, is_prod=is_prod)


def deploy_image() -> None:
    """
    Collects information needed in order to deploy image to Amazon Elastic Container Registry (ECR)
    """

    # Run integration tests before build and deploy
    execute_integration_tests()

    # Get list of possible image type to deploy
    image_types_list = list(IMAGE_METADATA.keys())

    # Get image type selected by script executor
    image_type = inquirer.fuzzy(
        message="Select job type to deploy:", choices=image_types_list
    ).execute()

    # Get envs for image type selected
    image_type_envs = IMAGE_METADATA.get(image_type, None)

    # If no environments are found then exit early
    if not image_type_envs:
        print(
            f"{Fore.RED}No environments found for selected job type. Exiting...{Style.RESET_ALL}"
        )
        exit()

    # Get env selected by user
    image_envs_list = [
        k for k in image_type_envs.keys() if k in ["test", "dev", "prod"]
    ]
    env = inquirer.select(
        message="Select environment:", choices=image_envs_list
    ).execute()

    # Get image details for selected image type and env
    image_details = image_type_envs.get(env)

    # Extract required parameters
    ecr_repo_name = image_details.get("ecr_repo_name", False)
    docker_file_path = image_details.get("docker_file_path", False)

    # Check to make sure required fields are included in metadata
    if not ecr_repo_name or not docker_file_path:
        print(
            f"{Fore.RED}Required field is missing from metadata. Exiting...{Style.RESET_ALL}"
        )
        exit()

    # Formate docker build arguments if provided
    build_args_list = image_details.get("build_args", [])
    build_args = ""
    if len(build_args_list) > 0:
        for arg in build_args_list:
            build_args += f" --build-arg {arg}"

    # Print and confirm deployment information
    pp = pprint.PrettyPrinter(indent=4)
    print(Fore.GREEN)
    pp.pprint(image_details)
    print(Style.RESET_ALL)
    confirm_deployment = inquirer.confirm(message="Confirm deployment:").execute()

    if not confirm_deployment:
        print(f"{Fore.RED}Deployment aborted. Exiting...{Style.RESET_ALL}")
        exit()

    # Run deployment commands
    execute_deployment_commands(
        build_args=build_args,
        ecr_repo_name=ecr_repo_name,
        docker_file_path=docker_file_path,
        env=env,
        is_lambda=image_type_envs.get("type") == "lambda",
    )

    # Roll ECS Nodes
    # TODO (P1): Add support for all ECS service types, not just freightgpt.
    if image_type_envs.get("type") == "ecs":
        roll_nodes = inquirer.confirm(
            message=f"Deployment complete! Would you like to roll the ECS cluster for env {env}? Note: we only support freightgpt cluster rolls currently."
        ).execute()
        if not roll_nodes:
            exit()

        roll_service(env)


if __name__ == "__main__":
    deploy_image()
