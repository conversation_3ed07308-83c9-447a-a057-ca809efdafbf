#!/bin/bash

# Deploy to AWS Lambda with Container Images
# Note: Lambda has limitations for file uploads and processing times

set -e

# Configuration
FUNCTION_NAME="broker-portal-serverless"
REGION="ap-south-1"
ACCOUNT_ID="************"
ECR_REPO="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${FUNCTION_NAME}"

echo "🚀 Deploying Broker Portal to AWS Lambda"
echo "========================================"

# Step 1: Build Lambda container image
echo "📦 Building Lambda container image..."
docker build -f Dockerfile.lambda -t ${FUNCTION_NAME}:lambda .

echo "🔐 Logging in to ECR..."
aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin ${ECR_REPO}

# Create ECR repository if it doesn't exist
echo "📋 Creating ECR repository..."
aws ecr create-repository --repository-name ${FUNCTION_NAME} --region ${REGION} || echo "Repository already exists"

echo "🏷️  Tagging and pushing Lambda image..."
docker tag ${FUNCTION_NAME}:lambda ${ECR_REPO}:lambda
docker push ${ECR_REPO}:lambda

# Step 2: Create or update Lambda function
echo "⚡ Creating Lambda function..."

# Check if function exists
if aws lambda get-function --function-name ${FUNCTION_NAME} --region ${REGION} >/dev/null 2>&1; then
    echo "📝 Updating existing Lambda function..."
    aws lambda update-function-code \
        --function-name ${FUNCTION_NAME} \
        --image-uri ${ECR_REPO}:lambda \
        --region ${REGION}
else
    echo "🆕 Creating new Lambda function..."
    aws lambda create-function \
        --function-name ${FUNCTION_NAME} \
        --package-type Image \
        --code ImageUri=${ECR_REPO}:lambda \
        --role arn:aws:iam::${ACCOUNT_ID}:role/broker-portal-lambda-role \
        --timeout 900 \
        --memory-size 3008 \
        --environment Variables='{
            "AWS_DEFAULT_REGION":"'${REGION}'",
            "BUCKET_NAME":"broker-portal-************-dev",
            "DYNAMODB_JOBS_TABLE":"BrokerPortalJobs-dev",
            "DYNAMODB_BROKERS_TABLE":"BrokerPortalBrokers-dev",
            "API_TITLE":"Broker Portal Serverless API",
            "API_VERSION":"1.0.0"
        }' \
        --region ${REGION}
fi

# Step 3: Create API Gateway (optional)
echo "🌐 Setting up API Gateway..."

# Create API Gateway
API_ID=$(aws apigatewayv2 create-api \
    --name ${FUNCTION_NAME}-api \
    --protocol-type HTTP \
    --target arn:aws:lambda:${REGION}:${ACCOUNT_ID}:function:${FUNCTION_NAME} \
    --region ${REGION} \
    --query 'ApiId' --output text 2>/dev/null || \
    aws apigatewayv2 get-apis \
    --query "Items[?Name=='${FUNCTION_NAME}-api'].ApiId" \
    --output text --region ${REGION})

# Add Lambda permission for API Gateway
aws lambda add-permission \
    --function-name ${FUNCTION_NAME} \
    --statement-id apigateway-invoke \
    --action lambda:InvokeFunction \
    --principal apigateway.amazonaws.com \
    --source-arn "arn:aws:execute-api:${REGION}:${ACCOUNT_ID}:${API_ID}/*/*" \
    --region ${REGION} 2>/dev/null || echo "Permission already exists"

echo "✅ Lambda function deployed successfully!"
echo "🌐 API Gateway URL: https://${API_ID}.execute-api.${REGION}.amazonaws.com/"
echo "⚠️  Note: Lambda has 15-minute timeout and 10GB memory limits"
echo "📊 Check the AWS Console for function details"

echo "🎉 Deployment complete!"
