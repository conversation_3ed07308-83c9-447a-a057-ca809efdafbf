IMAGE_METADATA = {
    "transparency_backend_lazy": {
        "dev": {
            "ecr_repo_name": "shipment_request_handler_lazy_dev",
            "docker_file_path": "backend/transparency/lambda/shipment_request_handler_lazy/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "shipment_request_handler_lazy",
            "docker_file_path": "backend/transparency/lambda/shipment_request_handler_lazy/Dockerfile",
        },
        "type": "lambda",
    },
    "transparency_account_handler": {
        "dev": {
            "ecr_repo_name": "account_handler_dev",
            "docker_file_path": "backend/transparency/lambda/account_request_handler/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "account_handler",
            "docker_file_path": "backend/transparency/lambda/account_request_handler/Dockerfile",
        },
    },
    "transparency_cache": {
        "dev": {
            "ecr_repo_name": "transparency_cache_dev",
            "docker_file_path": "backend/transparency/lambda/shipment_request_handler_lazy/Dockerfile_cache",
        },
        "prod": {
            "ecr_repo_name": "transparency_cache",
            "docker_file_path": "backend/transparency/lambda/shipment_request_handler_lazy/Dockerfile_cache",
        },
        "type": "lambda",
    },
    "ingestion": {
        "dev": {
            "ecr_repo_name": "ingestion_dev",
            "docker_file_path": "ingestion/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "ingestion",
            "docker_file_path": "ingestion/Dockerfile",
        },
        "type": "ecs",
    },
    "notification": {
        "dev": {
            "ecr_repo_name": "notification_handler_dev",
            "docker_file_path": "backend/transparency/lambda/notification_request_handler/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "notification_handler",
            "docker_file_path": "backend/transparency/lambda/notification_request_handler/Dockerfile",
        },
        "type": "lambda",
    },
    "high_margin": {
        "dev": {
            "ecr_repo_name": "high_margin_notifier_dev",
            "docker_file_path": "lambdas/notifications/high_margin/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "high_margin_notifier",
            "docker_file_path": "lambdas/notifications/high_margin/Dockerfile",
        },
        "type": "ecs",
    },
    "late_customer_directs": {
        "dev": {
            "ecr_repo_name": "late_customer_direct_notifier_dev",
            "docker_file_path": "lambdas/notifications/customer_direct/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "late_customer_direct_notifier",
            "docker_file_path": "lambdas/notifications/customer_direct/Dockerfile",
        },
        "type": "ecs",
    },
    "low_margin": {
        "dev": {
            "ecr_repo_name": "low_margin_notifier_dev",
            "docker_file_path": "lambdas/notifications/low_margin/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "low_margin_notifier",
            "docker_file_path": "lambdas/notifications/low_margin/Dockerfile",
        },
    },
    "user_analytics": {
        "dev": {
            "ecr_repo_name": "user_analytics",
            "docker_file_path": "lambdas/user_analytics/lambda/Dockerfile",
        },
        "type": "lambda",
    },
    "daily_analytics_report": {
        "dev": {
            "ecr_repo_name": "daily_analytics_report",
            "docker_file_path": "lambdas/user_analytics/jobs/daily_user_report/Dockerfile",
        },
        "type": "ecs",
    },
    "demo_data_refresh": {
        "dev": {
            "ecr_repo_name": "demo_data_refresh_dev",
            "docker_file_path": "lambdas/demo_data_refresh/Dockerfile_dev",
        },
        "prod": {
            "ecr_repo_name": "demo_data_refresh",
            "docker_file_path": "lambdas/demo_data_refresh/Dockerfile",
        },
        "type": "ecs",
    },
    "waitlist_validator": {
        "prod": {
            "ecr_repo_name": "waitlist_validator",
            "docker_file_path": "backend/freightgpt/lambda/waitlist_validator/Dockerfile",
        },
        "type": "lambda",
    },
    "post_signup_lambda": {
        "prod": {
            "ecr_repo_name": "post_signup",
            "docker_file_path": "backend/freightgpt/lambda/post_sign_up/Dockerfile",
            "build_args": ["FS_TYPE=RO"],
        },
        "type": "lambda",
    },
    "add_mailchimp_member": {
        "dev": {
            "ecr_repo_name": "add_mailchimp_member_dev",
            "docker_file_path": "backend/freightgpt/lambda/add_mailchimp_member/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "add_mailchimp_member",
            "docker_file_path": "backend/freightgpt/lambda/add_mailchimp_member/Dockerfile",
        },
        "type": "lambda",
    },
    "conversation_lambda": {
        "dev": {
            "ecr_repo_name": "freightgpt_conversations_dev",
            "docker_file_path": "backend/freightgpt/lambda/conversations/Dockerfile",
            "build_args": ["ENV_VALUE=DEV"],
        },
        "prod": {
            "ecr_repo_name": "freightgpt_conversations",
            "docker_file_path": "backend/freightgpt/lambda/conversations/Dockerfile",
            "build_args": ["ENV_VALUE=PROD"],
        },
        "type": "lambda",
    },
    "cpm_pull": {
        "dev": {
            "ecr_repo_name": "freightgpt_cpm_pull_dev",
            "docker_file_path": "backend/freightgpt/jobs/cpm_pull/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "freightgpt_cpm_pull",
            "docker_file_path": "backend/freightgpt/jobs/cpm_pull/Dockerfile",
        },
        "type": "ecs",
    },
    "session_cleaner": {
        "dev": {
            "ecr_repo_name": "session_cleaner_dev",
            "docker_file_path": "backend/freightgpt/jobs/clean_session/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "session_cleaner",
            "docker_file_path": "backend/freightgpt/jobs/clean_session/Dockerfile",
        },
        "type": "ecs",
    },
    "daily_cpm": {
        "prod": {
            "ecr_repo_name": "daily_cpm",
            "docker_file_path": "machine_learning/price_recommendation/daily_cpm/Dockerfile",
        },
        "type": "ecs",
    },
    "freightgpt": {
        "dev": {
            "ecr_repo_name": "freightgpt_dev",
            "docker_file_path": "backend/freightgpt/main_server/Dockerfile",
            "build_args": ["ENV_VALUE=DEV"],
        },
        "prod": {
            "ecr_repo_name": "freightgpt",
            "docker_file_path": "backend/freightgpt/main_server/Dockerfile",
            "build_args": ["ENV_VALUE=PROD"],
        },
        "type": "ecs",
    },
    "ml_pricer": {
        "dev": {
            "ecr_repo_name": "ml_pricer_dev",
            "docker_file_path": "machine_learning/price_recommendation/transparency_pricer/Dockerfile",
        },
        "prod": {
            "ecr_repo_name": "ml_pricer",
            "docker_file_path": "machine_learning/price_recommendation/transparency_pricer/Dockerfile",
        },
        "type": "ecs",
    },
}

INTEGRATION_TESTS = [
    {
        "name": "freightgpt",
        "value": {
            "name": "freightgpt",
            "source_dir": "backend/freightgpt/tests/",
            "test_filename": "integration_test_wet.py",
            "default_groups": ["basic_wet"],
        },
    }
]
