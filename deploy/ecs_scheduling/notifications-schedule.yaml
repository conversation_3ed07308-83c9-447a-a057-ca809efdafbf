AWSTemplateFormatVersion: "2010-09-09"
Description: Notification cluster and service tasks.
Parameters:
  VPCID:
    Description: Id of existing VPC
    Type: "AWS::EC2::VPC::Id"
    ConstraintDescription: Must be a valid VPC.

Resources:
  # Cluster
  MyServiceECSCluster:
    Type: "AWS::ECS::Cluster"
    Properties:
      ClusterName: notification-cluster
      ClusterSettings:
        - Name: containerInsights
          Value: enabled

  # Security Group
  MyServiceFargateSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: ECS Fargate Security Group
      VpcId: !Ref VPCID
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 10.0.0.0/16

  # ----------------------------------------------------------------------------------
  # high_margin_notifier - High margin notifier.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: high_margin_scheduled_run
      Description: "High margin notifier."
      State: ENABLED
      ScheduleExpression: cron(0 16 ? * MON-FRI *)
      Targets:
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: high_margin_notifier
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/high_margin:2
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # late_customer_direct - Late customer direct loads notifier.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: late_customer_direct_scehduled_run
      Description: "Late customer direct notifier."
      State: ENABLED
      ScheduleExpression: cron(0 17 ? * MON-FRI *)
      Targets:
        # Brokers (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: late_customer_direct
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/late_customer_direct:1
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Shippers (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: late_customer_direct_shipper
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/late_customer_direct_shipper:2
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
