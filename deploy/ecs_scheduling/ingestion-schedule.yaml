AWSTemplateFormatVersion: "2010-09-09"
Description: Ingestion cluster and service tasks.
Parameters:
  VPCID:
    Description: Id of existing VPC
    Type: "AWS::EC2::VPC::Id"
    ConstraintDescription: Must be a valid VPC.

Resources:
  # Cluster
  MyServiceECSCluster:
    Type: "AWS::ECS::Cluster"
    Properties:
      ClusterName: ingestion-network-cluster
      ClusterSettings:
        - Name: containerInsights
          Value: enabled

  # Security Group
  MyServiceFargateSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: ECS Fargate Security Group
      VpcId: !Ref VPCID
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 10.0.0.0/16

  # ----------------------------------------------------------------------------------
  # ingest_prod_0000 - Ingest (prod) arrive logistics, coyote logistics, echo global logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule1:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_0000
      Description: "Ingest (prod) arrive logistics, coyote logistics, echo global logistics"
      State: ENABLED
      ScheduleExpression: cron(0 0 * * ? *)
      Targets:
        # Arrive Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: arrive_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_arrive_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Coyote Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: coyote_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_coyote_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Echo Global Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: echo_global_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_echo_global_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0100 - Ingest (dev) prosponsive logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule2:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0100
      Description: "Ingest (dev) prosponsive logistics"
      State: ENABLED
      ScheduleExpression: cron(0 1 * * ? *)
      Targets:
        # Prosponsive Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: prosponsive_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_prosponsive_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0200 - Ingest (dev) royal transportation, schneider brokerage, capstone logistics, trailer bridge, ardent
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule3:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0200
      Description: "Ingest (dev) royal transportation, schneider brokerage, capstone logistics, trailer bridge, ardent"
      State: ENABLED
      ScheduleExpression: cron(0 2 * * ? *)
      Targets:
        # Royal Transportation (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: royal_transportation_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_royal_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Schneider Brokerage (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: schneider_brokerage_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_schneider_brokerage
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Capstone Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: capstone_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_capstone_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Trailer Bridge (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: trailer_bridge_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_trailer_bridge
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Ardent (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ardent_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_ardent
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_prod_0300 - Ingest (prod) mx solutions, pathmark transportation, forsla, loadsmart, schneider brokerage
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule4:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_0300
      Description: "Ingest (prod) mx solutions, pathmark transportation, forsla, loadsmart, schneider brokerage"
      State: ENABLED
      ScheduleExpression: cron(0 3 * * ? *)
      Targets:
        # MX Solutions (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: mx_solutions_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_mx_solutions
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Pathmark Transportation (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: pathmark_transportation_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_pathmark_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Forsla (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: forsla_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_forsla
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Loadsmart (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: loadsmart_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_loadsmart
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Schneider Brokerage (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: schneider_brokerage_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_schneider_brokerage
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0400 - Ingest (dev) mx solutions, ryan transportation, sage freight, sheer transport, uber freight
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule5:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0400
      Description: "Ingest (dev) mx solutions, ryan transportation, sage freight, sheer transport, uber freight"
      State: ENABLED
      ScheduleExpression: cron(0 4 * * ? *)
      Targets:
        # MX Solutions (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: mx_solutions_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_mx_solutions
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Ryan Transportation (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ryan_transportation_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_ryan_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Sage Freight (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: sage_freight_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_sage_freight
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Sheer Transport (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: sheer_transport_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_sheer_transport
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Uber Freight (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: uber_freight_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_uber_freight
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0600 - Ingest (dev) linehaul logistics, molo solutions, nolan transportation group, partnership llc, pathmark transportation
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule6:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0600
      Description: "Ingest (dev) linehaul logistics, molo solutions, nolan transportation group, partnership llc, pathmark transportation"
      State: ENABLED
      ScheduleExpression: cron(0 6 * * ? *)
      Targets:
        # Linehaul Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: linehaul_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_linehaul_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # MoLo Solutions (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: molo_solutions_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_molo_solutions
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Nolan Transportation Group (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: nolan_transportation_group_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_nolan_transportation_group
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # PartnerShip LLC (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: partnership_llc_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_partnership_llc
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Pathmark Transportation (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: pathmark_transportation_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_pathmark_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0700 - Ingest (dev) arrive logistics, bluegrace logistics, coyote logistics, dupre logistics llc, echo global logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule7:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0700
      Description: "Ingest (dev) arrive logistics, bluegrace logistics, coyote logistics, dupre logistics llc, echo global logistics"
      State: ENABLED
      ScheduleExpression: cron(0 7 * * ? *)
      Targets:
        # Arrive Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: arrive_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_arrive_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # BlueGrace Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: bluegrace_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_bluegrace_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Coyote Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: coyote_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_coyote_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Dupre Logistics LLC (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: dupre_logistics_llc_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_dupre_logistics_llc
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Echo Global Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: echo_global_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_echo_global_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0800 - Ingest (dev) vp logistics, its logistics, hill bros, widespread logistics, openroad
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule8:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0800
      Description: "Ingest (dev) vp logistics, its logistics, hill bros, widespread logistics, openroad"
      State: ENABLED
      ScheduleExpression: cron(0 8 * * ? *)
      Targets:
        # VP Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: vp_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_vp_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # ITS Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: its_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_its_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Hill Bros (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: hill_bros_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_hill_bros
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Widespread Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: widespread_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_widespread_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # OpenRoad (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: openroad_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_openroad
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_dev_0900 - Ingest (dev) integrity express logistics, forsla, loadsmart, child logistics, axle logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule9:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_dev_0900
      Description: "Ingest (dev) integrity express logistics, forsla, loadsmart, child logistics, axle logistics"
      State: ENABLED
      ScheduleExpression: cron(0 9 * * ? *)
      Targets:
        # Integrity Express Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: integrity_express_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_integrity_express_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Forsla (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: forsla_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_forsla
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Loadsmart (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: loadsmart_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_loadsmart
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Child Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: child_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_child_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Axle Logistics (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: axle_logistics_dev_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dev_axle_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_prod_1300 - Ingest (prod) dupre logistics llc, partnership llc, sage freight, axle logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule10:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_1300
      Description: "Ingest (prod) dupre logistics llc, partnership llc, sage freight, axle logistics"
      State: ENABLED
      ScheduleExpression: cron(0 13 * * ? *)
      Targets:
        # Dupre Logistics LLC (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: dupre_logistics_llc_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_dupre_logistics_llc
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # PartnerShip LLC (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: partnership_llc_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_partnership_llc
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Sage Freight (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: sage_freight_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_sage_freight
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Axle Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: axle_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_axle_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_prod_1500 - Ingest (prod) linehaul logistics, sheer transport, royal transportation, trailer bridge
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule11:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_1500
      Description: "Ingest (prod) linehaul logistics, sheer transport, royal transportation, trailer bridge"
      State: ENABLED
      ScheduleExpression: cron(0 15 * * ? *)
      Targets:
        # Linehaul Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: linehaul_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_linehaul_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Sheer Transport (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: sheer_transport_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_sheer_transport
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Royal Transportation (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: royal_transportation_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_royal_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Trailer Bridge (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: trailer_bridge_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_trailer_bridge
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_prod_1700 - Ingest (prod) molo solutions, uber freight, capstone logistics, ardent
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule12:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_1700
      Description: "Ingest (prod) molo solutions, uber freight, capstone logistics, ardent"
      State: ENABLED
      ScheduleExpression: cron(0 17 * * ? *)
      Targets:
        # MoLo Solutions (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: molo_solutions_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_molo_solutions
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Uber Freight (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: uber_freight_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_uber_freight
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Capstone Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: capstone_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_capstone_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Ardent (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ardent_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_ardent
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ingest_prod_2300 - Ingest (prod) bluegrace logistics, integrity express logistics, nolan transportation group, ryan transportation, child logistics
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule13:
    Type: AWS::Events::Rule
    Properties:
      Name: ingest_prod_2300
      Description: "Ingest (prod) bluegrace logistics, integrity express logistics, nolan transportation group, ryan transportation, child logistics"
      State: ENABLED
      ScheduleExpression: cron(0 23 * * ? *)
      Targets:
        # BlueGrace Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: bluegrace_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_bluegrace_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Integrity Express Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: integrity_express_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_integrity_express_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Nolan Transportation Group (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: nolan_transportation_group_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_nolan_transportation_group
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Ryan Transportation (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ryan_transportation_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_ryan_transportation
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
        # Child Logistics (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: child_logistics_ingestion_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ingest_child_logistics
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # scorer_dev - Run scorer (dev) once daily.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule14:
    Type: AWS::Events::Rule
    Properties:
      Name: scorer_dev
      Description: "Run scorer (dev) once daily."
      State: ENABLED
      ScheduleExpression: cron(0 11 * * ? *)
      Targets:
        # Scorer (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: scorer_dev_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/scorer_dev
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # scorer - Run scorer (prod) after ingestion.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule15:
    Type: AWS::Events::Rule
    Properties:
      Name: scorer
      Description: "Run scorer (prod) after ingestion."
      State: ENABLED
      ScheduleExpression: cron(0 5,18 * * ? *)
      Targets:
        # Scorer (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: scorer_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/scorer
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4

  # ----------------------------------------------------------------------------------
  # ml_pricer_dev - Run ml_pricer (dev) once daily.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule16:
    Type: AWS::Events::Rule
    Properties:
      Name: ml_pricer_dev
      Description: "Run ml_pricer (dev) once daily."
      State: ENABLED
      ScheduleExpression: cron(30 10 * * ? *)
      Targets:
        # Ml Pricer (DEV)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ml_pricer_dev_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ml_pricer_dev
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
    
  # ----------------------------------------------------------------------------------
  # ml_pricer - Run ml_pricer (prod) once daily.
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule17:
    Type: AWS::Events::Rule
    Properties:
      Name: ml_pricer
      Description: "Run ml_pricer (prod) once daily."
      State: ENABLED
      ScheduleExpression: cron(30 17 * * ? *)
      Targets:
        # Ml Pricer (PROD)
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: ml_pricer_target
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/ml_pricer
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4
    