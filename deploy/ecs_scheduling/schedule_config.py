from common.constants import BrokerNames

INGESTION_SCHEDULE = {
    "0000": (
        [
            BrokerNames.arrive_logistics,
            BrokerNames.coyote_logistics,
            BrokerNames.echo_global_logistics,
        ],
        "prod",
    ),
    "0100": (
        [
            BrokerNames.prosponsive_logistics,
        ],
        "dev",
    ),
    "0200": (
        [
            BrokerNames.royal_transportation,
            BrokerNames.schneider_brokerage,
            BrokerNames.capstone_logistics,
            BrokerNames.trailer_bridge,
            BrokerNames.ardent,
        ],
        "dev",
    ),
    "0300": (
        [
            BrokerNames.mx_solutions,
            BrokerNames.pathmark_transportation,
            BrokerNames.forsla,
            BrokerNames.loadsmart,
            BrokerNames.schneider_brokerage,
        ],
        "prod",
    ),
    "0400": (
        [
            BrokerNames.mx_solutions,
            BrokerNames.ryan_transportation,
            BrokerNames.sage_freight,
            BrokerNames.sheer_transport,
            BrokerNames.uber_freight,
        ],
        "dev",
    ),
    "0600": (
        [
            BrokerNames.linehaul_logistics,
            BrokerNames.molo_solutions,
            BrokerNames.nolan_transportation_group,
            BrokerNames.partnership_llc,
            BrokerNames.pathmark_transportation,
        ],
        "dev",
    ),
    "0700": (
        [
            BrokerNames.arrive_logistics,
            BrokerNames.bluegrace_logistics,
            BrokerNames.coyote_logistics,
            BrokerNames.dupre_logistics_llc,
            BrokerNames.echo_global_logistics,
        ],
        "dev",
    ),
    "0800": (
        [
            BrokerNames.vp_logistics,
            BrokerNames.its_logistics,
            BrokerNames.hill_bros,
            BrokerNames.widespread_logistics,
            BrokerNames.openroad,
        ],
        "dev",
    ),
    "0900": (
        [
            BrokerNames.integrity_express_logistics,
            # BrokerNames.transfix,
            BrokerNames.forsla,
            BrokerNames.loadsmart,
            BrokerNames.child_logistics,
            BrokerNames.axle_logistics,
        ],
        "dev",
    ),
    "1300": (
        [
            BrokerNames.dupre_logistics_llc,
            BrokerNames.partnership_llc,
            BrokerNames.sage_freight,
            BrokerNames.axle_logistics,
        ],
        "prod",
    ),
    "1500": (
        [
            BrokerNames.linehaul_logistics,
            BrokerNames.sheer_transport,
            BrokerNames.royal_transportation,
            BrokerNames.trailer_bridge,
        ],
        "prod",
    ),
    "1700": (
        [
            BrokerNames.molo_solutions,
            # BrokerNames.transfix,
            BrokerNames.uber_freight,
            BrokerNames.capstone_logistics,
            BrokerNames.ardent,
        ],
        "prod",
    ),
    "2300": (
        [
            BrokerNames.bluegrace_logistics,
            BrokerNames.integrity_express_logistics,
            BrokerNames.nolan_transportation_group,
            BrokerNames.ryan_transportation,
            BrokerNames.child_logistics,
        ],
        "prod",
    ),
}

SCORER_SCHEDULE = {
    "dev": ["1100"],
    "prod": ["0500", "1800"],
}

ML_PRICER_SCHEDULE = {
    "dev": ["1030"],
    "prod": ["1730"],
}

# TODO(P1): Add ML_SCHEDULE
