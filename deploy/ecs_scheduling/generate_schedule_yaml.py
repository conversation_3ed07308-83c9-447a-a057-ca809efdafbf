# Ensure imports work from root level directory.
from common import init_main

PROJECT_ROOT = init_main.initialize()

from deploy.ecs_scheduling.constants import OUTPUT_SCHEDULE_YAML
from deploy.ecs_scheduling.constants import SCHEDULE_RULE_TEMPLATE
from deploy.ecs_scheduling.constants import TASK_DEF_TEMPLATE
from deploy.ecs_scheduling.schedule_config import INGESTION_SCHEDULE
from deploy.ecs_scheduling.schedule_config import SCORER_SCHEDULE
from deploy.ecs_scheduling.schedule_config import ML_PRICER_SCHEDULE
import typing
import os


def convert_to_crontime(time: str) -> typing.Tuple[str, str]:
    """
    Convert a given time string into cron time format.

    Example:
    - Input: "0100"
    - Output: ("0", "1")


    Args:
    - time (str): Time string to be converted.

    Returns:
    - tuple: Converted minute and hour strings.
    """
    return (
        time[3] if time[2:] == "00" else time[2:],
        time[1] if time[0] == "0" else time[:2],
    )


def ingestion_broker_task(
    brokers_name: str, environment: str, time: str, schedule_rule_number: int
) -> str:
    """
    Generate a yaml formatted string for an ingestion broker task.

    Args:
    - brokers_name (str): Name of the broker.
    - environment (str): Environment (e.g., dev or prod).
    - time (str): Scheduled time for the task.
    - schedule_rule_number (int): Rule number for the task.

    Returns:
    - str: YAML formatted string.
    """

    if environment not in ["dev", "prod"]:
        raise ValueError("Environment must be either dev or prod.")
    name = f"ingest_{environment}_{time}"
    description = f"Ingest ({environment}) " + ", ".join(brokers_name).lower()
    minutes, hours = convert_to_crontime(time)
    cron_time = f"cron({minutes} {hours} * * ? *)"
    yaml_string = SCHEDULE_RULE_TEMPLATE.format(
        name=name,
        description=description,
        schedule_rule_number=schedule_rule_number,
        cron_time=cron_time,
    )
    for broker in brokers_name:
        target_id = broker.replace(" ", "_").lower() + (
            "_dev_ingestion_target" if environment == "dev" else "_ingestion_target"
        )
        taskdefinitionarn = (
            (f"ingest_dev_{broker.replace(' ', '_').lower()}")
            if environment == "dev"
            else f"ingest_{broker.replace(' ', '_').lower()}"
        )
        yaml_string += TASK_DEF_TEMPLATE.format(
            name=broker,
            environment=environment.upper(),
            target_id=target_id,
            taskdefinitionarn=taskdefinitionarn,
        )
    return yaml_string


def daily_task(
    task_name: str,
    description: str,
    environment: str,
    time: typing.List[str],
    schedule_rule_number: int,
) -> str:
    """
    Generate a yaml formatted string for an ingestion scorer task.

    Args:
    - task_name (str): Name of the task with underscores.
    - environment (str): Environment (e.g., dev or prod).
    - time (list): Scheduled times for the task.
    - schedule_rule_number (int): Rule number for the task.

    Returns:
    - str: YAML formatted string.
    """
    if environment not in ["dev", "prod"]:
        raise ValueError("Environment must be either dev or prod.")

    # Ensure time is always a list
    if not isinstance(time, list):
        time = [time]

    # Convert each time to its crontime equivalent
    crontimes = [convert_to_crontime(t) for t in time]

    # Extract unique minutes and hours
    unique_minutes = ",".join(sorted({t[0] for t in crontimes}, key=int))
    unique_hours = ",".join(sorted({t[1] for t in crontimes}, key=int))

    # Construct the cron_time string
    cron_time = f"cron({unique_minutes} {unique_hours} * * ? *)"

    name = task_name + "_dev" if environment == "dev" else task_name
    target_id = name + "_target"
    taskdefinitionarn = f"{name}"
    yaml_string = SCHEDULE_RULE_TEMPLATE.format(
        name=name,
        description=description,
        schedule_rule_number=schedule_rule_number,
        cron_time=cron_time,
    )
    yaml_string += TASK_DEF_TEMPLATE.format(
        name=" ".join(task_name.split("_")).title(),
        environment=environment.upper(),
        target_id=target_id,
        taskdefinitionarn=taskdefinitionarn,
    )
    return yaml_string


def ingestion_scorer_task(
    environment: str, times: typing.List, schedule_rule_number: int
) -> str:
    description = f"Run scorer ({environment}) " + (
        "once daily." if environment == "dev" else "after ingestion."
    )
    return daily_task("scorer", description, environment, times, schedule_rule_number)


def ml_pricer_task(
    environment: str, times: typing.List, schedule_rule_number: int
) -> str:
    description = f"Run ml_pricer ({environment}) once daily."
    return daily_task(
        "ml_pricer", description, environment, times, schedule_rule_number
    )


if __name__ == "__main__":
    schedule_rule_number = 1
    for key, value in INGESTION_SCHEDULE.items():
        OUTPUT_SCHEDULE_YAML += (
            ingestion_broker_task(value[0], value[1], key, schedule_rule_number) + "\n"
        )
        schedule_rule_number += 1
    for key, value in SCORER_SCHEDULE.items():
        OUTPUT_SCHEDULE_YAML += (
            ingestion_scorer_task(key, value, schedule_rule_number) + "\n"
        )
        schedule_rule_number += 1
    for key, value in ML_PRICER_SCHEDULE.items():
        OUTPUT_SCHEDULE_YAML += ml_pricer_task(key, value, schedule_rule_number)
        OUTPUT_SCHEDULE_YAML += """
    """
        schedule_rule_number += 1

    file = open(
        os.path.join(
            PROJECT_ROOT, "deploy", "ecs_scheduling", "ingestion-schedule.yaml"
        ),
        "w",
    )
    file.writelines(OUTPUT_SCHEDULE_YAML)
    print("Wrote ingestion-schedule.yaml")
    file.close()

# Command to generate ingestion-schedule.yaml:
# python3 deploy/ecs-scheduling/generate_schedule_yaml.py
