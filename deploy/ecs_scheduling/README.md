

# Deploy new ingestion schedule

You may need to add/remove/modify broker ingestion jobs

To update the ingestion schedule:

1. Modify `schedule_config.py`
2. Run `python generate_schedule_yaml.py`
3. This generates a new ingestion schedule under `ingestion-schedule.yaml`. Inspect the diff using GitHub.
4. Navigate to AWS CloudFormation > ingestion-cluster > Update ([quick link](https://us-east-1.console.aws.amazon.com/cloudformation/home?region=us-east-1#/stacks/update?stackId=arn%3Aaws%3Acloudformation%3Aus-east-1%3A903881895532%3Astack%2Fingestion-cluster%2F5d875c10-f0f1-11ed-8efe-0a2bc878981f))
5. Click "Replace current template"
6. Click "Upload a template file" and upload the newly generated ingestion-schedule.yaml
7. Select `vpc-08d44cdb40947c9b2 (10.0.0.0/16)`
8. Review diffs and submit

**Note**

If resources already exist, need to delete one time to tie in with the stack deployment, and never need to do it again.

For development purposes, change key to create new resource
