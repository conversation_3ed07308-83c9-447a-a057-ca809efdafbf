OUTPUT_SCHEDULE_YAML = """AWSTemplateFormatVersion: "2010-09-09"
Description: Ingestion cluster and service tasks.
Parameters:
  VPCID:
    Description: Id of existing VPC
    Type: "AWS::EC2::VPC::Id"
    ConstraintDescription: Must be a valid VPC.

Resources:
  # Cluster
  MyServiceECSCluster:
    Type: "AWS::ECS::Cluster"
    Properties:
      ClusterName: ingestion-network-cluster
      ClusterSettings:
        - Name: containerInsights
          Value: enabled

  # Security Group
  MyServiceFargateSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: ECS Fargate Security Group
      VpcId: !Ref VPCID
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 10.0.0.0/16
"""

SCHEDULE_RULE_TEMPLATE = """
  # ----------------------------------------------------------------------------------
  # {name} - {description}
  # ----------------------------------------------------------------------------------
  MyServiceScheduleRule{schedule_rule_number}:
    Type: AWS::Events::Rule
    Properties:
      Name: {name}
      Description: "{description}"
      State: ENABLED
      ScheduleExpression: {cron_time}
      Targets:"""

TASK_DEF_TEMPLATE = """
        # {name} ({environment})
        - Arn: !GetAtt MyServiceECSCluster.Arn
          Id: {target_id}
          RoleArn: arn:aws:iam::903881895532:role/ecsEventsRole
          EcsParameters:
            TaskDefinitionArn: arn:aws:ecs:us-east-1:903881895532:task-definition/{taskdefinitionarn}
            LaunchType: FARGATE
            PlatformVersion: LATEST
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                SecurityGroups:
                  - !Ref MyServiceFargateSecurityGroup
                Subnets:
                  - subnet-056a3636ed1dbb880
                  - subnet-0817f4f1c54f433e4"""
