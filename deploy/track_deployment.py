# Tracks deployment information
# Deployment tracker sheet: https://docs.google.com/spreadsheets/d/1dlc5LjsARW7VC05xLxHQmFnfa0NSJMYMpoKMqXmlBzA/edit#gid=0
import argparse
import datetime
import getpass

import gitinfo
import pandas as pd

from common import google_sheets_utils

DEPLOYMENT_TRACKER_SHEET_ID = "1dlc5LjsARW7VC05xLxHQmFnfa0NSJMYMpoKMqXmlBzA"
DEPLOYMENT_TRACKER_SHEET_NAME = "Deployments"
GIT_COMMIT_LINK = "https://github.com/truce-logistics/truce/commit/{commit}"
GIT_BRANCH_LINK = "https://github.com/truce-logistics/truce/tree/{branch}"
GSHEETS_URL = "https://docs.google.com/spreadsheets/d/1dlc5LjsARW7VC05xLxHQmFnfa0NSJMYMpoKMqXmlBzA/edit#gid=0"
ECR_URL = "https://us-east-1.console.aws.amazon.com/ecr/repositories/private/903881895532/{ecr_repo_name}?region=us-east-1"


def track_deployment(ecr_repo_name: str, is_prod: bool):
    # Compile local information and git information
    current_git_info = gitinfo.get_git_info()
    deployment_update_data = {
        "Deployment Date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
        "Category": ecr_repo_name.replace("_", " "),
        "Deployment Author": getpass.getuser(),
        "Environment": "PROD" if is_prod else "DEV",
        "Branch": GIT_BRANCH_LINK.format(branch=current_git_info["refs"]),
        "Commit": GIT_COMMIT_LINK.format(commit=current_git_info["commit"]),
        "Commit Message": current_git_info["message"],
        "Commit Author": current_git_info["author"],
        "Commit Date": current_git_info["author_date"],
        "ECR Repository Link": ECR_URL.format(ecr_repo_name=ecr_repo_name)
        if ecr_repo_name
        else "",
    }
    print(f"Tracking deployment information {deployment_update_data}")
    deployment_update_df = pd.DataFrame(deployment_update_data, index=[0])

    # Update tracker sheet
    gsheets_utils = google_sheets_utils.GoogleSheetsUtils()
    gsheets_utils.append_df_to_sheet(
        deployment_update_df,
        DEPLOYMENT_TRACKER_SHEET_ID,
        DEPLOYMENT_TRACKER_SHEET_NAME,
    )

    print(f"Updated sheet {GSHEETS_URL}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--prod",
        action="store_true",
        help="Running prod or dev deploy",
    )

    parser.add_argument(
        "--ecr_repo_name",
        type=str,
        help="ECR repository link for where you are pushing your image",
    )

    args = parser.parse_args()
    track_deployment(args.ecr_repo_name, args.prod)
