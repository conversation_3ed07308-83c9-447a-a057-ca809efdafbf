#!/bin/bash

: Sets up a fresh server to become a load balancing server.
: TODO (P1): alter to update a server.

: Ask for the server URL
echo "Enter the server URL:"
read server_url

: Ask for the PEM file path
echo "Enter the path to your PEM file:"
read pem_path

: Ask for environment
echo "Enter the environment: development or production"
read environment

: SSH into the server and run ls
echo "Testing connectivity"
ssh -i "$pem_path" "$server_url" 'ls'

: Install dependencies
echo "Installing dependencies"
ssh -i "$pem_path" "$server_url" 'sudo apt update && sudo apt upgrade'
ssh -i "$pem_path" "$server_url" 'sudo apt install nodejs npm'
ssh -i "$pem_path" "$server_url" 'npm install -g typescript ts-node pm2'

: SCP project - run from root directory, i.e. ~/truce
echo "Copying project"
scp -i "$pem_path" -r machine_learning/handlers/load_balancer "$server_url":~

: Install project dependencies
echo "Installing dependencies"
ssh -i "$pem_path" "$server_url" 'cd load_balancer && npm install'

: Start server
echo "Starting server"
ssh -i "$pem_path" "$server_url" 'cd load_balancer && pm2 start ecosystem.config.js --env $environment'
