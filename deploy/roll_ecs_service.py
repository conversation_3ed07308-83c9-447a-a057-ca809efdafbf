from common import ecs_task_util
from common import init_main

PROJECT_ROOT = init_main.initialize()

from colorama import Fore
from colorama import Style
from InquirerPy import inquirer
from backend.freightgpt.constants import CloudResources
from backend.freightgpt.constants import FREIGHTGPT_CLUSTER_NAME
import time


def roll_service(env: str) -> None:
    """Rolls an ecs service so that a newly deployed image takes effect."""

    env = env.upper()
    if env == "PROD":
        print(
            f"{Fore.YELLOW}WARNING: YOU ARE ROLLING THE PROD CLUSTER. THIS MAY CAUSE BACKEND CHANGES TO TAKE EFFECT IN PROD.{Style.RESET_ALL}"
        )

    confirm_deployment = inquirer.confirm(message="Confirm service roll:").execute()
    if not confirm_deployment:
        print(f"{Fore.RED}Not performing service roll.{Style.RESET_ALL}")
        exit()

    print(f"{Fore.CYAN}Performing service roll..{Style.RESET_ALL}")
    service_name = getattr(CloudResources, env).value.SERVICE_NAME.value

    # Roll service
    response = ecs_task_util.roll_service(
        cluster_name=FREIGHTGPT_CLUSTER_NAME, service_name=service_name
    )

    time.sleep(10)

    # Force stop tasks to speed up deploy
    ecs_task_util.stop_all_service_tasks(
        cluster_name=FREIGHTGPT_CLUSTER_NAME, service_name=service_name
    )

    if response["ResponseMetadata"]["HTTPStatusCode"] != 200:
        print(
            f"{Fore.RED}SERVICE ROLL {service_name} FAILED. PLEASE CHECK AWS FOR ADDITIONAL INFORMATION.{Style.RESET_ALL}"
        )
        exit()
    else:
        print(f"{Fore.CYAN}SERVICE ROLL {service_name} STARTED.{Style.RESET_ALL}")

    # 20 seconds for the deployment to register
    print(f"{Fore.CYAN}Waiting for roll to complete...{Style.RESET_ALL}")
    time.sleep(20)

    # Wait for roll to complete.
    roll_complete = ecs_task_util.wait_for_running(
        cluster_name=FREIGHTGPT_CLUSTER_NAME, service_name=service_name
    )

    if roll_complete:
        print(f"{Fore.GREEN}SERVICE ROLL COMPLETED.{Style.RESET_ALL}")
    else:
        print(
            f"{Fore.RED}NODE ROLL DID NOT REACH DESIRED STATES IN TIME. PLEASE CHECK AWS FOR ADDITIONAL INFORMATION.{Style.RESET_ALL}"
        )


if __name__ == "__main__":
    roll_service()
