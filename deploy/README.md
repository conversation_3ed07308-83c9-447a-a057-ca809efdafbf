# Scripts to Deploy

**Run all commands from the root directory, i.e.**

Windows: `C:\Users\<USER>\Code\truce> python .\deploy\deploy_image.py`
Unix: `python ./deploy/deploy_image.py`

This folder has both windows and mac/linux script deployments for ingestion and parts of the web backend.

TODO([arnob-sengupta](https://github.com/arnob-sengupta)): Separate into dev/prod directories.

## Dependencies:

-   common: `pip install .`
-   deploy requirements: `pip install -r deploy/requirements.txt`

## Creating new ECS Jobs

Let's say we want to create a new job `roopifier`

1. In your directory, create a Dockerfile. Reference `ingestion/Dockerfile` or `machine_learning/Dockerfile` for reference.
2. Create new [ECR repository](https://us-east-1.console.aws.amazon.com/ecr/repositories), with a \*\_dev version as well.

    1. [ ] `roopifier`
    2. [ ] `roopifier_dev`

3. Add the ECR details for your new job to constants.py as part of the IMAGE_METADATA dictionary. Ensure to include at least ecr_repo_name and docker_file_path as these are required keys. The 'job name' -> 'env' hierarchy is required in order for the deployment script to function properly.

`"roopifier": {
  "dev": {
      "ecr_repo_name": "roopifier_dev",
      "docker_file_path": "some/path/Dockerfile"
  },
  "prod": {
      "ecr_repo_name": "roopifier",
      "docker_file_path": "some/path/Dockerfile"
  }
}`

4. Verify the images are properly deployed to ECR.
5. Create an [ECS task definition](https://us-east-1.console.aws.amazon.com/ecs/v2/task-definitions) pointing to the ECR url. ([Example doc](https://docs.google.com/document/d/1L7VD7n3yWrBWR25oPUY8m3QKV_E-Bgrolh021CB4Yt4/edit).)
6. Add the scheduled runtime to ecs_scheduling, and follow the [README.md](ecs_scheduling/README.md) there.
    1. Make sure Launch type is `FARGATE`
    2. Platform version is `LATEST`
    3. Task role is `ecsTaskExecutionRole`
    4. If it needs to access the internet, turn on `Public IP`.

## Deployment tracker

Deployments are tracked here: `https://docs.google.com/spreadsheets/d/1dlc5LjsARW7VC05xLxHQmFnfa0NSJMYMpoKMqXmlBzA/edit#gid=0`

Deploy tracking is automatically managed by deployment script.

Args are:

-   `--prod`: whether running prod deploy or not
-   `--ecr_repo_name`: the name of the ecr repository you are pushing to.
