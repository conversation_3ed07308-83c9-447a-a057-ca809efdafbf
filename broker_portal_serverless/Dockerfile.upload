FROM public.ecr.aws/lambda/python:3.11

# Create a requirements file that resolves conflicts
COPY broker_portal_serverless/requirements.txt /tmp/broker_requirements.txt
COPY ingestion/requirements.txt /tmp/ingestion_requirements.txt
COPY common/requirements.txt /tmp/common_requirements.txt

# Create a consolidated requirements file
RUN echo "# Broker Portal Serverless requirements" > /tmp/consolidated_requirements.txt && \
    cat /tmp/broker_requirements.txt >> /tmp/consolidated_requirements.txt && \
    echo "# Ingestion requirements" >> /tmp/consolidated_requirements.txt && \
    cat /tmp/ingestion_requirements.txt >> /tmp/consolidated_requirements.txt && \
    echo "# Common requirements" >> /tmp/consolidated_requirements.txt && \
    cat /tmp/common_requirements.txt >> /tmp/consolidated_requirements.txt

# Install dependencies with pip-tools to resolve conflicts
RUN pip install pip-tools && \
    pip-compile --output-file=/tmp/requirements.txt /tmp/consolidated_requirements.txt && \
    pip install -r /tmp/requirements.txt && \
    pip install google-generativeai langchain langchain_community langchain_core

# Copy function code
COPY broker_portal_serverless/handlers/ ${LAMBDA_TASK_ROOT}/handlers/

# Copy ingestion and common modules
COPY ingestion/ ${LAMBDA_TASK_ROOT}/ingestion/
COPY common/ ${LAMBDA_TASK_ROOT}/common/

# Set the PYTHONPATH to include ingestion and common modules
ENV PYTHONPATH="${LAMBDA_TASK_ROOT}"

# Set the CMD to your handler
CMD ["handlers/upload_handler.lambda_handler"]
