"""
Configuration settings for the broker portal backend.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API settings
API_TITLE = "Broker Onboarding Portal API"
API_DESCRIPTION = "API for broker onboarding portal, providing file upload, column mapping, and ingestion capabilities."
API_VERSION = "0.1.0"
API_HOST = "0.0.0.0"
API_PORT = 8000

# AWS Configuration from environment variables
BUCKET_NAME = os.environ.get('BUCKET_NAME')
DYNAMODB_JOBS_TABLE = os.environ.get('DYNAMODB_JOBS_TABLE', 'BrokerPortalJobs-dev')
DYNAMODB_BROKERS_TABLE = os.environ.get('DYNAMODB_BROKERS_TABLE', 'BrokerPortalBrokers-dev')
SQS_QUEUE_URL = os.environ.get('SQS_QUEUE_URL')
AWS_REGION = os.environ.get('AWS_DEFAULT_REGION', 'ap-south-1')

# Validate required environment variables
if not BUCKET_NAME:
    raise ValueError("BUCKET_NAME environment variable is required")
if not SQS_QUEUE_URL:
    raise ValueError("SQS_QUEUE_URL environment variable is required")

# Default settings
DEFAULT_BROKER_NAME = None  # Will be set from the request
DEFAULT_USE_TQDM = False    # No progress bar in API mode
DEFAULT_LOG_LEVEL = "INFO"

# Legacy settings for backward compatibility (will be removed)
PROJECT_ROOT = Path(__file__).parent.parent.parent
UPLOAD_DIR = os.path.join(PROJECT_ROOT, "broker_portal_uploads")
LOG_DIR = os.path.join(PROJECT_ROOT, "broker_portal_logs")
