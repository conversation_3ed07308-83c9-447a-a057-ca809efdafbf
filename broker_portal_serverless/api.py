"""
FastAPI endpoints for the broker portal backend.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware

from .config import (API_DESCRIPTION, API_TITLE, API_VERSION, BUCKET_NAME,
                    DYNAMODB_JOBS_TABLE, SQS_QUEUE_URL)
from .logger import generate_job_id
from .models import (ColumnMappingRequest, ColumnMappingResponse,
                    IngestRequest, IngestResponse, JobStatus,
                    LogResponse, StatusResponse, UploadRequest, UploadResponse)
from .utils import analyze_columns_from_s3, save_upload_file_to_s3, apply_column_mapping, ingest_data
from .aws_clients import get_s3_client, get_dynamodb_resource, get_sqs_client, get_dynamodb_table
from .error_handling import safe_dynamodb_operation, safe_s3_operation, safe_sqs_operation, JobNotFoundError

# Create FastAPI app
app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Broker Portal API is running"}


@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "healthy"}


@app.get("/brokers")
async def list_brokers():
    """
    List all brokers with files and logs from S3.

    Returns:
        List of brokers with file and log counts
    """
    try:
        s3_client = get_s3_client()

        # Get all brokers from uploads directory
        upload_brokers = set()
        try:
            uploads_response = safe_s3_operation(
                s3_client.list_objects_v2,
                Bucket=BUCKET_NAME,
                Prefix="uploads/",
                Delimiter='/'
            )
            for prefix in uploads_response.get('CommonPrefixes', []):
                broker_name = prefix['Prefix'].split('/')[1]
                if broker_name:
                    upload_brokers.add(broker_name)
        except:
            pass

        # Get all brokers from logs directory
        log_brokers = set()
        try:
            logs_response = safe_s3_operation(
                s3_client.list_objects_v2,
                Bucket=BUCKET_NAME,
                Prefix="logs/",
                Delimiter='/'
            )
            for prefix in logs_response.get('CommonPrefixes', []):
                broker_name = prefix['Prefix'].split('/')[1]
                if broker_name:
                    log_brokers.add(broker_name)
        except:
            pass

        # Combine unique brokers
        all_brokers = upload_brokers.union(log_brokers)

        # Get file and log counts for each broker
        brokers = []
        for broker_name in all_brokers:
            # Count files
            try:
                files_response = safe_s3_operation(
                    s3_client.list_objects_v2,
                    Bucket=BUCKET_NAME,
                    Prefix=f"uploads/{broker_name}/",
                    Delimiter='/'
                )
                file_count = len(files_response.get('Contents', []))
            except:
                file_count = 0

            # Count logs
            try:
                logs_response = safe_s3_operation(
                    s3_client.list_objects_v2,
                    Bucket=BUCKET_NAME,
                    Prefix=f"logs/{broker_name}/",
                    Delimiter='/'
                )
                log_count = len([obj for obj in logs_response.get('Contents', []) if obj['Key'].endswith('.log')])
            except:
                log_count = 0

            brokers.append({
                "name": broker_name,
                "file_count": file_count,
                "log_count": log_count
            })

        return {
            "brokers": brokers,
            "bucket_name": BUCKET_NAME
        }

    except Exception as e:
        logging.error(f"Brokers listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/upload", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    broker_name: str = Form(...),
    broker_id: Optional[str] = Form(None),
    analyze: bool = Form(True)
):
    """
    Upload a broker file (CSV, Excel, or ZIP) to S3 and optionally analyze its columns.

    Args:
        file: The file to upload
        broker_name: Name of the broker
        broker_id: ID of the broker (optional)
        analyze: Whether to analyze columns after upload (default: True)

    Returns:
        UploadResponse: Response containing job ID, file details, and column analysis (if requested)
    """
    job_id = generate_job_id()

    try:
        # Save file to S3
        s3_key = await save_upload_file_to_s3(file, job_id, broker_name)
        logging.info(f"File uploaded to S3: s3://{BUCKET_NAME}/{s3_key}")

        # Create job record in DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        job_item = {
            'job_id': job_id,
            'created_at': int(datetime.now().timestamp()),
            'broker_name': broker_name,
            'broker_id': broker_id,
            'filename': file.filename,
            's3_key': s3_key,
            'status': JobStatus.PENDING.value  # Use the enum value
        }

        # Save job to DynamoDB first
        safe_dynamodb_operation(jobs_table.put_item, Item=job_item)

        # Create response object
        response = UploadResponse(
            job_id=job_id,
            broker_name=broker_name,
            broker_id=broker_id,
            filename=file.filename,
            s3_key=s3_key,
            status=JobStatus.PENDING,
            created_at=job_item['created_at']
        )

        # Analyze columns if requested
        if analyze:
            try:
                original_headers, template_headers, sample_data, ai_mapping = await analyze_columns_from_s3(
                    BUCKET_NAME, s3_key, job_id, broker_name
                )
                logging.info(f"Columns analyzed for job {job_id} for broker: {broker_name}")

                # Update job in DynamoDB with analysis results
                safe_dynamodb_operation(
                    jobs_table.update_item,
                    Key={'job_id': job_id, 'created_at': job_item['created_at']},
                    UpdateExpression='SET original_headers = :oh, template_headers = :th, sample_data = :sd, ai_mapping = :am',
                    ExpressionAttributeValues={
                        ':oh': original_headers,
                        ':th': template_headers,
                        ':sd': sample_data,
                        ':am': ai_mapping
                    }
                )

                # Add analysis results to response
                response.original_headers = original_headers
                response.template_headers = template_headers
                response.sample_data = sample_data
                response.ai_mapping = ai_mapping

                # Add hardcoded list of required columns (matching traditional broker_portal)
                response.required_columns = [
                    "origin_zip", "destination_zip", "origin_city", "destination_city",
                    "origin_state", "destination_state", "shipment_mode", "equipment_type",
                    "customer_name", "broker_name", "broker_primary_reference",
                    "cogs_total", "revenue_total", "origin_close"
                ]

            except Exception as e:
                logging.error(f"Error analyzing columns: {e}")
                # Update job status to failed in DynamoDB
                safe_dynamodb_operation(
                    jobs_table.update_item,
                    Key={'job_id': job_id, 'created_at': job_item['created_at']},
                    UpdateExpression='SET #status = :status, error_message = :error',
                    ExpressionAttributeNames={'#status': 'status'},
                    ExpressionAttributeValues={
                        ':status': JobStatus.FAILED.value,
                        ':error': str(e)
                    }
                )
                response.status = JobStatus.FAILED
                # Don't raise an exception here, just return the upload part without analysis

        return response

    except Exception as e:
        logging.error(f"Upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/map-columns", response_model=ColumnMappingResponse)
async def map_columns(request: ColumnMappingRequest):
    """
    Apply column mapping to the uploaded file.

    Args:
        request: Column mapping request

    Returns:
        ColumnMappingResponse: Response containing applied column mapping
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        # Since we have a composite key, we need to scan for the job_id
        response = safe_dynamodb_operation(
            jobs_table.scan,
            FilterExpression='job_id = :job_id',
            ExpressionAttributeValues={':job_id': request.job_id}
        )

        if not response.get('Items'):
            raise HTTPException(status_code=404, detail=f"Job {request.job_id} not found")

        job = response['Items'][0]  # Get the first (and should be only) matching job

        # Get column mapping
        column_mapping = request.column_mapping
        if request.use_ai_mapping and job.get('ai_mapping'):
            # Invert the AI mapping (template headers as keys -> original headers as keys)
            # This is needed because column_mapping is used to rename DataFrame columns,
            # which expects keys to be original column names and values to be new column names
            # Skip null values in the AI mapping
            column_mapping = {v: k for k, v in job['ai_mapping'].items() if v is not None}

        # Update job with column mapping and status to processing
        safe_dynamodb_operation(
            jobs_table.update_item,
            Key={'job_id': job['job_id'], 'created_at': job['created_at']},
            UpdateExpression='SET column_mapping = :mapping, #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':mapping': column_mapping,
                ':status': JobStatus.PROCESSING.value,
                ':updated_at': int(datetime.now().timestamp())
            }
        )

        logging.info(f"Column mapping applied for job {request.job_id}. Status updated to processing.")

        return ColumnMappingResponse(
            job_id=request.job_id,
            column_mapping=column_mapping,
            status=JobStatus.PROCESSING
        )

    except Exception as e:
        logging.error(f"Column mapping failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ingest", response_model=IngestResponse)
async def ingest(request: IngestRequest):
    """
    Ingest the uploaded file with the applied column mapping.

    Args:
        request: Ingestion request

    Returns:
        IngestResponse: Response containing ingestion results
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        # Since we have a composite key, we need to scan for the job_id
        response = safe_dynamodb_operation(
            jobs_table.scan,
            FilterExpression='job_id = :job_id',
            ExpressionAttributeValues={':job_id': request.job_id}
        )

        if not response.get('Items'):
            raise HTTPException(status_code=404, detail=f"Job {request.job_id} not found")

        job = response['Items'][0]  # Get the first (and should be only) matching job

        # Check if column mapping exists
        if 'column_mapping' not in job:
            raise HTTPException(status_code=400, detail="Column mapping not applied")

        # Use broker_name from job if not provided in request
        broker_name = request.broker_name or job.get('broker_name')
        if not broker_name:
            raise HTTPException(status_code=400, detail="Broker name not provided")

        # Update job status to processing
        safe_dynamodb_operation(
            jobs_table.update_item,
            Key={'job_id': job['job_id'], 'created_at': job['created_at']},
            UpdateExpression='SET #status = :status, updated_at = :updated_at, dev_db = :dev_db, dry_run = :dry_run, broker_name = :broker_name',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':status': JobStatus.PROCESSING.value,
                ':updated_at': int(datetime.now().timestamp()),
                ':dev_db': request.dev_db,
                ':dry_run': request.dry_run,
                ':broker_name': broker_name
            }
        )

        # Download file from S3 to temporary location for processing
        s3_client = get_s3_client()
        s3_key = job['s3_key']

        # Create temporary file
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(s3_key)[1]) as tmp_file:
            # Download from S3
            safe_s3_operation(
                s3_client.download_fileobj,
                BUCKET_NAME,
                s3_key,
                tmp_file
            )
            tmp_file_path = tmp_file.name

        try:
            # Ingest data using the traditional method
            results = await ingest_data(
                file_path=tmp_file_path,
                broker_name=broker_name,
                dev_db=request.dev_db,
                dry_run=request.dry_run,
                job_id=request.job_id,
            )
            logging.info(f"Data ingested for job {request.job_id}")

            # Update job status to completed
            safe_dynamodb_operation(
                jobs_table.update_item,
                Key={'job_id': job['job_id'], 'created_at': job['created_at']},
                UpdateExpression='SET #status = :status, total_rows = :total, successful_rows = :success, new_shipments = :new_ships, updated_shipments = :updated_ships, errors = :errors, completed_at = :completed_at',
                ExpressionAttributeNames={'#status': 'status'},
                ExpressionAttributeValues={
                    ':status': JobStatus.COMPLETED.value,
                    ':total': results["total_rows"],
                    ':success': results["successful_rows"],
                    ':new_ships': results["new_shipments"],
                    ':updated_ships': results["updated_shipments"],
                    ':errors': results["errors"],
                    ':completed_at': int(datetime.now().timestamp())
                }
            )

            # Return response
            return IngestResponse(
                job_id=request.job_id,
                broker_name=broker_name,
                dev_db=request.dev_db,
                dry_run=request.dry_run,
                total_rows=results["total_rows"],
                successful_rows=results["successful_rows"],
                new_shipments=results["new_shipments"],
                updated_shipments=results["updated_shipments"],
                errors=results["errors"],
                status=JobStatus.COMPLETED,
            )
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)

    except Exception as e:
        logging.error(f"Error ingesting data: {e}")
        # Update job status to failed
        try:
            safe_dynamodb_operation(
                jobs_table.update_item,
                Key={'job_id': job['job_id'], 'created_at': job['created_at']},
                UpdateExpression='SET #status = :status, error_message = :error, updated_at = :updated_at',
                ExpressionAttributeNames={'#status': 'status'},
                ExpressionAttributeValues={
                    ':status': JobStatus.FAILED.value,
                    ':error': str(e),
                    ':updated_at': int(datetime.now().timestamp())
                }
            )
        except:
            pass  # Don't fail if we can't update the status
        raise HTTPException(status_code=500, detail=f"Error ingesting data: {e}")


@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_job_status(job_id: str):
    """
    Get status of a specific job from DynamoDB.

    Args:
        job_id: Unique identifier for the job

    Returns:
        StatusResponse: Response containing job status
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        # Since we have a composite key, we need to scan for the job_id
        response = safe_dynamodb_operation(
            jobs_table.scan,
            FilterExpression='job_id = :job_id',
            ExpressionAttributeValues={':job_id': job_id}
        )

        if not response.get('Items'):
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        job = response['Items'][0]  # Get the first (and should be only) matching job

        return StatusResponse(
            job_id=job_id,
            status=JobStatus(job['status']),
            details={k: v for k, v in job.items() if k != 'status'}
        )

    except Exception as e:
        logging.error(f"Status retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/logs/{job_id}", response_model=LogResponse)
async def get_job_logs(job_id: str):
    """
    Get logs for a specific job from S3.

    Args:
        job_id: Unique identifier for the job

    Returns:
        LogResponse: Response containing log content
    """
    try:
        # Get job from DynamoDB to get broker name
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        # Since we have a composite key, we need to scan for the job_id
        response = safe_dynamodb_operation(
            jobs_table.scan,
            FilterExpression='job_id = :job_id',
            ExpressionAttributeValues={':job_id': job_id}
        )

        if not response.get('Items'):
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        job = response['Items'][0]  # Get the first (and should be only) matching job
        broker_name = job['broker_name']

        # Try to get log file from S3
        s3_client = get_s3_client()
        log_key = f"logs/{broker_name}/broker_portal_{job_id}.log"

        try:
            log_response = safe_s3_operation(
                s3_client.get_object,
                Bucket=BUCKET_NAME,
                Key=log_key
            )
            log_content = log_response['Body'].read().decode('utf-8')
        except s3_client.exceptions.NoSuchKey:
            log_content = "No logs available yet"

        return LogResponse(
            job_id=job_id,
            log_content=log_content
        )

    except Exception as e:
        logging.error(f"Log retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/broker-files/{broker_name}")
async def list_broker_files(broker_name: str):
    """
    List all files for a specific broker from S3.

    Args:
        broker_name: Name of the broker

    Returns:
        List of files for the broker
    """
    try:
        s3_client = get_s3_client()

        response = safe_s3_operation(
            s3_client.list_objects_v2,
            Bucket=BUCKET_NAME,
            Prefix=f"uploads/{broker_name}/",
            Delimiter='/'
        )

        files = []
        if 'Contents' in response:
            for obj in response['Contents']:
                files.append({
                    "filename": obj['Key'].split('/')[-1],
                    "path": f"s3://{BUCKET_NAME}/{obj['Key']}",
                    "size": obj['Size'],
                    "created": obj['LastModified'].timestamp(),
                    "modified": obj['LastModified'].timestamp(),
                })

        return {
            "broker_name": broker_name,
            "directory": f"s3://{BUCKET_NAME}/uploads/{broker_name}/",
            "files": files
        }

    except Exception as e:
        logging.error(f"Broker files listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/broker-logs/{broker_name}")
async def list_broker_logs(broker_name: str):
    """
    List all logs for a specific broker from S3.

    Args:
        broker_name: Name of the broker

    Returns:
        List of logs for the broker
    """
    try:
        s3_client = get_s3_client()

        response = safe_s3_operation(
            s3_client.list_objects_v2,
            Bucket=BUCKET_NAME,
            Prefix=f"logs/{broker_name}/",
            Delimiter='/'
        )

        logs = []
        if 'Contents' in response:
            for obj in response['Contents']:
                if obj['Key'].endswith('.log'):
                    logs.append({
                        "filename": obj['Key'].split('/')[-1],
                        "path": f"s3://{BUCKET_NAME}/{obj['Key']}",
                        "size": obj['Size'],
                        "created": obj['LastModified'].timestamp(),
                        "modified": obj['LastModified'].timestamp(),
                    })

        return {
            "broker_name": broker_name,
            "directory": f"s3://{BUCKET_NAME}/logs/{broker_name}/",
            "logs": logs
        }

    except Exception as e:
        logging.error(f"Broker logs listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# The FastAPI app is now fully integrated with AWS services
# All endpoints use S3, DynamoDB, and SQS as configured
