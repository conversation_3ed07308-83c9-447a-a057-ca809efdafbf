"""
FastAPI endpoints for the broker portal backend.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import Fast<PERSON>I, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware

from .config import (API_DESCRIPTION, API_TITLE, API_VERSION, BUCKET_NAME,
                    DYNAMODB_JOBS_TABLE, SQS_QUEUE_URL)
from .logger import generate_job_id
from .models import (ColumnMappingRequest, ColumnMappingResponse,
                    IngestRequest, IngestResponse, JobStatus,
                    LogResponse, StatusResponse, UploadRequest, UploadResponse)
from .utils import analyze_columns_from_s3, save_upload_file_to_s3
from .aws_clients import get_s3_client, get_dynamodb_resource, get_sqs_client, get_dynamodb_table
from .error_handling import safe_dynamodb_operation, safe_s3_operation, safe_sqs_operation, JobNotFoundError

# Create FastAPI app
app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Broker Portal API is running"}


@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "healthy"}


@app.get("/brokers")
async def list_brokers():
    """
    List all available brokers from DynamoDB.

    Returns:
        List of broker names
    """
    try:
        # Query DynamoDB for unique broker names
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        response = safe_dynamodb_operation(
            jobs_table.scan,
            ProjectionExpression='broker_name',
            FilterExpression='attribute_exists(broker_name)'
        )

        # Extract unique broker names
        brokers = list(set(item['broker_name'] for item in response['Items'] if 'broker_name' in item))

        return {"brokers": sorted(brokers)}

    except Exception as e:
        logging.error(f"Broker listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/upload", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    broker_name: str = Form(...),
    broker_id: Optional[str] = Form(None),
    analyze: bool = Form(True)
):
    """
    Upload a broker file (CSV, Excel, or ZIP) to S3 and optionally analyze its columns.

    Args:
        file: The file to upload
        broker_name: Name of the broker
        broker_id: ID of the broker (optional)
        analyze: Whether to analyze columns after upload (default: True)

    Returns:
        UploadResponse: Response containing job ID, file details, and column analysis (if requested)
    """
    job_id = generate_job_id()

    try:
        # Save file to S3
        s3_key = await save_upload_file_to_s3(file, job_id, broker_name)
        logging.info(f"File uploaded to S3: s3://{BUCKET_NAME}/{s3_key}")

        # Create job record in DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        job_item = {
            'job_id': job_id,
            'created_at': int(datetime.now().timestamp()),
            'broker_name': broker_name,
            'broker_id': broker_id,
            'filename': file.filename,
            's3_key': s3_key,
            'status': JobStatus.PENDING.value  # Use the enum value
        }

        # Analyze columns if requested
        if analyze:
            try:
                original_headers, template_headers, sample_data, ai_mapping = await analyze_columns_from_s3(
                    BUCKET_NAME, s3_key, job_id, broker_name
                )
                logging.info(f"Columns analyzed for job {job_id}")

                job_item.update({
                    'original_headers': original_headers,
                    'template_headers': template_headers,
                    'sample_data': sample_data,
                    'ai_mapping': ai_mapping
                })

            except Exception as e:
                logging.error(f"Column analysis failed: {e}")
                job_item['status'] = 'FAILED'
                job_item['error_message'] = str(e)

        # Save job to DynamoDB
        safe_dynamodb_operation(jobs_table.put_item, Item=job_item)

        return UploadResponse(**job_item)

    except Exception as e:
        logging.error(f"Upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/map-columns", response_model=ColumnMappingResponse)
async def map_columns(request: ColumnMappingRequest):
    """
    Apply column mapping to the uploaded file.

    Args:
        request: Column mapping request

    Returns:
        ColumnMappingResponse: Response containing applied column mapping
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        response = safe_dynamodb_operation(
            jobs_table.get_item,
            Key={'job_id': request.job_id}
        )

        if 'Item' not in response:
            raise HTTPException(status_code=404, detail="Job not found")

        job = response['Item']

        # Get column mapping
        column_mapping = request.column_mapping
        if request.use_ai_mapping and job.get('ai_mapping'):
            column_mapping = job['ai_mapping']

        # Update job with column mapping
        safe_dynamodb_operation(
            jobs_table.update_item,
            Key={'job_id': request.job_id},
            UpdateExpression='SET column_mapping = :mapping, #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':mapping': column_mapping,
                ':status': JobStatus.MAPPED.value,
                ':updated_at': int(datetime.now().timestamp())
            }
        )

        return ColumnMappingResponse(
            job_id=request.job_id,
            column_mapping=column_mapping,
            status=JobStatus.PROCESSING
        )

    except Exception as e:
        logging.error(f"Column mapping failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ingest", response_model=IngestResponse)
async def ingest_data_async(request: IngestRequest):
    """
    Queue the uploaded file for async ingestion processing.

    Args:
        request: Ingestion request

    Returns:
        IngestResponse: Response containing job queuing status
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        response = safe_dynamodb_operation(
            jobs_table.get_item,
            Key={'job_id': request.job_id}
        )

        if 'Item' not in response:
            raise HTTPException(status_code=404, detail="Job not found")

        job = response['Item']

        # Validate job has column mapping
        if 'column_mapping' not in job:
            raise HTTPException(status_code=400, detail="Job must have column mapping before ingestion")

        # Use broker_name from job if not provided in request
        broker_name = request.broker_name or job.get('broker_name')
        if not broker_name:
            raise HTTPException(status_code=400, detail="Broker name not provided")

        # Update job status to QUEUED
        safe_dynamodb_operation(
            jobs_table.update_item,
            Key={'job_id': request.job_id},
            UpdateExpression='SET #status = :status, updated_at = :updated_at, dev_db = :dev_db, dry_run = :dry_run',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':status': JobStatus.QUEUED.value,
                ':updated_at': int(datetime.now().timestamp()),
                ':dev_db': request.dev_db,
                ':dry_run': request.dry_run
            }
        )

        # Send message to SQS
        sqs_client = get_sqs_client()
        message_body = {
            'job_id': request.job_id,
            'broker_name': broker_name,
            's3_key': job['s3_key'],
            'column_mapping': job['column_mapping'],
            'dev_db': request.dev_db,
            'dry_run': request.dry_run
        }

        safe_sqs_operation(
            sqs_client.send_message,
            QueueUrl=SQS_QUEUE_URL,
            MessageBody=json.dumps(message_body),
            MessageAttributes={
                'job_id': {'StringValue': request.job_id, 'DataType': 'String'},
                'broker_name': {'StringValue': broker_name, 'DataType': 'String'}
            }
        )

        return IngestResponse(
            job_id=request.job_id,
            broker_name=broker_name,
            dev_db=request.dev_db,
            dry_run=request.dry_run,
            status=JobStatus.PROCESSING
        )

    except Exception as e:
        logging.error(f"Ingestion queueing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_job_status(job_id: str):
    """
    Get status of a specific job from DynamoDB.

    Args:
        job_id: Unique identifier for the job

    Returns:
        StatusResponse: Response containing job status
    """
    try:
        # Get job from DynamoDB
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        response = safe_dynamodb_operation(
            jobs_table.get_item,
            Key={'job_id': job_id}
        )

        if 'Item' not in response:
            raise HTTPException(status_code=404, detail="Job not found")

        job = response['Item']

        return StatusResponse(
            job_id=job_id,
            status=JobStatus(job['status']),
            broker_name=job.get('broker_name'),
            filename=job.get('filename'),
            created_at=job.get('created_at'),
            updated_at=job.get('updated_at'),
            total_rows=job.get('total_rows'),
            successful_rows=job.get('successful_rows'),
            new_shipments=job.get('new_shipments'),
            updated_shipments=job.get('updated_shipments'),
            errors=job.get('errors', {}),
            error_message=job.get('error_message')
        )

    except Exception as e:
        logging.error(f"Status retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/logs/{job_id}", response_model=LogResponse)
async def get_job_logs(job_id: str):
    """
    Get logs for a specific job from S3.

    Args:
        job_id: Unique identifier for the job

    Returns:
        LogResponse: Response containing log content
    """
    try:
        # Get job from DynamoDB to get broker name
        jobs_table = get_dynamodb_table(DYNAMODB_JOBS_TABLE)

        response = safe_dynamodb_operation(
            jobs_table.get_item,
            Key={'job_id': job_id}
        )

        if 'Item' not in response:
            raise HTTPException(status_code=404, detail="Job not found")

        job = response['Item']
        broker_name = job['broker_name']

        # Try to get log file from S3
        s3_client = get_s3_client()
        log_key = f"logs/{broker_name}/broker_portal_{job_id}.log"

        try:
            log_response = safe_s3_operation(
                s3_client.get_object,
                Bucket=BUCKET_NAME,
                Key=log_key
            )
            log_content = log_response['Body'].read().decode('utf-8')
        except s3_client.exceptions.NoSuchKey:
            log_content = "No logs available yet"

        return LogResponse(
            job_id=job_id,
            logs=log_content,
            log_file_path=f"s3://{BUCKET_NAME}/{log_key}"
        )

    except Exception as e:
        logging.error(f"Log retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/broker-files/{broker_name}")
async def list_broker_files(broker_name: str):
    """
    List all files for a specific broker from S3.

    Args:
        broker_name: Name of the broker

    Returns:
        List of files for the broker
    """
    try:
        s3_client = get_s3_client()

        response = safe_s3_operation(
            s3_client.list_objects_v2,
            Bucket=BUCKET_NAME,
            Prefix=f"uploads/{broker_name}/",
            Delimiter='/'
        )

        files = []
        if 'Contents' in response:
            files = [obj['Key'].split('/')[-1] for obj in response['Contents']]

        return {"broker_name": broker_name, "files": files}

    except Exception as e:
        logging.error(f"Broker files listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/broker-logs/{broker_name}")
async def list_broker_logs(broker_name: str):
    """
    List all logs for a specific broker from S3.

    Args:
        broker_name: Name of the broker

    Returns:
        List of logs for the broker
    """
    try:
        s3_client = get_s3_client()

        response = safe_s3_operation(
            s3_client.list_objects_v2,
            Bucket=BUCKET_NAME,
            Prefix=f"logs/{broker_name}/",
            Delimiter='/'
        )

        logs = []
        if 'Contents' in response:
            logs = [obj['Key'].split('/')[-1] for obj in response['Contents'] if obj['Key'].endswith('.log')]

        return {"broker_name": broker_name, "logs": logs}

    except Exception as e:
        logging.error(f"Broker logs listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# The FastAPI app is now fully integrated with AWS services
# All endpoints use S3, DynamoDB, and SQS as configured
