#!/usr/bin/env python3
"""
Simple development server script that can be run from within the broker_portal_serverless directory.
"""

import os
import sys
import uvicorn

# Add paths for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Add ingestion directory to path
ingestion_dir = os.path.join(project_root, 'ingestion')
sys.path.append(ingestion_dir)

if __name__ == "__main__":
    print("🚀 Starting Broker Portal Serverless Development Server")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔄 Auto-reload enabled for development")
    print("⏹️  Press CTRL+C to stop")
    print("-" * 50)
    
    # Change to project root for proper module resolution
    os.chdir(project_root)
    
    uvicorn.run(
        "broker_portal_serverless.api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
