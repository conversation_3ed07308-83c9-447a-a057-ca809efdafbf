"""
This module serves as a wrapper for the constants module.
It imports all constants from ingestion/constants.py and re-exports them.
This ensures that when code imports from 'constants', it gets the correct constants.
"""

# Import all constants from ingestion/constants.py
try:
    from ingestion.constants import *
    print("Successfully imported constants from ingestion.constants")
except ImportError as e:
    print(f"Error importing from ingestion.constants: {e}")
    # If that fails, try importing from the parent directory
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from ingestion.constants import *
        print("Successfully imported constants from parent directory")
    except ImportError as e:
        print(f"Error importing from parent directory: {e}")
        # If all else fails, define the most critical constants here
        import collections
        import os
        import pandas as pd
        import pytz
        
        # Define minimal versions of the constants needed
        class GoldenColumns:
            broker_name = "brokerName"
            customer_name = "customerName"
            business_unit = "businessUnit"
            broker_primary_reference = "brokerPrimaryReference"
            shipper_primary_reference = "shipperPrimaryReference"
            shipment_mode = "shipmentMode"
            equipment_type = "equipmentType"
            origin_name = "originName"
            destination_name = "destinationName"
            origin_city = "originCity"
            destination_city = "destinationCity"
            origin_state = "originState"
            destination_state = "destinationState"
            origin_zip = "originZip"
            destination_zip = "destinationZip"
            origin_country = "originCountry"
            destination_country = "destinationCountry"
            shipment_status = "shipmentStatus"
            shipment_rank = "shipmentRank"
            origin_open = "originOpenTime"
            destination_open = "destinationOpenTime"
            origin_close = "originCloseTime"
            destination_close = "destinationCloseTime"
            origin_arrival = "originArrivalTime"
            destination_arrival = "destinationArrivalTime"
            origin_departure = "originDepartureTime"
            destination_departure = "destinationDepartureTime"
            load_creation = "loadCreationTime"
            load_activation = "loadActivationTime"
            carrier_assigned = "carrierAssignedTime"
            stop_count = "stopCount"
            distance_miles = "distanceMiles"
            cogs_line_haul = "cogsLineHaul"
            revenue_line_haul = "revenueLineHaul"
            cogs_fuel = "cogsFuel"
            revenue_fuel = "revenueFuel"
            cogs_accessorial = "cogsAccessorial"
            revenue_accessorial = "revenueAccessorial"
            cogs_total = "cogsTotal"
            revenue_total = "revenueTotal"
            weight = "weight"
            
            @classmethod
            def __iter__(cls):
                for attr in dir(cls):
                    if not attr.startswith('__') and not callable(getattr(cls, attr)):
                        yield getattr(cls, attr)
        
        GC = GoldenColumns
        
        # Define empty versions of required constants
        BROKER_IGNORE_COLS = {}
        BROKER_NAME_DICT = {}
        CUSTOM_TIMEZONES_DICT = collections.defaultdict(lambda: {})
        DATETIME_COLUMNS = []
        NUMERIC_COLUMNS = []
        STRING_COLUMNS = []
        SHIPPER_NAME_DICT = {}
        NUM_THREADS = 1
        INGESTION_DATA_DIR = "/tmp"
        
        print("Using fallback constants definitions")