"""
Utility functions for the broker portal backend.
"""

import os
import tempfile
import logging
from typing import Dict, List, Optional, Tuple, Union

import pandas as pd
from fastapi import UploadFile

from .config import BUCKET_NAME
from .aws_clients import get_s3_client
from .error_handling import safe_s3_operation

logger = logging.getLogger(__name__)

# Import ingestion modules (with fallback for development)
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Add ingestion directory to path
ingestion_dir = os.path.join(project_root, 'ingestion')
sys.path.append(ingestion_dir)

# Global variables for ingestion modules
ingestion = None
GoldenColumns = None
ai_column_mapping = None
RDS = None

# Try to import ingestion modules, but don't fail if they're not available
INGESTION_AVAILABLE = False
try:
    # First try to import the constants to see if the module structure is correct
    from ingestion.constants import GC
    from ingestion.mapping.column_mapping import GoldenColumns
    from ingestion.mapping.ai_column_mapping import ai_column_mapping
    from ingestion import ingestion
    from common.rds_api import RDS
    INGESTION_AVAILABLE = True
    logger.info("Successfully imported ingestion modules")
except ImportError as e:
    logger.warning(f"Failed to import ingestion modules: {e}")
    logger.warning("Running in simplified mode without ingestion capabilities")
    logger.warning("Some features like column analysis and data ingestion will not be available")
except Exception as e:
    logger.warning(f"Error loading ingestion modules: {e}")
    logger.warning("Running in simplified mode")


async def save_upload_file_to_s3(file: UploadFile, job_id: str, broker_name: str) -> str:
    """
    Save an uploaded file to S3 under uploads/{broker_name}/{job_id}_{filename}.

    Args:
        file: The uploaded file
        job_id: Unique identifier for the job
        broker_name: Name of the broker

    Returns:
        S3 key for the uploaded file
    """
    s3_client = get_s3_client()
    s3_key = f"uploads/{broker_name}/{job_id}_{file.filename}"

    # Read file content
    file_content = await file.read()

    # Upload to S3 with retry logic
    safe_s3_operation(
        s3_client.put_object,
        Bucket=BUCKET_NAME,
        Key=s3_key,
        Body=file_content
    )

    logger.info(f"File saved to S3: s3://{BUCKET_NAME}/{s3_key}")
    return s3_key


def upload_logs_to_s3(log_content: str, job_id: str, broker_name: str) -> str:
    """
    Upload log content to S3 logs folder.

    Args:
        log_content: Content of the log file
        job_id: Unique identifier for the job
        broker_name: Name of the broker

    Returns:
        S3 key for the uploaded log file
    """
    s3_client = get_s3_client()
    log_key = f"logs/{broker_name}/broker_portal_{job_id}.log"

    safe_s3_operation(
        s3_client.put_object,
        Bucket=BUCKET_NAME,
        Key=log_key,
        Body=log_content.encode('utf-8'),
        ContentType='text/plain'
    )

    logger.info(f"Log uploaded to S3: s3://{BUCKET_NAME}/{log_key}")
    return log_key


async def analyze_columns(file_path: str, job_id: str, broker_name: Optional[str] = None) -> Tuple[List[str], List[str], List[Dict[str, str]], Optional[Dict[str, str]]]:
    """
    Analyze columns in the uploaded file.

    Args:
        file_path: Path to the uploaded file
        job_id: Unique identifier for the job
        broker_name: Name of the broker (optional)

    Returns:
        Tuple containing:
        - Original column headers
        - Template headers (golden columns)
        - Sample data
        - AI-suggested column mapping (if available)
    """
    if broker_name:
        logger.info(f"Analyzing columns for broker: {broker_name}")

    # Read the input file
    if ingestion is not None:
        df = ingestion.read_input_file(file_path)
    else:
        # Fallback file reading for development
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, nrows=5)
        else:
            df = pd.read_csv(file_path, nrows=5)

    # Get original headers and template headers
    original_headers = list(df.columns)

    if GoldenColumns is not None:
        template_headers = [g.value for g in GoldenColumns]
    else:
        # Default template headers for development
        template_headers = [
            "Broker Name", "Customer Name", "Origin City", "Destination City",
            "Origin State", "Destination State", "Origin Zip", "Destination Zip",
            "Shipment Mode", "Equipment Type", "COGS Total", "Revenue Total",
            "Origin Close", "Broker Primary Reference"
        ]
        logger.warning("Using default template headers (ingestion module not available)")

    # Get sample data and convert all values to strings to avoid Pydantic validation issues
    sample_data_raw = df.head(5).to_dict(orient='records')

    # Convert all values to strings to avoid Pydantic validation issues
    sample_data = []
    for row in sample_data_raw:
        string_row = {}
        for key, value in row.items():
            if pd.isna(value):
                string_row[key] = None
            else:
                string_row[key] = str(value)
        sample_data.append(string_row)

    # Try AI-based column mapping if available
    ai_mapping = None
    if ai_column_mapping is not None:
        try:
            ai_mapping = ai_column_mapping(
                original_headers=original_headers,
                template_headers=template_headers,
                sample_data=sample_data
            )
            logger.info(f"AI column mapping successful for {len(ai_mapping)} columns")
        except Exception as e:
            logger.warning(f"AI mapping failed: {e}")
    else:
        logger.warning("AI column mapping not available (ingestion module not loaded)")

    return original_headers, template_headers, sample_data, ai_mapping


async def analyze_columns_from_s3(bucket_name: str, s3_key: str, job_id: str, broker_name: str = None):
    """
    Download file from S3 to /tmp, analyze columns, then cleanup.

    Args:
        bucket_name: S3 bucket name
        s3_key: S3 key for the file
        job_id: Unique identifier for the job
        broker_name: Name of the broker

    Returns:
        Tuple containing column analysis results
    """
    s3_client = get_s3_client()

    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(s3_key)[1]) as tmp_file:
        try:
            # Download file from S3
            safe_s3_operation(
                s3_client.download_fileobj,
                bucket_name, s3_key, tmp_file
            )
            tmp_file_path = tmp_file.name

            # Analyze columns
            return await analyze_columns(tmp_file_path, job_id, broker_name)

        finally:
            # Cleanup temp file
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)


async def apply_column_mapping(
    file_path: str,
    column_mapping: Dict[str, str],
    broker_name: str,
    job_id: str
) -> pd.DataFrame:
    """
    Apply column mapping to the uploaded file.

    Args:
        file_path: Path to the uploaded file
        column_mapping: Mapping from original columns to golden columns
        broker_name: Name of the broker
        job_id: Unique identifier for the job

    Returns:
        DataFrame with applied column mapping
    """
    if ingestion is None:
        raise ValueError("Column mapping not available - ingestion module not loaded")

    # Read the input file
    df = ingestion.read_input_file(file_path)

    # Apply column mapping
    df = df.rename(columns=column_mapping)

    # Build type dictionary and convert types
    type_dict, date_cols = ingestion.build_df_type_dictionary(column_mapping)
    df = ingestion.read_input_file(file_path, type_dict, date_cols)
    df = df.rename(columns=column_mapping)

    # Handle broker-specific column drops
    if hasattr(ingestion, 'BROKER_IGNORE_COLS') and broker_name in ingestion.BROKER_IGNORE_COLS:
        drop_cols = ingestion.BROKER_IGNORE_COLS[broker_name]
        for col in drop_cols:
            if col.value in df.columns:
                df.drop(col.value, axis=1, inplace=True)
                logger.info(f"Dropped broker-specific column: {col.value}")

    # Ensure all required columns are present (only if GoldenColumns is available)
    if GoldenColumns is not None:
        required_cols = {
            GoldenColumns.origin_zip,
            GoldenColumns.destination_zip,
            GoldenColumns.origin_city,
            GoldenColumns.destination_city,
            GoldenColumns.origin_state,
            GoldenColumns.destination_state,
            GoldenColumns.shipment_mode,
            GoldenColumns.equipment_type,
            GoldenColumns.customer_name,
            GoldenColumns.broker_name,
            GoldenColumns.broker_primary_reference,
            GoldenColumns.cogs_total,
            GoldenColumns.revenue_total,
            GoldenColumns.origin_close,
        }

        added_empty_cols = []
        for g in GoldenColumns:
            if g.value not in df.columns:
                if g in required_cols:
                    raise ValueError(f'Missing required column "{g.value}".')
                df[g.value] = pd.NA
                added_empty_cols.append(g.value)
                logger.info(f"Added empty column: {g.value}")

        # Convert data types
        if hasattr(ingestion, 'convert_types'):
            ingestion.convert_types(df, added_empty_cols)

    return df


async def ingest_data(
    file_path: str,
    broker_name: str,
    dev_db: bool,
    dry_run: bool,
    job_id: str
) -> Dict[str, Union[int, Dict[str, int]]]:
    """
    Ingest data from the uploaded file.

    Args:
        file_path: Path to the uploaded file
        broker_name: Name of the broker
        dev_db: Whether to use the development database
        dry_run: Whether to run in dry run mode
        job_id: Unique identifier for the job

    Returns:
        Dictionary containing ingestion results
    """
    if not INGESTION_AVAILABLE:
        # Return mock results for development/testing
        logger.warning("Ingestion modules not available - returning mock results")
        return {
            "total_rows": 100,
            "successful_rows": 95,
            "new_shipments": 80,
            "updated_shipments": 15,
            "errors": {"validation_errors": 5}
        }

    try:
        from common.constants import AURORA_DB_DEV_NAME, AURORA_DB_NAME

        # Initialize RDS connection
        aurora_db = AURORA_DB_DEV_NAME if dev_db else AURORA_DB_NAME
        rds = RDS(aurora_db, create_tables=False, dry_mode=dry_run)

        # Ingest the file
        logger.info(f"Ingesting file: {file_path}")
        logger.info(f"Broker: {broker_name}")
        logger.info(f"Dev DB: {dev_db}")
        logger.info(f"Dry Run: {dry_run}")

        if hasattr(ingestion, 'ingest_file'):
            value_errors, successful_rows, total_rows, new_shipments, updated_shipments = ingestion.ingest_file(
                file_path, rds, broker_name, use_tqdm=False
            )
        else:
            raise ValueError("ingest_file function not available in ingestion module")

        # Return results
        return {
            "total_rows": total_rows,
            "successful_rows": successful_rows,
            "new_shipments": new_shipments,
            "updated_shipments": updated_shipments,
            "errors": value_errors
        }
    except ImportError as e:
        raise ValueError(f"Required modules not available for ingestion: {e}")
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        raise
