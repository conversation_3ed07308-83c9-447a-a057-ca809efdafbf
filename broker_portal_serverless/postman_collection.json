{"info": {"name": "Broker Portal API", "description": "API for broker onboarding portal, providing file upload, column mapping, and ingestion capabilities.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Root", "request": {"method": "GET", "url": {"raw": "http://localhost:8000/", "protocol": "http", "host": ["localhost"], "port": "8000", "path": [""]}, "description": "Root endpoint to check if the API is running."}, "response": []}, {"name": "Upload File", "request": {"method": "POST", "url": {"raw": "http://localhost:8000/upload", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["upload"]}, "description": "Upload a broker file (CSV, Excel, or ZIP) and optionally analyze its columns.", "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/file.csv"}, {"key": "broker_name", "type": "text", "value": "Example Broker"}, {"key": "broker_id", "type": "text", "value": "broker-123", "disabled": true}, {"key": "analyze", "type": "text", "value": "true", "description": "Whether to analyze columns after upload (default: true)"}]}}, "response": []}, {"name": "Map Columns", "request": {"method": "POST", "url": {"raw": "http://localhost:8000/map-columns", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["map-columns"]}, "description": "Apply column mapping to the uploaded file and change job status to 'processing'.", "body": {"mode": "raw", "raw": "{\n  \"job_id\": \"{{job_id}}\",\n  \"column_mapping\": {\n    \"Original Column 1\": \"Mapped Column 1\",\n    \"Original Column 2\": \"Mapped Column 2\"\n  },\n  \"use_ai_mapping\": false\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "Ingest File", "request": {"method": "POST", "url": {"raw": "http://localhost:8000/ingest", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["ingest"]}, "description": "Ingest the uploaded file with the applied column mapping.", "body": {"mode": "raw", "raw": "{\n  \"job_id\": \"{{job_id}}\",\n  \"broker_name\": \"Example Broker\",\n  \"dev_db\": true,\n  \"dry_run\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "Get Logs", "request": {"method": "GET", "url": {"raw": "http://localhost:8000/logs/{{job_id}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["logs", "{{job_id}}"]}, "description": "Get logs for a specific job."}, "response": []}, {"name": "Get Status", "request": {"method": "GET", "url": {"raw": "http://localhost:8000/status/{{job_id}}", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["status", "{{job_id}}"]}, "description": "Get status of a specific job."}, "response": []}], "variable": [{"key": "job_id", "value": "your-job-id-here"}]}