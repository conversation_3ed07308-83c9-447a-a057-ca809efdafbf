# Broker Portal Backend

This module provides a FastAPI-based backend for the broker onboarding portal. It includes endpoints for file upload, column mapping, and integration with the existing ingestion system.

## Features

- **File Upload**: Upload broker files (CSV, Excel, or ZIP) to broker-specific directories
- **Column Analysis**: Analyze columns in the uploaded file
- **Column Mapping**: Apply column mapping (AI or manual)
- **Ingestion**: Process the file with the existing ingestion system
- **Log Viewing**: View logs for a specific ingestion job
- **Status Checking**: Check the status of an ingestion job
- **Broker Management**: List all brokers, their files, and logs
- **Broker-Specific Organization**: Files and logs are organized by broker name
- **Development Database Mode**: Support for using the development database
- **Dry Run Mode**: Support for running in read-only mode without updating the database

## Directory Structure

The broker portal organizes files and logs in a broker-specific directory structure:

```
project_root/
├── broker_portal_uploads/           # Main directory for all uploaded files
│   ├── BrokerName1/                 # Broker-specific upload directory
│   │   ├── job_id_file1.csv
│   │   └── job_id_file2.xlsx
│   └── BrokerName2/                 # Another broker's directory
│       └── job_id_file3.csv
│
└── broker_portal_logs/              # Main directory for all logs
    ├── BrokerName1/                 # Broker-specific log directory
    │   ├── broker_portal_job_id1_timestamp.log
    │   └── broker_portal_job_id2_timestamp.log
    └── BrokerName2/                 # Another broker's log directory
        └── broker_portal_job_id3_timestamp.log
```

This organization makes it easy to:
- Keep files and logs organized by broker
- Manage and track data for different brokers separately
- Find all files and logs for a specific broker in one place

## Installation

1. Make sure you have Python 3.8+ installed
2. Install the required dependencies:

```bash
pip install -r broker_portal/requirements.txt
```

## Running the Server

To run the broker portal backend server:

```bash
source .venv/bin/activate && python -m broker_portal.run_server
```

By default, the server will run on `0.0.0.0:8000`. You can customize the host and port:

```bash
source .venv/bin/activate && python -m broker_portal.run_server --host 127.0.0.1 --port 8080
```

## API Endpoints

### File Upload

```
POST /upload
```

Upload a broker file (CSV, Excel, or ZIP) and optionally analyze its columns.

**Request**:
- Form data with:
  - `file`: The file to upload
  - `broker_name`: Name of the broker
  - `broker_id`: ID of the broker (optional)
  - `analyze`: Whether to analyze columns after upload (default: true)

**Response**:
```json
{
  "job_id": "string",
  "broker_name": "string",
  "broker_id": "string",
  "filename": "string",
  "file_path": "string",
  "status": "processing",
  "original_headers": ["string"],
  "template_headers": ["string"],
  "sample_data": [{}],
  "ai_mapping": {"string": "string"}
}
```

### Column Mapping

```
POST /map-columns
```

Apply column mapping to the uploaded file and change job status to "processing".

**Request**:
```json
{
  "job_id": "string",
  "column_mapping": {"string": "string"},
  "use_ai_mapping": false
}
```

**Response**:
```json
{
  "job_id": "string",
  "column_mapping": {"string": "string"},
  "status": "processing"
}
```

### Ingestion

```
POST /ingest
```

Ingest the uploaded file with the applied column mapping.

**Request**:
```json
{
  "job_id": "string",
  "broker_name": "string",  // Optional if provided during upload
  "dev_db": false,
  "dry_run": false
}
```

**Response**:
```json
{
  "job_id": "string",
  "broker_name": "string",
  "dev_db": false,
  "dry_run": false,
  "total_rows": 0,
  "successful_rows": 0,
  "new_shipments": 0,
  "updated_shipments": 0,
  "errors": {},
  "status": "completed"
}
```

### Log Viewing

```
GET /logs/{job_id}
```

Get logs for a specific job.

**Response**:
```json
{
  "job_id": "string",
  "log_content": "string"
}
```

### Status Checking

```
GET /status/{job_id}
```

Get status of a specific job.

**Response**:
```json
{
  "job_id": "string",
  "status": "string",
  "details": {}
}
```

### List All Brokers

```
GET /brokers
```

List all brokers with file and log counts.

**Response**:
```json
{
  "brokers": [
    {
      "name": "BrokerName1",
      "files_count": 2,
      "upload_dir": "/path/to/broker_portal_uploads/BrokerName1",
      "logs_count": 2,
      "log_dir": "/path/to/broker_portal_logs/BrokerName1"
    },
    {
      "name": "BrokerName2",
      "files_count": 1,
      "upload_dir": "/path/to/broker_portal_uploads/BrokerName2",
      "logs_count": 1,
      "log_dir": "/path/to/broker_portal_logs/BrokerName2"
    }
  ],
  "upload_dir": "/path/to/broker_portal_uploads",
  "log_dir": "/path/to/broker_portal_logs"
}
```

### List Broker Files

```
GET /broker-files/{broker_name}
```

List all files for a specific broker.

**Response**:
```json
{
  "broker_name": "BrokerName1",
  "directory": "/path/to/broker_portal_uploads/BrokerName1",
  "files": [
    {
      "filename": "job_id_file1.csv",
      "path": "/path/to/broker_portal_uploads/BrokerName1/job_id_file1.csv",
      "size": 1024,
      "created": 1619712000,
      "modified": 1619712000
    },
    {
      "filename": "job_id_file2.xlsx",
      "path": "/path/to/broker_portal_uploads/BrokerName1/job_id_file2.xlsx",
      "size": 2048,
      "created": 1619712000,
      "modified": 1619712000
    }
  ]
}
```

### List Broker Logs

```
GET /broker-logs/{broker_name}
```

List all logs for a specific broker.

**Response**:
```json
{
  "broker_name": "BrokerName1",
  "directory": "/path/to/broker_portal_logs/BrokerName1",
  "logs": [
    {
      "filename": "broker_portal_job_id1_timestamp.log",
      "path": "/path/to/broker_portal_logs/BrokerName1/broker_portal_job_id1_timestamp.log",
      "size": 1024,
      "created": 1619712000,
      "modified": 1619712000
    },
    {
      "filename": "broker_portal_job_id2_timestamp.log",
      "path": "/path/to/broker_portal_logs/BrokerName1/broker_portal_job_id2_timestamp.log",
      "size": 2048,
      "created": 1619712000,
      "modified": 1619712000
    }
  ]
}
```

## Testing with Postman

You can test the API using Postman:

1. Start the server
2. Import the Postman collection from `broker_portal/postman_collection.json` (if available)
3. Test the endpoints

### Testing Broker-Specific Organization

To test the broker-specific organization:

1. Upload files for different brokers using the `/upload` endpoint
2. List all brokers using the `/brokers` endpoint
3. List files for a specific broker using the `/broker-files/{broker_name}` endpoint
4. List logs for a specific broker using the `/broker-logs/{broker_name}` endpoint
5. Verify that files and logs are organized in the correct broker-specific directories

## Integration with Existing Ingestion System

The broker portal backend leverages the existing ingestion system's core functions:

- `read_input_file()`: For reading broker files
- `read_and_map_file()`: For applying column mappings
- `ingest_file()`: For the main ingestion process
- `convert_types()`: For data type conversion

### Broker-Specific Utility Functions

The broker portal also includes utility functions for broker-specific organization:

- `get_broker_upload_dir(broker_name)`: Gets or creates the broker-specific upload directory
- `get_broker_log_dir(broker_name)`: Gets or creates the broker-specific log directory
- `save_upload_file(file, job_id, broker_name)`: Saves a file to the broker-specific upload directory
- `setup_logging(job_id, broker_name)`: Sets up logging in the broker-specific log directory

## Development

To run the server in development mode with auto-reload:

```bash
source .venv/bin/activate && python -m broker_portal.run_server --log-level DEBUG
```

### Directory Structure Management

When developing new features, keep in mind the broker-specific directory structure:

- All uploaded files should be saved to `broker_portal_uploads/{broker_name}/`
- All logs should be saved to `broker_portal_logs/{broker_name}/`
- Use the utility functions `get_broker_upload_dir()` and `get_broker_log_dir()` to get the correct directories
- Always include the broker name when saving files or setting up logging
