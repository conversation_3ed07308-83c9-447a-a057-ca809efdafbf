#!/bin/bash

# This script builds and pushes the upload handler Docker image to ECR

# Set variables
ACCOUNT_ID=$(aws sts get-caller-identity --profile personalaccount --query Account --output text)
REGION=$(aws configure --profile personalaccount get region)
REPOSITORY_NAME="broker-portal-lambda-dev"
IMAGE_TAG="upload_file"
USE_ALT_DOCKERFILE=false  # Set to false by default
USE_FIX_DOCKERFILE=true   # Set to true by default to use the fixed Dockerfile

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --alt)
      USE_ALT_DOCKERFILE=true
      USE_FIX_DOCKERFILE=false
      shift
      ;;
    --std)
      USE_ALT_DOCKERFILE=false
      USE_FIX_DOCKERFILE=false
      shift
      ;;
    --fix)
      USE_FIX_DOCKERFILE=true
      USE_ALT_DOCKERFILE=false
      shift
      ;;
    *)
      shift
      ;;
  esac
done

# Create ECR repository if it doesn't exist
aws ecr describe-repositories --profile personalaccount --repository-names ${REPOSITORY_NAME} > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Creating ECR repository: ${REPOSITORY_NAME}"
    aws ecr create-repository --profile personalaccount --repository-name ${REPOSITORY_NAME}
fi

# Login to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region ${REGION} --profile personalaccount | docker login --username AWS --password-stdin ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com

# Select the appropriate Dockerfile
if [ "$USE_FIX_DOCKERFILE" = true ]; then
    DOCKERFILE="broker_portal_serverless/Dockerfile.upload.fix"
    echo "Using fixed Dockerfile..."
elif [ "$USE_ALT_DOCKERFILE" = true ]; then
    DOCKERFILE="broker_portal_serverless/Dockerfile.upload.alt"
    echo "Using alternative Dockerfile..."
else
    DOCKERFILE="broker_portal_serverless/Dockerfile.upload"
    echo "Using standard Dockerfile..."
fi

# Build Docker image
echo "Building Docker image with $DOCKERFILE..."
docker buildx build --platform=linux/amd64 --provenance=false --debug -t ${REPOSITORY_NAME}:${IMAGE_TAG} -f ${DOCKERFILE} .

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Docker build failed!"
    if [ "$USE_ALT_DOCKERFILE" = false ]; then
        echo "Try running with --alt to use the alternative Dockerfile"
    fi
    exit 1
fi

# Tag Docker image
echo "Tagging Docker image..."
docker tag ${REPOSITORY_NAME}:${IMAGE_TAG} ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${REPOSITORY_NAME}:${IMAGE_TAG}

# Push Docker image to ECR
echo "Pushing Docker image to ECR..."
docker push ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${REPOSITORY_NAME}:${IMAGE_TAG}

echo "Done! Image URI: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${REPOSITORY_NAME}:${IMAGE_TAG}"
