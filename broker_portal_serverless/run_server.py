"""
<PERSON><PERSON><PERSON> to run the broker portal API server.
"""

import argparse
import logging
import os
import sys
import uvicorn

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import from local config
from config import API_HOST, API_PORT, DEFAULT_LOG_LEVEL

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# Add ingestion directory to path
ingestion_dir = os.path.join(project_root, 'ingestion')
sys.path.append(ingestion_dir)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the broker portal API server.")
    parser.add_argument(
        "--host",
        type=str,
        default=API_HOST,
        help=f"Host to run the server on (default: {API_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=API_PORT,
        help=f"Port to run the server on (default: {API_PORT})",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help=f"Logging level (default: {DEFAULT_LOG_LEVEL})",
    )
    return parser.parse_args()


def setup_logging(log_level):
    """Set up logging for the server."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")

    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def main():
    """Run the broker portal API server."""
    args = parse_args()
    setup_logging(args.log_level)

    logging.info(f"Starting broker portal API server on {args.host}:{args.port}")
    uvicorn.run(
        "broker_portal_serverless.api:app",
        host=args.host,
        port=args.port,
        reload=True,
    )


if __name__ == "__main__":
    main()
