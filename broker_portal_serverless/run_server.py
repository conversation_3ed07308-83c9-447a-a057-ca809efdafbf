"""
<PERSON><PERSON><PERSON> to run the broker portal serverless API server locally.
"""

import argparse
import logging
import os
import sys
import uvicorn

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Add project root to path (parent of broker_portal_serverless)
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Add ingestion directory to path
ingestion_dir = os.path.join(project_root, 'ingestion')
sys.path.append(ingestion_dir)

# Import from local config
from config import API_HOST, API_PORT, DEFAULT_LOG_LEVEL


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the broker portal API server.")
    parser.add_argument(
        "--host",
        type=str,
        default=API_HOST,
        help=f"Host to run the server on (default: {API_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=API_PORT,
        help=f"Port to run the server on (default: {API_PORT})",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help=f"Logging level (default: {DEFAULT_LOG_LEVEL})",
    )
    return parser.parse_args()


def setup_logging(log_level):
    """Set up logging for the server."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")

    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def main():
    """Run the broker portal serverless API server."""
    args = parse_args()
    setup_logging(args.log_level)

    logging.info(f"Starting broker portal serverless API server on {args.host}:{args.port}")
    logging.info(f"API documentation available at: http://{args.host}:{args.port}/docs")

    # Use the module path for uvicorn to handle relative imports correctly
    uvicorn.run(
        "broker_portal_serverless.api:app",
        host=args.host,
        port=args.port,
        reload=True,
        log_level=args.log_level.lower(),
    )


if __name__ == "__main__":
    main()
