FROM public.ecr.aws/lambda/python:3.11

# Install core dependencies first
RUN pip install --no-cache-dir \
    boto3==1.34.0 \
    pandas==2.0.1 \
    numpy==1.23.3 \
    fastapi==0.109.0 \
    python-multipart==0.0.6 \
    pydantic==2.5.3 \
    requests==2.31.0 \
    openpyxl==3.1.2 \
    xlrd==2.0.1 \
    python-dateutil==2.8.2 \
    sqlalchemy==1.4.19 \
    sqlalchemy-aurora-data-api==0.2.7 \
    tqdm==4.41.1 \
    googlemaps==4.4.2 \
    google-auth-oauthlib==1.0.0 \
    google-api-python-client==2.29.0

# Install AI-related dependencies
RUN pip install --no-cache-dir \
    google-generativeai==0.3.2 \
    langchain==0.1.0 \
    langchain_community>=0.0.10 \
    langchain_core>=0.1.0 \
    pytz \
    regex

# Install AWS-specific dependencies
RUN pip install --no-cache-dir \
    aws-lambda-powertools==2.30.2 \
    python-jose==3.3.0 \
    aioboto3==11.2.0 \
    python-dotenv==1.0.0

# Copy function code and modules
COPY broker_portal_serverless/handlers/ ${LAMBDA_TASK_ROOT}/handlers/
COPY ingestion/ ${LAMBDA_TASK_ROOT}/ingestion/
COPY common/ ${LAMBDA_TASK_ROOT}/common/

# Create an empty __init__.py file in the root directory to make it a package
RUN touch ${LAMBDA_TASK_ROOT}/__init__.py

# Set the PYTHONPATH to include the Lambda task root
ENV PYTHONPATH="${LAMBDA_TASK_ROOT}"

# Create a symbolic link for constants.py to ensure it's found
RUN ln -sf ${LAMBDA_TASK_ROOT}/ingestion/constants.py ${LAMBDA_TASK_ROOT}/constants.py

# Set the CMD to your handler
CMD ["handlers/upload_handler.lambda_handler"]
