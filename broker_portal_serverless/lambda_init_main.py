"""
Modified version of init_main.py for Lambda environment.
This version doesn't require the project root to be named 'truce'.
"""
import inspect
import os
import re
import sys

import dotenv
import pkg_resources


def extract_version(file_path: str) -> str:
    """
    Extract the version number from setup.py file.

    Args:
        file_path (str): The path to the setup.py file.

    Returns:
        str: The version number.
    """
    version_pattern = r"version=['\"](.+?)['\"]"
    try:
        with open(file_path) as file:
            content = file.read()
            match = re.search(version_pattern, content)
            if match:
                return match.group(1)
    except FileNotFoundError:
        return "0.0.0"  # Return a default version if setup.py is not found
    return "0.0.0"


def initialize():
    """
    Adjust sys.path to include the project root directory.
    This function is modified for Lambda environment to use /var/task as the project root.
    """
    if getattr(initialize, "initialized", False):
        frame = inspect.currentframe().f_back
        print(
            f"Warning: Duplicate call to initialize()."
            f" Previously initialized in {frame.f_code.co_filename} at line {frame.f_lineno}."
        )
        return initialize.project_root

    # In Lambda, use /var/task as the project root
    project_root = "/var/task"
    
    sys.path.append(project_root)
    
    # Try to load environment variables, but don't fail if the file doesn't exist
    try:
        dotenv.load_dotenv(os.path.join(project_root, "common", "credentials.env"))
    except Exception as e:
        print(f"Warning: Failed to load credentials.env: {e}")
    
    initialize.initialized = True
    initialize.project_root = project_root
    
    # Skip version check in Lambda environment
    try:
        installed_version = pkg_resources.get_distribution("common").version
        setup_version = extract_version(os.path.join(project_root, "setup.py"))
        if installed_version != setup_version:
            print(
                f"Warning: The installed version of the common package ({installed_version}) "
                f"does not match the version in setup.py ({setup_version})."
            )
    except Exception as e:
        print(f"Warning: Failed to check package version: {e}")
    
    return project_root


if __name__ == "__main__":
    print(initialize())
    print(initialize())  # This should print a warning about duplicate calls
