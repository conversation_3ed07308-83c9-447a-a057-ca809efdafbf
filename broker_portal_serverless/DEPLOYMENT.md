# Broker Portal Serverless Deployment Guide

This guide provides step-by-step instructions for deploying the broker portal serverless application to AWS.

## Prerequisites

- AWS CLI installed and configured with appropriate permissions
- Docker installed and running
- Python 3.11 or later
- Access to the AWS Management Console

## 1. Create AWS Resources

### 1.1 Create S3 Bucket

1. Go to the S3 service in the AWS Console
2. Click "Create bucket"
3. Enter a name for your bucket: `broker-portal-dev-{your-account-id}`
4. Configure other settings as needed (default settings are usually fine)
5. Click "Create bucket"
6. Create the following folders in the bucket:
   - `uploads/`
   - `logs/`
   - `lambda/`

### 1.2 Create DynamoDB Tables

1. Go to the DynamoDB service in the AWS Console
2. Click "Create table"
3. Enter table name: `BrokerPortalJobs-dev`
4. Enter partition key: `job_id` (String)
5. Enter sort key: `created_at` (Number)
6. Click "Create table"
7. Once the table is created, go to the "Indexes" tab
8. Click "Create index"
9. Enter index name: `BrokerNameIndex`
10. Enter partition key: `broker_name` (String)
11. Enter sort key: `created_at` (Number)
12. Click "Create index"

13. Click "Create table" again
14. Enter table name: `BrokerPortalBrokers-dev`
15. Enter partition key: `broker_name` (String)
16. Click "Create table"

### 1.3 Create SQS Queue

1. Go to the SQS service in the AWS Console
2. Click "Create queue"
3. Enter queue name: `broker-portal-ingestion-queue-dev`
4. Select queue type: "Standard"
5. Configure other settings as needed (default settings are usually fine)
6. Click "Create queue"
7. Note the queue URL for later use

### 1.4 Create IAM Roles and Policies

1. Go to the IAM service in the AWS Console
2. Click "Roles" and then "Create role"
3. Select "Lambda" as the trusted entity
4. Click "Next: Permissions"
5. Attach the following managed policies:
   - `AWSLambdaBasicExecutionRole`
6. Click "Next: Tags" and add any tags if needed
7. Click "Next: Review"
8. Enter role name: `broker-portal-lambda-role-dev`
9. Click "Create role"

10. Click "Policies" and then "Create policy"
11. Select the JSON tab and paste the following policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::broker-portal-dev-{your-account-id}",
                "arn:aws:s3:::broker-portal-dev-{your-account-id}/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "dynamodb:GetItem",
                "dynamodb:PutItem",
                "dynamodb:UpdateItem",
                "dynamodb:Query",
                "dynamodb:Scan"
            ],
            "Resource": [
                "arn:aws:dynamodb:{region}:{your-account-id}:table/BrokerPortalJobs-dev",
                "arn:aws:dynamodb:{region}:{your-account-id}:table/BrokerPortalJobs-dev/index/*",
                "arn:aws:dynamodb:{region}:{your-account-id}:table/BrokerPortalBrokers-dev"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:GetQueueAttributes"
            ],
            "Resource": "arn:aws:sqs:{region}:{your-account-id}:broker-portal-ingestion-queue-dev"
        }
    ]
}
```

12. Click "Review policy"
13. Enter policy name: `broker-portal-lambda-policy-dev`
14. Click "Create policy"

15. Go back to the "Roles" section
16. Click on the `broker-portal-lambda-role-dev` role
17. Click "Attach policies"
18. Search for and select the `broker-portal-lambda-policy-dev` policy
19. Click "Attach policy"

## 2. Build and Push Docker Image

1. Make the build script executable:
   ```bash
   chmod +x broker_portal_serverless/build_upload_image.sh
   ```

2. Run the build script:
   ```bash
   ./broker_portal_serverless/build_upload_image.sh
   ```

3. Note the image URI that is output at the end of the script.

## 3. Create Lambda Function

1. Go to the Lambda service in the AWS Console
2. Click "Create function"
3. Select "Container image" as the function source
4. Enter function name: `broker-portal-upload-file-dev`
5. Click "Browse images" and select the upload_file image from the ECR repository
6. Under "Permissions", select "Use an existing role" and choose `broker-portal-lambda-role-dev`
7. Click "Create function"
8. Once created, go to the "Configuration" tab
9. Under "Environment variables", add:
   - `BUCKET_NAME`: `broker-portal-dev-{your-account-id}`
   - `DYNAMODB_JOBS_TABLE`: `BrokerPortalJobs-dev`
   - `DYNAMODB_BROKERS_TABLE`: `BrokerPortalBrokers-dev`
   - `SQS_QUEUE_URL`: (Your SQS queue URL)
   - `GEMINI_API_KEY`: (Your Gemini API key)
10. Under "General configuration", set:
    - Memory: 512 MB
    - Timeout: 30 seconds
11. Click "Save"

## 4. Create API Gateway

1. Go to the API Gateway service in the AWS Console
2. Click "Create API"
3. Select "REST API" and click "Build"
4. Enter API name: `broker-portal-api-dev`
5. Click "Create API"
6. Click "Actions" and select "Create Resource"
7. Enter resource name: `upload`
8. Click "Create Resource"
9. With the `/upload` resource selected, click "Actions" and select "Create Method"
10. Select "POST" and click the checkmark
11. For "Integration type", select "Lambda Function"
12. Check "Lambda Proxy integration"
13. Select the region where your Lambda function is deployed
14. Enter Lambda function name: `broker-portal-upload-file-dev`
15. Click "Save"
16. When prompted to add permissions, click "OK"
17. Click "Actions" and select "Deploy API"
18. For "Deployment stage", select "New Stage"
19. Enter stage name: `dev`
20. Click "Deploy"
21. Note the "Invoke URL" at the top of the page - this is the base URL for your API

## 5. Test the API

You can test the API using Postman or curl:

```bash
curl -X POST \
  "{invoke-url}/upload?broker_name=TestBroker&analyze=true" \
  -H "Content-Type: application/octet-stream" \
  -H "filename: test.csv" \
  --data-binary "@/path/to/your/test.csv"
```

Replace `{invoke-url}` with your API Gateway Invoke URL, and `/path/to/your/test.csv` with the path to a test CSV file.

## Next Steps

After implementing the upload functionality, you can proceed to implement:

1. Map Columns Handler
2. Status Handler
3. Ingest Handler
