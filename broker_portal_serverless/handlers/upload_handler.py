"""
Lambda handler for the upload endpoint.
"""

import boto3
import os
import uuid
import base64
import json
import logging
import tempfile
import pandas as pd
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Import ingestion modules
import sys
sys.path.append('/var/task')

# Add additional paths to sys.path to help find modules
sys.path.insert(0, '/var/task/ingestion')
sys.path.insert(0, '/var/task/common')

# Log the Python path for debugging
logger.info(f"PYTHONPATH: {sys.path}")

# Define utility functions directly in the handler to avoid import issues
def save_upload_file_to_s3(file_content: bytes, job_id: str, broker_name: str, filename: str, bucket_name: str) -> str:
    """
    Save an uploaded file to S3 under uploads/{broker_name}/{job_id}_{filename}.
    Returns the S3 key.
    """
    s3 = boto3.client('s3')
    s3_key = f"uploads/{broker_name}/{job_id}_{filename}"
    s3.put_object(Bucket=bucket_name, Key=s3_key, Body=file_content)
    return s3_key

def analyze_columns_from_s3(bucket_name: str, s3_key: str, job_id: str, broker_name: str = None):
    """
    Download file from S3 to /tmp, then analyze its columns.
    """
    logger.info(f"Analyzing columns from S3 for job {job_id}, broker {broker_name}")

    # Import ingestion modules with proper path setup
    try:
        # First try to import from ingestion
        from ingestion import constants # Use the wrapper at the root level
        from ingestion import ingestion
        logger.info("Successfully imported ingestion and constants modules")
    except ImportError as e:
        logger.error(f"Error importing modules: {e}")
        # Try a different approach if the first one fails
        try:
            from ingestion import constants
            from ingestion import ingestion
            logger.info("Successfully imported modules using alternative approach")
        except ImportError as e2:
            logger.error(f"Error importing modules (alternative approach): {e2}")
            raise

    # Import AI column mapping
    try:
        from ingestion.mapping.ai_column_mapping import ai_column_mapping
        logger.info("Successfully imported ai_column_mapping")
    except ImportError as e:
        logger.error(f"Error importing ai_column_mapping: {e}")
        # Create a simple fallback function if import fails
        def ai_column_mapping(*args, **kwargs):
            logger.warning("Using fallback mapping (identity mapping)")
            return {h: h for h in args[0] if h in args[1]}

    s3 = boto3.client('s3')
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(bucket_name, s3_key, tmp_file)
        tmp_file_path = tmp_file.name

    try:
        # Read the input file
        df = ingestion.read_input_file(tmp_file_path)

        # Get original headers and template headers
        original_headers = list(df.columns)
        template_headers = [g.value for g in constants.GC]

        # Get sample data
        sample_data_raw = df.head(5).to_dict(orient='records')

        # Convert all values to strings to avoid serialization issues
        sample_data = []
        for row in sample_data_raw:
            string_row = {}
            for key, value in row.items():
                if pd.isna(value):
                    string_row[key] = None
                else:
                    string_row[key] = str(value)
            sample_data.append(string_row)

        # Try AI-based mapping
        ai_mapping = None
        try:
            mapping_issues = {}
            ai_mapping = ai_column_mapping(
                original_headers=original_headers,
                template_headers=template_headers,
                sample_data=sample_data,
                issues=mapping_issues
            )
            logger.info(f"AI mapping successful: {len(ai_mapping) if ai_mapping else 0} columns mapped")
            # Log each mapping individually, similar to traditional mapping
            if ai_mapping:
                for orig_col, mapped_col in ai_mapping.items():
                    logger.info(f"AI mapping: '{orig_col}' -> '{mapped_col}'")
        except Exception as e:
            logger.warning(f"AI mapping failed: {e}")

        return original_headers, template_headers, sample_data, ai_mapping
    finally:
        # Clean up temporary file
        os.unlink(tmp_file_path)

def lambda_handler(event, _):
    """
    AWS Lambda handler for the upload endpoint (API Gateway proxy integration).
    Expects multipart or base64-encoded file in event['body'] and form fields in event['queryStringParameters'] or event['body'].

    Args:
        event: API Gateway event
        _: Lambda context (unused)

    Returns:
        API Gateway response
    """
    logger.info("Processing upload request")

    # Initialize AWS clients
    dynamodb = boto3.resource('dynamodb')

    # Get environment variables
    BUCKET_NAME = os.environ['BUCKET_NAME']
    JOBS_TABLE = os.environ['DYNAMODB_JOBS_TABLE']

    try:
        # Parse form fields (adjust as per your API Gateway integration)
        params = event.get('queryStringParameters', {}) or {}
        broker_name = params.get('broker_name')
        broker_id = params.get('broker_id')
        analyze = params.get('analyze', 'true').lower() == 'true'

        # Validate required parameters
        if not broker_name:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing required parameter: broker_name'}),
                'headers': {'Content-Type': 'application/json'}
            }

        # Parse file (assume base64-encoded in event['body'])
        try:
            file_content = base64.b64decode(event['body'])
        except Exception as e:
            logger.error(f"Error decoding file content: {str(e)}")
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Invalid file content'}),
                'headers': {'Content-Type': 'application/json'}
            }

        # Get filename from headers or generate a random one
        filename = event['headers'].get('filename', f"{uuid.uuid4()}.csv")

        # Generate job_id
        job_id = str(uuid.uuid4())

        logger.info(f"Saving file to S3 for job {job_id}")

        # Save file to S3
        s3_key = save_upload_file_to_s3(file_content, job_id, broker_name, filename, BUCKET_NAME)

        # Initialize response data
        original_headers = template_headers = sample_data = ai_mapping = None

        # Analyze columns if requested
        if analyze:
            logger.info(f"Analyzing columns for job {job_id}")
            try:
                original_headers, template_headers, sample_data, ai_mapping = analyze_columns_from_s3(
                    BUCKET_NAME, s3_key, job_id, broker_name
                )
                logger.info(f"Column analysis completed for job {job_id}")
            except Exception as e:
                logger.error(f"Error analyzing columns: {str(e)}")
                # Continue without analysis results

        # Create job record in DynamoDB
        logger.info(f"Creating job record in DynamoDB for job {job_id}")
        jobs_table = dynamodb.Table(JOBS_TABLE)
        job_item = {
            'job_id': job_id,
            'created_at': int(datetime.now().timestamp()),
            'broker_name': broker_name,
            'broker_id': broker_id,
            'filename': filename,
            's3_key': s3_key,
            'status': 'PENDING',
            'original_headers': original_headers,
            'template_headers': template_headers,
            'sample_data': sample_data,
            'ai_mapping': ai_mapping,
        }
        jobs_table.put_item(Item=job_item)

        # Return response
        logger.info(f"Upload and analyze completed for job {job_id}")
        return {
            'statusCode': 200,
            'body': json.dumps({
                'job_id': job_id,
                'broker_name': broker_name,
                'broker_id': broker_id,
                'filename': filename,
                's3_key': s3_key,
                'status': 'PENDING',
                'original_headers': original_headers,
                'template_headers': template_headers,
                'sample_data': sample_data,
                'ai_mapping': ai_mapping,
            }),
            'headers': {'Content-Type': 'application/json'}
        }
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': f'Internal server error: {str(e)}'}),
            'headers': {'Content-Type': 'application/json'}
        }
