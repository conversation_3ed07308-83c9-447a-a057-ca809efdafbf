FROM public.ecr.aws/lambda/python:3.11

# Copy requirements first for better caching
COPY requirements.txt ${LAMBDA_TASK_ROOT}

# Copy ingestion and common module requirements
COPY ingestion/requirements.txt ${LAMBDA_TASK_ROOT}/ingestion_requirements.txt
COPY common/requirements.txt ${LAMBDA_TASK_ROOT}/common_requirements.txt

# Install all dependencies
RUN pip install -r requirements.txt && \
    pip install -r ingestion_requirements.txt && \
    pip install -r common_requirements.txt

# Copy function code
COPY . ${LAMBDA_TASK_ROOT}

# Copy ingestion and common modules
COPY ingestion/ ${LAMBDA_TASK_ROOT}/ingestion/
COPY common/ ${LAMBDA_TASK_ROOT}/common/

# Set the PYTHONPATH to include ingestion and common modules
ENV PYTHONPATH="${LAMBDA_TASK_ROOT}:${PYTHONPATH}"
ENV DYNAMODB_JOBS_TABLE="BrokerPortalJobs-dev"
ENV BUCKET_NAME="broker-portal-183631323543-dev"
ENV AWS_DEFAULT_REGION="ap-south-1"
ENV AWS_ACCESS_KEY_ID="********************"
ENV AWS_SECRET_ACCESS_KEY="+XoNFIgED0zRD97+uvqGhk3AnzB6JoKqkbHXFFNT"

# Set the CMD to your handler
CMD ["handlers/upload_handler.lambda_handler"] 