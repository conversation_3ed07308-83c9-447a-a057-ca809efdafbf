#!/usr/bin/env python3
"""
Test script to check if all imports work correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Ensure we're in the project root directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Load environment variables
env_path = os.path.join(script_dir, 'broker_portal_serverless', '.env')
load_dotenv(env_path)

print(f"Working directory: {os.getcwd()}")
print(f"Python path: {sys.path[:3]}")  # Show first 3 entries

def test_imports():
    """Test if all required modules can be imported."""

    print("🧪 Testing Imports")
    print("=" * 20)

    try:
        print("✅ Testing basic imports...")
        import boto3
        import pandas as pd
        from dotenv import load_dotenv
        print("✅ Basic imports successful")
    except ImportError as e:
        print(f"❌ Basic imports failed: {e}")
        return False

    try:
        print("✅ Testing ingestion imports...")
        from ingestion import ingestion
        from ingestion.mapping.column_mapping import GoldenColumns
        from common.rds_api import RDS
        print("✅ Ingestion imports successful")
    except ImportError as e:
        print(f"❌ Ingestion imports failed: {e}")
        print("   Make sure you're running from the project root directory")
        return False

    try:
        print("✅ Testing broker portal imports...")
        from broker_portal_serverless.config import BUCKET_NAME
        from broker_portal_serverless.aws_clients import get_s3_client
        print("✅ Broker portal imports successful")
    except ImportError as e:
        print(f"❌ Broker portal imports failed: {e}")
        return False

    try:
        print("✅ Testing FastAPI imports...")
        from fastapi import FastAPI
        import uvicorn
        print("✅ FastAPI imports successful")
    except ImportError as e:
        print(f"❌ FastAPI imports failed: {e}")
        print("   Please install dependencies: pip install -r requirements-broker-portal.txt")
        return False

    try:
        print("✅ Testing broker portal API...")
        from broker_portal_serverless.api import app
        print("✅ Broker portal API import successful")
        print(f"✅ FastAPI app created: {type(app)}")
    except ImportError as e:
        print(f"❌ Broker portal API import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Broker portal API creation failed: {e}")
        return False

    print("\n🎉 All imports successful!")
    return True

def test_environment():
    """Test environment variables."""

    print("\n🔧 Testing Environment Variables")
    print("=" * 30)

    required_vars = [
        'BUCKET_NAME',
        'DYNAMODB_JOBS_TABLE',
        'DYNAMODB_BROKERS_TABLE',
        'SQS_QUEUE_URL',
        'AWS_DEFAULT_REGION'
    ]

    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
        else:
            print(f"❌ {var} = NOT SET")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n⚠️  Missing variables: {', '.join(missing_vars)}")
        print("   Please update broker_portal_serverless/.env")
        return False

    return True

def main():
    """Main test function."""

    print("🚀 Broker Portal Import Test")
    print("=" * 30)

    # Test imports
    if not test_imports():
        print("\n❌ Import test failed")
        return

    # Test environment
    if not test_environment():
        print("\n❌ Environment test failed")
        return

    print("\n✅ All tests passed!")
    print("🚀 You can now run: python run_broker_portal.py")

if __name__ == "__main__":
    main()
