#!/usr/bin/env python3
"""
Simple test to check if we can run the broker portal without complex dependencies.
"""

import os
import sys
from dotenv import load_dotenv

# Ensure we're in the project root directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Load environment variables
env_path = os.path.join(script_dir, 'broker_portal_serverless', '.env')
load_dotenv(env_path)

print(f"Working directory: {os.getcwd()}")
print(f"Environment loaded from: {env_path}")

def test_basic_setup():
    """Test basic setup without complex imports."""
    
    print("\n🔧 Environment Variables:")
    print("=" * 30)
    
    required_vars = [
        'BUCKET_NAME',
        'DYNAMODB_JOBS_TABLE', 
        'DYNAMODB_BROKERS_TABLE',
        'SQS_QUEUE_URL',
        'AWS_DEFAULT_REGION'
    ]
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
        else:
            print(f"❌ {var} = NOT SET")
    
    print("\n🔍 Testing AWS Connection:")
    print("=" * 30)
    
    try:
        import boto3
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"✅ AWS Account: {identity.get('Account')}")
        print(f"✅ AWS User/Role: {identity.get('Arn')}")
        
        # Test S3 access
        s3 = boto3.client('s3')
        bucket_name = os.environ.get('BUCKET_NAME')
        if bucket_name:
            try:
                s3.head_bucket(Bucket=bucket_name)
                print(f"✅ S3 Bucket accessible: {bucket_name}")
            except Exception as e:
                print(f"⚠️  S3 Bucket issue: {e}")
        
        # Test DynamoDB access
        dynamodb = boto3.client('dynamodb')
        jobs_table = os.environ.get('DYNAMODB_JOBS_TABLE')
        if jobs_table:
            try:
                dynamodb.describe_table(TableName=jobs_table)
                print(f"✅ DynamoDB Table accessible: {jobs_table}")
            except Exception as e:
                print(f"⚠️  DynamoDB Table issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AWS Connection failed: {e}")
        return False

def test_broker_portal_basic():
    """Test basic broker portal imports."""
    
    print("\n📦 Testing Broker Portal Imports:")
    print("=" * 35)
    
    try:
        print("✅ Testing config...")
        from broker_portal_serverless.config import BUCKET_NAME, API_TITLE
        print(f"   Config loaded: {API_TITLE}")
        
        print("✅ Testing models...")
        from broker_portal_serverless.models import JobStatus, UploadResponse
        print(f"   Models loaded: {JobStatus.PENDING}")
        
        print("✅ Testing AWS clients...")
        from broker_portal_serverless.aws_clients import get_s3_client
        s3_client = get_s3_client()
        print(f"   S3 client created: {type(s3_client)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Broker portal import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fastapi_basic():
    """Test if we can create a basic FastAPI app."""
    
    print("\n🚀 Testing FastAPI:")
    print("=" * 20)
    
    try:
        from fastapi import FastAPI
        app = FastAPI(title="Test App")
        
        @app.get("/health")
        def health():
            return {"status": "ok"}
        
        print("✅ FastAPI app created successfully")
        print("✅ Health endpoint added")
        return True, app
        
    except Exception as e:
        print(f"❌ FastAPI test failed: {e}")
        return False, None

def main():
    """Main test function."""
    
    print("🚀 Simple Broker Portal Test")
    print("=" * 30)
    
    # Test basic setup
    if not test_basic_setup():
        print("\n❌ Basic setup failed")
        return
    
    # Test broker portal imports
    if not test_broker_portal_basic():
        print("\n❌ Broker portal imports failed")
        return
    
    # Test FastAPI
    success, app = test_fastapi_basic()
    if not success:
        print("\n❌ FastAPI test failed")
        return
    
    print("\n✅ All basic tests passed!")
    print("\n🎉 You can try running the full application now!")
    print("   Try: python run_broker_portal.py")

if __name__ == "__main__":
    main()
